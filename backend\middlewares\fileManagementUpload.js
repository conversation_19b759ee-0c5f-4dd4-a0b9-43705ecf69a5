/**
 * 文件管理上传中间件
 * 处理文件管理相关的文件上传
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');
const config = require('../config');

/**
 * 创建文件管理上传目录
 * 目录结构：backend/uploads/file-management/客户名称/YYYY/MM/DD/
 * @param {string} customerName - 客户名称
 */
function createFileManagementDirectory(customerName = 'default') {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    // 清理客户名称，移除不安全的字符
    const safeCustomerName = customerName
        .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows不允许的字符
        .replace(/\s+/g, '_')           // 替换空格为下划线
        .trim();

    const uploadDir = path.join(
        process.cwd(),
        'backend',
        'uploads',
        'file-management',
        safeCustomerName,
        year.toString(),
        month,
        day
    );

    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
        logger.info(`创建文件管理目录: ${uploadDir}`);
    }

    return uploadDir;
}

/**
 * 生成唯一文件名
 */
function generateUniqueFilename(uploadDir, originalName) {
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext);
    let filename = originalName;
    let counter = 1;
    
    // 如果文件已存在，添加数字后缀
    while (fs.existsSync(path.join(uploadDir, filename))) {
        filename = `${baseName}_${counter}${ext}`;
        counter++;
    }
    
    return filename;
}

// 配置文件管理文件存储
const fileManagementStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        try {
            // 从请求体中获取客户名称
            let customerName = 'default';

            // 尝试从不同来源获取客户名称
            if (req.body && req.body.customer_name) {
                customerName = req.body.customer_name;
            } else if (req.body && req.body.customerName) {
                customerName = req.body.customerName;
            }

            const uploadDir = createFileManagementDirectory(customerName);

            // 将目录信息存储到请求对象中，供后续使用
            req.uploadDir = uploadDir;
            req.customerName = customerName;

            cb(null, uploadDir);
        } catch (error) {
            logger.error('创建文件管理上传目录失败:', error);
            cb(error);
        }
    },
    filename: function (req, file, cb) {
        try {
            // 使用已创建的目录
            const uploadDir = req.uploadDir || createFileManagementDirectory(req.customerName || 'default');
            // 生成唯一文件名（保持原始名称）
            const uniqueFilename = generateUniqueFilename(uploadDir, file.originalname);
            cb(null, uniqueFilename);
        } catch (error) {
            logger.error('生成文件管理文件名失败:', error);
            cb(error);
        }
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = config.upload.allowedTypes;
    
    if (allowedTypes.test(file.mimetype) || allowedTypes.test(path.extname(file.originalname).toLowerCase())) {
        cb(null, true);
    } else {
        const error = new Error('不支持的文件类型');
        error.code = 'INVALID_FILE_TYPE';
        cb(error, false);
    }
};

// 创建文件管理上传中间件
const fileManagementUpload = multer({
    storage: fileManagementStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: config.upload.maxFileSize, // 10MB
        files: 10 // 最多10个文件
    },
    // 确保正确处理中文文件名
    preservePath: false,
    // 设置字段名编码
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB for field data
});

// 错误处理中间件
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                return res.status(400).json({
                    success: false,
                    message: '文件大小超过限制（最大10MB）'
                });
            case 'LIMIT_FILE_COUNT':
                return res.status(400).json({
                    success: false,
                    message: '文件数量超过限制（最多10个）'
                });
            case 'LIMIT_UNEXPECTED_FILE':
                return res.status(400).json({
                    success: false,
                    message: '意外的文件字段'
                });
            default:
                return res.status(400).json({
                    success: false,
                    message: '文件上传失败'
                });
        }
    } else if (error && error.code === 'INVALID_FILE_TYPE') {
        return res.status(400).json({
            success: false,
            message: '不支持的文件类型'
        });
    }
    
    next(error);
};

module.exports = {
    fileManagementUpload,
    handleUploadError,
    createFileManagementDirectory,
    generateUniqueFilename
};
