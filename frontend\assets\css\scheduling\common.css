/* 智能排程模块通用样式文件 - Scheduling Common CSS */

/* 此文件包含智能排程模块的通用样式 */
/* 基础样式继承自 /assets/css/common.css */

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

/* 状态样式 */
.status-pending { @apply bg-yellow-100 text-yellow-800; }
.status-running { @apply bg-blue-100 text-blue-800; }
.status-completed { @apply bg-green-100 text-green-800; }
.status-failed { @apply bg-red-100 text-red-800; }

/* 风险等级样式 */
.risk-low { @apply bg-green-100 text-green-800; }
.risk-medium { @apply bg-yellow-100 text-yellow-800; }
.risk-high { @apply bg-red-100 text-red-800; }

/* 优先级样式 */
.priority-high { @apply bg-red-100 text-red-800; }
.priority-medium { @apply bg-yellow-100 text-yellow-800; }
.priority-low { @apply bg-green-100 text-green-800; }

/* 计划卡片样式 */
.plan-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.plan-card.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}
