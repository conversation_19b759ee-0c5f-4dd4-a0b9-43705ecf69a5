/**
 * 维修保养记录列表组件
 * 显示维修保养记录列表，支持搜索、筛选、分页
 */

import {
    getMaintenanceRecords,
    deleteMaintenanceRecord,
    formatDateTime,
    getStatusClass,
    getTypeClass,
    getMaintenanceOptions
} from '/scripts/api/maintenance.js';
import { getEquipmentList } from '/scripts/api/equipment.js';
import ConfirmDialog from '../common/ConfirmDialog.js';

export default {
    components: {
        ConfirmDialog
    },
    props: {
        refreshTrigger: {
            type: Number,
            default: 0
        },
        selectedRecords: {
            type: Array,
            default: () => []
        }
    },
    emits: ['edit', 'view', 'selection-change'],
    setup(props, { emit }) {
        const { ref, reactive, onMounted, watch, computed } = Vue;

        // 数据状态
        const records = ref([]);
        const loading = ref(false);
        const statistics = ref({});
        
        // 分页状态
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const totalRecords = ref(0);
        const totalPages = computed(() => Math.ceil(totalRecords.value / itemsPerPage.value));

        // 搜索筛选状态
        const filters = reactive({
            search: '',
            equipmentId: '',
            type: '',
            status: '',
            technician: '',
            area: '',
            startDate: '',
            endDate: ''
        });

        // 选项数据
        const options = reactive({
            types: [],
            statuses: [],
            areas: [],
            equipmentList: []
        });

        // 批量选择状态 - 使用父组件传入的selectedRecords
        const selectedRecords = computed({
            get: () => props.selectedRecords,
            set: (value) => emit('selection-change', value)
        });
        const selectAll = ref(false);

        // 确认对话框状态
        const showConfirmDialog = ref(false);
        const confirmDialogConfig = ref({
            title: '确认删除',
            message: '确定要删除这条维修记录吗？删除后无法恢复。',
            confirmText: '删除',
            cancelText: '取消'
        });
        const recordToDelete = ref(null);

        // 根据选择的厂区过滤设备列表
        const filteredEquipmentList = computed(() => {
            if (!filters.area) {
                return options.equipmentList;
            }
            return options.equipmentList.filter(equipment => equipment.area === filters.area);
        });

        // 监听选择状态变化，更新全选状态
        watch(() => props.selectedRecords, () => {
            selectAll.value = records.value.length > 0 && props.selectedRecords.length === records.value.length;
        }, { deep: true });

        watch(records, () => {
            // 当记录列表变化时，清空选择状态
            emit('selection-change', []);
            selectAll.value = false;
        });

        /**
         * 加载选项数据
         */
        const loadOptions = async () => {
            try {
                // 获取维修选项数据
                const maintenanceOptionsResponse = await getMaintenanceOptions();
                if (maintenanceOptionsResponse.success) {
                    const data = maintenanceOptionsResponse.data;
                    options.types = data.types || [];
                    options.statuses = data.statuses || [];
                    options.areas = data.areas || [];
                }

                // 获取设备列表
                const equipmentResponse = await getEquipmentList();
                if (equipmentResponse.success) {
                    options.equipmentList = equipmentResponse.data.equipment || [];
                }
            } catch (error) {
                console.error('加载选项数据失败:', error);
            }
        };

        /**
         * 加载维修记录列表
         */
        const loadRecords = async () => {
            try {
                loading.value = true;

                const params = {
                    page: currentPage.value,
                    limit: itemsPerPage.value,
                    ...filters
                };

                // 移除空值参数
                Object.keys(params).forEach(key => {
                    if (params[key] === '' || params[key] === null || params[key] === undefined) {
                        delete params[key];
                    }
                });

                const response = await getMaintenanceRecords(params);

                if (response.success) {
                    records.value = response.data.records || [];

                    if (response.data.pagination) {
                        totalRecords.value = response.data.pagination.total || 0;
                    } else {
                        totalRecords.value = records.value.length;
                    }

                    if (response.data.statistics) {
                        statistics.value = response.data.statistics;
                    }
                } else {
                    console.error('获取维修记录失败:', response.message);
                    records.value = [];
                    totalRecords.value = 0;
                }
            } catch (error) {
                console.error('加载维修记录失败:', error);
                records.value = [];
                totalRecords.value = 0;
            } finally {
                loading.value = false;
            }
        };

        /**
         * 搜索记录（实时筛选）
         */
        const searchRecords = () => {
            currentPage.value = 1;
            loadRecords();
        };

        /**
         * 全选/取消全选
         */
        const toggleSelectAll = () => {
            if (selectAll.value) {
                emit('selection-change', records.value.map(record => record.id));
            } else {
                emit('selection-change', []);
            }
        };

        /**
         * 批量删除记录
         */
        const batchDeleteRecords = () => {
            if (props.selectedRecords.length === 0) {
                return;
            }

            confirmDialogConfig.value = {
                title: '确认批量删除',
                message: `确定要删除选中的 ${props.selectedRecords.length} 条维修记录吗？删除后无法恢复。`,
                confirmText: '删除',
                cancelText: '取消'
            };
            recordToDelete.value = 'batch';
            showConfirmDialog.value = true;
        };

        /**
         * 重置筛选条件
         */
        const resetFilters = () => {
            Object.keys(filters).forEach(key => {
                filters[key] = '';
            });
            currentPage.value = 1;
            loadRecords();
        };

        /**
         * 跳转到指定页码
         */
        const goToPage = (page) => {
            if (page >= 1 && page <= totalPages.value) {
                currentPage.value = page;
                loadRecords();
            }
        };

        /**
         * 查看记录详情
         */
        const viewRecord = (record) => {
            emit('view', record);
        };

        /**
         * 编辑记录
         */
        const editRecord = (record) => {
            emit('edit', record);
        };

        /**
         * 删除记录
         */
        const deleteRecord = (record) => {
            console.log('准备删除记录:', record);
            recordToDelete.value = record;
            showConfirmDialog.value = true;
        };

        /**
         * 确认删除
         */
        const confirmDelete = async () => {
            if (!recordToDelete.value) return;

            try {
                if (recordToDelete.value === 'batch') {
                    // 批量删除
                    console.log('开始批量删除记录:', props.selectedRecords);

                    let successCount = 0;
                    let failCount = 0;

                    for (const recordId of props.selectedRecords) {
                        try {
                            const response = await deleteMaintenanceRecord(recordId);
                            if (response.success) {
                                successCount++;
                            } else {
                                failCount++;
                                console.error('删除记录失败:', recordId, response.message);
                            }
                        } catch (error) {
                            failCount++;
                            console.error('删除记录失败:', recordId, error);
                        }
                    }

                    // 清空选择
                    emit('selection-change', []);
                    selectAll.value = false;

                    // 显示结果消息
                    if (window.showToast) {
                        if (failCount === 0) {
                            window.showToast(`成功删除 ${successCount} 条维修记录`, 'success');
                        } else {
                            window.showToast(`删除完成：成功 ${successCount} 条，失败 ${failCount} 条`, 'warning');
                        }
                    }
                } else {
                    // 单个删除
                    console.log('开始删除记录:', recordToDelete.value.id);
                    const response = await deleteMaintenanceRecord(recordToDelete.value.id);
                    console.log('删除响应:', response);

                    if (response.success) {
                        if (window.showToast) {
                            window.showToast('维修记录删除成功', 'success');
                        }
                    } else {
                        console.error('删除维修记录失败:', response.message);
                        if (window.showToast) {
                            window.showToast(response.message || '删除失败', 'error');
                        }
                    }
                }

                // 重新加载列表
                await loadRecords();

            } catch (error) {
                console.error('删除维修记录失败:', error);
                if (window.showToast) {
                    window.showToast('删除失败', 'error');
                }
            } finally {
                showConfirmDialog.value = false;
                recordToDelete.value = null;
            }
        };

        /**
         * 取消删除
         */
        const cancelDelete = () => {
            showConfirmDialog.value = false;
            recordToDelete.value = null;
        };

        /**
         * 格式化日期（只显示日期，不显示时间）
         */
        const formatDate = (dateString) => {
            if (!dateString) return '-';

            try {
                // 如果是 YYYY-MM-DD 格式，直接返回
                if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                    return dateString;
                }

                // 如果包含时间，提取日期部分
                if (dateString.includes('T')) {
                    return dateString.split('T')[0];
                }

                // 其他情况，解析日期并格式化
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        };

        /**
         * 获取状态样式
         */
        const getStatusStyle = (status) => {
            return getStatusClass(status);
        };

        /**
         * 获取类型样式
         */
        const getTypeStyle = (type) => {
            return getTypeClass(type);
        };

        /**
         * 获取类型样式类
         */
        const getTypeClassLocal = (type) => {
            const typeClasses = {
                'maintenance': 'bg-green-100 text-green-800',
                'repair': 'bg-red-100 text-red-800'
            };
            return typeClasses[type] || 'bg-gray-100 text-gray-800';
        };

        /**
         * 获取类型标签
         */
        const getTypeLabel = (type) => {
            const typeMap = {
                'maintenance': '保养',
                'repair': '维修'
            };
            return typeMap[type] || type;
        };

        /**
         * 获取故障程度样式
         */
        const getSeverityClass = (severity) => {
            const severityMap = {
                'low': 'bg-green-100 text-green-800',
                'medium': 'bg-yellow-100 text-yellow-800',
                'high': 'bg-orange-100 text-orange-800',
                'critical': 'bg-red-100 text-red-800'
            };
            return severityMap[severity] || 'bg-gray-100 text-gray-800';
        };

        /**
         * 获取故障程度标签
         */
        const getSeverityLabel = (severity) => {
            const severityMap = {
                'low': '轻微',
                'medium': '中等',
                'high': '严重',
                'critical': '紧急'
            };
            return severityMap[severity] || severity;
        };

        // 监听刷新触发器
        watch(() => props.refreshTrigger, () => {
            loadRecords();
        });

        // 监听厂区变化，清空设备选择
        watch(() => filters.area, () => {
            filters.equipmentId = '';
        });

        // 监听筛选条件变化，实时筛选
        watch(filters, () => {
            searchRecords();
        }, { deep: true });

        // 组件挂载时加载数据
        onMounted(async () => {
            await loadOptions();
            loadRecords();
        });

        return {
            records,
            loading,
            statistics,
            currentPage,
            itemsPerPage,
            totalRecords,
            totalPages,
            filters,
            options,
            filteredEquipmentList,
            selectedRecords,
            selectAll,
            showConfirmDialog,
            confirmDialogConfig,
            loadRecords,
            searchRecords,
            resetFilters,
            toggleSelectAll,
            batchDeleteRecords,
            goToPage,
            viewRecord,
            editRecord,
            deleteRecord,
            confirmDelete,
            cancelDelete,
            formatDate,
            getStatusStyle,
            getTypeStyle,
            getTypeLabel,
            getTypeClass: getTypeClassLocal,
            getSeverityClass,
            getSeverityLabel
        };
    },
    template: `
        <div class="maintenance-list">
            <!-- 搜索筛选区域 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <!-- 第一行筛选条件 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">搜索记录</label>
                        <input
                            v-model="filters.search"
                            type="text"
                            placeholder="搜索记录编号、描述或操作人..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            @keyup.enter="searchRecords"
                        />
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">记录类型</label>
                        <select
                            v-model="filters.type"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">全部类型</option>
                            <option v-for="type in options.types" :key="type.value" :value="type.value">
                                {{ type.label }}
                            </option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">厂区</label>
                        <select
                            v-model="filters.area"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">全部厂区</option>
                            <option v-for="area in options.areas" :key="area.value" :value="area.label">
                                {{ area.label }}
                            </option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">关联设备</label>
                        <select
                            v-model="filters.equipmentId"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">全部设备</option>
                            <option v-for="equipment in filteredEquipmentList" :key="equipment.id" :value="equipment.id">
                                {{ equipment.code }} - {{ equipment.name }}
                            </option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">操作人</label>
                        <input
                            v-model="filters.technician"
                            type="text"
                            placeholder="请输入操作人..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>

                <!-- 第二行日期筛选 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                        <input
                            v-model="filters.startDate"
                            type="date"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                        <input
                            v-model="filters.endDate"
                            type="date"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div class="flex items-end">
                        <button
                            @click="resetFilters"
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 记录列表 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div v-if="loading" class="p-8 text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-gray-600">加载中...</p>
                </div>

                <div v-else-if="records.length === 0" class="p-8 text-center text-gray-500">
                    <svg class="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <p class="text-gray-500 text-sm">暂无维修记录</p>
                </div>

                <!-- 桌面端表格 -->
                <div v-else-if="records.length > 0" class="hidden md:block overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4">
                                    <input
                                        type="checkbox"
                                        v-model="selectAll"
                                        @change="toggleSelectAll"
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">
                                    记录编号
                                </th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">
                                    关联设备
                                </th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">
                                    类型
                                </th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">
                                    日期/时间
                                </th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">
                                    操作人
                                </th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">
                                    描述
                                </th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-700">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="record in records" :key="record.id" class="border-b border-gray-100 hover:bg-gray-50">
                                <td class="py-3 px-4">
                                    <input
                                        type="checkbox"
                                        :value="record.id"
                                        v-model="selectedRecords"
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="py-3 px-4 text-sm font-medium text-blue-600">
                                    {{ record.id }}
                                </td>
                                <td class="py-3 px-4 text-sm text-gray-900">
                                    <div class="font-medium">{{ record.equipment?.code || '-' }}</div>
                                    <div class="text-xs text-gray-500">{{ record.equipment?.name || '-' }}</div>
                                </td>
                                <td class="py-3 px-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="getTypeClass(record.type)">
                                        {{ getTypeLabel(record.type) }}
                                    </span>
                                </td>
                                <td class="py-3 px-4 text-sm text-gray-900">
                                    <div class="font-medium">{{ formatDate(record.maintenanceDate) }}</div>
                                    <div class="text-xs text-gray-500">
                                        {{ record.startTime || '-' }} - {{ record.endTime || '-' }}
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-sm text-gray-900">
                                    {{ record.technician }}
                                </td>
                                <td class="py-3 px-4 text-sm text-gray-900 max-w-xs">
                                    <div class="truncate" :title="record.description">
                                        {{ record.description || '-' }}
                                    </div>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex items-center space-x-2">
                                        <button @click="viewRecord(record)" class="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded border border-blue-200 hover:bg-blue-50">查看</button>
                                        <button @click="editRecord(record)" class="text-green-600 hover:text-green-800 text-xs px-2 py-1 rounded border border-green-200 hover:bg-green-50">编辑</button>
                                        <button @click="deleteRecord(record)" class="text-red-600 hover:text-red-800 text-xs px-2 py-1 rounded border border-red-200 hover:bg-red-50">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 移动端卡片布局 -->
                <div v-if="records.length > 0" class="md:hidden space-y-3 p-4">
                    <div v-for="record in records" :key="record.id"
                         class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                        <!-- 卡片头部 -->
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <input
                                    type="checkbox"
                                    :value="record.id"
                                    v-model="selectedRecords"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <div>
                                    <h3 class="font-medium text-gray-900 text-sm">{{ record.id }}</h3>
                                    <p class="text-xs text-gray-500">{{ record.equipment?.code || '-' }} - {{ record.equipment?.name || '-' }}</p>
                                </div>
                            </div>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                  :class="getTypeClass(record.type)">
                                {{ getTypeLabel(record.type) }}
                            </span>
                        </div>

                        <!-- 卡片内容 -->
                        <div class="grid grid-cols-2 gap-3 text-xs mb-3">
                            <div>
                                <span class="text-gray-500">日期:</span>
                                <span class="text-gray-900 ml-1">{{ formatDate(record.maintenanceDate) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">操作人:</span>
                                <span class="text-gray-900 ml-1">{{ record.technician }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">开始时间:</span>
                                <span class="text-gray-900 ml-1">{{ record.startTime || '-' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">结束时间:</span>
                                <span class="text-gray-900 ml-1">{{ record.endTime || '-' }}</span>
                            </div>
                        </div>

                        <!-- 描述 -->
                        <div class="text-xs mb-3">
                            <span class="text-gray-500">描述:</span>
                            <p class="text-gray-900 mt-1">{{ record.description || '-' }}</p>
                        </div>

                        <!-- 卡片操作按钮 -->
                        <div class="flex items-center justify-end space-x-2 pt-3 border-t border-gray-100">
                            <button @click="viewRecord(record)"
                                    class="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded border border-blue-200 hover:bg-blue-50">
                                查看
                            </button>
                            <button @click="editRecord(record)"
                                    class="text-orange-600 hover:text-orange-800 text-xs px-2 py-1 rounded border border-orange-200 hover:bg-orange-50">
                                编辑
                            </button>
                            <button @click="deleteRecord(record)"
                                    class="text-red-600 hover:text-red-800 text-xs px-2 py-1 rounded border border-red-200 hover:bg-red-50">
                                删除
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 分页组件 -->
                <div v-if="totalRecords > 0" class="flex flex-col sm:flex-row items-start sm:items-center justify-between px-4 md:px-6 py-4 border-t border-gray-200 space-y-3 sm:space-y-0">
                    <!-- 左侧：记录统计信息 -->
                    <div class="text-sm text-gray-600">
                        共 {{ totalRecords }} 条记录，每页 {{ itemsPerPage }} 条
                    </div>

                    <!-- 右侧：分页控件 -->
                    <div class="flex items-center space-x-1 sm:space-x-2 overflow-x-auto">
                        <!-- 上一页 -->
                        <button @click="goToPage(currentPage - 1)"
                                :disabled="currentPage <= 1"
                                :class="['px-2 sm:px-3 py-1 rounded-md border text-xs sm:text-sm', currentPage <= 1 ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                            <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>

                        <!-- 动态分页按钮 -->
                        <template v-if="totalPages <= 7">
                            <button v-for="page in totalPages" :key="page" @click="goToPage(page)"
                                    :class="['px-2 sm:px-3 py-1 rounded-md text-xs sm:text-sm', currentPage === page ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                {{ page }}
                            </button>
                        </template>
                        <template v-else>
                            <!-- 前部分页码 -->
                            <button v-if="currentPage > 3" @click="goToPage(1)"
                                    class="px-2 sm:px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 text-xs sm:text-sm">
                                1
                            </button>
                            <span v-if="currentPage > 4" class="px-1 text-gray-500 text-xs sm:text-sm">...</span>

                            <!-- 中间页码 -->
                            <button v-for="page in 3" :key="page"
                                    v-if="currentPage - 2 + page > 0 && currentPage - 2 + page <= totalPages"
                                    @click="goToPage(currentPage - 2 + page)"
                                    :class="['px-2 sm:px-3 py-1 rounded-md text-xs sm:text-sm', currentPage === (currentPage - 2 + page) ? 'bg-blue-500 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50']">
                                {{ currentPage - 2 + page }}
                            </button>

                            <!-- 后部分页码 -->
                            <span v-if="currentPage < totalPages - 3" class="px-1 text-gray-500 text-xs sm:text-sm">...</span>
                            <button v-if="currentPage < totalPages - 2" @click="goToPage(totalPages)"
                                    class="px-2 sm:px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 text-xs sm:text-sm">
                                {{ totalPages }}
                            </button>
                        </template>

                        <!-- 下一页 -->
                        <button @click="goToPage(currentPage + 1)"
                                :disabled="currentPage >= totalPages"
                                :class="['px-2 sm:px-3 py-1 rounded-md border text-xs sm:text-sm', currentPage >= totalPages ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-700 hover:bg-gray-50']">
                            <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>

                        <!-- 每页显示数量选择器 -->
                        <select v-model="itemsPerPage" @change="searchRecords"
                                class="ml-2 sm:ml-4 px-1 sm:px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm">
                            <option :value="10">10条/页</option>
                            <option :value="20">20条/页</option>
                            <option :value="50">50条/页</option>
                            <option :value="100">100条/页</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 确认删除对话框 -->
            <ConfirmDialog
                :visible="showConfirmDialog"
                :title="confirmDialogConfig.title"
                :message="confirmDialogConfig.message"
                :confirm-text="confirmDialogConfig.confirmText"
                :cancel-text="confirmDialogConfig.cancelText"
                @confirm="confirmDelete"
                @cancel="cancelDelete"
            />
        </div>
    `
};
