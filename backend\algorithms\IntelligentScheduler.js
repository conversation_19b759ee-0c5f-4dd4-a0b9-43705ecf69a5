/**
 * 智能排程算法核心
 * 实现多方案生成、方案评估和优化算法
 */

const logger = require('../utils/logger');
const schedulingCache = require('../utils/SchedulingCache');
const performanceMonitor = require('../utils/PerformanceMonitor');
const CapacityCalculator = require('./CapacityCalculator');
const ResourceOptimizer = require('./ResourceOptimizer');
const DeliveryPredictor = require('./DeliveryPredictor');

/**
 * 智能排程器类
 * 核心排程算法实现
 */
class IntelligentScheduler {
    constructor() {
        this.capacityCalculator = new CapacityCalculator();
        this.resourceOptimizer = new ResourceOptimizer();
        this.deliveryPredictor = new DeliveryPredictor();
        
        // 算法配置参数
        this.config = {
            maxSolutions: 5,           // 最大方案数量
            optimizationIterations: 100, // 优化迭代次数
            timeHorizonDays: 30,       // 排程时间范围（天）
            bufferTimeRatio: 0.1,      // 缓冲时间比例
            priorityWeights: {         // 优先级权重
                delivery: 0.4,         // 交期权重
                efficiency: 0.3,       // 效率权重
                resource: 0.2,         // 资源利用率权重
                cost: 0.1             // 成本权重
            }
        };

        logger.info('智能排程算法核心初始化完成');
    }

    /**
     * 生成智能排程方案
     * @param {Object} orderData 订单数据
     * @param {Object} constraints 约束条件
     * @returns {Promise<Object>} 排程方案结果
     */
    async generateSchedulePlans(orderData, constraints = {}) {
        // 开始性能监控
        const perfContext = performanceMonitor.startMeasurement('generateSchedulePlans', {
            orderId: orderData.id,
            productId: orderData.productId,
            quantity: orderData.quantity
        });

        try {
            logger.info('开始生成智能排程方案', {
                orderId: orderData.id,
                productId: orderData.productId,
                quantity: orderData.quantity
            });

            // 检查缓存
            const cacheKey = schedulingCache.generateCacheKey(orderData, constraints);
            const cachedResult = schedulingCache.get(cacheKey);

            if (cachedResult) {
                logger.info('使用缓存的排程方案', {
                    orderId: orderData.id,
                    cacheKey
                });

                // 标记为缓存结果
                cachedResult.data.metadata.cached = true;

                // 结束性能监控
                performanceMonitor.endMeasurement(perfContext, true, { cached: true });

                return cachedResult;
            }

            performanceMonitor.addCheckpoint(perfContext, 'cache_check_completed');

            // 1. 数据预处理和验证
            const processedData = await this.preprocessOrderData(orderData);
            performanceMonitor.addCheckpoint(perfContext, 'data_preprocessing_completed');
            
            // 2. 计算产能需求
            const capacityRequirements = await this.capacityCalculator.calculateRequirements(processedData);
            performanceMonitor.addCheckpoint(perfContext, 'capacity_calculation_completed');

            // 3. 资源可用性分析
            const resourceAvailability = await this.resourceOptimizer.analyzeAvailability(
                capacityRequirements,
                constraints
            );
            performanceMonitor.addCheckpoint(perfContext, 'resource_analysis_completed');

            // 4. 生成多个候选方案
            const candidatePlans = await this.generateCandidatePlans(
                processedData,
                capacityRequirements,
                resourceAvailability
            );
            performanceMonitor.addCheckpoint(perfContext, 'candidate_plans_generated', {
                planCount: candidatePlans.length
            });

            // 5. 方案评估和优化
            const optimizedPlans = await this.optimizePlans(candidatePlans, constraints);
            performanceMonitor.addCheckpoint(perfContext, 'plans_optimized');

            // 6. 交期预测
            const plansWithDelivery = await this.addDeliveryPredictions(optimizedPlans);
            performanceMonitor.addCheckpoint(perfContext, 'delivery_predictions_added');

            // 7. 方案排序和筛选
            const finalPlans = this.rankAndSelectPlans(plansWithDelivery);
            performanceMonitor.addCheckpoint(perfContext, 'plans_ranked_and_selected', {
                finalPlanCount: finalPlans.length
            });

            logger.info('智能排程方案生成完成', {
                orderId: orderData.id,
                planCount: finalPlans.length
            });

            const result = {
                success: true,
                data: {
                    orderId: orderData.id,
                    plans: finalPlans,
                    metadata: {
                        generatedAt: new Date().toISOString(),
                        algorithm: 'IntelligentScheduler',
                        version: '1.0.0',
                        cached: false
                    }
                }
            };

            // 缓存结果
            schedulingCache.set(cacheKey, result);

            // 结束性能监控
            performanceMonitor.endMeasurement(perfContext, true, {
                planCount: finalPlans.length,
                cached: false
            });

            return result;

        } catch (error) {
            logger.error('生成智能排程方案失败', {
                error: error.message,
                orderId: orderData.id
            });

            // 结束性能监控（失败）
            performanceMonitor.endMeasurement(perfContext, false, {
                error: error.message
            });

            return {
                success: false,
                message: '生成排程方案失败',
                error: error.message
            };
        }
    }

    /**
     * 预处理订单数据
     * @param {Object} orderData 原始订单数据
     * @returns {Promise<Object>} 处理后的数据
     */
    async preprocessOrderData(orderData) {
        // 数据验证
        if (!orderData.productId || !orderData.quantity || !orderData.requiredDate) {
            throw new Error('订单数据不完整');
        }

        // 获取产品信息和工艺流程
        const productInfo = await this.getProductInfo(orderData.productId);
        const processFlow = await this.getProcessFlow(orderData.productId);

        return {
            ...orderData,
            productInfo,
            processFlow,
            processedAt: new Date().toISOString()
        };
    }

    /**
     * 生成候选排程方案
     * @param {Object} orderData 订单数据
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Array>} 候选方案列表
     */
    async generateCandidatePlans(orderData, capacityReq, resourceAvail) {
        const candidatePlans = [];

        // 方案1: 最早完成方案（优先考虑交期）
        const earliestPlan = await this.generateEarliestCompletionPlan(
            orderData, capacityReq, resourceAvail
        );
        candidatePlans.push(earliestPlan);

        // 方案2: 最高效率方案（优先考虑资源效率）
        const efficiencyPlan = await this.generateHighEfficiencyPlan(
            orderData, capacityReq, resourceAvail
        );
        candidatePlans.push(efficiencyPlan);

        // 方案3: 负载均衡方案（平衡资源使用）
        const balancedPlan = await this.generateBalancedPlan(
            orderData, capacityReq, resourceAvail
        );
        candidatePlans.push(balancedPlan);

        // 方案4: 成本优化方案（最低成本）
        const costOptimizedPlan = await this.generateCostOptimizedPlan(
            orderData, capacityReq, resourceAvail
        );
        candidatePlans.push(costOptimizedPlan);

        // 方案5: 风险最小方案（最稳定）
        const lowRiskPlan = await this.generateLowRiskPlan(
            orderData, capacityReq, resourceAvail
        );
        candidatePlans.push(lowRiskPlan);

        return candidatePlans.filter(plan => plan !== null);
    }

    /**
     * 生成最早完成方案
     * @param {Object} orderData 订单数据
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 排程方案
     */
    async generateEarliestCompletionPlan(orderData, capacityReq, resourceAvail) {
        try {
            // 选择最高产能的设备组合
            const optimalResources = await this.resourceOptimizer.selectOptimalResources(
                capacityReq,
                resourceAvail,
                'earliest_completion'
            );

            // 计算最早完成时间
            const schedule = await this.calculateEarliestSchedule(
                orderData,
                optimalResources
            );

            return {
                id: this.generatePlanId(),
                type: 'earliest_completion',
                name: '最早完成方案',
                description: '优先考虑交期，选择最高产能设备组合',
                schedule,
                resources: optimalResources,
                metrics: await this.calculatePlanMetrics(schedule, optimalResources),
                priority: 1
            };

        } catch (error) {
            logger.error('生成最早完成方案失败', { error: error.message });
            return null;
        }
    }

    /**
     * 生成高效率方案
     * @param {Object} orderData 订单数据
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 排程方案
     */
    async generateHighEfficiencyPlan(orderData, capacityReq, resourceAvail) {
        try {
            // 选择效率最高的设备和操作员组合
            const efficientResources = await this.resourceOptimizer.selectOptimalResources(
                capacityReq,
                resourceAvail,
                'high_efficiency'
            );

            // 计算高效率排程
            const schedule = await this.calculateEfficiencyOptimizedSchedule(
                orderData,
                efficientResources
            );

            return {
                id: this.generatePlanId(),
                type: 'high_efficiency',
                name: '高效率方案',
                description: '优先考虑生产效率，选择最佳设备操作员组合',
                schedule,
                resources: efficientResources,
                metrics: await this.calculatePlanMetrics(schedule, efficientResources),
                priority: 2
            };

        } catch (error) {
            logger.error('生成高效率方案失败', { error: error.message });
            return null;
        }
    }

    /**
     * 生成负载均衡方案
     * @param {Object} orderData 订单数据
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 排程方案
     */
    async generateBalancedPlan(orderData, capacityReq, resourceAvail) {
        try {
            // 平衡资源使用，避免设备过载
            const balancedResources = await this.resourceOptimizer.selectOptimalResources(
                capacityReq,
                resourceAvail,
                'load_balanced'
            );

            // 计算负载均衡排程
            const schedule = await this.calculateBalancedSchedule(
                orderData,
                balancedResources
            );

            return {
                id: this.generatePlanId(),
                type: 'load_balanced',
                name: '负载均衡方案',
                description: '平衡资源使用，避免设备和人员过载',
                schedule,
                resources: balancedResources,
                metrics: await this.calculatePlanMetrics(schedule, balancedResources),
                priority: 3
            };

        } catch (error) {
            logger.error('生成负载均衡方案失败', { error: error.message });
            return null;
        }
    }

    /**
     * 生成成本优化方案
     * @param {Object} orderData 订单数据
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 排程方案
     */
    async generateCostOptimizedPlan(orderData, capacityReq, resourceAvail) {
        try {
            // 选择成本最低的资源组合
            const costOptimalResources = await this.resourceOptimizer.selectOptimalResources(
                capacityReq,
                resourceAvail,
                'cost_optimized'
            );

            // 计算成本优化排程
            const schedule = await this.calculateCostOptimizedSchedule(
                orderData,
                costOptimalResources
            );

            return {
                id: this.generatePlanId(),
                type: 'cost_optimized',
                name: '成本优化方案',
                description: '优先考虑生产成本，选择最经济的资源组合',
                schedule,
                resources: costOptimalResources,
                metrics: await this.calculatePlanMetrics(schedule, costOptimalResources),
                priority: 4
            };

        } catch (error) {
            logger.error('生成成本优化方案失败', { error: error.message });
            return null;
        }
    }

    /**
     * 生成低风险方案
     * @param {Object} orderData 订单数据
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 排程方案
     */
    async generateLowRiskPlan(orderData, capacityReq, resourceAvail) {
        try {
            // 选择最稳定可靠的资源组合
            const reliableResources = await this.resourceOptimizer.selectOptimalResources(
                capacityReq,
                resourceAvail,
                'low_risk'
            );

            // 计算低风险排程（包含更多缓冲时间）
            const schedule = await this.calculateLowRiskSchedule(
                orderData,
                reliableResources
            );

            return {
                id: this.generatePlanId(),
                type: 'low_risk',
                name: '风险最小方案',
                description: '优先考虑稳定性，包含充足缓冲时间',
                schedule,
                resources: reliableResources,
                metrics: await this.calculatePlanMetrics(schedule, reliableResources),
                priority: 5
            };

        } catch (error) {
            logger.error('生成低风险方案失败', { error: error.message });
            return null;
        }
    }

    /**
     * 生成方案ID
     * @returns {string} 方案ID
     */
    generatePlanId() {
        return `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取产品信息
     * @param {string} productId 产品ID
     * @returns {Promise<Object>} 产品信息
     */
    async getProductInfo(productId) {
        // 这里应该调用产品服务获取产品信息
        // 暂时返回模拟数据
        return {
            id: productId,
            name: '产品名称',
            standardTime: 120,
            unit: 'pcs'
        };
    }

    /**
     * 获取工艺流程
     * @param {string} productId 产品ID
     * @returns {Promise<Array>} 工艺流程
     */
    async getProcessFlow(productId) {
        // 这里应该调用工艺流程服务获取流程信息
        // 暂时返回模拟数据
        return [
            { id: 'process1', name: '原料准备', standardTime: 30, setupTime: 10 },
            { id: 'process2', name: '加工成型', standardTime: 60, setupTime: 15 },
            { id: 'process3', name: '质量检测', standardTime: 20, setupTime: 5 },
            { id: 'process4', name: '包装入库', standardTime: 10, setupTime: 5 }
        ];
    }
}

module.exports = IntelligentScheduler;
