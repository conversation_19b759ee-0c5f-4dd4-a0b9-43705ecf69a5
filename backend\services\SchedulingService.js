/**
 * 智能排程服务
 * 提供智能排程功能的业务逻辑层
 */

const IntelligentScheduler = require('../algorithms/IntelligentScheduler');
const logger = require('../utils/logger');

/**
 * 排程服务类
 * 封装智能排程算法，提供业务接口
 */
class SchedulingService {
    constructor() {
        this.scheduler = new IntelligentScheduler();
        logger.info('智能排程服务初始化完成');
    }

    /**
     * 创建智能排程方案
     * @param {Object} orderData 订单数据
     * @param {Object} options 选项参数
     * @returns {Promise<Object>} 排程方案结果
     */
    async createSchedulePlans(orderData, options = {}) {
        try {
            logger.info('开始创建智能排程方案', { 
                orderId: orderData.id,
                productId: orderData.productId,
                quantity: orderData.quantity
            });

            // 数据验证
            const validation = this.validateOrderData(orderData);
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '订单数据验证失败',
                    errors: validation.errors
                };
            }

            // 设置约束条件
            const constraints = {
                maxDeliveryDate: orderData.requiredDate,
                priorityLevel: orderData.priority || 'normal',
                specialRequirements: orderData.specialRequirements || [],
                ...options.constraints
            };

            // 生成排程方案
            const result = await this.scheduler.generateSchedulePlans(orderData, constraints);

            if (result.success) {
                // 保存排程方案到数据库
                await this.saveSchedulePlans(result.data);
                
                logger.info('智能排程方案创建成功', { 
                    orderId: orderData.id,
                    planCount: result.data.plans.length
                });
            }

            return result;

        } catch (error) {
            logger.error('创建智能排程方案失败', { 
                error: error.message, 
                orderId: orderData.id 
            });
            
            return {
                success: false,
                message: '创建排程方案失败',
                error: error.message
            };
        }
    }

    /**
     * 获取排程方案
     * @param {string} orderId 订单ID
     * @returns {Promise<Object>} 排程方案
     */
    async getSchedulePlans(orderId) {
        try {
            logger.info('获取排程方案', { orderId });

            // 从数据库获取排程方案
            const plans = await this.loadSchedulePlans(orderId);

            if (!plans) {
                return {
                    success: false,
                    message: '未找到排程方案'
                };
            }

            return {
                success: true,
                data: plans
            };

        } catch (error) {
            logger.error('获取排程方案失败', { 
                error: error.message, 
                orderId 
            });
            
            return {
                success: false,
                message: '获取排程方案失败',
                error: error.message
            };
        }
    }

    /**
     * 更新排程方案
     * @param {string} orderId 订单ID
     * @param {string} planId 方案ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateSchedulePlan(orderId, planId, updateData) {
        try {
            logger.info('更新排程方案', { orderId, planId });

            // 获取现有方案
            const existingPlans = await this.loadSchedulePlans(orderId);
            if (!existingPlans) {
                return {
                    success: false,
                    message: '未找到排程方案'
                };
            }

            // 查找要更新的方案
            const planIndex = existingPlans.plans.findIndex(plan => plan.id === planId);
            if (planIndex === -1) {
                return {
                    success: false,
                    message: '未找到指定的排程方案'
                };
            }

            // 更新方案
            existingPlans.plans[planIndex] = {
                ...existingPlans.plans[planIndex],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            // 保存更新后的方案
            await this.saveSchedulePlans(existingPlans);

            logger.info('排程方案更新成功', { orderId, planId });

            return {
                success: true,
                data: existingPlans.plans[planIndex],
                message: '排程方案更新成功'
            };

        } catch (error) {
            logger.error('更新排程方案失败', { 
                error: error.message, 
                orderId, 
                planId 
            });
            
            return {
                success: false,
                message: '更新排程方案失败',
                error: error.message
            };
        }
    }

    /**
     * 选择排程方案
     * @param {string} orderId 订单ID
     * @param {string} planId 方案ID
     * @returns {Promise<Object>} 选择结果
     */
    async selectSchedulePlan(orderId, planId) {
        try {
            logger.info('选择排程方案', { orderId, planId });

            // 获取排程方案
            const plans = await this.loadSchedulePlans(orderId);
            if (!plans) {
                return {
                    success: false,
                    message: '未找到排程方案'
                };
            }

            // 查找选择的方案
            const selectedPlan = plans.plans.find(plan => plan.id === planId);
            if (!selectedPlan) {
                return {
                    success: false,
                    message: '未找到指定的排程方案'
                };
            }

            // 标记为选中状态
            plans.plans.forEach(plan => {
                plan.selected = plan.id === planId;
            });

            plans.selectedPlanId = planId;
            plans.selectedAt = new Date().toISOString();

            // 保存选择结果
            await this.saveSchedulePlans(plans);

            // 创建生产计划
            await this.createProductionPlan(orderId, selectedPlan);

            logger.info('排程方案选择成功', { orderId, planId });

            return {
                success: true,
                data: selectedPlan,
                message: '排程方案选择成功'
            };

        } catch (error) {
            logger.error('选择排程方案失败', { 
                error: error.message, 
                orderId, 
                planId 
            });
            
            return {
                success: false,
                message: '选择排程方案失败',
                error: error.message
            };
        }
    }

    /**
     * 重新生成排程方案
     * @param {string} orderId 订单ID
     * @param {Object} newConstraints 新的约束条件
     * @returns {Promise<Object>} 重新生成结果
     */
    async regenerateSchedulePlans(orderId, newConstraints = {}) {
        try {
            logger.info('重新生成排程方案', { orderId });

            // 获取原始订单数据
            const orderData = await this.getOrderData(orderId);
            if (!orderData) {
                return {
                    success: false,
                    message: '未找到订单数据'
                };
            }

            // 合并新的约束条件
            const constraints = {
                ...orderData.constraints,
                ...newConstraints
            };

            // 重新生成方案
            const result = await this.createSchedulePlans(orderData, { constraints });

            if (result.success) {
                logger.info('排程方案重新生成成功', { orderId });
            }

            return result;

        } catch (error) {
            logger.error('重新生成排程方案失败', { 
                error: error.message, 
                orderId 
            });
            
            return {
                success: false,
                message: '重新生成排程方案失败',
                error: error.message
            };
        }
    }

    /**
     * 获取排程统计信息
     * @param {Object} filters 筛选条件
     * @returns {Promise<Object>} 统计信息
     */
    async getSchedulingStatistics(filters = {}) {
        try {
            logger.info('获取排程统计信息', { filters });

            // 这里应该从数据库获取统计数据
            // 暂时返回模拟数据
            const statistics = {
                totalOrders: 150,
                scheduledOrders: 120,
                completedOrders: 80,
                onTimeDeliveries: 72,
                averageLeadTime: 12.5,
                resourceUtilization: {
                    equipment: 0.85,
                    operators: 0.78
                },
                planTypes: {
                    earliest_completion: 45,
                    high_efficiency: 35,
                    load_balanced: 25,
                    cost_optimized: 10,
                    low_risk: 5
                },
                riskDistribution: {
                    low: 0.6,
                    medium: 0.3,
                    high: 0.1
                }
            };

            return {
                success: true,
                data: statistics
            };

        } catch (error) {
            logger.error('获取排程统计信息失败', { error: error.message });
            
            return {
                success: false,
                message: '获取统计信息失败',
                error: error.message
            };
        }
    }

    /**
     * 验证订单数据
     * @param {Object} orderData 订单数据
     * @returns {Object} 验证结果
     */
    validateOrderData(orderData) {
        const errors = [];

        if (!orderData.id) {
            errors.push('订单ID不能为空');
        }

        if (!orderData.productId) {
            errors.push('产品ID不能为空');
        }

        if (!orderData.quantity || orderData.quantity <= 0) {
            errors.push('订单数量必须大于0');
        }

        if (!orderData.requiredDate) {
            errors.push('要求交期不能为空');
        } else {
            const requiredDate = new Date(orderData.requiredDate);
            const today = new Date();
            if (requiredDate <= today) {
                errors.push('要求交期必须晚于当前日期');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 保存排程方案到数据库
     * @param {Object} plansData 方案数据
     * @returns {Promise<void>}
     */
    async saveSchedulePlans(plansData) {
        try {
            // 这里应该保存到数据库
            // 暂时使用内存存储
            if (!this.schedulePlansCache) {
                this.schedulePlansCache = new Map();
            }
            
            this.schedulePlansCache.set(plansData.orderId, plansData);
            
            logger.info('排程方案保存成功', { orderId: plansData.orderId });

        } catch (error) {
            logger.error('保存排程方案失败', { 
                error: error.message, 
                orderId: plansData.orderId 
            });
            throw error;
        }
    }

    /**
     * 从数据库加载排程方案
     * @param {string} orderId 订单ID
     * @returns {Promise<Object|null>} 方案数据
     */
    async loadSchedulePlans(orderId) {
        try {
            // 这里应该从数据库加载
            // 暂时使用内存存储
            if (!this.schedulePlansCache) {
                return null;
            }
            
            const plans = this.schedulePlansCache.get(orderId);
            
            if (plans) {
                logger.info('排程方案加载成功', { orderId });
            }
            
            return plans || null;

        } catch (error) {
            logger.error('加载排程方案失败', { 
                error: error.message, 
                orderId 
            });
            throw error;
        }
    }

    /**
     * 获取订单数据
     * @param {string} orderId 订单ID
     * @returns {Promise<Object|null>} 订单数据
     */
    async getOrderData(orderId) {
        try {
            // 这里应该从数据库获取订单数据
            // 暂时返回模拟数据
            return {
                id: orderId,
                productId: 'prod1',
                quantity: 1000,
                requiredDate: '2024-12-31',
                priority: 'normal',
                constraints: {}
            };

        } catch (error) {
            logger.error('获取订单数据失败', { 
                error: error.message, 
                orderId 
            });
            throw error;
        }
    }

    /**
     * 创建生产计划
     * @param {string} orderId 订单ID
     * @param {Object} selectedPlan 选中的方案
     * @returns {Promise<void>}
     */
    async createProductionPlan(orderId, selectedPlan) {
        try {
            // 这里应该创建详细的生产计划
            // 包括具体的时间安排、资源分配等
            
            const productionPlan = {
                orderId,
                planId: selectedPlan.id,
                schedule: selectedPlan.schedule,
                resources: selectedPlan.resources,
                createdAt: new Date().toISOString(),
                status: 'planned'
            };

            // 保存生产计划
            // 暂时只记录日志
            logger.info('生产计划创建成功', { 
                orderId, 
                planId: selectedPlan.id 
            });

        } catch (error) {
            logger.error('创建生产计划失败', { 
                error: error.message, 
                orderId 
            });
            throw error;
        }
    }
}

module.exports = SchedulingService;
