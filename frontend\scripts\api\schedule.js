/**
 * 生产排程API调用模块
 * 封装所有排程相关的API请求
 */

import { API_URL } from '../config.js';

/**
 * 排程API类
 */
class ScheduleAPI {
    /**
     * 获取排程列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} API响应
     */
    static async getSchedules(params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const url = `${API_URL}/schedules${queryString ? '?' + queryString : ''}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取排程列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取排程详情
     * @param {string} scheduleId 排程ID
     * @returns {Promise<Object>} API响应
     */
    static async getScheduleById(scheduleId) {
        try {
            const response = await fetch(`${API_URL}/schedules/${scheduleId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取排程详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建排程
     * @param {Object} scheduleData 排程数据
     * @returns {Promise<Object>} API响应
     */
    static async createSchedule(scheduleData) {
        try {
            const response = await fetch(`${API_URL}/schedules`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(scheduleData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建排程失败:', error);
            throw error;
        }
    }

    /**
     * 更新排程
     * @param {string} scheduleId 排程ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} API响应
     */
    static async updateSchedule(scheduleId, updateData) {
        try {
            const response = await fetch(`${API_URL}/schedules/${scheduleId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('更新排程失败:', error);
            throw error;
        }
    }

    /**
     * 删除排程
     * @param {string} scheduleId 排程ID
     * @returns {Promise<Object>} API响应
     */
    static async deleteSchedule(scheduleId) {
        try {
            const response = await fetch(`${API_URL}/schedules/${scheduleId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('删除排程失败:', error);
            throw error;
        }
    }

    /**
     * 开始执行排程
     * @param {string} scheduleId 排程ID
     * @returns {Promise<Object>} API响应
     */
    static async startSchedule(scheduleId) {
        try {
            const response = await fetch(`${API_URL}/schedules/${scheduleId}/start`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('开始排程失败:', error);
            throw error;
        }
    }

    /**
     * 暂停排程
     * @param {string} scheduleId 排程ID
     * @returns {Promise<Object>} API响应
     */
    static async pauseSchedule(scheduleId) {
        try {
            const response = await fetch(`${API_URL}/schedules/${scheduleId}/pause`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('暂停排程失败:', error);
            throw error;
        }
    }

    /**
     * 完成排程
     * @param {string} scheduleId 排程ID
     * @returns {Promise<Object>} API响应
     */
    static async completeSchedule(scheduleId) {
        try {
            const response = await fetch(`${API_URL}/schedules/${scheduleId}/complete`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('完成排程失败:', error);
            throw error;
        }
    }

    /**
     * 取消排程
     * @param {string} scheduleId 排程ID
     * @returns {Promise<Object>} API响应
     */
    static async cancelSchedule(scheduleId) {
        try {
            const response = await fetch(`${API_URL}/schedules/${scheduleId}/cancel`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('取消排程失败:', error);
            throw error;
        }
    }

}

export default ScheduleAPI;
