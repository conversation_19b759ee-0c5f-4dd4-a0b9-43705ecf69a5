/**
 * 产能利用率组件
 * 用于显示设备和资源的产能利用情况
 */

export default {
    name: 'CapacityUtilization',
    props: {
        utilizationData: {
            type: Array,
            required: true
        },
        title: {
            type: String,
            default: '产能利用率'
        },
        showChart: {
            type: Boolean,
            default: true
        },
        height: {
            type: Number,
            default: 300
        }
    },
    template: `
        <div class="capacity-utilization">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600">
                        平均利用率: <span class="font-medium">{{ averageUtilization }}%</span>
                    </div>
                    <div class="text-sm text-gray-600">
                        峰值利用率: <span class="font-medium">{{ maxUtilization }}%</span>
                    </div>
                </div>
            </div>

            <!-- 利用率统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">高效利用</p>
                            <p class="text-lg font-bold text-gray-900">{{ highUtilizationCount }}</p>
                            <p class="text-xs text-gray-500">≥80% 利用率</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">中等利用</p>
                            <p class="text-lg font-bold text-gray-900">{{ mediumUtilizationCount }}</p>
                            <p class="text-xs text-gray-500">50-80% 利用率</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">低效利用</p>
                            <p class="text-lg font-bold text-gray-900">{{ lowUtilizationCount }}</p>
                            <p class="text-xs text-gray-500">&lt;50% 利用率</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 利用率图表 -->
            <div v-if="showChart" class="bg-white border border-gray-200 rounded-lg p-4">
                <div class="chart-container" :style="{ height: height + 'px' }">
                    <canvas ref="chartCanvas"></canvas>
                </div>
            </div>

            <!-- 详细数据表格 -->
            <div class="mt-6">
                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <div class="px-4 py-3 border-b border-gray-200">
                        <h4 class="font-medium text-gray-900">详细利用率数据</h4>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        资源名称
                                    </th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        当前利用率
                                    </th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        状态
                                    </th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        趋势
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="item in utilizationData" :key="item.id">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ item.type }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div :class="['h-2 rounded-full', getUtilizationClass(item.utilization)]" 
                                                     :style="{ width: item.utilization + '%' }"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900">{{ item.utilization }}%</span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span :class="['px-2 py-1 text-xs font-medium rounded-full', getStatusClass(item.utilization)]">
                                            {{ getStatusText(item.utilization) }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <svg v-if="item.trend === 'up'" class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg v-else-if="item.trend === 'down'" class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg v-else class="w-4 h-4 text-gray-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="text-sm text-gray-600">{{ getTrendText(item.trend) }}</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `,
    data() {
        return {
            chartInstance: null
        };
    },
    computed: {
        averageUtilization() {
            if (this.utilizationData.length === 0) return 0;
            const total = this.utilizationData.reduce((sum, item) => sum + item.utilization, 0);
            return Math.round(total / this.utilizationData.length);
        },

        maxUtilization() {
            if (this.utilizationData.length === 0) return 0;
            return Math.max(...this.utilizationData.map(item => item.utilization));
        },

        highUtilizationCount() {
            return this.utilizationData.filter(item => item.utilization >= 80).length;
        },

        mediumUtilizationCount() {
            return this.utilizationData.filter(item => item.utilization >= 50 && item.utilization < 80).length;
        },

        lowUtilizationCount() {
            return this.utilizationData.filter(item => item.utilization < 50).length;
        }
    },
    mounted() {
        if (this.showChart) {
            this.createChart();
        }
    },
    watch: {
        utilizationData: {
            handler() {
                if (this.showChart) {
                    this.updateChart();
                }
            },
            deep: true
        }
    },
    beforeUnmount() {
        if (this.chartInstance) {
            this.chartInstance.destroy();
        }
    },
    methods: {
        createChart() {
            if (!this.$refs.chartCanvas || this.utilizationData.length === 0) return;

            const ctx = this.$refs.chartCanvas.getContext('2d');
            
            if (this.chartInstance) {
                this.chartInstance.destroy();
            }

            const labels = this.utilizationData.map(item => item.name);
            const data = this.utilizationData.map(item => item.utilization);
            const colors = data.map(value => {
                if (value >= 80) return 'rgba(16, 185, 129, 0.8)';
                if (value >= 50) return 'rgba(245, 158, 11, 0.8)';
                return 'rgba(239, 68, 68, 0.8)';
            });

            this.chartInstance = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '利用率 (%)',
                        data: data,
                        backgroundColor: colors,
                        borderColor: colors.map(color => color.replace('0.8', '1')),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '利用率 (%)'
                            }
                        }
                    }
                }
            });
        },

        updateChart() {
            if (this.chartInstance) {
                this.chartInstance.destroy();
            }
            this.createChart();
        },

        getUtilizationClass(utilization) {
            if (utilization >= 80) return 'bg-green-500';
            if (utilization >= 50) return 'bg-yellow-500';
            return 'bg-red-500';
        },

        getStatusClass(utilization) {
            if (utilization >= 80) return 'bg-green-100 text-green-800';
            if (utilization >= 50) return 'bg-yellow-100 text-yellow-800';
            return 'bg-red-100 text-red-800';
        },

        getStatusText(utilization) {
            if (utilization >= 80) return '高效';
            if (utilization >= 50) return '中等';
            return '低效';
        },

        getTrendText(trend) {
            const textMap = {
                'up': '上升',
                'down': '下降',
                'stable': '稳定'
            };
            return textMap[trend] || '稳定';
        }
    }
};
