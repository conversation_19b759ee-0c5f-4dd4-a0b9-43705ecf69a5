/**
 * 设备产能管理路由
 * 处理设备产能配置和操作员技能管理相关的API请求
 */

const express = require('express');
const router = express.Router();
const CapacityService = require('../services/CapacityService');
const capacityService = new CapacityService();

// 获取设备的产能配置
router.get('/equipment/:equipmentId/capabilities', async (req, res) => {
    try {
        const { equipmentId } = req.params;
        const capabilities = await capacityService.getEquipmentCapabilities(equipmentId);
        
        res.json({
            success: true,
            data: capabilities
        });
    } catch (error) {
        console.error('获取设备产能配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备产能配置失败',
            error: error.message
        });
    }
});

// 创建设备产能配置
router.post('/equipment/capabilities', async (req, res) => {
    try {
        const capabilityData = req.body;
        const capability = await capacityService.createEquipmentCapability(capabilityData);
        
        res.status(201).json({
            success: true,
            data: capability,
            message: '设备产能配置创建成功'
        });
    } catch (error) {
        console.error('创建设备产能配置失败:', error);
        res.status(500).json({
            success: false,
            message: '创建设备产能配置失败',
            error: error.message
        });
    }
});

// 更新设备产能配置
router.put('/equipment/capabilities/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const capability = await capacityService.updateEquipmentCapability(id, updateData);
        
        res.json({
            success: true,
            data: capability,
            message: '设备产能配置更新成功'
        });
    } catch (error) {
        console.error('更新设备产能配置失败:', error);
        res.status(500).json({
            success: false,
            message: '更新设备产能配置失败',
            error: error.message
        });
    }
});

// 删除设备产能配置
router.delete('/equipment/capabilities/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await capacityService.deleteEquipmentCapability(id);
        
        res.json({
            success: true,
            message: '设备产能配置删除成功'
        });
    } catch (error) {
        console.error('删除设备产能配置失败:', error);
        res.status(500).json({
            success: false,
            message: '删除设备产能配置失败',
            error: error.message
        });
    }
});

// 获取产品的可用设备
router.get('/products/:productId/equipment', async (req, res) => {
    try {
        const { productId } = req.params;
        const equipment = await capacityService.getAvailableEquipmentForProduct(productId);
        
        res.json({
            success: true,
            data: equipment
        });
    } catch (error) {
        console.error('获取产品可用设备失败:', error);
        res.status(500).json({
            success: false,
            message: '获取产品可用设备失败',
            error: error.message
        });
    }
});

// 获取设备的操作员技能
router.get('/equipment/:equipmentId/operator-skills', async (req, res) => {
    try {
        const { equipmentId } = req.params;
        const skills = await capacityService.getEquipmentOperatorSkills(equipmentId);
        
        res.json({
            success: true,
            data: skills
        });
    } catch (error) {
        console.error('获取设备操作员技能失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备操作员技能失败',
            error: error.message
        });
    }
});

// 创建操作员技能记录
router.post('/operator-skills', async (req, res) => {
    try {
        const skillData = req.body;
        const skill = await capacityService.createOperatorSkill(skillData);
        
        res.status(201).json({
            success: true,
            data: skill,
            message: '操作员技能记录创建成功'
        });
    } catch (error) {
        console.error('创建操作员技能记录失败:', error);
        res.status(500).json({
            success: false,
            message: '创建操作员技能记录失败',
            error: error.message
        });
    }
});

// 更新操作员技能记录
router.put('/operator-skills/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const skill = await capacityService.updateOperatorSkill(id, updateData);
        
        res.json({
            success: true,
            data: skill,
            message: '操作员技能记录更新成功'
        });
    } catch (error) {
        console.error('更新操作员技能记录失败:', error);
        res.status(500).json({
            success: false,
            message: '更新操作员技能记录失败',
            error: error.message
        });
    }
});

// 删除操作员技能记录
router.delete('/operator-skills/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await capacityService.deleteOperatorSkill(id);
        
        res.json({
            success: true,
            message: '操作员技能记录删除成功'
        });
    } catch (error) {
        console.error('删除操作员技能记录失败:', error);
        res.status(500).json({
            success: false,
            message: '删除操作员技能记录失败',
            error: error.message
        });
    }
});

// 获取操作员的技能列表
router.get('/operators/:operatorId/skills', async (req, res) => {
    try {
        const { operatorId } = req.params;
        const skills = await capacityService.getOperatorSkills(operatorId);
        
        res.json({
            success: true,
            data: skills
        });
    } catch (error) {
        console.error('获取操作员技能列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取操作员技能列表失败',
            error: error.message
        });
    }
});

// 获取设备的操作员列表
router.get('/equipment/:equipmentId/operators', async (req, res) => {
    try {
        const { equipmentId } = req.params;
        const operators = await capacityService.getEquipmentOperators(equipmentId);
        
        res.json({
            success: true,
            data: operators
        });
    } catch (error) {
        console.error('获取设备操作员列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备操作员列表失败',
            error: error.message
        });
    }
});

// 创建设备操作员关联
router.post('/equipment-operators', async (req, res) => {
    try {
        const relationData = req.body;
        const relation = await capacityService.createEquipmentOperator(relationData);
        
        res.status(201).json({
            success: true,
            data: relation,
            message: '设备操作员关联创建成功'
        });
    } catch (error) {
        console.error('创建设备操作员关联失败:', error);
        res.status(500).json({
            success: false,
            message: '创建设备操作员关联失败',
            error: error.message
        });
    }
});

module.exports = router;
