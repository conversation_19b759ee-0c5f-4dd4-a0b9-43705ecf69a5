/**
 * 权限模板数据访问层
 * 使用SQLite数据库
 */

const databaseManager = require('./database');
const logger = require('../utils/logger');

class PermissionTemplateRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        this.initStatements();
    }

    /**
     * 初始化预编译SQL语句
     */
    initStatements() {
        this.statements = {
            findAll: this.db.prepare(`
                SELECT * FROM permission_templates
                ORDER BY is_built_in DESC, created_at DESC
            `),
            findById: this.db.prepare('SELECT * FROM permission_templates WHERE id = ?'),
            findByName: this.db.prepare('SELECT * FROM permission_templates WHERE name = ?'),
            insert: this.db.prepare(`
                INSERT INTO permission_templates (
                    id, name, description, permissions, is_built_in, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `),
            update: this.db.prepare(`
                UPDATE permission_templates SET
                    name = ?, description = ?, permissions = ?, updated_at = ?
                WHERE id = ?
            `),
            delete: this.db.prepare('DELETE FROM permission_templates WHERE id = ?'),
            checkNameExists: this.db.prepare(`
                SELECT COUNT(*) as count FROM permission_templates
                WHERE name = ? AND id != ?
            `)
        };
    }

    /**
     * 获取所有权限模板
     */
    findAll() {
        try {
            const templates = this.statements.findAll.all();
            return templates.map(template => this.transformTemplate(template));
        } catch (error) {
            logger.error('获取所有权限模板失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找权限模板
     */
    findById(id) {
        try {
            const template = this.statements.findById.get(id);
            return template ? this.transformTemplate(template) : null;
        } catch (error) {
            logger.error(`根据ID查找权限模板失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据名称查找权限模板
     */
    findByName(name) {
        try {
            const template = this.statements.findByName.get(name);
            return template ? this.transformTemplate(template) : null;
        } catch (error) {
            logger.error(`根据名称查找权限模板失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 创建新权限模板
     */
    create(templateData) {
        try {
            const now = new Date().toISOString();
            const id = templateData.id || Date.now().toString();

            this.statements.insert.run(
                id,
                templateData.name,
                templateData.description || '',
                JSON.stringify(templateData.permissions || []),
                templateData.isBuiltIn ? 1 : 0,
                now,
                now
            );

            return this.findById(id);
        } catch (error) {
            logger.error('创建权限模板失败:', error);
            throw error;
        }
    }

    /**
     * 更新权限模板
     */
    update(id, templateData) {
        try {
            const now = new Date().toISOString();

            this.statements.update.run(
                templateData.name,
                templateData.description || '',
                JSON.stringify(templateData.permissions || []),
                now,
                id
            );

            return this.findById(id);
        } catch (error) {
            logger.error(`更新权限模板失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除权限模板
     */
    delete(id) {
        try {
            const result = this.statements.delete.run(id);
            return result.changes > 0;
        } catch (error) {
            logger.error(`删除权限模板失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 检查模板名称是否存在
     */
    isNameExists(name, excludeId = '') {
        try {
            const result = this.statements.checkNameExists.get(name, excludeId);
            return result.count > 0;
        } catch (error) {
            logger.error(`检查模板名称是否存在失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 初始化内置权限模板
     */
    initBuiltInTemplates() {
        const builtInTemplates = [
            {
                id: 'standard',
                name: '标准用户权限',
                description: '适用于普通员工的标准权限配置',
                permissions: ['new_application', 'application_record', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'approver',
                name: '审批人员权限',
                description: '适用于具有审批权限的管理人员',
                permissions: ['pending_approval', 'approved_applications', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'viewer',
                name: '只读用户权限',
                description: '适用于只需要查看权限的用户',
                permissions: ['application_record', 'approved_applications', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'production_manager',
                name: '生产管理员权限',
                description: '适用于生产管理人员的权限配置',
                permissions: [
                    'schedule_view', 'schedule_create', 'schedule_edit', 'schedule_delete',
                    'schedule_execute', 'resource_manage', 'schedule_report', 'user_settings'
                ],
                isBuiltIn: true
            },
            {
                id: 'production_operator',
                name: '生产操作员权限',
                description: '适用于生产操作人员的权限配置',
                permissions: ['schedule_view', 'schedule_execute', 'resource_manage', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'equipment_manager',
                name: '设备管理员权限',
                description: '适用于设备管理人员的权限配置',
                permissions: [
                    'equipment_manage', 'equipment_info', 'equipment_maintenance',
                    'equipment_health', 'user_settings'
                ],
                isBuiltIn: true
            },
            {
                id: 'maintenance_technician',
                name: '维修技术员权限',
                description: '适用于设备维修技术人员的权限配置',
                permissions: ['equipment_info', 'equipment_maintenance', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'quality_inspector',
                name: '质量检测员权限',
                description: '适用于质量检测人员的权限配置',
                permissions: ['quality_upload', 'quality_view', 'quality_download', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'quality_manager',
                name: '质量管理员权限',
                description: '适用于质量管理人员的权限配置',
                permissions: ['quality_upload', 'quality_view', 'quality_download', 'quality_manage', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'quality_viewer',
                name: '质量查看权限',
                description: '适用于只需要查看质量报告的人员',
                permissions: ['quality_view', 'quality_download', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'file_manager',
                name: '文件管理员权限',
                description: '适用于文件管理人员的权限配置',
                permissions: ['file_upload', 'file_view', 'file_download', 'file_manage', 'file_confirm', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'file_uploader',
                name: '文件上传员权限',
                description: '适用于文件上传人员的权限配置',
                permissions: ['file_upload', 'file_view', 'file_download', 'user_settings'],
                isBuiltIn: true
            },
            {
                id: 'file_viewer',
                name: '文件查看权限',
                description: '适用于只需要查看和确认文件的人员',
                permissions: ['file_view', 'file_download', 'file_confirm', 'user_settings'],
                isBuiltIn: true
            }
        ];

        const transaction = this.db.transaction(() => {
            for (const template of builtInTemplates) {
                try {
                    // 检查是否已存在
                    const existing = this.findById(template.id);
                    if (!existing) {
                        this.create(template);
                        logger.info(`创建内置权限模板: ${template.name}`);
                    }
                } catch (error) {
                    logger.error(`创建内置权限模板失败 (${template.name}):`, error);
                }
            }
        });

        transaction();
    }

    /**
     * 转换数据库模板对象为应用层对象
     */
    transformTemplate(dbTemplate) {
        if (!dbTemplate) return null;

        return {
            id: dbTemplate.id,
            name: dbTemplate.name,
            description: dbTemplate.description,
            permissions: JSON.parse(dbTemplate.permissions || '[]'),
            isBuiltIn: dbTemplate.is_built_in === 1,
            createdAt: dbTemplate.created_at,
            updatedAt: dbTemplate.updated_at
        };
    }
}

module.exports = new PermissionTemplateRepository();
