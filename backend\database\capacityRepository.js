/**
 * 设备产能数据访问层
 * 处理设备产能相关的数据库操作
 */

const databaseManager = require('./database');
const { EquipmentCapabilityModel, OperatorSkillModel, EquipmentOperatorModel } = require('../models/capacityModel');
const logger = require('../utils/logger');

class CapacityRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        logger.info('设备产能数据访问层初始化完成');
    }

    /**
     * 创建设备产能记录
     * @param {EquipmentCapabilityModel} capability 设备产能模型
     * @returns {Promise<EquipmentCapabilityModel>} 创建的设备产能
     */
    async createCapability(capability) {
        try {
            const dbData = capability.toDatabase();

            const stmt = this.db.prepare(`
                INSERT INTO equipment_capabilities (
                    id, equipment_id, product_id, process_id, capacity_per_hour,
                    efficiency_factor, setup_time, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `);

            stmt.run(
                dbData.id, dbData.equipment_id, dbData.product_id, dbData.process_id,
                dbData.capacity_per_hour, dbData.efficiency_factor, dbData.setup_time,
                dbData.created_at
            );

            logger.info('设备产能记录创建成功', { capabilityId: capability.id });
            return capability;
        } catch (error) {
            logger.error('设备产能记录创建失败', { error: error.message, capabilityId: capability.id });
            throw error;
        }
    }

    /**
     * 获取设备的产品产能
     * @param {string} equipmentId 设备ID
     * @param {string} productId 产品ID
     * @returns {Promise<EquipmentCapabilityModel|null>} 设备产能模型
     */
    async getEquipmentCapability(equipmentId, productId) {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM equipment_capabilities 
                WHERE equipment_id = ? AND product_id = ?
            `);
            const row = stmt.get(equipmentId, productId);
            
            return row ? EquipmentCapabilityModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('获取设备产能失败', { error: error.message, equipmentId, productId });
            throw error;
        }
    }

    /**
     * 获取设备的所有产能记录
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Array>} 设备产能列表
     */
    async getEquipmentCapabilities(equipmentId) {
        try {
            const stmt = this.db.prepare(`
                SELECT ec.*, p.name as product_name, p.code as product_code
                FROM equipment_capabilities ec
                LEFT JOIN products p ON ec.product_id = p.id
                WHERE ec.equipment_id = ?
                ORDER BY p.name
            `);
            const rows = stmt.all(equipmentId);
            
            return rows.map(row => ({
                ...EquipmentCapabilityModel.fromDatabase(row),
                productName: row.product_name,
                productCode: row.product_code
            }));
        } catch (error) {
            logger.error('获取设备产能列表失败', { error: error.message, equipmentId });
            throw error;
        }
    }

    /**
     * 获取产品的可用设备
     * @param {string} productId 产品ID
     * @returns {Promise<Array>} 可用设备列表
     */
    async getAvailableEquipmentForProduct(productId) {
        try {
            const stmt = this.db.prepare(`
                SELECT ec.*, e.name as equipment_name, e.code as equipment_code, e.status
                FROM equipment_capabilities ec
                LEFT JOIN equipment e ON ec.equipment_id = e.id
                WHERE ec.product_id = ? AND e.status = 'active'
                ORDER BY ec.capacity_per_hour DESC
            `);
            const rows = stmt.all(productId);
            
            return rows.map(row => ({
                ...EquipmentCapabilityModel.fromDatabase(row),
                equipmentName: row.equipment_name,
                equipmentCode: row.equipment_code,
                equipmentStatus: row.status
            }));
        } catch (error) {
            logger.error('获取产品可用设备失败', { error: error.message, productId });
            throw error;
        }
    }

    /**
     * 创建操作员技能记录
     * @param {OperatorSkillModel} skill 操作员技能模型
     * @returns {Promise<OperatorSkillModel>} 创建的操作员技能
     */
    async createOperatorSkill(skill) {
        try {
            const dbData = skill.toDatabase();

            const stmt = this.db.prepare(`
                INSERT INTO operator_skills (
                    id, operator_id, equipment_id, skill_level,
                    efficiency_factor, certification_date, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `);

            stmt.run(
                dbData.id, dbData.operator_id, dbData.equipment_id, dbData.skill_level,
                dbData.efficiency_factor, dbData.certification_date, dbData.created_at
            );

            logger.info('操作员技能记录创建成功', { skillId: skill.id });
            return skill;
        } catch (error) {
            logger.error('操作员技能记录创建失败', { error: error.message, skillId: skill.id });
            throw error;
        }
    }

    /**
     * 获取操作员在特定设备上的技能
     * @param {string} operatorId 操作员ID
     * @param {string} equipmentId 设备ID
     * @returns {Promise<OperatorSkillModel|null>} 操作员技能模型
     */
    async getOperatorSkill(operatorId, equipmentId) {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM operator_skills 
                WHERE operator_id = ? AND equipment_id = ?
            `);
            const row = stmt.get(operatorId, equipmentId);
            
            return row ? OperatorSkillModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('获取操作员技能失败', { error: error.message, operatorId, equipmentId });
            throw error;
        }
    }

    /**
     * 获取设备的所有操作员技能
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Array>} 操作员技能列表
     */
    async getEquipmentOperatorSkills(equipmentId) {
        try {
            const stmt = this.db.prepare(`
                SELECT os.*, u.name as operator_name, u.username
                FROM operator_skills os
                LEFT JOIN users u ON os.operator_id = u.id
                WHERE os.equipment_id = ?
                ORDER BY os.skill_level DESC, os.efficiency_factor DESC
            `);
            const rows = stmt.all(equipmentId);
            
            return rows.map(row => ({
                ...OperatorSkillModel.fromDatabase(row),
                operatorName: row.operator_name,
                operatorUsername: row.username
            }));
        } catch (error) {
            logger.error('获取设备操作员技能失败', { error: error.message, equipmentId });
            throw error;
        }
    }

    /**
     * 获取操作员的所有技能
     * @param {string} operatorId 操作员ID
     * @returns {Promise<Array>} 操作员技能列表
     */
    async getOperatorSkills(operatorId) {
        try {
            const stmt = this.db.prepare(`
                SELECT os.*, e.name as equipment_name, e.code as equipment_code
                FROM operator_skills os
                LEFT JOIN equipment e ON os.equipment_id = e.id
                WHERE os.operator_id = ?
                ORDER BY os.skill_level DESC
            `);
            const rows = stmt.all(operatorId);
            
            return rows.map(row => ({
                ...OperatorSkillModel.fromDatabase(row),
                equipmentName: row.equipment_name,
                equipmentCode: row.equipment_code
            }));
        } catch (error) {
            logger.error('获取操作员技能列表失败', { error: error.message, operatorId });
            throw error;
        }
    }

    /**
     * 创建设备操作员关联
     * @param {EquipmentOperatorModel} relation 设备操作员关联模型
     * @returns {Promise<EquipmentOperatorModel>} 创建的关联
     */
    async createEquipmentOperator(relation) {
        try {
            const dbData = relation.toDatabase();

            const stmt = this.db.prepare(`
                INSERT INTO equipment_operators (
                    id, equipment_id, operator_id, is_primary, created_at
                ) VALUES (?, ?, ?, ?, ?)
            `);

            stmt.run(
                dbData.id, dbData.equipment_id, dbData.operator_id,
                dbData.is_primary, dbData.created_at
            );

            logger.info('设备操作员关联创建成功', { relationId: relation.id });
            return relation;
        } catch (error) {
            logger.error('设备操作员关联创建失败', { error: error.message, relationId: relation.id });
            throw error;
        }
    }

    /**
     * 获取设备的操作员列表
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Array>} 操作员列表
     */
    async getEquipmentOperators(equipmentId) {
        try {
            const stmt = this.db.prepare(`
                SELECT eo.*, u.name as operator_name, u.username,
                       os.skill_level, os.efficiency_factor
                FROM equipment_operators eo
                LEFT JOIN users u ON eo.operator_id = u.id
                LEFT JOIN operator_skills os ON eo.operator_id = os.operator_id AND eo.equipment_id = os.equipment_id
                WHERE eo.equipment_id = ?
                ORDER BY eo.is_primary DESC, os.skill_level DESC
            `);
            const rows = stmt.all(equipmentId);
            
            return rows.map(row => ({
                ...EquipmentOperatorModel.fromDatabase(row),
                operatorName: row.operator_name,
                operatorUsername: row.username,
                skillLevel: row.skill_level,
                efficiencyFactor: row.efficiency_factor
            }));
        } catch (error) {
            logger.error('获取设备操作员列表失败', { error: error.message, equipmentId });
            throw error;
        }
    }

    /**
     * 更新设备产能
     * @param {string} id 产能记录ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<EquipmentCapabilityModel|null>} 更新后的产能记录
     */
    async updateCapability(id, updateData) {
        try {
            const stmt = this.db.prepare(`
                UPDATE equipment_capabilities SET
                    capacity_per_hour = ?, efficiency_factor = ?, setup_time = ?
                WHERE id = ?
            `);

            const result = stmt.run(
                updateData.capacityPerHour,
                updateData.efficiencyFactor,
                updateData.setupTime,
                id
            );

            if (result.changes > 0) {
                logger.info('设备产能更新成功', { capabilityId: id });
                return await this.getCapabilityById(id);
            }

            return null;
        } catch (error) {
            logger.error('设备产能更新失败', { error: error.message, capabilityId: id });
            throw error;
        }
    }

    /**
     * 根据ID获取产能记录
     * @param {string} id 产能记录ID
     * @returns {Promise<EquipmentCapabilityModel|null>} 产能记录
     */
    async getCapabilityById(id) {
        try {
            const stmt = this.db.prepare('SELECT * FROM equipment_capabilities WHERE id = ?');
            const row = stmt.get(id);

            return row ? EquipmentCapabilityModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('根据ID获取产能记录失败', { error: error.message, capabilityId: id });
            throw error;
        }
    }

    /**
     * 删除设备产能记录
     * @param {string} id 产能记录ID
     * @returns {Promise<boolean>} 删除结果
     */
    async deleteCapability(id) {
        try {
            const stmt = this.db.prepare('DELETE FROM equipment_capabilities WHERE id = ?');
            const result = stmt.run(id);

            if (result.changes > 0) {
                logger.info('设备产能记录删除成功', { capabilityId: id });
                return true;
            }

            return false;
        } catch (error) {
            logger.error('设备产能记录删除失败', { error: error.message, capabilityId: id });
            throw error;
        }
    }

    /**
     * 更新操作员技能记录
     * @param {string} id 技能记录ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<OperatorSkillModel|null>} 更新后的技能记录
     */
    async updateOperatorSkill(id, updateData) {
        try {
            const stmt = this.db.prepare(`
                UPDATE operator_skills SET
                    skill_level = ?, efficiency_factor = ?, certification_date = ?
                WHERE id = ?
            `);

            const result = stmt.run(
                updateData.skillLevel,
                updateData.efficiencyFactor,
                updateData.certificationDate,
                id
            );

            if (result.changes > 0) {
                logger.info('操作员技能记录更新成功', { skillId: id });
                return await this.getOperatorSkillById(id);
            }

            return null;
        } catch (error) {
            logger.error('操作员技能记录更新失败', { error: error.message, skillId: id });
            throw error;
        }
    }

    /**
     * 删除操作员技能记录
     * @param {string} id 技能记录ID
     * @returns {Promise<boolean>} 删除结果
     */
    async deleteOperatorSkill(id) {
        try {
            const stmt = this.db.prepare('DELETE FROM operator_skills WHERE id = ?');
            const result = stmt.run(id);

            if (result.changes > 0) {
                logger.info('操作员技能记录删除成功', { skillId: id });
                return true;
            }

            return false;
        } catch (error) {
            logger.error('操作员技能记录删除失败', { error: error.message, skillId: id });
            throw error;
        }
    }

    /**
     * 根据ID获取操作员技能记录
     * @param {string} id 技能记录ID
     * @returns {Promise<OperatorSkillModel|null>} 技能记录
     */
    async getOperatorSkillById(id) {
        try {
            const stmt = this.db.prepare('SELECT * FROM operator_skills WHERE id = ?');
            const row = stmt.get(id);

            return row ? OperatorSkillModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('根据ID获取操作员技能记录失败', { error: error.message, skillId: id });
            throw error;
        }
    }

    /**
     * 获取设备的操作员技能列表
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Array>} 操作员技能列表
     */
    async getEquipmentOperatorSkills(equipmentId) {
        try {
            const stmt = this.db.prepare(`
                SELECT os.*, u.name as operator_name, u.username,
                       eo.is_primary
                FROM operator_skills os
                LEFT JOIN users u ON os.operator_id = u.id
                LEFT JOIN equipment_operators eo ON os.operator_id = eo.operator_id AND os.equipment_id = eo.equipment_id
                WHERE os.equipment_id = ?
                ORDER BY os.skill_level DESC, u.name
            `);
            const rows = stmt.all(equipmentId);

            return rows.map(row => ({
                ...OperatorSkillModel.fromDatabase(row),
                operatorName: row.operator_name,
                operatorUsername: row.username,
                isPrimary: row.is_primary
            }));
        } catch (error) {
            logger.error('获取设备操作员技能列表失败', { error: error.message, equipmentId });
            throw error;
        }
    }
}

module.exports = CapacityRepository;
