/**
 * 设备管理API调用模块
 * 封装所有设备相关的API请求
 */

import { API_URL } from '../config.js';

/**
 * 设备API类
 */
class EquipmentAPI {
    /**
     * 获取设备列表
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.limit - 每页数量
     * @param {string} params.search - 搜索关键词
     * @param {string} params.area - 厂区过滤
     * @param {string} params.status - 状态过滤
     * @returns {Promise<Object>} API响应
     */
    static async getEquipment(params = {}) {
        try {
            // 移除空值参数
            const cleanParams = {};
            Object.keys(params).forEach(key => {
                if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
                    cleanParams[key] = params[key];
                }
            });

            // 强制添加时间戳和随机数参数，绕过所有缓存
            cleanParams._t = Date.now();
            cleanParams._r = Math.random().toString(36).substring(7);

            const queryString = new URLSearchParams(cleanParams).toString();
            const url = `${API_URL}/equipment${queryString ? '?' + queryString : ''}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json',
                    // 添加防缓存请求头
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取设备列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备详情
     * @param {string} id - 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async getEquipmentById(id) {
        try {
            const response = await fetch(`${API_URL}/equipment/${id}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取设备详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建设备
     * @param {Object} equipmentData - 设备数据
     * @returns {Promise<Object>} API响应
     */
    static async createEquipment(equipmentData) {
        try {
            const response = await fetch(`${API_URL}/equipment`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(equipmentData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建设备失败:', error);
            throw error;
        }
    }

    /**
     * 更新设备
     * @param {string} id - 设备ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} API响应
     */
    static async updateEquipment(id, updateData) {
        try {
            const response = await fetch(`${API_URL}/equipment/${id}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('更新设备失败:', error);
            throw error;
        }
    }

    /**
     * 删除设备
     * @param {string} id - 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async deleteEquipment(id) {
        try {
            const response = await fetch(`${API_URL}/equipment/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('删除设备失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备状态统计
     * @returns {Promise<Object>} API响应
     */
    static async getEquipmentStats() {
        try {
            const response = await fetch(`${API_URL}/equipment/stats`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取设备统计失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备维护记录
     * @param {string} equipmentId - 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async getMaintenanceRecords(equipmentId) {
        try {
            const response = await fetch(`${API_URL}/equipment/${equipmentId}/maintenance`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取维护记录失败:', error);
            throw error;
        }
    }

    /**
     * 创建维护记录
     * @param {Object} maintenanceData - 维护记录数据
     * @returns {Promise<Object>} API响应
     */
    static async createMaintenanceRecord(maintenanceData) {
        try {
            const response = await fetch(`${API_URL}/equipment/maintenance`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(maintenanceData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建维护记录失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备健康度评估
     * @param {string} equipmentId - 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async getHealthAssessment(equipmentId) {
        try {
            const response = await fetch(`${API_URL}/equipment/${equipmentId}/health`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取健康度评估失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备可用时间
     * @param {string} equipmentId - 设备ID
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} API响应
     */
    static async getAvailableTime(equipmentId, params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const url = `${API_URL}/equipment/${equipmentId}/available-time${queryString ? '?' + queryString : ''}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取设备可用时间失败:', error);
            throw error;
        }
    }

    /**
     * 计算设备健康度
     * @param {string} equipmentId - 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async calculateHealth(equipmentId) {
        try {
            const response = await fetch(`${API_URL}/equipment/${equipmentId}/health/calculate`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('计算设备健康度失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备健康度历史
     * @param {string} equipmentId - 设备ID
     * @param {Object} options - 查询选项
     * @returns {Promise<Object>} API响应
     */
    static async getHealthHistory(equipmentId, options = {}) {
        try {
            const params = new URLSearchParams();
            if (options.limit) params.append('limit', options.limit);
            if (options.startDate) params.append('startDate', options.startDate);
            if (options.endDate) params.append('endDate', options.endDate);

            const response = await fetch(`${API_URL}/equipment/${equipmentId}/health/history?${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取健康度历史失败:', error);
            throw error;
        }
    }

    /**
     * 获取故障预测
     * @param {string} equipmentId - 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async getFaultPrediction(equipmentId) {
        try {
            const response = await fetch(`${API_URL}/equipment/${equipmentId}/health/prediction`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取故障预测失败:', error);
            throw error;
        }
    }

    /**
     * 获取健康度统计
     * @param {Object} filters - 筛选条件
     * @returns {Promise<Object>} API响应
     */
    static async getHealthStatistics(filters = {}) {
        try {
            const params = new URLSearchParams();
            if (filters.area) params.append('area', filters.area);
            if (filters.level) params.append('level', filters.level);

            const response = await fetch(`${API_URL}/equipment/health/statistics?${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取健康度统计失败:', error);
            throw error;
        }
    }

    /**
     * 批量计算健康度
     * @param {Array} equipmentIds - 设备ID列表
     * @param {Object} options - 选项
     * @returns {Promise<Object>} API响应
     */
    static async batchCalculateHealth(equipmentIds, options = {}) {
        try {
            const response = await fetch(`${API_URL}/equipment/health/batch-calculate`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    equipmentIds,
                    options
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('批量计算健康度失败:', error);
            throw error;
        }
    }
}

export default EquipmentAPI;

/**
 * 获取设备列表（简化版本，用于下拉选择）
 * @returns {Promise<Object>} 设备列表
 */
export async function getEquipmentList() {
    try {
        const response = await EquipmentAPI.getEquipment();
        return response;
    } catch (error) {
        console.error('获取设备列表失败:', error);
        throw error;
    }
}
