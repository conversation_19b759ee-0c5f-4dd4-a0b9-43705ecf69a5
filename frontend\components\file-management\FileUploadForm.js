/**
 * 文件上传表单组件
 * 用于上传客户二认文件
 */

import { getActiveCustomers, getCustomerProducts, createCustomer, createFileRecord, getFileManagementUsers, getHistoricalBatches, fileManagementUtils, FILE_MANAGEMENT_CONSTANTS } from '../../scripts/api/file-management.js';

const { createApp, ref, reactive, computed, onMounted, watch } = Vue;

export default {
    name: 'FileUploadForm',
    setup() {
        // 响应式数据
        const formData = reactive({
            customer_id: '',
            customer_name: '', // 新客户名称输入
            product_model: '',
            batch_number: '',
            certification_content: '', // 认证内容（原产品描述）
            title: '',
            description: '',
            change_description: ''
        });

        const formErrors = reactive({});
        const customers = ref([]);
        const customerProducts = ref([]);
        const allUsers = ref([]);
        const historicalBatches = ref([]); // 历史批次号
        const canInputBatch = ref(true); // 是否可以手动输入批次号
        const selectedUsers = ref([]);
        const selectedFiles = ref([]);
        const isUploading = ref(false);
        const uploadProgress = ref(0);
        const showUserSelection = ref(false);
        const isLoadingUsers = ref(false);
        const userSearchTerm = ref('');
        const expandedSections = reactive({
            notification: true // 默认展开，因为邮件通知现在是必填项
        });

        // 客户选择模式：'select' 或 'input'
        const customerMode = ref('select');

        // 计算属性
        const isFormValid = computed(() => {
            const hasCustomer = customerMode.value === 'select' ?
                formData.customer_id :
                formData.customer_name.trim();

            return hasCustomer &&
                   formData.product_model &&
                   formData.batch_number &&
                   formData.title &&
                   selectedFiles.value.length > 0 &&
                   selectedUsers.value.length > 0;
        });

        // 筛选用户列表
        const filteredUsers = computed(() => {
            if (!userSearchTerm.value) {
                return allUsers.value;
            }

            const searchTerm = userSearchTerm.value.toLowerCase();
            return allUsers.value.filter(user =>
                (user.username && user.username.toLowerCase().includes(searchTerm)) ||
                (user.name && user.name.toLowerCase().includes(searchTerm)) ||
                (user.email && user.email.toLowerCase().includes(searchTerm)) ||
                (user.department && user.department.toLowerCase().includes(searchTerm))
            );
        });

        // 检查用户是否已选择
        const isUserSelected = (user) => {
            return selectedUsers.value.some(u => u.id === user.id);
        };

        const isFirstVersion = computed(() => {
            return customerProducts.value.length === 0 || 
                   !customerProducts.value.some(p => 
                       p.product_model === formData.product_model && 
                       p.batch_number === formData.batch_number
                   );
        });

        const filePreviewList = computed(() => {
            return selectedFiles.value.map(file => fileManagementUtils.generateFilePreview(file));
        });

        // 生命周期
        onMounted(async () => {
            await loadCustomers();
        });

        // 监听客户变化
        watch(() => formData.customer_id, async (newCustomerId) => {
            if (newCustomerId) {
                await loadCustomerProducts(newCustomerId);
            } else {
                customerProducts.value = [];
            }
            // 客户变化时重新加载批次号
            await loadHistoricalBatches();
            handleBatchNumberChange();
        });

        // 监听产品型号变化
        watch(() => formData.product_model, async () => {
            // 产品型号变化时重新加载批次号
            await loadHistoricalBatches();
            handleBatchNumberChange();
        });

        // 方法
        async function loadCustomers() {
            try {
                const response = await getActiveCustomers();
                if (response.success) {
                    customers.value = response.data;
                }
            } catch (error) {
                console.error('加载客户列表失败:', error);
            }
        }

        // 获取历史批次号
        async function loadHistoricalBatches() {
            if (!formData.customer_id || !formData.product_model) {
                historicalBatches.value = [];
                canInputBatch.value = true;
                return;
            }

            try {
                // 这里需要调用API获取历史批次号
                // 暂时模拟数据，后续需要实现对应的API
                const response = await getHistoricalBatches(formData.customer_id, formData.product_model);
                if (response.success && response.data.length > 0) {
                    historicalBatches.value = response.data;
                    canInputBatch.value = false; // 有历史批次时不能手动输入
                } else {
                    historicalBatches.value = [];
                    canInputBatch.value = true; // 没有历史批次时可以手动输入
                }
            } catch (error) {
                console.error('获取历史批次失败:', error);
                historicalBatches.value = [];
                canInputBatch.value = true;
            }
        }

        // 生成下一个批次号
        function generateNextBatchNumber() {
            if (historicalBatches.value.length === 0) {
                return 'V1.0';
            }

            // 找到最大的版本号
            const maxVersion = historicalBatches.value.reduce((max, batch) => {
                const match = batch.match(/V(\d+)\.(\d+)/);
                if (match) {
                    const major = parseInt(match[1]);
                    const minor = parseInt(match[2]);
                    const version = major * 100 + minor;
                    return Math.max(max, version);
                }
                return max;
            }, 0);

            const nextMajor = Math.floor(maxVersion / 100) + 1;
            return `V${nextMajor}.0`;
        }

        // 当客户或产品型号变化时，自动设置批次号
        function handleBatchNumberChange() {
            if (!canInputBatch.value && historicalBatches.value.length > 0) {
                // 如果有历史批次，自动设置为下一个版本
                formData.batch_number = generateNextBatchNumber();
            } else if (canInputBatch.value && !formData.batch_number) {
                // 如果没有历史批次且批次号为空，设置默认值
                formData.batch_number = 'V1.0';
            }
        }

        async function loadCustomerProducts(customerId) {
            try {
                const response = await getCustomerProducts(customerId);
                if (response.success) {
                    customerProducts.value = response.data;
                }
            } catch (error) {
                console.error('加载客户产品失败:', error);
            }
        }

        // 加载所有用户
        async function loadAllUsers() {
            try {
                isLoadingUsers.value = true;
                const response = await getFileManagementUsers();

                if (response.success) {
                    // 只显示有邮箱的活跃用户
                    allUsers.value = response.data.filter(user =>
                        user.active && user.email && user.email.trim() !== ''
                    );
                    showUserSelection.value = true;
                } else {
                    throw new Error(response.message || '获取用户列表失败');
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
                alert('加载用户列表失败: ' + error.message);
            } finally {
                isLoadingUsers.value = false;
            }
        }

        // 切换用户选择显示
        function toggleUserSelectionDisplay() {
            if (showUserSelection.value) {
                showUserSelection.value = false;
            } else {
                loadAllUsers();
            }
        }

        // 切换用户选择
        function toggleUserSelection(user) {
            const index = selectedUsers.value.findIndex(u => u.id === user.id);
            if (index > -1) {
                selectedUsers.value.splice(index, 1);
            } else {
                selectedUsers.value.push(user);
            }
        }

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }

        function handleFileDrop(event) {
            event.preventDefault();
            const files = Array.from(event.dataTransfer.files);
            addFiles(files);
        }

        function addFiles(files) {
            const validFiles = files.filter(file => {
                if (!fileManagementUtils.validateFileType(file)) {
                    alert(`文件 ${file.name} 类型不支持`);
                    return false;
                }
                if (!fileManagementUtils.validateFileSize(file)) {
                    alert(`文件 ${file.name} 大小超过限制（最大10MB）`);
                    return false;
                }
                return true;
            });

            // 检查文件数量限制
            const totalFiles = selectedFiles.value.length + validFiles.length;
            if (totalFiles > FILE_MANAGEMENT_CONSTANTS.MAX_FILE_COUNT) {
                alert(`最多只能上传 ${FILE_MANAGEMENT_CONSTANTS.MAX_FILE_COUNT} 个文件`);
                return;
            }

            selectedFiles.value = [...selectedFiles.value, ...validFiles];
        }

        function removeFile(index) {
            selectedFiles.value.splice(index, 1);
        }

        function validateForm() {
            const errors = {};

            // 验证客户信息
            if (customerMode.value === 'select') {
                if (!formData.customer_id) {
                    errors.customer_id = '请选择客户';
                }
            } else {
                if (!formData.customer_name.trim()) {
                    errors.customer_name = '请输入客户名称';
                }
            }

            if (!formData.product_model) {
                errors.product_model = '请填写产品型号';
            }
            if (!formData.batch_number) {
                errors.batch_number = '请填写或选择批次号';
            }
            if (!formData.title) {
                errors.title = '请填写文件标题';
            }
            if (selectedFiles.value.length === 0) {
                errors.files = '请选择要上传的文件';
            }
            if (selectedUsers.value.length === 0) {
                errors.users = '请选择至少一个用户进行邮件通知';
            }
            if (!isFirstVersion.value && !formData.change_description) {
                errors.change_description = '非首次版本必须填写变更内容';
            }

            Object.assign(formErrors, errors);
            return Object.keys(errors).length === 0;
        }

        async function handleSubmit() {
            if (!validateForm()) {
                return;
            }

            try {
                isUploading.value = true;
                uploadProgress.value = 0;

                let customerId = formData.customer_id;

                // 如果是输入新客户模式，先创建客户
                if (customerMode.value === 'input' && formData.customer_name.trim()) {
                    try {
                        const customerResponse = await createCustomer({
                            customer_name: formData.customer_name.trim(),
                            active: true
                        });

                        if (customerResponse.success) {
                            customerId = customerResponse.data.id;
                            // 重新加载客户列表
                            await loadCustomers();
                        } else {
                            throw new Error(customerResponse.message || '创建客户失败');
                        }
                    } catch (error) {
                        // 如果客户已存在，尝试查找现有客户
                        if (error.message && error.message.includes('已存在')) {
                            const existingCustomer = customers.value.find(c =>
                                c.customer_name === formData.customer_name.trim()
                            );
                            if (existingCustomer) {
                                customerId = existingCustomer.id;
                            } else {
                                throw error;
                            }
                        } else {
                            throw error;
                        }
                    }
                }

                const submitData = new FormData();

                // 添加表单数据，使用确定的客户ID
                Object.keys(formData).forEach(key => {
                    if (key === 'customer_id') {
                        submitData.append(key, customerId);
                    } else if (key !== 'customer_name') { // 不包含customer_name字段
                        submitData.append(key, formData[key]);
                    }
                });

                // 添加选中的用户ID列表
                if (selectedUsers.value.length > 0) {
                    const userIds = selectedUsers.value.map(user => user.id);
                    submitData.append('notifyUsers', JSON.stringify(userIds));
                }

                // 添加文件
                selectedFiles.value.forEach(file => {
                    submitData.append('files', file);
                });

                // 模拟上传进度
                const progressInterval = setInterval(() => {
                    if (uploadProgress.value < 90) {
                        uploadProgress.value += 10;
                    }
                }, 200);

                const response = await createFileRecord(submitData);

                clearInterval(progressInterval);
                uploadProgress.value = 100;

                if (response.success) {
                    alert('文件上传成功！');
                    resetForm();
                } else {
                    throw new Error(response.message || '上传失败');
                }
            } catch (error) {
                console.error('文件上传失败:', error);
                alert('文件上传失败: ' + error.message);
            } finally {
                isUploading.value = false;
                uploadProgress.value = 0;
            }
        }

        function resetForm() {
            Object.keys(formData).forEach(key => {
                formData[key] = '';
            });
            selectedFiles.value = [];
            selectedUsers.value = [];
            showUserSelection.value = false;
            userSearchTerm.value = '';
            Object.keys(formErrors).forEach(key => {
                delete formErrors[key];
            });
        }



        return {
            formData,
            formErrors,
            customers,
            customerProducts,
            allUsers,
            selectedUsers,
            selectedFiles,
            isUploading,
            uploadProgress,
            showUserSelection,
            isLoadingUsers,
            userSearchTerm,
            expandedSections,
            customerMode,
            isFormValid,
            isFirstVersion,
            filePreviewList,
            filteredUsers,
            isUserSelected,
            historicalBatches,
            canInputBatch,
            handleFileSelect,
            handleFileDrop,
            removeFile,
            handleSubmit,
            resetForm,
            loadAllUsers,
            toggleUserSelectionDisplay,
            toggleUserSelection,
            generateNextBatchNumber,
            handleBatchNumberChange,
            FILE_MANAGEMENT_CONSTANTS
        };
    },
    template: `
        <div class="file-upload-form">
            <form @submit.prevent="handleSubmit" class="space-y-6">
                <!-- 客户信息区域 -->
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">客户信息</h3>
                    
                    <div class="space-y-4">
                        <!-- 客户选择模式切换 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">客户选择方式</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input v-model="customerMode" value="select" type="radio"
                                           class="text-blue-600 border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">选择现有客户</span>
                                </label>
                                <label class="flex items-center">
                                    <input v-model="customerMode" value="input" type="radio"
                                           class="text-blue-600 border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">输入新客户</span>
                                </label>
                            </div>
                        </div>

                        <!-- 客户选择/输入 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                客户名称 <span class="text-red-500">*</span>
                            </label>

                            <!-- 选择现有客户 -->
                            <select v-if="customerMode === 'select'"
                                    v-model="formData.customer_id"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    :class="{ 'border-red-500': formErrors.customer_id }">
                                <option value="">请选择客户</option>
                                <option v-for="customer in customers" :key="customer.id" :value="customer.id">
                                    {{ customer.customer_name }}
                                </option>
                            </select>

                            <!-- 输入新客户 -->
                            <input v-else
                                   v-model="formData.customer_name"
                                   type="text"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   :class="{ 'border-red-500': formErrors.customer_name }"
                                   placeholder="请输入新客户名称">

                            <p v-if="formErrors.customer_id" class="text-red-500 text-sm mt-1">{{ formErrors.customer_id }}</p>
                            <p v-if="formErrors.customer_name" class="text-red-500 text-sm mt-1">{{ formErrors.customer_name }}</p>

                            <p v-if="customerMode === 'input'" class="text-sm text-gray-500 mt-1">
                                如果客户已存在，系统将自动关联到现有客户
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 产品信息区域 -->
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">产品信息</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                产品型号 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" v-model="formData.product_model"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   :class="{ 'border-red-500': formErrors.product_model }"
                                   placeholder="请输入产品型号">
                            <p v-if="formErrors.product_model" class="text-red-500 text-sm mt-1">{{ formErrors.product_model }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                批次号 <span class="text-red-500">*</span>
                            </label>
                            <!-- 有历史批次时显示下拉选择 -->
                            <select v-if="!canInputBatch" v-model="formData.batch_number"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    :class="{ 'border-red-500': formErrors.batch_number }">
                                <option value="">请选择新的批次号</option>
                                <option :value="generateNextBatchNumber()">{{ generateNextBatchNumber() }} (新版本)</option>
                            </select>
                            <!-- 没有历史批次时显示输入框 -->
                            <input v-else type="text" v-model="formData.batch_number"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   :class="{ 'border-red-500': formErrors.batch_number }"
                                   placeholder="请输入批次号，例如：V1.0">
                            <p v-if="formErrors.batch_number" class="text-red-500 text-sm mt-1">{{ formErrors.batch_number }}</p>
                            <p v-if="!canInputBatch && historicalBatches.length > 0" class="text-gray-500 text-sm mt-1">
                                历史批次：{{ historicalBatches.join(', ') }}
                            </p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">认证内容</label>
                        <textarea v-model="formData.certification_content"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  rows="3" placeholder="请输入认证内容"></textarea>
                    </div>
                </div>

                <!-- 文件信息区域 -->
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">文件信息</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                文件标题 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" v-model="formData.title" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   :class="{ 'border-red-500': formErrors.title }"
                                   placeholder="请输入文件标题">
                            <p v-if="formErrors.title" class="text-red-500 text-sm mt-1">{{ formErrors.title }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">文件描述</label>
                            <textarea v-model="formData.description" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      rows="3" placeholder="请输入文件描述"></textarea>
                        </div>
                        
                        <div v-if="!isFirstVersion">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                变更内容 <span class="text-red-500">*</span>
                            </label>
                            <textarea v-model="formData.change_description" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      :class="{ 'border-red-500': formErrors.change_description }"
                                      rows="3" placeholder="请详细描述此次变更的内容"></textarea>
                            <p v-if="formErrors.change_description" class="text-red-500 text-sm mt-1">{{ formErrors.change_description }}</p>
                        </div>
                    </div>
                </div>

                <!-- 文件上传区域 -->
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">文件上传</h3>
                    
                    <!-- 拖拽上传区域 -->
                    <div @drop="handleFileDrop" @dragover.prevent @dragenter.prevent
                         class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                        <div class="space-y-2">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="text-gray-600">
                                <label class="cursor-pointer">
                                    <span class="text-blue-600 hover:text-blue-500">点击选择文件</span>
                                    或拖拽文件到此处
                                    <input type="file" multiple @change="handleFileSelect" class="hidden" accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.txt,.csv">
                                </label>
                            </div>
                            <p class="text-sm text-gray-500">
                                支持: {{ FILE_MANAGEMENT_CONSTANTS.SUPPORTED_FILE_TYPES.join(', ') }}
                            </p>
                            <p class="text-sm text-gray-500">
                                最大文件大小: 10MB，最多上传 {{ FILE_MANAGEMENT_CONSTANTS.MAX_FILE_COUNT }} 个文件
                            </p>
                        </div>
                    </div>
                    
                    <!-- 文件预览列表 -->
                    <div v-if="selectedFiles.length > 0" class="mt-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">已选择的文件 ({{ selectedFiles.length }})</h4>
                        <div class="space-y-2">
                            <div v-for="(preview, index) in filePreviewList" :key="index"
                                 class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <span class="text-2xl">{{ preview.icon }}</span>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ preview.name }}</p>
                                        <p class="text-xs text-gray-500">{{ preview.size }}</p>
                                    </div>
                                </div>
                                <button type="button" @click="removeFile(index)"
                                        class="text-red-600 hover:text-red-800 text-sm">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <p v-if="formErrors.files" class="text-red-500 text-sm mt-2">{{ formErrors.files }}</p>
                </div>

                <!-- 邮件通知区域 -->
                <div class="bg-white rounded-lg shadow border">
                    <div class="border-b border-gray-200">
                        <div @click="expandedSections.notification = !expandedSections.notification"
                             class="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-50 transition-colors">
                            <div class="flex items-center space-x-3">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-gray-900">邮件通知 <span class="text-red-500">*</span></h3>
                                <span v-if="selectedUsers.length > 0" class="text-sm text-gray-500">已选择 {{ selectedUsers.length }} 人</span>
                                <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                             expandedSections.notification ? 'rotate-180' : '']"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div v-show="expandedSections.notification" class="p-6">
                        <div class="mb-4">
                            <p class="text-sm text-gray-600 mb-4">
                                选择要通知的用户，他们将收到文件上传完成的邮件通知。
                            </p>
                            <p v-if="formErrors.users" class="text-red-500 text-sm mb-4">{{ formErrors.users }}</p>
                            <button
                                type="button"
                                @click="toggleUserSelectionDisplay"
                                :disabled="isLoadingUsers"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
                            >
                                <span v-if="isLoadingUsers">加载中...</span>
                                <span v-else>{{ showUserSelection ? '隐藏用户列表' : '选择通知用户' }}</span>
                            </button>
                        </div>

                        <!-- 已选择的用户显示 -->
                        <div v-if="selectedUsers.length > 0" class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-3">已选择的用户 ({{ selectedUsers.length }})</h4>
                            <div class="flex flex-wrap gap-2">
                                <span v-for="(user, index) in selectedUsers" :key="user.id"
                                      class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                                    {{ user.username || user.name }}
                                    <button type="button" @click="selectedUsers.splice(index, 1)"
                                            class="ml-2 text-blue-600 hover:text-blue-800 font-bold">
                                        ×
                                    </button>
                                </span>
                            </div>
                        </div>

                        <!-- 用户选择列表 -->
                        <div v-if="showUserSelection" class="border border-gray-200 rounded-lg bg-gray-50">
                            <div class="p-4 border-b border-gray-200">
                                <input
                                    type="text"
                                    v-model="userSearchTerm"
                                    placeholder="搜索用户姓名、工号或部门..."
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                                >
                            </div>
                            <div class="max-h-60 overflow-y-auto">
                                <div
                                    v-for="user in filteredUsers"
                                    :key="user.id"
                                    @click="toggleUserSelection(user)"
                                    class="flex items-center p-4 hover:bg-white cursor-pointer transition-colors border-b border-gray-100 last:border-b-0"
                                    :class="{ 'bg-blue-50 border-blue-200': isUserSelected(user) }"
                                >
                                    <div class="flex-shrink-0">
                                        <div class="w-5 h-5 border-2 border-gray-300 rounded flex items-center justify-center"
                                             :class="{ 'bg-blue-600 border-blue-600': isUserSelected(user) }">
                                            <svg v-if="isUserSelected(user)" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <div class="text-sm font-medium text-gray-900">{{ user.username || user.name }}</div>
                                        <div class="text-sm text-gray-500">{{ user.email }}</div>
                                        <div v-if="user.department" class="text-xs text-gray-400">{{ user.department }}</div>
                                    </div>
                                </div>
                                <div v-if="filteredUsers.length === 0" class="p-4 text-center text-gray-500">
                                    没有找到匹配的用户
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 上传进度 -->
                <div v-if="isUploading" class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">上传进度</h3>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                             :style="{ width: uploadProgress + '%' }"></div>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">{{ uploadProgress }}%</p>
                </div>

                <!-- 提交按钮 -->
                <div class="flex justify-end space-x-4">
                    <button type="button" @click="resetForm"
                            class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                        重置
                    </button>
                    <button type="submit" :disabled="!isFormValid || isUploading"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                        {{ isUploading ? '上传中...' : '提交' }}
                    </button>
                </div>
            </form>
        </div>
    `
};
