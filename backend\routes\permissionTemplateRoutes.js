/**
 * 权限模板路由
 * 处理权限模板相关的路由
 */

const express = require('express');
const router = express.Router();
const permissionTemplateController = require('../controllers/permissionTemplateController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 获取所有权限模板 - 需要管理权限权限
router.get('/',
    authenticateJWT,
    checkPermission('manage_permissions'),
    permissionTemplateController.getAllTemplates
);

// 根据ID获取权限模板 - 需要管理权限权限
router.get('/:id',
    authenticateJWT,
    checkPermission('manage_permissions'),
    permissionTemplateController.getTemplateById
);

// 创建权限模板 - 需要管理权限权限
router.post('/',
    authenticateJWT,
    checkPermission('manage_permissions'),
    permissionTemplateController.createTemplate
);

// 更新权限模板 - 需要管理权限权限
router.put('/:id',
    authenticateJWT,
    checkPermission('manage_permissions'),
    permissionTemplateController.updateTemplate
);

// 删除权限模板 - 需要管理权限权限
router.delete('/:id',
    authenticateJWT,
    checkPermission('manage_permissions'),
    permissionTemplateController.deleteTemplate
);

module.exports = router;
