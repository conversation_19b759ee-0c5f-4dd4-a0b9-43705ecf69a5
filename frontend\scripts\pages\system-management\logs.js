/**
 * 系统管理 - 系统日志页面
 * 查看系统运行日志和操作记录
 */

import { createAdminApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';

createAdminApp({
    components: {
        Sidebar
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        return {
            sidebarOpen
        };
    },
    onUserLoaded: async (user) => {
        console.log('系统日志页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
