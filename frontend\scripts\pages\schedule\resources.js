/**
 * 资源管理页面逻辑
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const mockData = ref({
            equipment: [
                { id: 'EQ001', name: '生产线A', type: '自动化生产线', status: 'available' },
                { id: 'EQ002', name: '生产线B', type: '自动化生产线', status: 'busy' },
                { id: 'EQ003', name: '包装机1', type: '包装设备', status: 'available' },
                { id: 'EQ004', name: '质检设备', type: '检测设备', status: 'maintenance' },
                { id: 'EQ005', name: '仓储机器人', type: '物流设备', status: 'available' }
            ],
            personnel: [
                { id: 'P001', name: '张三', department: '生产部', status: 'available' },
                { id: 'P002', name: '李四', department: '生产部', status: 'busy' },
                { id: 'P003', name: '王五', department: '质检部', status: 'available' },
                { id: 'P004', name: '赵六', department: '包装部', status: 'available' },
                { id: 'P005', name: '钱七', department: '物流部', status: 'offline' }
            ],
            materials: [
                { id: 'MAT001', name: '原料A', currentStock: 1500, minStock: 1000, unit: 'kg' },
                { id: 'MAT002', name: '原料B', currentStock: 800, minStock: 1200, unit: 'kg' },
                { id: 'MAT003', name: '包装材料', currentStock: 5000, minStock: 3000, unit: '个' },
                { id: 'MAT004', name: '标签纸', currentStock: 2000, minStock: 1500, unit: '张' },
                { id: 'MAT005', name: '胶水', currentStock: 50, minStock: 100, unit: 'L' }
            ]
        });

        // 初始化
        onMounted(async () => {
            try {
                await checkAuth();
            } finally {
                // 确保加载指示器被隐藏
                hideLoading();
            }
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
                throw error; // 重新抛出错误以便onMounted能够捕获
            }
        }

        // 刷新数据
        function refreshData() {
            window.showNotification('数据已刷新', 'success');
        }

        // 获取可用资源数量
        function getAvailableCount(resources) {
            return resources.filter(r => r.status === 'available').length;
        }

        // 获取充足物料数量
        function getSufficientCount(materials) {
            return materials.filter(m => m.currentStock >= m.minStock).length;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusClasses = {
                available: 'bg-green-100 text-green-800',
                busy: 'bg-yellow-100 text-yellow-800',
                maintenance: 'bg-red-100 text-red-800',
                offline: 'bg-gray-100 text-gray-800'
            };
            return statusClasses[status] || 'bg-gray-100 text-gray-800';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                available: '可用',
                busy: '忙碌',
                maintenance: '维护中',
                offline: '离线'
            };
            return statusMap[status] || status;
        }

        // 获取物料状态样式类
        function getMaterialStatusClass(material) {
            if (material.currentStock >= material.minStock) {
                return 'bg-green-100 text-green-800';
            } else if (material.currentStock >= material.minStock * 0.5) {
                return 'bg-yellow-100 text-yellow-800';
            } else {
                return 'bg-red-100 text-red-800';
            }
        }

        // 获取物料状态文本
        function getMaterialStatusText(material) {
            if (material.currentStock >= material.minStock) {
                return '充足';
            } else if (material.currentStock >= material.minStock * 0.5) {
                return '偏低';
            } else {
                return '不足';
            }
        }

        return {
            currentUser,
            isAuthenticated,
            mockData,
            refreshData,
            getAvailableCount,
            getSufficientCount,
            getStatusClass,
            getStatusText,
            getMaterialStatusClass,
            getMaterialStatusText
        };
    }
}).mount('#app');
