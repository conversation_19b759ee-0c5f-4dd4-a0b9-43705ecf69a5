/**
 * 用户邮箱管理脚本
 * 用于为现有用户添加邮箱地址
 */

const userService = require('../services/userService');
const logger = require('../utils/logger');

// 示例用户邮箱配置
const userEmailConfig = {
    // 根据用户角色设置示例邮箱
    'factory_manager': '<EMAIL>',
    'director': '<EMAIL>',
    'manager': '<EMAIL>',
    'ceo': '<EMAIL>',
    'admin': '<EMAIL>',
    'readonly': '<EMAIL>',
    'user': '<EMAIL>'
};

async function addUserEmails() {
    console.log('开始为用户添加邮箱地址...\n');

    try {
        // 获取所有用户
        const users = userService.getAllUsers();
        console.log(`找到 ${users.length} 个用户\n`);

        let updatedCount = 0;

        for (const user of users) {
            console.log(`处理用户: ${user.username} (${user.usercode}) - 角色: ${user.role}`);
            
            // 检查用户是否已有邮箱
            if (user.email) {
                console.log(`  ✅ 已有邮箱: ${user.email}`);
                continue;
            }

            // 根据角色设置示例邮箱
            const roleEmail = userEmailConfig[user.role.toLowerCase()];
            if (roleEmail) {
                // 为避免重复，在邮箱前加上用户代码
                const userEmail = roleEmail.replace('@', `+${user.usercode}@`);
                user.email = userEmail;
                
                // 保存用户
                const success = userService.writeUserFile(user);
                if (success) {
                    console.log(`  ✅ 添加邮箱: ${userEmail}`);
                    updatedCount++;
                } else {
                    console.log(`  ❌ 保存失败`);
                }
            } else {
                console.log(`  ⚠️  未知角色，跳过`);
            }
        }

        console.log(`\n完成！共更新了 ${updatedCount} 个用户的邮箱地址。`);
        
        // 显示更新后的用户列表
        console.log('\n当前用户邮箱配置:');
        const updatedUsers = userService.getAllUsers();
        updatedUsers.forEach(user => {
            console.log(`  ${user.username} (${user.role}): ${user.email || '未设置'}`);
        });

    } catch (error) {
        console.error('添加用户邮箱失败:', error);
        process.exit(1);
    }
}

// 运行脚本
if (require.main === module) {
    addUserEmails().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = { addUserEmails };
