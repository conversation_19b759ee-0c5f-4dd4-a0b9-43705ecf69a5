# 质量管理 API 文档

## 📋 概述

质量管理模块提供检测报告管理、文件上传下载、质量追溯等功能，支持多格式文件管理。

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 上传检测报告 | `quality_upload` |
| 查看检测报告 | `quality_view` |
| 下载文件 | `quality_download` |
| 管理质量数据 | `quality_manage` |

## 📊 API 接口

### 1. 获取检测报告列表

**GET** `/api/quality`

获取检测报告列表，支持分页、搜索和筛选。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |
| search | string | 否 | 搜索关键词（标题、编号） |
| type | string | 否 | 报告类型筛选 |
| status | string | 否 | 状态筛选（已发布、草稿） |
| startDate | string | 否 | 开始日期 (YYYY-MM-DD) |
| endDate | string | 否 | 结束日期 (YYYY-MM-DD) |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "qr001",
      "reportNumber": "QR-20250729-001",
      "title": "产品质量检测报告",
      "type": "产品检测",
      "testDate": "2025-07-29",
      "inspector": "质检员A",
      "department": "质量部",
      "status": "已发布",
      "conclusion": "合格",
      "testDetails": {
        "testItems": ["外观检查", "尺寸测量", "性能测试"],
        "testResults": ["合格", "合格", "合格"],
        "testStandards": ["GB/T 1234-2020", "ISO 9001", "企业标准"]
      },
      "files": [
        {
          "id": "file001",
          "filename": "检测数据.xlsx",
          "originalName": "检测数据.xlsx",
          "path": "/uploads/quality-reports/2025/07/29/file001.xlsx",
          "size": 25600,
          "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "uploadedAt": "2025-07-29T10:00:00.000Z"
        }
      ],
      "createdAt": "2025-07-29T10:00:00.000Z",
      "updatedAt": "2025-07-29T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

### 2. 获取检测报告详情

**GET** `/api/quality/:id`

获取指定检测报告的详细信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 报告ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "qr001",
    "reportNumber": "QR-20250729-001",
    "title": "产品质量检测报告",
    "type": "产品检测",
    "testDate": "2025-07-29",
    "inspector": "质检员A",
    "department": "质量部",
    "status": "已发布",
    "conclusion": "合格",
    "testDetails": {
      "testItems": ["外观检查", "尺寸测量", "性能测试"],
      "testResults": ["合格", "合格", "合格"],
      "testStandards": ["GB/T 1234-2020", "ISO 9001", "企业标准"],
      "testConditions": "常温常湿",
      "testEquipment": ["游标卡尺", "万能试验机", "显微镜"],
      "sampleInfo": {
        "sampleId": "SP-001",
        "sampleName": "产品A",
        "batchNumber": "B20250729",
        "quantity": "10件"
      }
    },
    "files": [
      {
        "id": "file001",
        "filename": "检测数据.xlsx",
        "originalName": "检测数据.xlsx",
        "path": "/uploads/quality-reports/2025/07/29/file001.xlsx",
        "size": 25600,
        "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "uploadedAt": "2025-07-29T10:00:00.000Z"
      },
      {
        "id": "file002",
        "filename": "检测照片.jpg",
        "originalName": "检测照片.jpg",
        "path": "/uploads/quality-reports/2025/07/29/file002.jpg",
        "size": 512000,
        "type": "image/jpeg",
        "uploadedAt": "2025-07-29T10:05:00.000Z"
      }
    ],
    "createdAt": "2025-07-29T10:00:00.000Z",
    "updatedAt": "2025-07-29T10:30:00.000Z"
  }
}
```

### 3. 创建检测报告

**POST** `/api/quality`

创建新的检测报告，支持多文件上传。

#### 请求格式
- **Content-Type**: `multipart/form-data`
- **文件字段**: `files` (支持多文件)
- **支持格式**: PDF, Excel, Word, 图片等

#### 请求参数

```json
{
  "title": "新产品质量检测报告",
  "type": "产品检测",
  "testDate": "2025-07-29",
  "inspector": "质检员B",
  "department": "质量部",
  "status": "草稿",
  "conclusion": "待检测",
  "testDetails": {
    "testItems": ["外观检查", "尺寸测量"],
    "testResults": ["待测", "待测"],
    "testStandards": ["GB/T 1234-2020"],
    "testConditions": "常温常湿",
    "sampleInfo": {
      "sampleId": "SP-002",
      "sampleName": "产品B",
      "batchNumber": "B20250729-02",
      "quantity": "5件"
    }
  }
}
```

#### 响应示例

**成功响应 (201)**:
```json
{
  "success": true,
  "message": "检测报告创建成功",
  "data": {
    "id": "qr002",
    "reportNumber": "QR-20250729-002",
    "title": "新产品质量检测报告",
    "status": "草稿",
    "files": [
      {
        "id": "file003",
        "filename": "测试数据.xlsx",
        "size": 18432
      }
    ],
    "createdAt": "2025-07-29T11:00:00.000Z"
  }
}
```

### 4. 更新检测报告

**PUT** `/api/quality/:id`

更新检测报告信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 报告ID |

#### 请求参数

```json
{
  "title": "更新后的检测报告",
  "status": "已发布",
  "conclusion": "合格",
  "testDetails": {
    "testItems": ["外观检查", "尺寸测量", "性能测试"],
    "testResults": ["合格", "合格", "合格"],
    "testStandards": ["GB/T 1234-2020", "ISO 9001"]
  }
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "检测报告更新成功",
  "data": {
    "id": "qr002",
    "title": "更新后的检测报告",
    "status": "已发布",
    "updatedAt": "2025-07-29T11:30:00.000Z"
  }
}
```

### 5. 删除检测报告

**DELETE** `/api/quality/:id`

删除检测报告及其关联文件。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 报告ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "检测报告删除成功"
}
```

### 6. 下载报告文件

**GET** `/api/quality/:id/files/:fileId`

下载检测报告的附件文件。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 报告ID |
| fileId | string | 是 | 文件ID |

#### 响应
返回文件流，浏览器会自动下载文件。

### 7. 批量删除文件

**DELETE** `/api/quality/:id/files`

批量删除检测报告的文件。

#### 请求参数

```json
{
  "fileIds": ["file001", "file002", "file003"]
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "文件删除成功",
  "data": {
    "deleted": 3,
    "failed": 0
  }
}
```

### 8. 获取质量统计

**GET** `/api/quality/statistics`

获取质量管理统计数据。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| period | string | 否 | 统计周期（week、month、year） |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "totalReports": 150,
    "publishedReports": 120,
    "draftReports": 30,
    "byType": {
      "产品检测": 80,
      "原料检测": 40,
      "环境检测": 30
    },
    "byConclusion": {
      "合格": 140,
      "不合格": 8,
      "待检测": 2
    },
    "monthlyTrend": [
      {
        "month": "2025-06",
        "reports": 45,
        "qualified": 42,
        "unqualified": 3
      },
      {
        "month": "2025-07",
        "reports": 52,
        "qualified": 50,
        "unqualified": 2
      }
    ]
  }
}
```

## 📁 文件管理

### 支持的文件格式

| 类型 | 格式 | 最大大小 | 描述 |
|------|------|----------|------|
| 文档 | PDF, DOC, DOCX | 10MB | 检测报告文档 |
| 表格 | XLS, XLSX, CSV | 5MB | 检测数据表格 |
| 图片 | JPG, PNG, GIF | 2MB | 检测照片 |
| 压缩包 | ZIP, RAR | 20MB | 批量文件 |

### 文件存储结构

```
uploads/quality-reports/
├── 2025/
│   ├── 07/
│   │   ├── 29/
│   │   │   ├── file001.xlsx
│   │   │   ├── file002.jpg
│   │   │   └── ...
│   │   └── ...
│   └── ...
└── ...
```

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| REPORT_NOT_FOUND | 404 | 检测报告不存在 |
| FILE_NOT_FOUND | 404 | 文件不存在 |
| FILE_TOO_LARGE | 413 | 文件过大 |
| UNSUPPORTED_FORMAT | 400 | 不支持的文件格式 |
| UPLOAD_FAILED | 500 | 文件上传失败 |
| PERMISSION_DENIED | 403 | 权限不足 |

### 报告状态说明

| 状态 | 描述 | 可执行操作 |
|------|------|------------|
| 草稿 | 编辑中 | 编辑、发布、删除 |
| 已发布 | 正式发布 | 查看、下载 |
| 已归档 | 已归档 | 查看 |

---

**更新时间**: 2025-07-29
**版本**: v1.0
