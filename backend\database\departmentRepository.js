/**
 * 部门数据访问层
 * 处理部门相关的数据库操作
 */

const databaseManager = require('./database');
const logger = require('../utils/logger');

class DepartmentRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        this.initStatements();
    }

    /**
     * 初始化预编译SQL语句
     */
    initStatements() {
        this.statements = {
            findAll: this.db.prepare('SELECT * FROM departments ORDER BY name ASC'),
            findById: this.db.prepare('SELECT * FROM departments WHERE id = ?'),
            findByName: this.db.prepare('SELECT * FROM departments WHERE name = ?'),
            insert: this.db.prepare(`
                INSERT INTO departments (
                    id, name, description, active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            `),
            update: this.db.prepare(`
                UPDATE departments SET
                    name = ?, description = ?, active = ?, updated_at = ?
                WHERE id = ?
            `),
            delete: this.db.prepare('DELETE FROM departments WHERE id = ?'),
            checkNameExists: this.db.prepare('SELECT COUNT(*) as count FROM departments WHERE name = ? AND id != ?'),
            checkUsersInDepartment: this.db.prepare('SELECT COUNT(*) as count FROM users WHERE department = ? AND active = 1')
        };
    }

    /**
     * 获取所有部门
     */
    findAll() {
        try {
            const departments = this.statements.findAll.all();
            return departments.map(dept => this.transformDepartment(dept));
        } catch (error) {
            logger.error('获取部门列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找部门
     */
    findById(id) {
        try {
            const department = this.statements.findById.get(id);
            return department ? this.transformDepartment(department) : null;
        } catch (error) {
            logger.error(`根据ID查找部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据名称查找部门
     */
    findByName(name) {
        try {
            const department = this.statements.findByName.get(name);
            return department ? this.transformDepartment(department) : null;
        } catch (error) {
            logger.error(`根据名称查找部门失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 创建新部门
     */
    create(departmentData) {
        try {
            const now = new Date().toISOString();
            const id = departmentData.id || this.generateId();

            this.statements.insert.run(
                id,
                departmentData.name,
                departmentData.description || '',
                1, // 默认设置为活跃状态
                now,
                now
            );

            return this.findById(id);
        } catch (error) {
            logger.error('创建部门失败:', error);
            throw error;
        }
    }

    /**
     * 更新部门
     */
    update(id, departmentData) {
        try {
            const now = new Date().toISOString();

            this.statements.update.run(
                departmentData.name,
                departmentData.description || '',
                1, // 保持活跃状态
                now,
                id
            );

            return this.findById(id);
        } catch (error) {
            logger.error(`更新部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除部门（硬删除）
     */
    delete(id) {
        try {
            const result = this.statements.delete.run(id);
            return result.changes > 0;
        } catch (error) {
            logger.error(`删除部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 检查部门名称是否存在
     */
    isNameExists(name, excludeId = '') {
        try {
            const result = this.statements.checkNameExists.get(name, excludeId);
            return result.count > 0;
        } catch (error) {
            logger.error(`检查部门名称是否存在失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 检查部门是否有用户关联
     */
    hasUsersInDepartment(departmentName) {
        try {
            const result = this.statements.checkUsersInDepartment.get(departmentName);
            return result.count > 0;
        } catch (error) {
            logger.error(`检查部门用户关联失败 (${departmentName}):`, error);
            throw error;
        }
    }

    /**
     * 初始化默认部门数据
     */
    initDefaultDepartments() {
        const defaultDepartments = [
            { name: '管理部', description: '公司管理部门' },
            { name: '生产部', description: '生产制造部门' },
            { name: '工程部', description: '工程技术部门' },
            { name: '品管部', description: '品质管理部门' },
            { name: '机电部', description: '机电维护部门' },
            { name: '业务部', description: '业务销售部门' },
            { name: '财务部', description: '财务管理部门' },
            { name: '人事部', description: '人力资源部门' },
            { name: '研发部', description: '研发创新部门' },
            { name: '采购部', description: '采购管理部门' },
            { name: '仓储部', description: '仓储物流部门' }
        ];

        for (const dept of defaultDepartments) {
            // 检查部门是否已存在
            if (!this.findByName(dept.name)) {
                this.create(dept);
                logger.info(`初始化部门: ${dept.name}`);
            }
        }
    }

    /**
     * 转换数据库部门对象为应用层对象
     */
    transformDepartment(dbDepartment) {
        if (!dbDepartment) return null;

        return {
            id: dbDepartment.id,
            name: dbDepartment.name,
            description: dbDepartment.description,
            active: dbDepartment.active === 1,
            createdAt: dbDepartment.created_at,
            updatedAt: dbDepartment.updated_at
        };
    }

    /**
     * 生成部门ID
     */
    generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `DEPT${timestamp}${random}`;
    }
}

module.exports = new DepartmentRepository();
