/**
 * 数据库迁移脚本：添加 selected_managers 字段
 * 为 applications 表添加 selected_managers 字段，用于存储总监选择的经理列表
 */

const Database = require('better-sqlite3');
const path = require('path');
const logger = require('../../utils/logger');

/**
 * 执行迁移
 * @param {string} dbPath - 数据库文件路径
 */
function migrate(dbPath) {
    try {
        const db = new Database(dbPath);
        
        logger.info('开始执行数据库迁移：添加 selected_managers 字段');
        
        // 检查字段是否已存在
        const tableInfo = db.pragma('table_info(applications)');
        const hasSelectedManagersField = tableInfo.some(column => column.name === 'selected_managers');
        
        if (hasSelectedManagersField) {
            logger.info('selected_managers 字段已存在，跳过迁移');
            db.close();
            return { success: true, message: '字段已存在，无需迁移' };
        }
        
        // 添加 selected_managers 字段
        db.exec(`
            ALTER TABLE applications 
            ADD COLUMN selected_managers TEXT DEFAULT '[]'
        `);
        
        // 验证字段是否添加成功
        const updatedTableInfo = db.pragma('table_info(applications)');
        const fieldAdded = updatedTableInfo.some(column => column.name === 'selected_managers');
        
        if (!fieldAdded) {
            throw new Error('字段添加失败');
        }
        
        // 为现有记录设置默认值（空数组）
        const updateResult = db.prepare(`
            UPDATE applications 
            SET selected_managers = '[]' 
            WHERE selected_managers IS NULL
        `).run();
        
        logger.info(`迁移完成，更新了 ${updateResult.changes} 条记录`);
        
        db.close();
        
        return {
            success: true,
            message: `成功添加 selected_managers 字段，更新了 ${updateResult.changes} 条记录`
        };
        
    } catch (error) {
        logger.error('数据库迁移失败:', error);
        throw error;
    }
}

/**
 * 回滚迁移（如果需要）
 * @param {string} dbPath - 数据库文件路径
 */
function rollback(dbPath) {
    try {
        const db = new Database(dbPath);
        
        logger.info('开始回滚数据库迁移：删除 selected_managers 字段');
        
        // SQLite 不支持直接删除列，需要重建表
        // 这里只是记录，实际生产环境中需要谨慎处理
        logger.warn('SQLite 不支持直接删除列，回滚需要重建表，请手动处理');
        
        db.close();
        
        return {
            success: false,
            message: 'SQLite 不支持直接删除列，需要手动处理回滚'
        };
        
    } catch (error) {
        logger.error('数据库回滚失败:', error);
        throw error;
    }
}

module.exports = {
    migrate,
    rollback
};
