/**
 * 智能排程创建页面
 * 支持智能排程和传统排程两种模式
 */

import { checkAuth } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import SchedulingAPI from '../../api/scheduling.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const submitting = ref(false);
        const showTraditionalMode = ref(false);
        const schedulePlans = ref([]);
        const selectedPlan = ref(null);

        // 智能排程订单表单
        const orderForm = ref({
            id: '',
            productId: '',
            quantity: 1,
            requiredDate: '',
            priority: 'normal',
            customerName: '',
            specialRequirements: ''
        });

        // 传统排程表单（保留原有结构）
        const formData = ref({
            title: '',
            productId: '',
            productName: '',
            quantity: 1,
            priority: 'medium',
            startTime: '',
            endTime: '',
            notes: '',
            assignedEquipment: [],
            assignedPersonnel: [],
            requiredMaterials: []
        });

        // 初始化
        onMounted(async () => {
            try {
                // 检查用户认证状态
                const isAuthenticated = await checkAuth();
                if (!isAuthenticated) {
                    window.location.href = '/login';
                    return;
                }

                // 获取用户信息
                const userJson = sessionStorage.getItem('user');
                if (userJson) {
                    currentUser.value = JSON.parse(userJson);
                }

                // 设置默认交期（当前日期后15天）
                setDefaultRequiredDate();
            } catch (error) {
                console.error('初始化页面失败:', error);
                window.location.href = '/login';
            } finally {
                hideLoading();
            }
        });

        // 设置默认要求交期
        function setDefaultRequiredDate() {
            const date = new Date();
            date.setDate(date.getDate() + 15); // 默认15天后
            orderForm.value.requiredDate = date.toISOString().split('T')[0];
        }

        // 生成智能排程方案
        async function generateSchedulePlans() {
            if (submitting.value) return;

            // 基本验证
            if (!orderForm.value.id) {
                window.showNotification('请输入订单ID', 'error');
                return;
            }

            if (!orderForm.value.productId) {
                window.showNotification('请输入产品ID', 'error');
                return;
            }

            if (!orderForm.value.quantity || orderForm.value.quantity <= 0) {
                window.showNotification('请输入有效的订单数量', 'error');
                return;
            }

            if (!orderForm.value.requiredDate) {
                window.showNotification('请选择要求交期', 'error');
                return;
            }

            // 检查交期是否合理
            const requiredDate = new Date(orderForm.value.requiredDate);
            const today = new Date();
            if (requiredDate <= today) {
                window.showNotification('要求交期必须晚于当前日期', 'error');
                return;
            }

            submitting.value = true;

            try {
                const response = await SchedulingAPI.createPlans(orderForm.value);

                if (response.success) {
                    window.showNotification('智能排程方案生成成功', 'success');

                    // 显示生成的方案
                    schedulePlans.value = response.data.plans.map(plan => ({
                        ...plan,
                        orderId: orderForm.value.id,
                        selected: false
                    }));
                } else {
                    window.showNotification(response.message || '生成排程方案失败', 'error');
                }
            } catch (error) {
                console.error('生成智能排程方案失败:', error);
                window.showNotification('生成排程方案失败', 'error');
            } finally {
                submitting.value = false;
            }
        }

        // 选择排程方案
        function selectPlan(plan) {
            // 取消其他方案的选中状态
            schedulePlans.value.forEach(p => p.selected = false);

            // 选中当前方案
            plan.selected = true;
            selectedPlan.value = plan;
        }

        // 查看方案详情
        function viewPlanDetails(plan) {
            console.log('查看方案详情:', plan);
            window.showNotification('方案详情功能开发中', 'info');
        }

        // 确认方案
        async function confirmPlan(plan) {
            if (!confirm(`确定要选择"${plan.name}"作为最终排程方案吗？`)) {
                return;
            }

            try {
                const response = await SchedulingAPI.selectPlan(plan.orderId, plan.id);

                if (response.success) {
                    window.showNotification('排程方案确认成功', 'success');

                    // 跳转到排程列表页面
                    setTimeout(() => {
                        window.location.href = '/schedule/list';
                    }, 1500);
                } else {
                    window.showNotification(response.message || '确认方案失败', 'error');
                }
            } catch (error) {
                console.error('确认方案失败:', error);
                window.showNotification('确认方案失败', 'error');
            }
        }

        // 模式切换
        function switchToTraditional() {
            showTraditionalMode.value = true;
        }

        function switchToIntelligent() {
            showTraditionalMode.value = false;
        }

        // 生成订单ID
        function generateOrderId() {
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.random().toString(36).substr(2, 4).toUpperCase();
            orderForm.value.id = `ORD${timestamp}${random}`;
        }

        // 生成产品ID
        function generateProductId() {
            const products = ['PROD001', 'PROD002', 'PROD003', 'PROD004', 'PROD005'];
            orderForm.value.productId = products[Math.floor(Math.random() * products.length)];
        }

        // 填充示例数据
        function fillExampleData() {
            generateOrderId();
            generateProductId();
            orderForm.value.quantity = Math.floor(Math.random() * 1000) + 100;
            orderForm.value.customerName = '示例客户';
            orderForm.value.priority = ['low', 'normal', 'high'][Math.floor(Math.random() * 3)];
            orderForm.value.specialRequirements = '示例特殊要求';
        }

        // 重置订单表单
        function resetOrderForm() {
            orderForm.value = {
                id: '',
                productId: '',
                quantity: 1,
                requiredDate: '',
                priority: 'normal',
                customerName: '',
                specialRequirements: ''
            };
            setDefaultRequiredDate();
        }

        // 获取风险等级样式
        function getRiskClass(riskLevel) {
            const classMap = {
                'low': 'bg-green-100 text-green-800',
                'medium': 'bg-yellow-100 text-yellow-800',
                'high': 'bg-red-100 text-red-800'
            };
            return classMap[riskLevel] || 'bg-gray-100 text-gray-800';
        }

        // 获取风险等级文本
        function getRiskText(riskLevel) {
            const textMap = {
                'low': '低风险',
                'medium': '中风险',
                'high': '高风险'
            };
            return textMap[riskLevel] || '未知';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }

        return {
            currentUser,
            submitting,
            showTraditionalMode,
            schedulePlans,
            selectedPlan,
            orderForm,
            formData,
            generateSchedulePlans,
            selectPlan,
            viewPlanDetails,
            confirmPlan,
            switchToTraditional,
            switchToIntelligent,
            generateOrderId,
            generateProductId,
            fillExampleData,
            resetOrderForm,
            getRiskClass,
            getRiskText,
            formatDate
        };
    }
}).mount('#app');
