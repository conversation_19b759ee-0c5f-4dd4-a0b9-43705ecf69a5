/**
 * 资源优化器
 * 实现设备分配优化、人员调度优化、负载均衡算法
 */

const logger = require('../utils/logger');

/**
 * 资源优化器类
 * 负责优化设备和人员的分配
 */
class ResourceOptimizer {
    constructor() {
        // 优化配置参数
        this.config = {
            maxIterations: 100,         // 最大优化迭代次数
            convergenceThreshold: 0.01, // 收敛阈值
            loadBalanceWeight: 0.3,     // 负载均衡权重
            efficiencyWeight: 0.4,      // 效率权重
            costWeight: 0.2,            // 成本权重
            riskWeight: 0.1,            // 风险权重
            maxOverloadRatio: 1.2       // 最大过载比例
        };

        logger.info('资源优化器初始化完成');
    }

    /**
     * 分析资源可用性
     * @param {Object} capacityRequirements 产能需求
     * @param {Object} constraints 约束条件
     * @returns {Promise<Object>} 资源可用性分析结果
     */
    async analyzeAvailability(capacityRequirements, constraints = {}) {
        try {
            logger.info('开始分析资源可用性');

            // 1. 获取可用设备
            const availableEquipment = await this.getAvailableEquipment(constraints);
            
            // 2. 获取可用操作员
            const availableOperators = await this.getAvailableOperators(constraints);
            
            // 3. 分析设备产能匹配
            const equipmentMatching = await this.analyzeEquipmentMatching(
                capacityRequirements, 
                availableEquipment
            );
            
            // 4. 分析操作员技能匹配
            const operatorMatching = await this.analyzeOperatorMatching(
                capacityRequirements, 
                availableOperators
            );
            
            // 5. 计算资源利用率
            const utilizationAnalysis = await this.calculateResourceUtilization(
                capacityRequirements,
                availableEquipment,
                availableOperators
            );

            const result = {
                availableEquipment,
                availableOperators,
                equipmentMatching,
                operatorMatching,
                utilizationAnalysis,
                constraints,
                analyzedAt: new Date().toISOString()
            };

            logger.info('资源可用性分析完成', {
                equipmentCount: availableEquipment.length,
                operatorCount: availableOperators.length
            });

            return result;

        } catch (error) {
            logger.error('分析资源可用性失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 选择最优资源组合
     * @param {Object} capacityRequirements 产能需求
     * @param {Object} resourceAvailability 资源可用性
     * @param {string} optimizationType 优化类型
     * @returns {Promise<Object>} 最优资源组合
     */
    async selectOptimalResources(capacityRequirements, resourceAvailability, optimizationType) {
        try {
            logger.info('开始选择最优资源组合', { optimizationType });

            let optimalResources;

            switch (optimizationType) {
                case 'earliest_completion':
                    optimalResources = await this.optimizeForEarliestCompletion(
                        capacityRequirements, 
                        resourceAvailability
                    );
                    break;
                    
                case 'high_efficiency':
                    optimalResources = await this.optimizeForHighEfficiency(
                        capacityRequirements, 
                        resourceAvailability
                    );
                    break;
                    
                case 'load_balanced':
                    optimalResources = await this.optimizeForLoadBalance(
                        capacityRequirements, 
                        resourceAvailability
                    );
                    break;
                    
                case 'cost_optimized':
                    optimalResources = await this.optimizeForCost(
                        capacityRequirements, 
                        resourceAvailability
                    );
                    break;
                    
                case 'low_risk':
                    optimalResources = await this.optimizeForLowRisk(
                        capacityRequirements, 
                        resourceAvailability
                    );
                    break;
                    
                default:
                    throw new Error(`未知的优化类型: ${optimizationType}`);
            }

            logger.info('最优资源组合选择完成', { 
                optimizationType,
                equipmentCount: optimalResources.equipment.length,
                operatorCount: optimalResources.operators.length
            });

            return optimalResources;

        } catch (error) {
            logger.error('选择最优资源组合失败', { 
                error: error.message, 
                optimizationType 
            });
            throw error;
        }
    }

    /**
     * 优化最早完成时间
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeForEarliestCompletion(capacityReq, resourceAvail) {
        // 选择产能最高的设备组合
        const selectedEquipment = this.selectHighestCapacityEquipment(
            capacityReq.equipmentRequirements,
            resourceAvail.availableEquipment
        );

        // 为每台设备选择最高效的操作员
        const selectedOperators = await this.selectHighestEfficiencyOperators(
            selectedEquipment,
            resourceAvail.availableOperators
        );

        return {
            type: 'earliest_completion',
            equipment: selectedEquipment,
            operators: selectedOperators,
            estimatedCompletionTime: await this.calculateCompletionTime(selectedEquipment, selectedOperators),
            efficiency: await this.calculateOverallEfficiency(selectedEquipment, selectedOperators),
            cost: await this.calculateTotalCost(selectedEquipment, selectedOperators),
            risk: await this.calculateRiskLevel(selectedEquipment, selectedOperators)
        };
    }

    /**
     * 优化高效率
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeForHighEfficiency(capacityReq, resourceAvail) {
        // 使用遗传算法或模拟退火算法寻找最优组合
        const optimizedCombination = await this.geneticAlgorithmOptimization(
            capacityReq,
            resourceAvail,
            'efficiency'
        );

        return {
            type: 'high_efficiency',
            equipment: optimizedCombination.equipment,
            operators: optimizedCombination.operators,
            estimatedCompletionTime: optimizedCombination.completionTime,
            efficiency: optimizedCombination.efficiency,
            cost: optimizedCombination.cost,
            risk: optimizedCombination.risk
        };
    }

    /**
     * 优化负载均衡
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeForLoadBalance(capacityReq, resourceAvail) {
        // 平衡各设备和操作员的工作负载
        const balancedAllocation = await this.balanceWorkload(
            capacityReq,
            resourceAvail.availableEquipment,
            resourceAvail.availableOperators
        );

        return {
            type: 'load_balanced',
            equipment: balancedAllocation.equipment,
            operators: balancedAllocation.operators,
            estimatedCompletionTime: balancedAllocation.completionTime,
            efficiency: balancedAllocation.efficiency,
            cost: balancedAllocation.cost,
            risk: balancedAllocation.risk,
            loadBalance: balancedAllocation.loadBalance
        };
    }

    /**
     * 优化成本
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeForCost(capacityReq, resourceAvail) {
        // 选择成本最低的资源组合
        const costOptimizedCombination = await this.minimizeCost(
            capacityReq,
            resourceAvail.availableEquipment,
            resourceAvail.availableOperators
        );

        return {
            type: 'cost_optimized',
            equipment: costOptimizedCombination.equipment,
            operators: costOptimizedCombination.operators,
            estimatedCompletionTime: costOptimizedCombination.completionTime,
            efficiency: costOptimizedCombination.efficiency,
            cost: costOptimizedCombination.cost,
            risk: costOptimizedCombination.risk
        };
    }

    /**
     * 优化低风险
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeForLowRisk(capacityReq, resourceAvail) {
        // 选择最稳定可靠的资源组合
        const lowRiskCombination = await this.minimizeRisk(
            capacityReq,
            resourceAvail.availableEquipment,
            resourceAvail.availableOperators
        );

        return {
            type: 'low_risk',
            equipment: lowRiskCombination.equipment,
            operators: lowRiskCombination.operators,
            estimatedCompletionTime: lowRiskCombination.completionTime,
            efficiency: lowRiskCombination.efficiency,
            cost: lowRiskCombination.cost,
            risk: lowRiskCombination.risk,
            reliability: lowRiskCombination.reliability
        };
    }

    /**
     * 选择最高产能设备
     * @param {Object} equipmentReq 设备需求
     * @param {Array} availableEquipment 可用设备
     * @returns {Array} 选中的设备
     */
    selectHighestCapacityEquipment(equipmentReq, availableEquipment) {
        const selectedEquipment = [];

        for (const type in equipmentReq.byType) {
            const requirement = equipmentReq.byType[type];
            
            // 筛选该类型的设备
            const typeEquipment = availableEquipment.filter(eq => eq.type === type);
            
            // 按产能排序
            typeEquipment.sort((a, b) => (b.capacityPerHour || 0) - (a.capacityPerHour || 0));
            
            // 选择所需数量的设备
            const needed = Math.min(requirement.requiredCount, typeEquipment.length);
            selectedEquipment.push(...typeEquipment.slice(0, needed));
        }

        return selectedEquipment;
    }

    /**
     * 选择最高效率操作员
     * @param {Array} selectedEquipment 选中的设备
     * @param {Array} availableOperators 可用操作员
     * @returns {Promise<Array>} 选中的操作员
     */
    async selectHighestEfficiencyOperators(selectedEquipment, availableOperators) {
        const selectedOperators = [];

        for (const equipment of selectedEquipment) {
            // 找到能操作该设备的操作员
            const qualifiedOperators = availableOperators.filter(operator => {
                return operator.skills && operator.skills.some(skill => 
                    skill.equipmentId === equipment.id && skill.skillLevel >= 2
                );
            });

            if (qualifiedOperators.length > 0) {
                // 按效率系数排序
                qualifiedOperators.sort((a, b) => {
                    const aSkill = a.skills.find(s => s.equipmentId === equipment.id);
                    const bSkill = b.skills.find(s => s.equipmentId === equipment.id);
                    return (bSkill?.efficiencyFactor || 1) - (aSkill?.efficiencyFactor || 1);
                });

                selectedOperators.push({
                    operator: qualifiedOperators[0],
                    equipment: equipment,
                    skill: qualifiedOperators[0].skills.find(s => s.equipmentId === equipment.id)
                });
            }
        }

        return selectedOperators;
    }

    /**
     * 遗传算法优化
     * @param {Object} capacityReq 产能需求
     * @param {Object} resourceAvail 资源可用性
     * @param {string} objective 优化目标
     * @returns {Promise<Object>} 优化结果
     */
    async geneticAlgorithmOptimization(capacityReq, resourceAvail, objective) {
        // 简化的遗传算法实现
        const populationSize = 50;
        const generations = 20;
        
        // 初始化种群
        let population = this.initializePopulation(
            populationSize, 
            capacityReq, 
            resourceAvail
        );

        for (let generation = 0; generation < generations; generation++) {
            // 评估适应度
            const fitness = await this.evaluateFitness(population, objective);
            
            // 选择
            const selected = this.selection(population, fitness);
            
            // 交叉
            const offspring = this.crossover(selected);
            
            // 变异
            const mutated = this.mutation(offspring);
            
            population = mutated;
        }

        // 返回最优解
        const finalFitness = await this.evaluateFitness(population, objective);
        const bestIndex = finalFitness.indexOf(Math.max(...finalFitness));
        
        return await this.evaluateIndividual(population[bestIndex]);
    }

    /**
     * 获取可用设备
     * @param {Object} constraints 约束条件
     * @returns {Promise<Array>} 可用设备列表
     */
    async getAvailableEquipment(constraints) {
        // 这里应该调用设备服务获取可用设备
        // 暂时返回模拟数据
        return [
            {
                id: 'eq1',
                name: '设备A',
                type: 'general',
                status: 'active',
                capacityPerHour: 100,
                efficiencyFactor: 1.0,
                costPerHour: 50,
                reliability: 0.95
            },
            {
                id: 'eq2',
                name: '设备B',
                type: 'precision',
                status: 'active',
                capacityPerHour: 80,
                efficiencyFactor: 1.2,
                costPerHour: 80,
                reliability: 0.98
            }
        ];
    }

    /**
     * 获取可用操作员
     * @param {Object} constraints 约束条件
     * @returns {Promise<Array>} 可用操作员列表
     */
    async getAvailableOperators(constraints) {
        // 这里应该调用用户服务获取可用操作员
        // 暂时返回模拟数据
        return [
            {
                id: 'op1',
                name: '张三',
                skills: [
                    { equipmentId: 'eq1', skillLevel: 3, efficiencyFactor: 1.1 },
                    { equipmentId: 'eq2', skillLevel: 2, efficiencyFactor: 1.0 }
                ],
                costPerHour: 30,
                availability: 0.9
            },
            {
                id: 'op2',
                name: '李四',
                skills: [
                    { equipmentId: 'eq1', skillLevel: 4, efficiencyFactor: 1.3 },
                    { equipmentId: 'eq2', skillLevel: 3, efficiencyFactor: 1.2 }
                ],
                costPerHour: 40,
                availability: 0.85
            }
        ];
    }

    /**
     * 分析设备匹配度
     * @param {Object} capacityReq 产能需求
     * @param {Array} availableEquipment 可用设备
     * @returns {Promise<Object>} 匹配分析结果
     */
    async analyzeEquipmentMatching(capacityReq, availableEquipment) {
        const matching = {};
        
        for (const type in capacityReq.equipmentRequirements.byType) {
            const requirement = capacityReq.equipmentRequirements.byType[type];
            const typeEquipment = availableEquipment.filter(eq => eq.type === type);
            
            matching[type] = {
                required: requirement.requiredCount,
                available: typeEquipment.length,
                shortage: Math.max(0, requirement.requiredCount - typeEquipment.length),
                utilization: typeEquipment.length > 0 ? 
                    Math.min(1, requirement.requiredCount / typeEquipment.length) : 0
            };
        }
        
        return matching;
    }

    /**
     * 分析操作员匹配度
     * @param {Object} capacityReq 产能需求
     * @param {Array} availableOperators 可用操作员
     * @returns {Promise<Object>} 匹配分析结果
     */
    async analyzeOperatorMatching(capacityReq, availableOperators) {
        const matching = {};
        
        for (const type in capacityReq.operatorRequirements.byEquipmentType) {
            const requirement = capacityReq.operatorRequirements.byEquipmentType[type];
            
            // 统计能操作该类型设备的操作员
            const qualifiedOperators = availableOperators.filter(operator => {
                return operator.skills && operator.skills.some(skill => 
                    skill.equipmentType === type && 
                    skill.skillLevel >= requirement.skillLevelRequired
                );
            });
            
            matching[type] = {
                required: requirement.requiredCount,
                available: qualifiedOperators.length,
                shortage: Math.max(0, requirement.requiredCount - qualifiedOperators.length),
                averageSkillLevel: this.calculateAverageSkillLevel(qualifiedOperators, type)
            };
        }
        
        return matching;
    }

    /**
     * 计算资源利用率
     * @param {Object} capacityReq 产能需求
     * @param {Array} availableEquipment 可用设备
     * @param {Array} availableOperators 可用操作员
     * @returns {Promise<Object>} 利用率分析
     */
    async calculateResourceUtilization(capacityReq, availableEquipment, availableOperators) {
        const totalEquipmentHours = availableEquipment.reduce((sum, eq) => {
            return sum + (this.config.workingHoursPerDay * eq.availability || 8);
        }, 0);

        const requiredEquipmentHours = Object.values(capacityReq.equipmentRequirements.byType)
            .reduce((sum, req) => sum + req.adjustedHours, 0);

        const totalOperatorHours = availableOperators.reduce((sum, op) => {
            return sum + (this.config.workingHoursPerDay * op.availability || 8);
        }, 0);

        const requiredOperatorHours = Object.values(capacityReq.operatorRequirements.byEquipmentType)
            .reduce((sum, req) => sum + req.totalWorkHours, 0);

        return {
            equipment: {
                totalAvailable: totalEquipmentHours,
                required: requiredEquipmentHours,
                utilization: totalEquipmentHours > 0 ? requiredEquipmentHours / totalEquipmentHours : 0
            },
            operators: {
                totalAvailable: totalOperatorHours,
                required: requiredOperatorHours,
                utilization: totalOperatorHours > 0 ? requiredOperatorHours / totalOperatorHours : 0
            }
        };
    }
}

module.exports = ResourceOptimizer;
