/**
 * 产能数据API路由
 * 处理设备产能、操作员技能、产品工艺等数据管理API
 */

const express = require('express');
const router = express.Router();
const capacityService = require('../services/capacityService');

/**
 * 获取设备产能配置
 * GET /api/capacity-data/equipment/:equipmentId
 */
router.get('/equipment/:equipmentId', async (req, res) => {
    try {
        const { equipmentId } = req.params;
        const result = await capacityService.getEquipmentCapacity(equipmentId);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('获取设备产能配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备产能配置失败',
            error: error.message
        });
    }
});

/**
 * 更新设备产能配置
 * PUT /api/capacity-data/equipment/:equipmentId
 */
router.put('/equipment/:equipmentId', async (req, res) => {
    try {
        const { equipmentId } = req.params;
        const capacityData = req.body;
        
        const result = await capacityService.updateEquipmentCapacity(equipmentId, capacityData);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新设备产能配置失败:', error);
        res.status(500).json({
            success: false,
            message: '更新设备产能配置失败',
            error: error.message
        });
    }
});

/**
 * 获取所有设备产能列表
 * GET /api/capacity-data/equipment
 */
router.get('/equipment', async (req, res) => {
    try {
        const { page = 1, limit = 20, status, type } = req.query;
        
        const filters = {};
        if (status) filters.status = status;
        if (type) filters.type = type;
        
        const result = await capacityService.getEquipmentCapacityList({
            page: parseInt(page),
            limit: parseInt(limit),
            filters
        });
        
        res.json(result);
    } catch (error) {
        console.error('获取设备产能列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备产能列表失败',
            error: error.message
        });
    }
});

/**
 * 获取操作员技能信息
 * GET /api/capacity-data/operator/:operatorId/skills
 */
router.get('/operator/:operatorId/skills', async (req, res) => {
    try {
        const { operatorId } = req.params;
        const result = await capacityService.getOperatorSkills(operatorId);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('获取操作员技能信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取操作员技能信息失败',
            error: error.message
        });
    }
});

/**
 * 更新操作员技能信息
 * PUT /api/capacity-data/operator/:operatorId/skills
 */
router.put('/operator/:operatorId/skills', async (req, res) => {
    try {
        const { operatorId } = req.params;
        const skillsData = req.body;
        
        const result = await capacityService.updateOperatorSkills(operatorId, skillsData);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新操作员技能信息失败:', error);
        res.status(500).json({
            success: false,
            message: '更新操作员技能信息失败',
            error: error.message
        });
    }
});

/**
 * 获取产品工艺流程
 * GET /api/capacity-data/product/:productId/process
 */
router.get('/product/:productId/process', async (req, res) => {
    try {
        const { productId } = req.params;
        const result = await capacityService.getProductProcess(productId);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('获取产品工艺流程失败:', error);
        res.status(500).json({
            success: false,
            message: '获取产品工艺流程失败',
            error: error.message
        });
    }
});

/**
 * 更新产品工艺流程
 * PUT /api/capacity-data/product/:productId/process
 */
router.put('/product/:productId/process', async (req, res) => {
    try {
        const { productId } = req.params;
        const processData = req.body;
        
        const result = await capacityService.updateProductProcess(productId, processData);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新产品工艺流程失败:', error);
        res.status(500).json({
            success: false,
            message: '更新产品工艺流程失败',
            error: error.message
        });
    }
});

/**
 * 计算产能需求
 * POST /api/capacity-data/calculate-requirements
 */
router.post('/calculate-requirements', async (req, res) => {
    try {
        const orderData = req.body;
        const result = await capacityService.calculateCapacityRequirements(orderData);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('计算产能需求失败:', error);
        res.status(500).json({
            success: false,
            message: '计算产能需求失败',
            error: error.message
        });
    }
});

/**
 * 获取设备利用率统计
 * GET /api/capacity-data/equipment-utilization
 */
router.get('/equipment-utilization', async (req, res) => {
    try {
        const { startDate, endDate, equipmentIds } = req.query;
        
        const filters = {};
        if (startDate) filters.startDate = startDate;
        if (endDate) filters.endDate = endDate;
        if (equipmentIds) filters.equipmentIds = equipmentIds.split(',');
        
        const result = await capacityService.getEquipmentUtilization(filters);
        
        res.json(result);
    } catch (error) {
        console.error('获取设备利用率统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备利用率统计失败',
            error: error.message
        });
    }
});

/**
 * 获取操作员效率统计
 * GET /api/capacity-data/operator-efficiency
 */
router.get('/operator-efficiency', async (req, res) => {
    try {
        const { startDate, endDate, operatorIds } = req.query;
        
        const filters = {};
        if (startDate) filters.startDate = startDate;
        if (endDate) filters.endDate = endDate;
        if (operatorIds) filters.operatorIds = operatorIds.split(',');
        
        const result = await capacityService.getOperatorEfficiency(filters);
        
        res.json(result);
    } catch (error) {
        console.error('获取操作员效率统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取操作员效率统计失败',
            error: error.message
        });
    }
});

/**
 * 获取产能分析报告
 * GET /api/capacity-data/analysis-report
 */
router.get('/analysis-report', async (req, res) => {
    try {
        const { startDate, endDate, reportType = 'summary' } = req.query;
        
        const options = {
            startDate,
            endDate,
            reportType
        };
        
        const result = await capacityService.generateCapacityAnalysisReport(options);
        
        res.json(result);
    } catch (error) {
        console.error('获取产能分析报告失败:', error);
        res.status(500).json({
            success: false,
            message: '获取产能分析报告失败',
            error: error.message
        });
    }
});

/**
 * 批量导入设备产能数据
 * POST /api/capacity-data/equipment/batch-import
 */
router.post('/equipment/batch-import', async (req, res) => {
    try {
        const { equipmentData } = req.body;
        
        if (!Array.isArray(equipmentData) || equipmentData.length === 0) {
            return res.status(400).json({
                success: false,
                message: '设备数据不能为空'
            });
        }
        
        const result = await capacityService.batchImportEquipmentCapacity(equipmentData);
        
        res.json(result);
    } catch (error) {
        console.error('批量导入设备产能数据失败:', error);
        res.status(500).json({
            success: false,
            message: '批量导入设备产能数据失败',
            error: error.message
        });
    }
});

/**
 * 批量导入操作员技能数据
 * POST /api/capacity-data/operator/batch-import
 */
router.post('/operator/batch-import', async (req, res) => {
    try {
        const { operatorData } = req.body;
        
        if (!Array.isArray(operatorData) || operatorData.length === 0) {
            return res.status(400).json({
                success: false,
                message: '操作员数据不能为空'
            });
        }
        
        const result = await capacityService.batchImportOperatorSkills(operatorData);
        
        res.json(result);
    } catch (error) {
        console.error('批量导入操作员技能数据失败:', error);
        res.status(500).json({
            success: false,
            message: '批量导入操作员技能数据失败',
            error: error.message
        });
    }
});

/**
 * 导出产能数据
 * GET /api/capacity-data/export
 */
router.get('/export', async (req, res) => {
    try {
        const { type, format = 'json', ...filters } = req.query;
        
        const result = await capacityService.exportCapacityData(type, filters);
        
        if (!result.success) {
            return res.status(400).json(result);
        }
        
        if (format === 'json') {
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', `attachment; filename="capacity_data_${type}.json"`);
            res.json(result.data);
        } else if (format === 'csv') {
            const csvData = convertToCSV(result.data, type);
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="capacity_data_${type}.csv"`);
            res.send(csvData);
        } else {
            res.status(400).json({
                success: false,
                message: '不支持的导出格式'
            });
        }
    } catch (error) {
        console.error('导出产能数据失败:', error);
        res.status(500).json({
            success: false,
            message: '导出产能数据失败',
            error: error.message
        });
    }
});

/**
 * 转换为CSV格式
 * @param {Array} data 数据
 * @param {string} type 数据类型
 * @returns {string} CSV字符串
 */
function convertToCSV(data, type) {
    if (!Array.isArray(data) || data.length === 0) {
        return '';
    }

    let headers = [];
    let rows = [];

    switch (type) {
        case 'equipment':
            headers = ['设备ID', '设备名称', '类型', '产能/小时', '效率系数', '状态'];
            rows = data.map(item => [
                item.id,
                item.name,
                item.type,
                item.capacityPerHour,
                item.efficiencyFactor,
                item.status
            ]);
            break;
            
        case 'operator':
            headers = ['操作员ID', '姓名', '技能等级', '效率系数', '可操作设备'];
            rows = data.map(item => [
                item.id,
                item.name,
                item.skillLevel,
                item.efficiencyFactor,
                item.equipmentTypes?.join(';') || ''
            ]);
            break;
            
        default:
            headers = Object.keys(data[0]);
            rows = data.map(item => headers.map(header => item[header] || ''));
    }

    const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

    return csvContent;
}

module.exports = router;
