/**
 * 已审核页面逻辑
 */

import { createApprovalApp } from '../../common/pageInit.js';
import { getCurrentUser } from '../../../scripts/api/auth.js';
import { getApplicationById } from '../../../scripts/api/application.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ApprovedApplicationList from '../../../components/application/ApprovedApplicationList.js';
import ApplicationDetail from '../../../components/application/ApplicationDetail.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

// 使用标准的审批页面应用创建函数，自动处理权限检查
createApprovalApp({
    components: {
        Sidebar,
        ApprovedApplicationList,
        ApplicationDetail
    },
    requiredPermissions: ['approved_applications'], // 需要已审核申请查看权限
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const showDetailModal = ref(false);
        const currentDetail = ref(null);

        // 初始化
        onMounted(() => {
            checkAuth();
            // 确保加载指示器被隐藏
            hideLoading();
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
            }
        }

        // 查看申请详情
        async function viewDetail(id) {
            try {
                currentDetail.value = await getApplicationById(id);
                showDetailModal.value = true;
            } catch (error) {
                console.error('获取详情失败:', error);
                alert('获取详情失败: ' + (error.response?.data?.message || error.message));
            }
        }

        // 关闭详情模态框
        function closeDetail() {
            showDetailModal.value = false;
        }

        return {
            currentUser,
            isAuthenticated,
            showDetailModal,
            currentDetail,
            viewDetail,
            closeDetail
        };
    }
}).mount('#app');
