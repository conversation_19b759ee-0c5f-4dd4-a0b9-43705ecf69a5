/**
 * 质量检测报告上传表单组件
 * 用于创建和编辑检测报告
 */

import { getQualityUsers } from '../../scripts/api/quality.js';

export default {
    name: 'QualityReportForm',
    props: {
        report: {
            type: Object,
            default: null
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    emits: ['submit', 'cancel'],
    setup(props, { emit }) {
        const { ref, reactive, computed, watch, onMounted } = Vue;

        // 表单数据
        const formData = reactive({
            title: '',
            description: '',
            testType: '',
            testDate: '',
            sampleInfo: '',
            testMethod: '',
            testStandard: '',
            testResult: '',
            conclusion: '',
            status: 'published'
        });

        // 文件上传相关
        const selectedFiles = ref([]);
        const fileInput = ref(null);
        const dragOver = ref(false);
        const isUploading = ref(false);

        // 表单验证
        const errors = reactive({});

        // 检测类型选项
        const testTypes = [
            '原材料检测',
            '半成品检测',
            '成品检测',
            '环境检测',
            '设备检测',
            '工艺检测',
            '其他检测'
        ];

        // 状态选项
        const statusOptions = [
            { value: 'published', label: '已发布' },
            { value: 'draft', label: '草稿' }
        ];

        // 用户选择相关
        const showUserSelection = ref(false);
        const allUsers = ref([]);
        const selectedUsers = ref([]);
        const isLoadingUsers = ref(false);
        const userSearchTerm = ref('');

        // 表单区域展开状态
        const expandedSections = reactive({
            basic: true,         // 基本信息默认展开
            details: true,       // 详细信息默认展开
            notification: true,  // 邮件通知默认展开
            attachments: true    // 文件上传默认展开
        });

        // 监听props变化，初始化表单数据
        watch(() => props.report, (newReport) => {
            if (newReport) {
                Object.assign(formData, {
                    title: newReport.title || '',
                    description: newReport.description || '',
                    testType: newReport.test_type || '',
                    testDate: newReport.test_date || '',
                    sampleInfo: newReport.sample_info || '',
                    testMethod: newReport.test_method || '',
                    testStandard: newReport.test_standard || '',
                    testResult: newReport.test_result || '',
                    conclusion: newReport.conclusion || '',
                    status: newReport.status || 'published'
                });
            }
        }, { immediate: true });

        // 表单验证规则
        function validateForm() {
            const newErrors = {};

            if (!formData.title.trim()) {
                newErrors.title = '报告标题不能为空';
            }

            if (!formData.testType) {
                newErrors.testType = '请选择检测类型';
            }

            if (!formData.testDate) {
                newErrors.testDate = '请选择检测日期';
            }

            if (!props.isEdit && selectedFiles.value.length === 0) {
                newErrors.files = '请至少上传一个检测报告文件';
            }

            Object.assign(errors, newErrors);
            return Object.keys(newErrors).length === 0;
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            processFiles(files);
        }

        // 处理文件拖放
        function handleFileDrop(event) {
            dragOver.value = false;
            const files = Array.from(event.dataTransfer.files);
            processFiles(files);
        }

        // 处理文件
        function processFiles(files) {
            const validFiles = [];
            const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx'];
            const maxFileSize = 10 * 1024 * 1024; // 10MB

            files.forEach(file => {
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                
                if (!allowedTypes.includes(fileExtension)) {
                    alert(`文件 ${file.name} 格式不支持。支持的格式：${allowedTypes.join(', ')}`);
                    return;
                }

                if (file.size > maxFileSize) {
                    alert(`文件 ${file.name} 大小超过10MB限制`);
                    return;
                }

                validFiles.push(file);
            });

            selectedFiles.value = [...selectedFiles.value, ...validFiles];
            
            // 清除文件错误
            if (errors.files && selectedFiles.value.length > 0) {
                delete errors.files;
            }
        }

        // 移除文件
        function removeFile(index) {
            selectedFiles.value.splice(index, 1);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取文件图标
        function getFileIcon(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();
            switch (extension) {
                case 'pdf':
                    return '📄';
                case 'doc':
                case 'docx':
                    return '📝';
                case 'xls':
                case 'xlsx':
                    return '📊';
                default:
                    return '📎';
            }
        }

        // 过滤后的用户列表
        const filteredUsers = computed(() => {
            if (!userSearchTerm.value) {
                return allUsers.value;
            }
            const searchTerm = userSearchTerm.value.toLowerCase();
            return allUsers.value.filter(user =>
                (user.name && user.name.toLowerCase().includes(searchTerm)) ||
                (user.code && user.code.toLowerCase().includes(searchTerm)) ||
                (user.department && user.department.toLowerCase().includes(searchTerm))
            );
        });

        // 切换用户选择显示状态
        async function toggleUserSelectionDisplay() {
            if (showUserSelection.value) {
                // 如果已经显示，则隐藏
                showUserSelection.value = false;
            } else {
                // 如果未显示，则加载用户并显示
                await loadAllUsers();
            }
        }

        // 加载所有用户
        async function loadAllUsers() {
            try {
                isLoadingUsers.value = true;
                const response = await getQualityUsers();

                if (response.success && response.data) {
                    // 用户已经在后端过滤了活跃且有邮箱的用户
                    allUsers.value = response.data;
                    showUserSelection.value = true;
                } else {
                    throw new Error(response.message || '获取用户列表失败');
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
                alert('加载用户列表失败: ' + error.message);
            } finally {
                isLoadingUsers.value = false;
            }
        }

        // 切换用户选择
        function toggleUserSelection(user) {
            const index = selectedUsers.value.findIndex(u => u.id === user.id);
            if (index > -1) {
                selectedUsers.value.splice(index, 1);
            } else {
                selectedUsers.value.push(user);
            }
        }

        // 检查用户是否被选中
        function isUserSelected(user) {
            return selectedUsers.value.some(u => u.id === user.id);
        }

        // 清除用户选择
        function clearUserSelection() {
            selectedUsers.value = [];
            showUserSelection.value = false;
            userSearchTerm.value = '';
        }

        // 切换区域展开状态
        function toggleSection(section) {
            expandedSections[section] = !expandedSections[section];
        }

        // 提交表单
        async function handleSubmit() {
            if (!validateForm()) {
                return;
            }

            try {
                isUploading.value = true;

                const submitData = new FormData();
                
                // 添加表单数据
                Object.keys(formData).forEach(key => {
                    submitData.append(key, formData[key]);
                });

                // 添加选中的用户ID列表
                if (selectedUsers.value.length > 0) {
                    const userIds = selectedUsers.value.map(user => user.id);
                    submitData.append('notifyUsers', JSON.stringify(userIds));
                }

                // 添加文件
                selectedFiles.value.forEach(file => {
                    submitData.append('files', file);
                });

                emit('submit', submitData);
            } catch (error) {
                console.error('提交表单失败:', error);
                alert('提交失败: ' + error.message);
            } finally {
                isUploading.value = false;
            }
        }

        // 检查表单是否有数据
        const hasFormData = computed(() => {
            return formData.title.trim() !== '' ||
                   formData.testType !== '' ||
                   formData.testDate !== '' ||
                   formData.sampleInfo.trim() !== '' ||
                   formData.testMethod.trim() !== '' ||
                   formData.testStandard.trim() !== '' ||
                   formData.testResult.trim() !== '' ||
                   formData.conclusion.trim() !== '' ||
                   formData.description.trim() !== '' ||
                   selectedUsers.value.length > 0 ||
                   selectedFiles.value.length > 0;
        });

        // 重置表单
        function resetForm() {
            // 重置表单数据
            formData.title = '';
            formData.testType = '';
            formData.testDate = '';
            formData.sampleInfo = '';
            formData.testMethod = '';
            formData.testStandard = '';
            formData.testResult = '';
            formData.conclusion = '';
            formData.description = '';
            formData.status = 'draft';

            // 清空选择的用户和文件
            selectedUsers.value = [];
            selectedFiles.value = [];
            showUserSelection.value = false;
            userSearchTerm.value = '';

            // 清空错误信息
            Object.keys(errors).forEach(key => {
                errors[key] = '';
            });
        }

        // 取消操作
        function handleCancel() {
            if (hasFormData.value) {
                if (confirm('确定要清空表单吗？所有已填写的数据将被清除。')) {
                    resetForm();
                }
            }
        }

        // 设置今天为默认检测日期
        onMounted(() => {
            if (!props.isEdit && !formData.testDate) {
                const today = new Date().toISOString().split('T')[0];
                formData.testDate = today;
            }
        });

        return {
            formData,
            selectedFiles,
            fileInput,
            dragOver,
            isUploading,
            errors,
            testTypes,
            statusOptions,
            // 折叠区域相关
            expandedSections,
            toggleSection,
            // 用户选择相关
            showUserSelection,
            allUsers,
            selectedUsers,
            isLoadingUsers,
            userSearchTerm,
            filteredUsers,
            loadAllUsers,
            toggleUserSelectionDisplay,
            toggleUserSelection,
            isUserSelected,
            clearUserSelection,
            // 表单状态和操作
            hasFormData,
            resetForm,
            // 文件和表单处理
            handleFileSelect,
            handleFileDrop,
            removeFile,
            formatFileSize,
            getFileIcon,
            handleSubmit,
            handleCancel
        };
    },
    template: `
        <section class="space-y-6">
            <form @submit.prevent="handleSubmit" class="space-y-6">

                <!-- 基本信息区域 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('basic')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"></path>
                            </svg>
                            基本信息
                        </h4>
                        <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                     expandedSections.basic ? 'rotate-180' : '']"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    <div v-show="expandedSections.basic" class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- 报告标题 -->
                            <div class="md:col-span-3">
                                <label class="block text-gray-700 mb-2 font-medium">
                                    报告标题 <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    v-model="formData.title"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                                    :class="{ 'border-red-500': errors.title }"
                                    placeholder="请输入检测报告标题"
                                >
                                <p v-if="errors.title" class="mt-1 text-sm text-red-500">{{ errors.title }}</p>
                            </div>

                            <!-- 检测类型 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">
                                    检测类型 <span class="text-red-500">*</span>
                                </label>
                                <select
                                    v-model="formData.testType"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                                    :class="{ 'border-red-500': errors.testType }"
                                >
                                    <option value="">请选择检测类型</option>
                                    <option v-for="type in testTypes" :key="type" :value="type">{{ type }}</option>
                                </select>
                                <p v-if="errors.testType" class="mt-1 text-sm text-red-500">{{ errors.testType }}</p>
                            </div>

                            <!-- 检测日期 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">
                                    检测日期 <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="date"
                                    v-model="formData.testDate"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                                    :class="{ 'border-red-500': errors.testDate }"
                                >
                                <p v-if="errors.testDate" class="mt-1 text-sm text-red-500">{{ errors.testDate }}</p>
                            </div>

                            <!-- 发布状态 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">发布状态</label>
                                <select
                                    v-model="formData.status"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                                >
                                    <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细信息区域 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('details')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            详细信息
                        </h4>
                        <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                     expandedSections.details ? 'rotate-180' : '']"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    <div v-show="expandedSections.details" class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 样品信息 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">样品信息</label>
                                <textarea
                                    v-model="formData.sampleInfo"
                                    rows="3"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 resize-vertical"
                                    placeholder="请输入样品信息，如样品名称、规格、批次等"
                                ></textarea>
                            </div>

                            <!-- 检测方法 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">检测方法</label>
                                <textarea
                                    v-model="formData.testMethod"
                                    rows="3"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 resize-vertical"
                                    placeholder="请输入检测方法和流程"
                                ></textarea>
                            </div>

                            <!-- 检测标准 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">检测标准</label>
                                <textarea
                                    v-model="formData.testStandard"
                                    rows="3"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 resize-vertical"
                                    placeholder="请输入执行的检测标准"
                                ></textarea>
                            </div>

                            <!-- 检测结果 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">检测结果</label>
                                <textarea
                                    v-model="formData.testResult"
                                    rows="3"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 resize-vertical"
                                    placeholder="请输入检测结果数据"
                                ></textarea>
                            </div>
                        </div>

                        <!-- 结论和描述 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 检测结论 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">检测结论</label>
                                <textarea
                                    v-model="formData.conclusion"
                                    rows="4"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 resize-vertical"
                                    placeholder="请输入检测结论和建议"
                                ></textarea>
                            </div>

                            <!-- 报告描述 -->
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">报告描述</label>
                                <textarea
                                    v-model="formData.description"
                                    rows="4"
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 resize-vertical"
                                    placeholder="请输入报告的补充描述"
                                ></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 邮件通知区域 -->
                <div v-if="!isEdit" class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('notification')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            邮件通知
                            <span class="text-sm text-gray-500 ml-2">(可选)</span>
                        </h4>
                        <div class="flex items-center space-x-2">
                            <span v-if="selectedUsers.length > 0" class="text-sm text-gray-500">已选择 {{ selectedUsers.length }} 人</span>
                            <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                         expandedSections.notification ? 'rotate-180' : '']"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div v-show="expandedSections.notification" class="p-6">
                        <div class="mb-4">
                            <p class="text-sm text-gray-600 mb-4">
                                选择要通知的用户，他们将收到检测报告上传完成的邮件通知。
                            </p>
                            <button
                                type="button"
                                @click="toggleUserSelectionDisplay"
                                :disabled="isLoadingUsers"
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                            >
                                <svg v-if="isLoadingUsers" class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                                </svg>
                                {{ isLoadingUsers ? '加载中...' : (showUserSelection ? '收起用户列表' : '选择用户') }}
                            </button>
                        </div>

                        <!-- 已选择的用户显示 -->
                        <div v-if="selectedUsers.length > 0" class="mb-4">
                            <h5 class="text-sm font-medium text-gray-700 mb-3">已选择的用户：</h5>
                            <div class="flex flex-wrap gap-2 mb-3">
                                <div v-for="user in selectedUsers" :key="user.id"
                                     class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                                    <span>{{ user.name }}</span>
                                    <button @click="toggleUserSelection(user)" type="button"
                                            class="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-600 hover:bg-blue-200 hover:text-blue-800">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <button
                                type="button"
                                @click="clearUserSelection"
                                class="text-sm text-blue-600 hover:text-blue-800"
                            >
                                清除所有选择
                            </button>
                        </div>

                        <!-- 用户选择列表 -->
                        <div v-if="showUserSelection" class="border border-gray-200 rounded-lg bg-gray-50">
                            <div class="p-4 border-b border-gray-200">
                                <input
                                    type="text"
                                    v-model="userSearchTerm"
                                    placeholder="搜索用户姓名、工号或部门..."
                                    class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                                >
                            </div>
                            <div class="max-h-60 overflow-y-auto">
                                <div
                                    v-for="user in filteredUsers"
                                    :key="user.id"
                                    @click="toggleUserSelection(user)"
                                    class="flex items-center p-4 hover:bg-white cursor-pointer transition-colors border-b border-gray-100 last:border-b-0"
                                    :class="{ 'bg-blue-50 border-blue-200': isUserSelected(user) }"
                                >
                                    <div class="flex-shrink-0">
                                        <div :class="['w-10 h-10 rounded-full flex items-center justify-center text-white font-medium',
                                                     isUserSelected(user) ? 'bg-blue-600' : 'bg-gray-400']">
                                            {{ user.name.charAt(0) }}
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <div class="font-medium text-gray-900">{{ user.name }}</div>
                                        <div class="text-sm text-gray-500">
                                            {{ user.code }} | {{ user.department || '未填写部门' }} | {{ user.email }}
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <svg v-if="isUserSelected(user)"
                                             class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div v-if="filteredUsers.length === 0" class="p-8 text-center text-gray-500">
                                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"></path>
                                    </svg>
                                    没有找到匹配的用户
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- 文件上传区域 -->
                <div v-if="!isEdit" class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('attachments')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            文件上传
                            <span class="text-red-500 ml-1">*</span>
                        </h4>
                        <div class="flex items-center space-x-2">
                            <span v-if="selectedFiles.length > 0" class="text-sm text-gray-500">{{ selectedFiles.length }} 个文件</span>
                            <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                         expandedSections.attachments ? 'rotate-180' : '']"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div v-show="expandedSections.attachments" class="p-6">
                        <div
                            class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 transition-colors"
                            :class="{
                                'border-blue-500 bg-blue-50': dragOver,
                                'border-red-500': errors.files
                            }"
                            @click="fileInput.click()"
                            @dragover.prevent="dragOver = true"
                            @dragleave.prevent="dragOver = false"
                            @drop.prevent="handleFileDrop"
                        >
                            <input
                                ref="fileInput"
                                type="file"
                                multiple
                                accept=".pdf,.doc,.docx,.xls,.xlsx"
                                @change="handleFileSelect"
                                class="hidden"
                            >
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <p class="mt-2 text-lg font-medium text-gray-700">点击或拖拽文件到此处上传</p>
                                <p class="text-sm text-gray-500">支持 PDF、Word、Excel 格式，单个文件最大 10MB</p>
                            </div>
                        </div>
                        <p v-if="errors.files" class="mt-2 text-sm text-red-500">{{ errors.files }}</p>

                        <!-- 已选择的文件列表 -->
                        <div v-if="selectedFiles.length > 0" class="mt-6">
                            <h5 class="text-sm font-medium text-gray-700 mb-3">已选择的文件：</h5>
                            <div class="space-y-2">
                                <div
                                    v-for="(file, index) in selectedFiles"
                                    :key="index"
                                    class="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200"
                                >
                                    <div class="flex items-center space-x-3">
                                        <span class="text-2xl">{{ getFileIcon(file.name) }}</span>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ file.name }}</p>
                                            <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
                                        </div>
                                    </div>
                                    <button
                                        type="button"
                                        @click="removeFile(index)"
                                        class="text-red-600 hover:text-red-800 transition-colors"
                                    >
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- 提交按钮区域 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row gap-3 justify-start">
                            <!-- 上传按钮 -->
                            <button
                                type="submit"
                                class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center font-medium"
                                :disabled="isUploading"
                            >
                                <span v-if="isUploading" class="mr-2">
                                    <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"></path>
                                </svg>
                                {{ isUploading ? '上传中...' : '上传报告' }}
                            </button>

                            <!-- 取消按钮 -->
                            <button
                                v-if="hasFormData"
                                type="button"
                                @click="handleCancel"
                                class="px-8 py-3 bg-white text-gray-700 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center font-medium"
                                :disabled="isUploading"
                            >
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </section>
    `
};
