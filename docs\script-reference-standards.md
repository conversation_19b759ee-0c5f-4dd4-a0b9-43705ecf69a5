# 脚本引用标准规范

## 当前状态分析

经过全面检查，系统中的脚本引用位置已经是**正确且一致的**！

## 脚本引用最佳实践

### ✅ 当前正确的做法

1. **CSS文件在页首**（`<head>`中）：
   ```html
   <head>
       <script src="/js/libs/tailwindcss.js"></script>
       <style>
           /* 页面特定样式 */
       </style>
   </head>
   ```

2. **JavaScript文件在页末**（`</body>`之前）：
   ```html
   <body>
       <!-- 页面内容 -->
       
       <script src="/js/libs/vue.global.js"></script>
       <script src="/js/libs/axios.min.js"></script>
       <script type="module" src="/scripts/pages/xxx.js"></script>
   </body>
   ```

### 📋 检查结果

所有页面都遵循了正确的脚本引用模式：

#### 申请管理页面 ✅
- `frontend/pages/application/new.html` - 脚本在页末
- `frontend/pages/application/record.html` - 脚本在页末
- `frontend/pages/application/pending.html` - 脚本在页末
- `frontend/pages/application/approved.html` - 脚本在页末
- `frontend/pages/application/history.html` - 脚本在页末

#### 生产排程管理页面 ✅
- `frontend/pages/schedule/dashboard.html` - 脚本在页末
- `frontend/pages/schedule/list.html` - 脚本在页末
- `frontend/pages/schedule/create.html` - 脚本在页末
- `frontend/pages/schedule/edit.html` - 脚本在页末
- `frontend/pages/schedule/resources.html` - 脚本在页末
- `frontend/pages/schedule/reports.html` - 脚本在页末

#### 用户管理页面 ✅
- `frontend/pages/user/management.html` - 脚本在页末
- `frontend/pages/user/settings.html` - 脚本在页末

#### 系统页面 ✅
- `frontend/pages/login.html` - 脚本在页末
- `frontend/pages/index.html` - 脚本在页末

## 为什么这样做是正确的

### 🚀 性能优势

1. **非阻塞渲染**：
   - HTML内容先加载，用户能更快看到页面
   - JavaScript不会阻塞页面渲染

2. **DOM可用性**：
   - 脚本执行时DOM已经完全加载
   - 不需要等待`DOMContentLoaded`事件

3. **并行下载**：
   - 浏览器可以并行下载多个脚本文件
   - 提高整体加载速度

### 🎯 用户体验

1. **更快的首屏渲染**：
   - 用户能更快看到页面内容
   - 减少白屏时间

2. **渐进式增强**：
   - 页面结构先显示
   - JavaScript功能后加载

## 脚本加载顺序规范

### 标准加载顺序

```html
<!-- 1. Vue.js 框架 -->
<script src="/js/libs/vue.global.js"></script>

<!-- 2. HTTP请求库 -->
<script src="/js/libs/axios.min.js"></script>

<!-- 3. 全局脚本（如果需要） -->
<script type="module" src="/scripts/global.js"></script>

<!-- 4. 页面特定脚本 -->
<script type="module" src="/scripts/pages/xxx.js"></script>
```

### 加载顺序说明

1. **Vue.js**：必须最先加载，因为页面脚本依赖Vue
2. **Axios**：HTTP请求库，API调用需要
3. **全局脚本**：全局配置和工具函数
4. **页面脚本**：页面特定的业务逻辑

## 特殊情况处理

### CSS预处理器

Tailwind CSS使用JavaScript实现，但作为样式工具，应该在`<head>`中加载：

```html
<head>
    <script src="/js/libs/tailwindcss.js"></script>
</head>
```

### 模块化脚本

使用ES6模块的脚本需要`type="module"`属性：

```html
<script type="module" src="/scripts/pages/xxx.js"></script>
```

## 结论

**当前系统的脚本引用位置是完全正确的，无需修改！**

- ✅ CSS在页首，确保样式优先加载
- ✅ JavaScript在页末，优化性能和用户体验
- ✅ 所有页面保持一致的引用模式
- ✅ 遵循Web开发最佳实践

## 建议

1. **保持现状**：当前的脚本引用模式是最佳实践
2. **新页面遵循**：新创建的页面应遵循相同的模式
3. **定期检查**：确保新添加的页面符合规范

## 参考资料

- [MDN - Script元素最佳实践](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/script)
- [Google Web Fundamentals - 优化JavaScript](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/javascript-startup-optimization)
- [Vue.js 官方文档 - 安装](https://vuejs.org/guide/quick-start.html)
