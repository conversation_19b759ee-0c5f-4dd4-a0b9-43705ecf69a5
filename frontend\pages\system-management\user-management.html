<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/css/libs/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/user/common.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <!-- 面包屑导航 -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="inline-flex items-center text-sm font-medium text-gray-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            系统管理 - 用户管理
                        </span>
                    </li>
                </ol>
            </nav>
            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-6">用户管理</h2>

                <!-- 用户管理选项卡 -->
                <div class="border-b border-gray-200 mb-6">
                    <div class="flex space-x-8">
                        <button
                            @click="activeTab = 'users'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'users'}">
                            用户列表
                        </button>
                        <button
                            @click="activeTab = 'permissions'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'permissions'}">
                            权限管理
                        </button>
                        <button
                            @click="activeTab = 'templates'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'templates'}">
                            权限模板
                        </button>
                        <button
                            @click="activeTab = 'departments'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'departments'}">
                            部门管理
                        </button>
                    </div>
                </div>

                <!-- 用户列表 -->
                <user-list
                    v-if="activeTab === 'users'"
                    :user="currentUser"
                    @edit-user="openEditUserModal"
                    @add-user="openAddUserModal">
                </user-list>

                <!-- 权限管理 -->
                <permission-manager
                    v-if="activeTab === 'permissions' && selectedUser"
                    :user="selectedUser"
                    :current-user="currentUser"
                    @updated="onUserUpdated"
                    @batch-updated="onBatchUpdated"
                    @back="clearSelectedUser">
                </permission-manager>

                <!-- 权限模板管理 -->
                <permission-template-manager
                    v-if="activeTab === 'templates'"
                    :current-user="currentUser">
                </permission-template-manager>

                <!-- 部门管理 -->
                <department-manager
                    v-if="activeTab === 'departments'"
                    :current-user="currentUser"
                    @updated="onDepartmentUpdated">
                </department-manager>

                <!-- 用户选择 -->
                <div v-if="activeTab === 'permissions' && !selectedUser" class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">选择用户</h3>
                    <p class="text-gray-600 mb-4">请从下面的列表中选择一个用户来管理其权限：</p>

                    <!-- 筛选选项 -->
                    <div class="mb-6 flex flex-wrap items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-700 mr-2">筛选：</span>
                                <select v-model="permissionFilter" class="px-3 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="all">所有用户</option>
                                    <option value="with">已设置权限</option>
                                    <option value="without">未设置权限</option>
                                </select>
                            </div>

                            <div class="relative w-64">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <input type="text" v-model="userSearchTerm" placeholder="搜索用户名或角色..."
                                       class="pl-10 pr-8 py-1.5 w-full border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <button v-if="userSearchTerm" @click="userSearchTerm = ''" class="absolute inset-y-0 right-0 pr-2 flex items-center text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div v-if="isLoadingUsers" class="flex justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <span class="ml-2 text-gray-600">加载中...</span>
                    </div>

                    <div v-else-if="filteredUsersList.length === 0" class="py-8 text-center text-gray-500">
                        没有找到符合条件的用户
                    </div>

                    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div v-for="user in filteredUsersList" :key="user.id"
                             @click="selectUser(user)"
                             class="border border-gray-200 rounded-md p-4 hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-colors duration-200"
                             :class="{'border-green-300 bg-green-50': hasUserPermissions(user)}">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold">
                                        {{ user.name.charAt(0) }}
                                    </div>
                                </div>
                                <div class="ml-3 flex-1">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ user.name }}</p>
                                            <p class="text-xs text-gray-500">{{ user.role }}</p>
                                        </div>
                                        <div v-if="hasUserPermissions(user)" class="ml-2 text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                            已设置权限
                                        </div>
                                        <div v-else class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                                            未设置权限
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分页控制 -->
                    <div v-if="filteredUsersList.length > 0" class="mt-6 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            共 {{ filteredUsersList.length }} 个用户
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户表单模态框 -->
        <div v-if="showUserModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg p-4 md:p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4 sticky top-0 bg-white pt-1">
                    <h3 class="text-lg font-semibold">{{ isEditing ? '编辑用户' : '添加用户' }}</h3>
                    <button @click="closeUserModal" class="text-gray-500 hover:text-gray-700 p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitUserForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户代码</label>
                        <input type="text" v-model="userForm.code" placeholder="例如：E10001"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                        <input type="text" v-model="userForm.name" @blur="validateField('name')" required placeholder="请输入用户姓名"
                               :class="['w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                                      formTouched.name && formErrors.name ? 'border-red-500' : 'border-gray-300']">
                        <p v-if="formTouched.name && formErrors.name" class="mt-1 text-xs text-red-500">{{ formErrors.name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                        <input type="email" v-model="userForm.email" @blur="validateField('email')" placeholder="<EMAIL>"
                               :class="['w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                                      formTouched.email && formErrors.email ? 'border-red-500' : 'border-gray-300']">
                        <p v-if="formTouched.email && formErrors.email" class="mt-1 text-xs text-red-500">{{ formErrors.email }}</p>
                        <p v-else class="text-xs text-gray-500 mt-1">邮箱将显示在用户姓名下方</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">角色 <span class="text-red-500">*</span></label>
                        <select v-model="userForm.role" @blur="validateField('role')" required
                                :class="['w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                                       formTouched.role && formErrors.role ? 'border-red-500' : 'border-gray-300']">
                            <option value="">请选择角色</option>
                            <option v-for="role in roles" :key="role" :value="role">{{ role }}</option>
                        </select>
                        <p v-if="formTouched.role && formErrors.role" class="mt-1 text-xs text-red-500">{{ formErrors.role }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                        <select v-model="userForm.department"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择部门</option>
                            <option v-for="dept in departments" :key="dept.id || dept" :value="dept.name || dept">{{ dept.name || dept }}</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            密码 <span v-if="!isEditing" class="text-red-500">*</span>
                        </label>
                        <input type="password" v-model="userForm.password" @blur="validateField('password')" :required="!isEditing" placeholder="至少6个字符"
                               :class="['w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                                      formTouched.password && formErrors.password ? 'border-red-500' : 'border-gray-300']">
                        <p v-if="formTouched.password && formErrors.password" class="mt-1 text-xs text-red-500">{{ formErrors.password }}</p>
                        <p v-else-if="isEditing" class="text-xs text-gray-500 mt-1">如果不需要修改密码，请留空</p>
                    </div>

                    <div v-if="!isEditing || userForm.password">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            确认密码 <span v-if="!isEditing" class="text-red-500">*</span>
                        </label>
                        <input type="password" v-model="userForm.confirmPassword" @blur="validateField('confirmPassword')" :required="!isEditing || userForm.password" placeholder="再次输入密码"
                               :class="['w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                                      formTouched.confirmPassword && formErrors.confirmPassword ? 'border-red-500' : 'border-gray-300']">
                        <p v-if="formTouched.confirmPassword && formErrors.confirmPassword" class="mt-1 text-xs text-red-500">{{ formErrors.confirmPassword }}</p>
                    </div>

                    <div class="flex items-center">
                        <label class="inline-flex items-center cursor-pointer">
                            <div class="relative inline-block w-12 h-6 mr-2 align-middle select-none">
                                <input type="checkbox" v-model="userForm.active" id="toggle-active"
                                       class="toggle-checkbox"/>
                                <label for="toggle-active" class="toggle-label"></label>
                            </div>
                            <span class="text-sm font-medium text-gray-700">启用</span>
                        </label>
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" @click="closeUserModal" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none">
                            取消
                        </button>
                        <button type="submit" :disabled="isSubmitting" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none disabled:opacity-50">
                            {{ isSubmitting ? '提交中...' : (isEditing ? '更新' : '创建') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/system-management/user-management.js"></script>
</body>
</html>
