/**
 * 智能排程API路由
 * 处理智能排程相关的API请求
 */

const express = require('express');
const router = express.Router();
const SchedulingService = require('../services/SchedulingService');
const { authenticateJWT, authorizeRoles } = require('../middlewares/auth');

// 创建排程服务实例
const schedulingService = new SchedulingService();

// 中间件：所有智能排程操作需要认证
router.use(authenticateJWT);

/**
 * 创建智能排程方案
 * POST /api/scheduling/plans
 */
router.post('/plans', async (req, res) => {
    try {
        const orderData = req.body;
        const options = req.body.options || {};

        const result = await schedulingService.createSchedulePlans(orderData, options);

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }

    } catch (error) {
        console.error('创建智能排程方案失败:', error);
        res.status(500).json({
            success: false,
            message: '创建排程方案失败',
            error: error.message
        });
    }
});

/**
 * 获取排程方案
 * GET /api/scheduling/plans/:orderId
 */
router.get('/plans/:orderId', async (req, res) => {
    try {
        const { orderId } = req.params;

        const result = await schedulingService.getSchedulePlans(orderId);

        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }

    } catch (error) {
        console.error('获取排程方案失败:', error);
        res.status(500).json({
            success: false,
            message: '获取排程方案失败',
            error: error.message
        });
    }
});

/**
 * 更新排程方案
 * PUT /api/scheduling/plans/:orderId/:planId
 */
router.put('/plans/:orderId/:planId', async (req, res) => {
    try {
        const { orderId, planId } = req.params;
        const updateData = req.body;

        const result = await schedulingService.updateSchedulePlan(orderId, planId, updateData);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }

    } catch (error) {
        console.error('更新排程方案失败:', error);
        res.status(500).json({
            success: false,
            message: '更新排程方案失败',
            error: error.message
        });
    }
});

/**
 * 选择排程方案
 * POST /api/scheduling/plans/:orderId/:planId/select
 */
router.post('/plans/:orderId/:planId/select', async (req, res) => {
    try {
        const { orderId, planId } = req.params;

        const result = await schedulingService.selectSchedulePlan(orderId, planId);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }

    } catch (error) {
        console.error('选择排程方案失败:', error);
        res.status(500).json({
            success: false,
            message: '选择排程方案失败',
            error: error.message
        });
    }
});

/**
 * 重新生成排程方案
 * POST /api/scheduling/plans/:orderId/regenerate
 */
router.post('/plans/:orderId/regenerate', async (req, res) => {
    try {
        const { orderId } = req.params;
        const newConstraints = req.body.constraints || {};

        const result = await schedulingService.regenerateSchedulePlans(orderId, newConstraints);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }

    } catch (error) {
        console.error('重新生成排程方案失败:', error);
        res.status(500).json({
            success: false,
            message: '重新生成排程方案失败',
            error: error.message
        });
    }
});

/**
 * 获取排程统计信息
 * GET /api/scheduling/statistics
 */
router.get('/statistics', async (req, res) => {
    try {
        const filters = req.query;

        const result = await schedulingService.getSchedulingStatistics(filters);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }

    } catch (error) {
        console.error('获取排程统计信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取统计信息失败',
            error: error.message
        });
    }
});

/**
 * 批量创建排程方案
 * POST /api/scheduling/plans/batch
 */
router.post('/plans/batch', async (req, res) => {
    try {
        const { orders, options } = req.body;

        if (!Array.isArray(orders) || orders.length === 0) {
            return res.status(400).json({
                success: false,
                message: '订单列表不能为空'
            });
        }

        const results = [];
        const errors = [];

        // 并行处理多个订单
        const promises = orders.map(async (orderData, index) => {
            try {
                const result = await schedulingService.createSchedulePlans(orderData, options);
                return { index, result };
            } catch (error) {
                return { index, error: error.message };
            }
        });

        const responses = await Promise.allSettled(promises);

        responses.forEach((response, index) => {
            if (response.status === 'fulfilled') {
                if (response.value.result) {
                    results.push({
                        orderId: orders[index].id,
                        ...response.value.result
                    });
                } else {
                    errors.push({
                        orderId: orders[index].id,
                        error: response.value.error
                    });
                }
            } else {
                errors.push({
                    orderId: orders[index].id,
                    error: response.reason
                });
            }
        });

        res.json({
            success: true,
            data: {
                successful: results,
                failed: errors,
                summary: {
                    total: orders.length,
                    successful: results.length,
                    failed: errors.length
                }
            }
        });

    } catch (error) {
        console.error('批量创建排程方案失败:', error);
        res.status(500).json({
            success: false,
            message: '批量创建排程方案失败',
            error: error.message
        });
    }
});


/**
 * 导出排程方案
 * GET /api/scheduling/plans/:orderId/export
 */
router.get('/plans/:orderId/export', async (req, res) => {
    try {
        const { orderId } = req.params;
        const { format = 'json' } = req.query;

        const result = await schedulingService.getSchedulePlans(orderId);

        if (!result.success) {
            return res.status(404).json(result);
        }

        const exportData = {
            orderId,
            exportedAt: new Date().toISOString(),
            plans: result.data.plans,
            metadata: result.data.metadata
        };

        switch (format.toLowerCase()) {
            case 'json':
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Content-Disposition', `attachment; filename="schedule_${orderId}.json"`);
                res.json(exportData);
                break;

            case 'csv':
                // 简化的CSV导出
                const csvData = this.convertToCSV(exportData);
                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="schedule_${orderId}.csv"`);
                res.send(csvData);
                break;

            default:
                res.status(400).json({
                    success: false,
                    message: '不支持的导出格式'
                });
        }

    } catch (error) {
        console.error('导出排程方案失败:', error);
        res.status(500).json({
            success: false,
            message: '导出排程方案失败',
            error: error.message
        });
    }
});

/**
 * 转换为CSV格式
 * @param {Object} data 数据
 * @returns {string} CSV字符串
 */
function convertToCSV(data) {
    const headers = ['方案ID', '方案名称', '方案类型', '预计交期', '按时概率', '效率', '成本', '风险等级'];
    const rows = data.plans.map(plan => [
        plan.id,
        plan.name,
        plan.type,
        plan.finalPrediction?.deliveryDate || '',
        plan.finalPrediction?.onTimeProb || '',
        plan.metrics?.efficiency || '',
        plan.metrics?.cost || '',
        plan.riskAssessment?.level || ''
    ]);

    const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

    return csvContent;
}

module.exports = router;
