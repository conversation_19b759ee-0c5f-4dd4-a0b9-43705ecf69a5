/**
 * 维修保养记录数据模型
 * 处理维修保养记录的数据结构和验证
 */

const { v4: uuidv4 } = require('uuid');

/**
 * 维修保养记录模型
 */
class MaintenanceModel {
    constructor(data = {}) {
        this.id = data.id || '';
        this.equipmentId = data.equipmentId || data.equipment_id || '';
        this.equipment = data.equipment || null;
        this.type = data.type || '';
        this.severityLevel = data.severityLevel || data.severity_level || '';
        this.description = data.description || '';
        this.maintenanceDate = data.maintenanceDate || data.maintenance_date || '';
        this.startTime = data.startTime || data.start_time || '';
        this.endTime = data.endTime || data.end_time || '';
        this.cost = data.cost || 0;
        this.technician = data.technician || '';
        this.status = data.status || 'pending';
        this.notes = data.notes || '';
        this.result = data.result || '';
        this.reviewer = data.reviewer || '';
        this.createdAt = data.createdAt || data.created_at || new Date().toISOString();
        this.updatedAt = data.updatedAt || data.updated_at || new Date().toISOString();
    }

    /**
     * 验证数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        // 必填字段验证
        if (!this.equipmentId) {
            errors.push('设备ID不能为空');
        }

        if (!this.type) {
            errors.push('维修类型不能为空');
        } else if (!['maintenance', 'repair'].includes(this.type)) {
            errors.push('维修类型必须是 maintenance（保养）或 repair（维修）');
        }

        if (!this.description) {
            errors.push('描述不能为空');
        } else if (this.description.length > 500) {
            errors.push('描述长度不能超过500字符');
        }

        if (!this.maintenanceDate) {
            errors.push('维修日期不能为空');
        } else if (!this.isValidDate(this.maintenanceDate)) {
            errors.push('维修日期格式不正确');
        }

        if (this.startTime && !this.isValidTime(this.startTime)) {
            errors.push('开始时间格式不正确');
        }

        if (this.endTime && !this.isValidTime(this.endTime)) {
            errors.push('结束时间格式不正确');
        }

        if (this.startTime && this.endTime && this.startTime >= this.endTime) {
            errors.push('结束时间必须晚于开始时间');
        }

        if (!this.technician) {
            errors.push('技术员不能为空');
        } else if (this.technician.length > 50) {
            errors.push('技术员名称长度不能超过50字符');
        }

        if (!['pending', 'in_progress', 'completed', 'cancelled'].includes(this.status)) {
            errors.push('状态必须是 pending、in_progress、completed 或 cancelled');
        }

        if (this.cost < 0) {
            errors.push('费用不能为负数');
        }

        if (this.notes && this.notes.length > 1000) {
            errors.push('备注长度不能超过1000字符');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证日期格式 (YYYY-MM-DD)
     * @param {string} dateString 日期字符串
     * @returns {boolean} 是否有效
     */
    isValidDate(dateString) {
        // 检查格式是否为 YYYY-MM-DD
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(dateString)) {
            return false;
        }

        // 检查日期是否有效 - 使用UTC时间避免时区问题
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(Date.UTC(year, month - 1, day));

        // 验证日期是否有效（例如：2月30日是无效的）
        return date instanceof Date && !isNaN(date) &&
               date.getUTCFullYear() === year &&
               date.getUTCMonth() === month - 1 &&
               date.getUTCDate() === day;
    }

    /**
     * 验证时间格式
     * @param {string} timeString 时间字符串 (HH:MM)
     * @returns {boolean} 是否有效
     */
    isValidTime(timeString) {
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        return timeRegex.test(timeString);
    }

    /**
     * 格式化日期为YYYY-MM-DD格式
     * @param {string} dateString 日期字符串
     * @returns {string} 格式化后的日期
     */
    formatDateOnly(dateString) {
        if (!dateString) return '';

        try {
            // 如果已经是YYYY-MM-DD格式，直接返回
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                return dateString;
            }

            // 如果包含时间部分，只取日期部分
            if (dateString.includes('T')) {
                return dateString.split('T')[0];
            }

            // 其他格式转换
            const date = new Date(dateString);
            return date.toISOString().split('T')[0];
        } catch (error) {
            return dateString;
        }
    }

    /**
     * 转换为数据库格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            equipment_id: this.equipmentId,
            type: this.type,
            severity_level: this.severityLevel,
            description: this.description,
            maintenance_date: this.maintenanceDate,
            start_time: this.startTime,
            end_time: this.endTime,
            cost: this.cost,
            technician: this.technician,
            status: this.status,
            notes: this.notes,
            result: this.result,
            reviewer: this.reviewer,
            created_at: this.createdAt,
            updated_at: this.updatedAt
        };
    }

    /**
     * 从数据库格式创建模型
     * @param {Object} dbData 数据库数据
     * @returns {MaintenanceModel} 模型实例
     */
    static fromDatabase(dbData) {
        const model = new MaintenanceModel({
            id: dbData.id,
            equipmentId: dbData.equipment_id,
            type: dbData.type,
            severityLevel: dbData.severity_level,
            description: dbData.description,
            maintenanceDate: dbData.maintenance_date,
            startTime: dbData.start_time,
            endTime: dbData.end_time,
            cost: dbData.cost,
            technician: dbData.technician,
            status: dbData.status,
            notes: dbData.notes,
            result: dbData.result,
            reviewer: dbData.reviewer,
            createdAt: dbData.created_at,
            updatedAt: dbData.updated_at
        });

        // 添加设备信息
        if (dbData.equipment) {
            model.equipment = dbData.equipment;
        }

        return model;
    }

    /**
     * 转换为API响应格式
     * @returns {Object} API响应格式的数据
     */
    toResponse() {
        return {
            id: this.id,
            equipmentId: this.equipmentId,
            equipment: this.equipment,
            type: this.type,
            typeLabel: this.getTypeLabel(),
            severityLevel: this.severityLevel,
            description: this.description,
            maintenanceDate: this.formatDateOnly(this.maintenanceDate),
            startTime: this.startTime,
            endTime: this.endTime,
            cost: this.cost,
            technician: this.technician,
            status: this.status,
            statusLabel: this.getStatusLabel(),
            notes: this.notes,
            result: this.result,
            reviewer: this.reviewer,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    /**
     * 获取类型标签
     * @returns {string} 类型标签
     */
    getTypeLabel() {
        const typeLabels = {
            'maintenance': '保养',
            'repair': '维修'
        };
        return typeLabels[this.type] || this.type;
    }

    /**
     * 获取状态标签
     * @returns {string} 状态标签
     */
    getStatusLabel() {
        const statusLabels = {
            'pending': '待处理',
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消'
        };
        return statusLabels[this.status] || this.status;
    }

    /**
     * 生成维修记录ID
     * @returns {string} 维修记录ID
     */
    static generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `MNT${timestamp}${random}`;
    }

    /**
     * 获取维修类型选项
     * @returns {Array} 类型选项
     */
    static getTypeOptions() {
        return [
            { value: 'maintenance', label: '保养' },
            { value: 'repair', label: '维修' }
        ];
    }

    /**
     * 获取状态选项
     * @returns {Array} 状态选项
     */
    static getStatusOptions() {
        return [
            { value: 'pending', label: '待处理' },
            { value: 'in_progress', label: '进行中' },
            { value: 'completed', label: '已完成' },
            { value: 'cancelled', label: '已取消' }
        ];
    }
}

module.exports = {
    MaintenanceModel
};
