# 统一错误处理系统

## 概述

为工厂管理系统提供统一的错误处理和用户反馈机制，确保用户在遇到错误时能够得到清晰、一致的反馈。

## 主要特性

- 🎯 **统一的错误分类**：自动识别网络、认证、权限、验证等错误类型
- 🚨 **智能错误严重程度判断**：根据错误类型自动判断严重程度
- 💬 **用户友好的错误提示**：将技术错误转换为用户易懂的消息
- 🔄 **自动认证处理**：认证失败时自动重定向到登录页
- 📝 **详细的错误日志**：记录完整的错误信息用于调试
- ⚙️ **灵活的配置选项**：支持自定义错误处理行为

## 快速开始

### 1. 导入错误处理器

```javascript
import ErrorHandler from '/scripts/utils/errorHandler.js';
```

### 2. 在API调用中使用

```javascript
// 基本用法
try {
    const response = await axios.get('/api/data');
    return response.data;
} catch (error) {
    ErrorHandler.handleAPIError(error, '获取数据');
    throw error; // 可选：重新抛出错误让调用者处理
}
```

### 3. 自定义错误处理选项

```javascript
try {
    const response = await axios.post('/api/data', data);
    return response.data;
} catch (error) {
    ErrorHandler.handleAPIError(error, '保存数据', {
        showToast: true,           // 是否显示Toast提示
        logError: true,            // 是否记录错误日志
        redirectOnAuth: true,      // 认证失败时是否重定向
        customMessage: '自定义错误消息' // 自定义错误消息
    });
    throw error;
}
```

## API 参考

### ErrorHandler.handleAPIError(error, context, options)

处理API调用错误的主要方法。

**参数：**
- `error` (Error): 错误对象
- `context` (string): 错误上下文，描述正在执行的操作
- `options` (Object): 可选配置
  - `showToast` (boolean): 是否显示Toast提示，默认 `true`
  - `logError` (boolean): 是否记录错误日志，默认 `true`
  - `redirectOnAuth` (boolean): 认证失败时是否重定向，默认 `true`
  - `customMessage` (string): 自定义错误消息

**返回值：**
- 返回错误信息对象，包含类型、严重程度等信息

### 其他实用方法

```javascript
// 显示成功提示
ErrorHandler.showSuccess('操作成功完成！');

// 显示警告提示
ErrorHandler.showWarning('请注意检查数据');

// 显示信息提示
ErrorHandler.showInfo('系统将在5分钟后维护');

// 创建API调用包装器
const wrappedAPI = ErrorHandler.wrapAPICall(originalAPIFunction, '操作描述');
```

## 错误类型

系统自动识别以下错误类型：

| 错误类型 | 描述 | 用户提示示例 |
|---------|------|-------------|
| `NETWORK` | 网络连接错误 | "网络连接失败，请检查网络设置后重试" |
| `AUTH` | 认证错误 (401) | "登录已过期，请重新登录" |
| `PERMISSION` | 权限错误 (403) | "您没有执行此操作的权限" |
| `VALIDATION` | 验证错误 (400, 422) | "数据验证失败，请检查输入信息" |
| `SERVER` | 服务器错误 (500, 502, 503) | "服务器错误，请稍后重试" |
| `TIMEOUT` | 超时错误 | "请求超时，请稍后重试" |
| `UNKNOWN` | 未知错误 | "操作失败，请重试" |

## 错误严重程度

| 严重程度 | 描述 | 提示持续时间 |
|---------|------|-------------|
| `LOW` | 低级错误，不影响主要功能 | 3秒 |
| `MEDIUM` | 中级错误，影响部分功能 | 4秒 |
| `HIGH` | 高级错误，影响主要功能 | 6秒 |
| `CRITICAL` | 严重错误，系统无法正常使用 | 6秒 |

## 使用示例

### 在组件中使用

```javascript
// Vue组件中的使用示例
export default {
    methods: {
        async loadData() {
            try {
                this.loading = true;
                const data = await getApplications();
                this.applications = data;
                ErrorHandler.showSuccess('数据加载成功');
            } catch (error) {
                ErrorHandler.handleAPIError(error, '加载申请列表');
            } finally {
                this.loading = false;
            }
        },
        
        async saveData() {
            try {
                await createApplication(this.formData);
                ErrorHandler.showSuccess('申请提交成功');
                this.$emit('saved');
            } catch (error) {
                ErrorHandler.handleAPIError(error, '提交申请');
            }
        }
    }
}
```

### 在API模块中使用

```javascript
// API模块中的使用示例
export async function getApplications() {
    try {
        const response = await axios.get(`${API_URL}/applications`);
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取申请列表');
        throw error;
    }
}
```

## 测试

访问 `/test-error-handler.html` 页面可以测试各种错误类型的处理效果。

## 注意事项

1. **重新抛出错误**：在API模块中处理错误后，通常需要重新抛出错误，让调用者决定如何处理
2. **认证错误处理**：认证错误会自动清除用户信息并重定向到登录页
3. **错误日志**：所有错误都会记录到浏览器控制台，便于调试
4. **Toast依赖**：需要确保全局的 `showNotification` 函数可用

## 迁移指南

### 从旧的错误处理迁移

**旧代码：**
```javascript
try {
    const response = await axios.get('/api/data');
    return response.data;
} catch (error) {
    console.error('获取数据失败:', error);
    if (error.response?.status === 401) {
        sessionStorage.removeItem('authToken');
        window.location.href = '/login';
    } else {
        alert('获取数据失败: ' + error.message);
    }
    throw error;
}
```

**新代码：**
```javascript
try {
    const response = await axios.get('/api/data');
    return response.data;
} catch (error) {
    ErrorHandler.handleAPIError(error, '获取数据');
    throw error;
}
```


