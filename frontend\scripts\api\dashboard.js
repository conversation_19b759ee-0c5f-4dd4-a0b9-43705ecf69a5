/**
 * 主页API模块
 * 封装主页相关的API请求
 */

import { API_URL, getAuthHeaders } from './config.js';

/**
 * 主页API类
 */
class DashboardAPI {
    /**
     * 获取主页统计数据
     * @returns {Promise<Object>} API响应
     */
    static async getStats() {
        try {
            const response = await fetch(`${API_URL}/dashboard/stats`, {
                method: 'GET',
                headers: getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取主页统计数据失败:', error);
            throw error;
        }
    }

    /**
     * 获取最近活动
     * @param {number} limit - 限制数量
     * @returns {Promise<Object>} API响应
     */
    static async getActivities(limit = 10) {
        try {
            const response = await fetch(`${API_URL}/dashboard/activities?limit=${limit}`, {
                method: 'GET',
                headers: getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取最近活动失败:', error);
            throw error;
        }
    }

    /**
     * 获取通知信息
     * @param {number} limit - 限制数量
     * @returns {Promise<Object>} API响应
     */
    static async getNotifications(limit = 5) {
        try {
            const response = await fetch(`${API_URL}/dashboard/notifications?limit=${limit}`, {
                method: 'GET',
                headers: getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取通知信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取待处理任务
     * @returns {Promise<Object>} API响应
     */
    static async getPendingTasks() {
        try {
            const response = await fetch(`${API_URL}/dashboard/pending-tasks`, {
                method: 'GET',
                headers: getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取待处理任务失败:', error);
            throw error;
        }
    }

    /**
     * 获取快速操作菜单
     * @returns {Promise<Object>} API响应
     */
    static async getQuickActions() {
        try {
            const response = await fetch(`${API_URL}/dashboard/quick-actions`, {
                method: 'GET',
                headers: getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取快速操作菜单失败:', error);
            throw error;
        }
    }

    /**
     * 获取系统健康状态
     * @returns {Promise<Object>} API响应
     */
    static async getSystemHealth() {
        try {
            const response = await fetch(`${API_URL}/dashboard/health`, {
                method: 'GET',
                headers: getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取系统健康状态失败:', error);
            throw error;
        }
    }
}

export default DashboardAPI;
