/**
 * 设备产能API调用模块
 * 封装所有设备产能相关的API请求
 */

import { API_URL } from '../config.js';

/**
 * 设备产能API类
 */
class CapacityAPI {
    /**
     * 获取设备的产能配置
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async getEquipmentCapabilities(equipmentId) {
        try {
            const response = await fetch(`${API_URL}/equipment/${equipmentId}/capabilities`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取设备产能配置失败:', error);
            throw error;
        }
    }

    /**
     * 创建设备产能记录
     * @param {Object} capabilityData 产能数据
     * @returns {Promise<Object>} API响应
     */
    static async createEquipmentCapability(capabilityData) {
        try {
            const response = await fetch(`${API_URL}/equipment/capabilities`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(capabilityData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建设备产能记录失败:', error);
            throw error;
        }
    }

    /**
     * 更新设备产能
     * @param {string} id 产能记录ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} API响应
     */
    static async updateEquipmentCapability(id, updateData) {
        try {
            const response = await fetch(`${API_URL}/equipment/capabilities/${id}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('更新设备产能失败:', error);
            throw error;
        }
    }

    /**
     * 获取产品的可用设备
     * @param {string} productId 产品ID
     * @returns {Promise<Object>} API响应
     */
    static async getAvailableEquipmentForProduct(productId) {
        try {
            const response = await fetch(`${API_URL}/products/${productId}/equipment`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取产品可用设备失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备的操作员技能
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async getEquipmentOperatorSkills(equipmentId) {
        try {
            const response = await fetch(`${API_URL}/equipment/${equipmentId}/operator-skills`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取设备操作员技能失败:', error);
            throw error;
        }
    }

    /**
     * 创建操作员技能记录
     * @param {Object} skillData 技能数据
     * @returns {Promise<Object>} API响应
     */
    static async createOperatorSkill(skillData) {
        try {
            const response = await fetch(`${API_URL}/operator-skills`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(skillData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建操作员技能记录失败:', error);
            throw error;
        }
    }

    /**
     * 获取操作员的技能列表
     * @param {string} operatorId 操作员ID
     * @returns {Promise<Object>} API响应
     */
    static async getOperatorSkills(operatorId) {
        try {
            const response = await fetch(`${API_URL}/operators/${operatorId}/skills`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取操作员技能列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备的操作员列表
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Object>} API响应
     */
    static async getEquipmentOperators(equipmentId) {
        try {
            const response = await fetch(`${API_URL}/equipment/${equipmentId}/operators`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取设备操作员列表失败:', error);
            throw error;
        }
    }

    /**
     * 创建设备操作员关联
     * @param {Object} relationData 关联数据
     * @returns {Promise<Object>} API响应
     */
    static async createEquipmentOperator(relationData) {
        try {
            const response = await fetch(`${API_URL}/equipment-operators`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(relationData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建设备操作员关联失败:', error);
            throw error;
        }
    }

    /**
     * 更新操作员技能记录
     * @param {string} id 技能记录ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} API响应
     */
    static async updateOperatorSkill(id, updateData) {
        try {
            const response = await fetch(`${API_URL}/operator-skills/${id}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('更新操作员技能记录失败:', error);
            throw error;
        }
    }

    /**
     * 删除操作员技能记录
     * @param {string} id 技能记录ID
     * @returns {Promise<Object>} API响应
     */
    static async deleteOperatorSkill(id) {
        try {
            const response = await fetch(`${API_URL}/operator-skills/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('删除操作员技能记录失败:', error);
            throw error;
        }
    }
}

export default CapacityAPI;
