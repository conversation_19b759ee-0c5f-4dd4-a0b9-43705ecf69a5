/**
 * 生产排程业务逻辑服务
 * 处理排程相关的业务逻辑
 */

const ScheduleRepository = require('../database/scheduleRepository');
const { ScheduleModel, SCHEDULE_STATUS } = require('../models/scheduleModel');
const logger = require('../utils/logger');

class ScheduleService {
    constructor() {
        this.scheduleRepository = new ScheduleRepository();
    }

    /**
     * 创建排程
     * @param {Object} scheduleData 排程数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createSchedule(scheduleData, userId) {
        try {
            // 生成排程ID
            const scheduleId = ScheduleModel.generateId();
            
            // 创建排程模型
            const schedule = new ScheduleModel({
                ...scheduleData,
                id: scheduleId,
                createdBy: userId,
                status: SCHEDULE_STATUS.PLANNED
            });

            // 验证数据
            const validation = schedule.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 检查资源冲突
            const conflictCheck = await this.checkResourceConflict(schedule);
            if (!conflictCheck.success) {
                return conflictCheck;
            }

            // 保存到数据库
            const createdSchedule = await this.scheduleRepository.create(schedule);

            logger.info('排程创建成功', { 
                scheduleId: createdSchedule.id, 
                title: createdSchedule.title,
                createdBy: userId 
            });

            return {
                success: true,
                message: '排程创建成功',
                data: createdSchedule
            };
        } catch (error) {
            logger.error('排程创建失败', { error: error.message, userId });
            return {
                success: false,
                message: '排程创建失败',
                error: error.message
            };
        }
    }

    /**
     * 获取排程列表
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 排程列表
     */
    async getSchedules(options = {}) {
        try {
            const result = await this.scheduleRepository.findAll(options);
            
            return {
                success: true,
                data: result
            };
        } catch (error) {
            logger.error('获取排程列表失败', { error: error.message });
            return {
                success: false,
                message: '获取排程列表失败',
                error: error.message
            };
        }
    }

    /**
     * 获取排程详情
     * @param {string} scheduleId 排程ID
     * @returns {Promise<Object>} 排程详情
     */
    async getScheduleById(scheduleId) {
        try {
            const schedule = await this.scheduleRepository.findById(scheduleId);
            
            if (!schedule) {
                return {
                    success: false,
                    message: '排程不存在'
                };
            }

            return {
                success: true,
                data: schedule
            };
        } catch (error) {
            logger.error('获取排程详情失败', { error: error.message, scheduleId });
            return {
                success: false,
                message: '获取排程详情失败',
                error: error.message
            };
        }
    }

    /**
     * 更新排程
     * @param {string} scheduleId 排程ID
     * @param {Object} updateData 更新数据
     * @param {string} userId 操作用户ID
     * @returns {Promise<Object>} 更新结果
     */
    async updateSchedule(scheduleId, updateData, userId) {
        try {
            // 检查排程是否存在
            const existingSchedule = await this.scheduleRepository.findById(scheduleId);
            if (!existingSchedule) {
                return {
                    success: false,
                    message: '排程不存在'
                };
            }

            // 检查是否可以编辑
            if (existingSchedule.status === SCHEDULE_STATUS.COMPLETED) {
                return {
                    success: false,
                    message: '已完成的排程不能编辑'
                };
            }

            // 创建临时模型进行验证
            const tempSchedule = new ScheduleModel({
                ...existingSchedule,
                ...updateData
            });

            const validation = tempSchedule.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 如果更新了资源分配，检查冲突
            if (updateData.assignedEquipment || updateData.assignedPersonnel || 
                updateData.startTime || updateData.endTime) {
                const conflictCheck = await this.checkResourceConflict(tempSchedule, scheduleId);
                if (!conflictCheck.success) {
                    return conflictCheck;
                }
            }

            // 执行更新
            const updatedSchedule = await this.scheduleRepository.update(scheduleId, updateData);

            logger.info('排程更新成功', { 
                scheduleId, 
                updatedBy: userId 
            });

            return {
                success: true,
                message: '排程更新成功',
                data: updatedSchedule
            };
        } catch (error) {
            logger.error('排程更新失败', { error: error.message, scheduleId, userId });
            return {
                success: false,
                message: '排程更新失败',
                error: error.message
            };
        }
    }

    /**
     * 删除排程
     * @param {string} scheduleId 排程ID
     * @param {string} userId 操作用户ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteSchedule(scheduleId, userId) {
        try {
            // 检查排程是否存在
            const existingSchedule = await this.scheduleRepository.findById(scheduleId);
            if (!existingSchedule) {
                return {
                    success: false,
                    message: '排程不存在'
                };
            }

            // 检查是否可以删除
            if (existingSchedule.status === SCHEDULE_STATUS.IN_PROGRESS) {
                return {
                    success: false,
                    message: '正在执行的排程不能删除'
                };
            }

            // 执行删除
            const deleted = await this.scheduleRepository.delete(scheduleId);
            
            if (deleted) {
                logger.info('排程删除成功', { scheduleId, deletedBy: userId });
                return {
                    success: true,
                    message: '排程删除成功'
                };
            } else {
                return {
                    success: false,
                    message: '排程删除失败'
                };
            }
        } catch (error) {
            logger.error('排程删除失败', { error: error.message, scheduleId, userId });
            return {
                success: false,
                message: '排程删除失败',
                error: error.message
            };
        }
    }

    /**
     * 更新排程状态
     * @param {string} scheduleId 排程ID
     * @param {string} action 操作类型 (start|pause|complete|cancel)
     * @param {string} userId 操作用户ID
     * @returns {Promise<Object>} 操作结果
     */
    async updateScheduleStatus(scheduleId, action, userId) {
        try {
            const existingSchedule = await this.scheduleRepository.findById(scheduleId);
            if (!existingSchedule) {
                return {
                    success: false,
                    message: '排程不存在'
                };
            }

            let newStatus;
            const currentStatus = existingSchedule.status;

            // 根据操作类型确定新状态
            switch (action) {
                case 'start':
                    if (currentStatus !== SCHEDULE_STATUS.PLANNED && currentStatus !== SCHEDULE_STATUS.PAUSED) {
                        return {
                            success: false,
                            message: '只有计划中或暂停的排程可以开始执行'
                        };
                    }
                    newStatus = SCHEDULE_STATUS.IN_PROGRESS;
                    break;
                
                case 'pause':
                    if (currentStatus !== SCHEDULE_STATUS.IN_PROGRESS) {
                        return {
                            success: false,
                            message: '只有执行中的排程可以暂停'
                        };
                    }
                    newStatus = SCHEDULE_STATUS.PAUSED;
                    break;
                
                case 'complete':
                    if (currentStatus !== SCHEDULE_STATUS.IN_PROGRESS) {
                        return {
                            success: false,
                            message: '只有执行中的排程可以完成'
                        };
                    }
                    newStatus = SCHEDULE_STATUS.COMPLETED;
                    break;
                
                case 'cancel':
                    if (currentStatus === SCHEDULE_STATUS.COMPLETED) {
                        return {
                            success: false,
                            message: '已完成的排程不能取消'
                        };
                    }
                    newStatus = SCHEDULE_STATUS.CANCELLED;
                    break;
                
                default:
                    return {
                        success: false,
                        message: '无效的操作类型'
                    };
            }

            // 更新状态
            const updated = await this.scheduleRepository.updateStatus(scheduleId, newStatus);
            
            if (updated) {
                logger.info('排程状态更新成功', { 
                    scheduleId, 
                    action, 
                    newStatus, 
                    updatedBy: userId 
                });
                
                return {
                    success: true,
                    message: `排程${this.getActionDescription(action)}成功`,
                    data: { status: newStatus }
                };
            } else {
                return {
                    success: false,
                    message: '状态更新失败'
                };
            }
        } catch (error) {
            logger.error('排程状态更新失败', { error: error.message, scheduleId, action, userId });
            return {
                success: false,
                message: '状态更新失败',
                error: error.message
            };
        }
    }

    /**
     * 检查资源冲突
     * @param {ScheduleModel} schedule 排程模型
     * @param {string} excludeScheduleId 排除的排程ID（用于更新时）
     * @returns {Promise<Object>} 检查结果
     */
    async checkResourceConflict(schedule, excludeScheduleId = null) {
        try {
            // 这里应该实现资源冲突检测逻辑
            // 检查设备、人员在指定时间段是否已被占用
            // 为了简化，这里先返回成功
            
            return {
                success: true
            };
        } catch (error) {
            logger.error('资源冲突检查失败', { error: error.message });
            return {
                success: false,
                message: '资源冲突检查失败'
            };
        }
    }

    /**
     * 获取操作描述
     * @param {string} action 操作类型
     * @returns {string} 操作描述
     */
    getActionDescription(action) {
        const descriptions = {
            start: '开始',
            pause: '暂停',
            complete: '完成',
            cancel: '取消'
        };
        return descriptions[action] || action;
    }
}

module.exports = ScheduleService;
