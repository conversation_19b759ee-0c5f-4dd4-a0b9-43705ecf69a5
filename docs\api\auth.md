# 认证 API 文档

## 📋 概述

认证模块提供用户登录、登出、令牌验证等功能，采用JWT令牌进行身份验证。

## 🔐 API 接口

### 1. 用户登录

**POST** `/api/auth/login`

用户登录获取JWT令牌。

#### 请求参数

```json
{
  "username": "admin",
  "password": "admin123"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6InVzZXIxMjMiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaWF0IjoxNjQwOTk1MjAwLCJleHAiOjE2NDA5OTg4MDB9.signature",
  "user": {
    "id": "user123",
    "username": "admin",
    "usercode": "ADM001",
    "role": "admin",
    "department": "管理部",
    "email": "<EMAIL>",
    "permissions": [
      "new_application",
      "application_record",
      "pending_approval",
      "approved_applications",
      "view_users",
      "create_user",
      "edit_user",
      "delete_user",
      "manage_permissions"
    ],
    "active": true,
    "hasSignature": true,
    "lastLogin": "2025-07-29T10:30:00.000Z"
  }
}
```

**错误响应 (401)**:
```json
{
  "success": false,
  "message": "用户名或密码错误"
}
```

**错误响应 (403)**:
```json
{
  "success": false,
  "message": "账户已被禁用，请联系管理员"
}
```

### 2. 获取当前用户信息

**GET** `/api/auth/me`

获取当前登录用户的详细信息。

#### 请求头
```http
Authorization: Bearer <jwt-token>
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "user": {
    "id": "user123",
    "username": "admin",
    "usercode": "ADM001",
    "role": "admin",
    "department": "管理部",
    "email": "<EMAIL>",
    "permissions": ["new_application", "view_users", ...],
    "active": true,
    "hasSignature": true,
    "createdAt": "2025-01-01T00:00:00.000Z",
    "lastLogin": "2025-07-29T10:30:00.000Z"
  }
}
```

**错误响应 (401)**:
```json
{
  "success": false,
  "message": "未提供认证令牌"
}
```

**错误响应 (403)**:
```json
{
  "success": false,
  "message": "令牌无效或已过期"
}
```

### 3. 用户登出

**POST** `/api/auth/logout`

用户登出（前端处理，清除本地存储的令牌）。

#### 请求头
```http
Authorization: Bearer <jwt-token>
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

## 🔧 JWT 令牌说明

### 令牌结构
JWT令牌包含以下信息：
- **用户ID**: 唯一标识符
- **用户名**: 登录用户名
- **用户代码**: 用户编码
- **角色**: 用户角色（admin、user等）
- **权限列表**: 用户拥有的权限数组
- **状态**: 用户激活状态
- **过期时间**: 令牌过期时间

### 令牌有效期
- **默认有效期**: 24小时
- **自动续期**: 不支持，需重新登录
- **存储方式**: sessionStorage（浏览器关闭时自动清除）

### 权限验证
系统采用基于角色和权限的混合授权模式：
- **admin角色**: 自动拥有所有权限
- **其他角色**: 根据权限数组进行验证
- **权限检查**: 前端+后端双重验证

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| INVALID_CREDENTIALS | 401 | 用户名或密码错误 | 检查登录凭据 |
| ACCOUNT_DISABLED | 403 | 账户已被禁用 | 联系管理员 |
| TOKEN_EXPIRED | 403 | 令牌已过期 | 重新登录 |
| TOKEN_INVALID | 403 | 令牌格式无效 | 重新登录 |
| NO_TOKEN | 401 | 未提供令牌 | 添加Authorization头 |

### 安全建议

1. **令牌保护**: 不要在URL中传递令牌
2. **HTTPS**: 生产环境使用HTTPS传输
3. **定期更新**: 定期更换密码
4. **权限最小化**: 只分配必要的权限
5. **监控异常**: 监控异常登录行为

---

**更新时间**: 2025-07-29  
**版本**: v1.0
