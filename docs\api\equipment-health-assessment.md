# 设备健康评估 API 文档

## 📋 概述

设备健康评估系统基于AI算法和四维度评估体系，提供设备健康度计算、故障预测、维护建议和生命周期管理功能。

## 🌐 基础信息

- **Base URL**: `http://localhost:5050/api`
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

## 🔐 认证

所有API请求都需要在请求头中包含有效的JWT令牌：

```http
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 查看设备健康状态 | `equipment_health` |
| 管理设备维护 | `equipment_maintenance` |
| 设备信息管理 | `equipment_manage` |
| 健康评估配置 | `equipment_config` |

## 📊 评估体系

### 四维度权重分配
- **设备年龄**: 20% - 基于购买日期和预期寿命
- **维修频率**: 30% - 基于历史维修记录和频率
- **故障严重程度**: 30% - 基于故障类型和影响程度
- **保养情况**: 20% - 基于保养计划执行情况

### 健康度等级
- **90-100分**: 优秀 🟢 - 设备状态极佳，正常运行
- **80-89分**: 良好 🟡 - 设备状态良好，建议定期检查
- **70-79分**: 一般 🟠 - 设备需要关注，增加检查频率
- **60-69分**: 较差 🔴 - 设备状态不佳，需要维护
- **0-59分**: 危险 ⚫ - 设备存在严重问题，立即停机检修

## 🔧 API 接口

### 1. 计算设备健康度

**POST** `/api/equipment/:id/health/calculate`

基于四维度评估体系计算指定设备的健康度评分。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 设备ID |

#### 请求参数

```json
{
  "assessmentDate": "2025-07-29",
  "includeRecommendations": true,
  "includePrediction": true,
  "includeHistory": false
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| assessmentDate | string | 否 | 评估日期，默认当前日期 |
| includeRecommendations | boolean | 否 | 是否包含维护建议 |
| includePrediction | boolean | 否 | 是否包含故障预测 |
| includeHistory | boolean | 否 | 是否包含历史趋势 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "equipmentId": "EQ001",
    "equipmentCode": "EQ-001",
    "equipmentName": "数控机床A",
    "totalScore": 85.5,
    "level": "良好",
    "healthGrade": "B+",
    "assessmentDate": "2025-07-29T12:00:00.000Z",
    "riskLevel": "低",
    "dimensions": {
      "age": {
        "score": 80,
        "weight": 0.2,
        "contribution": 16,
        "status": "正常",
        "details": {
          "ageInYears": 5.5,
          "category": "5-8年",
          "purchaseDate": "2020-01-15",
          "expectedLifespan": 15,
          "remainingLife": 9.5,
          "depreciationRate": 0.37
        }
      },
      "repairFrequency": {
        "score": 90,
        "weight": 0.3,
        "contribution": 27,
        "status": "优秀",
        "details": {
          "annualRepairRate": 1.2,
          "totalRepairs": 6,
          "category": "1-2次/年",
          "lastRepairDate": "2025-05-15",
          "averageRepairCost": 1500,
          "repairTrend": "decreasing"
        }
      },
      "faultSeverity": {
        "score": 85,
        "weight": 0.3,
        "contribution": 25.5,
        "status": "良好",
        "details": {
          "weightedDeduction": 15,
          "criticalCount": 0,
          "majorCount": 1,
          "minorCount": 5,
          "lastFaultDate": "2025-06-10",
          "averageDowntime": 2.5,
          "faultTrend": "stable"
        }
      },
      "maintenance": {
        "score": 85,
        "weight": 0.2,
        "contribution": 17,
        "status": "良好",
        "details": {
          "maintenanceRatio": 0.87,
          "daysSinceLastMaintenance": 14,
          "scheduledMaintenances": 15,
          "completedMaintenances": 13,
          "overdueMaintenances": 1,
          "averageDelay": 2.5,
          "complianceRate": 0.87
        }
      }
    },
    "trends": {
      "healthScoreHistory": [
        {
          "date": "2025-06-29",
          "score": 82.3
        },
        {
          "date": "2025-05-29",
          "score": 84.1
        },
        {
          "date": "2025-04-29",
          "score": 81.8
        }
      ],
      "trend": "improving",
      "changeRate": 0.038,
      "volatility": "low"
    },
    "recommendations": [
      {
        "id": "rec001",
        "priority": "high",
        "category": "preventive_maintenance",
        "type": "maintenance_plan",
        "title": "提前进行预防性维护",
        "description": "建议提前2周进行下次预防性维护，以保持设备良好状态",
        "estimatedCost": 2500,
        "expectedImprovement": 5,
        "timeframe": "2周内",
        "urgency": "medium"
      },
      {
        "id": "rec002",
        "priority": "medium",
        "category": "monitoring",
        "type": "condition_monitoring",
        "title": "增加振动监测",
        "description": "建议增加设备振动监测频率，及时发现潜在问题",
        "estimatedCost": 500,
        "expectedImprovement": 2,
        "timeframe": "1个月内",
        "urgency": "low"
      }
    ],
    "predictions": {
      "nextFailureProbability": 0.15,
      "predictedFailureDate": "2025-12-15",
      "confidence": 0.78,
      "failureType": "mechanical_wear",
      "maintenanceWindow": {
        "optimal": "2025-08-15",
        "latest": "2025-09-15",
        "emergency": "2025-10-15"
      },
      "costPrediction": {
        "preventiveCost": 2500,
        "correctiveCost": 15000,
        "savingsPotential": 12500
      }
    },
    "alerts": [
      {
        "id": "alert001",
        "level": "warning",
        "type": "maintenance_due",
        "message": "设备运行时间接近维护周期",
        "actionRequired": "安排维护计划",
        "deadline": "2025-08-15",
        "priority": "medium"
      }
    ],
    "metadata": {
      "calculatedAt": "2025-07-29T12:00:00.000Z",
      "algorithm": "HealthAssessmentV2",
      "version": "2.0.1",
      "dataQuality": "excellent",
      "dataPoints": 156,
      "processingTime": 850
    }
  }
}
```
  },
  "message": "健康度计算成功"
}
```

### 2. 获取设备健康度历史

**GET** `/api/equipment/:id/health/history`

获取设备健康度历史记录。

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | number | 否 | 返回记录数量，默认30 |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "equipmentId": "EQ001",
    "history": [
      {
        "assessmentDate": "2024-07-22",
        "totalScore": 78,
        "level": "一般",
        "ageScore": 80,
        "repairFrequencyScore": 75,
        "faultSeverityScore": 70,
        "maintenanceScore": 85
      }
    ],
    "trends": {
      "totalScore": {
        "current": 78,
        "previous": 82,
        "change": -4,
        "trend": "decreasing"
      }
    }
  },
  "message": "获取历史记录成功"
}
```

### 3. 故障预测

**GET** `/api/equipment/:id/health/prediction`

基于历史维修记录预测下次故障时间。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "equipmentId": "EQ001",
    "canPredict": true,
    "prediction": {
      "predictedDate": "2024-09-15T00:00:00.000Z",
      "confidence": 70,
      "avgInterval": 120,
      "dataQuality": "good"
    },
    "analysis": {
      "totalRepairs": 5,
      "intervals": [110, 125, 115, 130],
      "avgInterval": 120,
      "standardDeviation": 8.2,
      "coefficientOfVariation": 0.068
    }
  },
  "message": "故障预测成功"
}
```

### 4. 获取设备健康度统计

**GET** `/api/equipment-health/statistics`

获取设备健康度的统计分析数据。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| period | string | 否 | 统计周期（week、month、quarter、year） |
| factory | string | 否 | 厂区筛选 |
| equipmentType | string | 否 | 设备类型筛选 |
| healthLevel | string | 否 | 健康等级筛选 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalEquipment": 150,
      "averageHealthScore": 82.5,
      "healthDistribution": {
        "优秀": 45,
        "良好": 60,
        "一般": 30,
        "较差": 12,
        "危险": 3
      }
    },
    "trends": {
      "monthlyAverage": [
        {
          "month": "2025-06",
          "averageScore": 81.2,
          "equipmentCount": 148
        },
        {
          "month": "2025-07",
          "averageScore": 82.5,
          "equipmentCount": 150
        }
      ],
      "improvement": 1.3,
      "trend": "improving"
    },
    "riskAnalysis": {
      "highRiskEquipment": 15,
      "predictedFailures": 8,
      "maintenanceAlerts": 23,
      "criticalAlerts": 3
    },
    "recommendations": {
      "immediateAction": 3,
      "scheduledMaintenance": 15,
      "monitoring": 25,
      "replacement": 2
    }
  }
}
```

### 5. 批量健康度评估

**POST** `/api/equipment-health/batch-calculate`

批量计算多个设备的健康度评估。

#### 请求参数

```json
{
  "equipmentIds": ["EQ001", "EQ002", "EQ003"],
  "assessmentDate": "2025-07-29",
  "includeRecommendations": true,
  "includePrediction": false
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "totalProcessed": 3,
    "successful": 3,
    "failed": 0,
    "results": [
      {
        "equipmentId": "EQ001",
        "healthScore": 85.5,
        "level": "良好",
        "status": "success"
      },
      {
        "equipmentId": "EQ002",
        "healthScore": 78.2,
        "level": "一般",
        "status": "success"
      },
      {
        "equipmentId": "EQ003",
        "healthScore": 92.1,
        "level": "优秀",
        "status": "success"
      }
    ],
    "summary": {
      "averageScore": 85.3,
      "highestScore": 92.1,
      "lowestScore": 78.2,
      "riskEquipment": 0
    }
  }
}
```

获取所有设备的健康度统计信息。

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| area | string | 否 | 厂区筛选 |
| level | string | 否 | 健康度等级筛选 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "overview": {
      "totalEquipment": 25,
      "averageHealth": 82.5,
      "lastUpdated": "2024-07-22T10:30:00.000Z"
    },
    "distribution": {
      "excellent": 8,
      "good": 10,
      "average": 5,
      "poor": 2,
      "dangerous": 0
    },
    "trends": {
      "thisMonth": 82.5,
      "lastMonth": 85.2,
      "change": -2.7,
      "trend": "decreasing"
    },
    "warningEquipment": [
      {
        "equipmentId": "EQ003",
        "equipmentName": "生产线A-设备3",
        "totalScore": 65,
        "level": "较差",
        "urgentRecommendations": 2
      }
    ]
  },
  "message": "获取统计信息成功"
}
```

### 5. 批量计算健康度

**POST** `/api/equipment/health/batch-calculate`

批量计算多个设备的健康度。

#### 请求体

```json
{
  "equipmentIds": ["EQ001", "EQ002", "EQ003"],
  "options": {
    "includeRecommendations": true,
    "includePrediction": false
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "results": [
      {
        "equipmentId": "EQ001",
        "totalScore": 78,
        "level": "一般",
        "calculatedAt": "2024-07-22T10:30:00.000Z"
      }
    ],
    "summary": {
      "total": 3,
      "successful": 3,
      "failed": 0,
      "averageScore": 79.3
    }
  },
  "message": "批量计算完成"
}
```

## 🚨 错误处理

### 标准错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "HEALTH_ASSESSMENT_ERROR",
  "details": {
    "field": "具体错误信息",
    "code": "INSUFFICIENT_DATA"
  },
  "timestamp": "2025-07-29T12:00:00.000Z"
}
```

### 健康评估特定错误码

| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| EQUIPMENT_NOT_FOUND | 404 | 设备不存在 | 检查设备ID是否正确 |
| INSUFFICIENT_DATA | 400 | 数据不足，无法计算 | 确保设备有足够的历史数据 |
| CALCULATION_ERROR | 500 | 健康度计算失败 | 检查设备数据完整性 |
| INVALID_PARAMETERS | 400 | 参数无效 | 检查请求参数格式和值 |
| MAINTENANCE_DATA_MISSING | 400 | 缺少维护数据 | 补充设备维护记录 |
| PREDICTION_FAILED | 500 | 故障预测失败 | 简化预测参数重试 |
| BATCH_LIMIT_EXCEEDED | 400 | 批量处理超限 | 减少批量处理数量 |

### 常见HTTP状态码

| 状态码 | 描述 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理响应数据 |
| 400 | 请求参数错误 | 检查请求参数格式和值 |
| 401 | 未认证 | 检查JWT令牌是否有效 |
| 403 | 权限不足 | 确认用户具有设备健康评估权限 |
| 404 | 设备不存在 | 检查设备ID是否正确 |
| 500 | 服务器错误 | 联系技术支持 |

## 📊 评估算法说明

### 四维度评分算法

#### 1. 设备年龄评分 (20%)
```
年龄评分 = max(0, 100 - (当前年龄 / 预期寿命) * 100)
```

#### 2. 维修频率评分 (30%)
```
频率评分 = max(0, 100 - (年维修次数 - 标准次数) * 扣分系数)
```

#### 3. 故障严重程度评分 (30%)
```
严重程度评分 = 100 - Σ(故障次数 × 严重程度权重)
```

#### 4. 保养情况评分 (20%)
```
保养评分 = (按时保养次数 / 计划保养次数) × 100
```

### 综合健康度计算
```
健康度 = Σ(各维度评分 × 对应权重)
```

## 🔧 开发工具

### API测试示例 (curl)

```bash
# 计算设备健康度
curl -X POST http://localhost:5050/api/equipment/EQ001/health/calculate \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "assessmentDate": "2025-07-29",
    "includeRecommendations": true,
    "includePrediction": true
  }'

# 获取健康度统计
curl -X GET "http://localhost:5050/api/equipment-health/statistics?period=month" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 批量健康度评估
curl -X POST http://localhost:5050/api/equipment-health/batch-calculate \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "equipmentIds": ["EQ001", "EQ002", "EQ003"],
    "includeRecommendations": true
  }'
```

## 📈 最佳实践

### 数据质量要求
- **维护记录**: 至少6个月的维护历史数据
- **故障记录**: 完整的故障类型和严重程度记录
- **设备信息**: 准确的购买日期和预期寿命
- **定期更新**: 建议每月进行健康度评估

### 性能优化建议
- **批量处理**: 使用批量API减少请求次数
- **缓存策略**: 合理使用缓存减少重复计算
- **数据预处理**: 确保输入数据质量和完整性
- **增量更新**: 只更新有变化的设备数据

### 预测准确性提升
- **数据完整性**: 确保历史数据的完整性和准确性
- **定期校准**: 根据实际故障情况调整预测模型
- **多维度验证**: 结合多个维度进行综合判断
- **专家经验**: 结合设备专家的经验进行调整

---

**更新时间**: 2025-07-29
**版本**: v2.0.1
**技术支持**: [<EMAIL>](mailto:<EMAIL>)
