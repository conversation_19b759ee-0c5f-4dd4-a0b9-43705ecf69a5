/* 用户管理模块通用样式文件 - User Common CSS */

/* 此文件包含用户管理模块的通用样式 */
/* 基础样式继承自 /assets/css/common.css */

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

/* 切换开关样式 */
.toggle-checkbox {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    width: 3rem !important;
    height: 1.5rem !important;
    background-color: #E2E8F0 !important;
    border-radius: 9999px !important;
    position: relative !important;
    cursor: pointer !important;
    transition: background-color 0.2s !important;
    border: none !important;
    outline: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.toggle-checkbox::before {
    content: '' !important;
    position: absolute !important;
    top: 2px !important;
    left: 2px !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    background-color: white !important;
    border-radius: 50% !important;
    transition: transform 0.2s !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

.toggle-checkbox:checked {
    background-color: #3B82F6 !important;
}

.toggle-checkbox:checked::before {
    transform: translateX(1.5rem) !important;
}

.toggle-checkbox:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

.toggle-checkbox:hover {
    background-color: #CBD5E0 !important;
}

.toggle-checkbox:checked:hover {
    background-color: #2563EB !important;
}

.toggle-label {
    display: none !important;
}
