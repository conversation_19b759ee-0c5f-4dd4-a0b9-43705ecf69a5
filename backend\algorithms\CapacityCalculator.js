/**
 * 产能计算器
 * 实现精确的产能计算、时间估算、资源需求分析
 */

const logger = require('../utils/logger');

/**
 * 产能计算器类
 * 负责计算生产产能、时间需求和资源分配
 */
class CapacityCalculator {
    constructor() {
        // 计算配置参数
        this.config = {
            workingHoursPerDay: 8,      // 每日工作小时数
            workingDaysPerWeek: 5,      // 每周工作天数
            efficiencyBuffer: 0.85,     // 效率缓冲系数
            setupTimeBuffer: 1.2,       // 准备时间缓冲系数
            qualityLossRate: 0.02,      // 质量损失率
            maintenanceTimeRatio: 0.05  // 维护时间比例
        };

        logger.info('产能计算器初始化完成');
    }

    /**
     * 计算订单产能需求
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 产能需求分析结果
     */
    async calculateRequirements(orderData) {
        try {
            logger.info('开始计算产能需求', { 
                orderId: orderData.id,
                productId: orderData.productId,
                quantity: orderData.quantity
            });

            // 1. 基础产能计算
            const baseCapacity = await this.calculateBaseCapacity(orderData);
            
            // 2. 工序产能需求
            const processRequirements = await this.calculateProcessRequirements(orderData);
            
            // 3. 设备需求分析
            const equipmentRequirements = await this.calculateEquipmentRequirements(orderData);
            
            // 4. 人员需求分析
            const operatorRequirements = await this.calculateOperatorRequirements(orderData);
            
            // 5. 时间需求计算
            const timeRequirements = await this.calculateTimeRequirements(orderData);

            const result = {
                orderId: orderData.id,
                baseCapacity,
                processRequirements,
                equipmentRequirements,
                operatorRequirements,
                timeRequirements,
                calculatedAt: new Date().toISOString()
            };

            logger.info('产能需求计算完成', { 
                orderId: orderData.id,
                totalTime: timeRequirements.totalTime
            });

            return result;

        } catch (error) {
            logger.error('计算产能需求失败', { 
                error: error.message, 
                orderId: orderData.id 
            });
            throw error;
        }
    }

    /**
     * 计算基础产能
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 基础产能信息
     */
    async calculateBaseCapacity(orderData) {
        const { quantity, productInfo } = orderData;
        const standardTime = productInfo.standardTime; // 分钟

        // 考虑质量损失，需要额外生产
        const adjustedQuantity = Math.ceil(quantity * (1 + this.config.qualityLossRate));
        
        // 基础生产时间（分钟）
        const baseProductionTime = adjustedQuantity * standardTime;
        
        // 转换为小时
        const baseProductionHours = baseProductionTime / 60;

        return {
            requiredQuantity: quantity,
            adjustedQuantity,
            standardTimePerUnit: standardTime,
            baseProductionTime,
            baseProductionHours,
            qualityLossRate: this.config.qualityLossRate
        };
    }

    /**
     * 计算工序产能需求
     * @param {Object} orderData 订单数据
     * @returns {Promise<Array>} 工序需求列表
     */
    async calculateProcessRequirements(orderData) {
        const { quantity, processFlow } = orderData;
        const adjustedQuantity = Math.ceil(quantity * (1 + this.config.qualityLossRate));

        const processRequirements = [];

        for (const process of processFlow) {
            // 工序生产时间
            const processTime = adjustedQuantity * process.standardTime;
            
            // 准备时间（考虑缓冲）
            const setupTime = process.setupTime * this.config.setupTimeBuffer;
            
            // 总时间
            const totalTime = processTime + setupTime;
            
            // 考虑效率缓冲
            const adjustedTime = totalTime / this.config.efficiencyBuffer;

            processRequirements.push({
                processId: process.id,
                processName: process.name,
                standardTime: process.standardTime,
                setupTime: process.setupTime,
                adjustedSetupTime: setupTime,
                processTime,
                totalTime,
                adjustedTime,
                requiredQuantity: adjustedQuantity,
                requiredEquipmentType: process.requiredEquipmentType || 'general'
            });
        }

        return processRequirements;
    }

    /**
     * 计算设备需求
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 设备需求分析
     */
    async calculateEquipmentRequirements(orderData) {
        const processRequirements = await this.calculateProcessRequirements(orderData);
        
        // 按设备类型分组需求
        const equipmentTypeRequirements = {};
        
        for (const process of processRequirements) {
            const equipmentType = process.requiredEquipmentType;
            
            if (!equipmentTypeRequirements[equipmentType]) {
                equipmentTypeRequirements[equipmentType] = {
                    type: equipmentType,
                    totalTime: 0,
                    processes: [],
                    requiredCapacity: 0
                };
            }
            
            equipmentTypeRequirements[equipmentType].totalTime += process.adjustedTime;
            equipmentTypeRequirements[equipmentType].processes.push(process);
        }

        // 计算每种设备类型的需求数量
        for (const type in equipmentTypeRequirements) {
            const requirement = equipmentTypeRequirements[type];
            const totalHours = requirement.totalTime / 60;
            
            // 考虑维护时间
            const adjustedHours = totalHours * (1 + this.config.maintenanceTimeRatio);
            
            // 计算需要的设备数量（向上取整）
            const requiredEquipmentCount = Math.ceil(
                adjustedHours / this.config.workingHoursPerDay
            );
            
            requirement.totalHours = totalHours;
            requirement.adjustedHours = adjustedHours;
            requirement.requiredCount = requiredEquipmentCount;
        }

        return {
            byType: equipmentTypeRequirements,
            summary: {
                totalTypes: Object.keys(equipmentTypeRequirements).length,
                totalEquipmentNeeded: Object.values(equipmentTypeRequirements)
                    .reduce((sum, req) => sum + req.requiredCount, 0)
            }
        };
    }

    /**
     * 计算操作员需求
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 操作员需求分析
     */
    async calculateOperatorRequirements(orderData) {
        const equipmentRequirements = await this.calculateEquipmentRequirements(orderData);
        
        const operatorRequirements = {};
        
        // 基于设备需求计算操作员需求
        for (const type in equipmentRequirements.byType) {
            const equipmentReq = equipmentRequirements.byType[type];
            
            // 假设每台设备需要1个操作员
            const requiredOperators = equipmentReq.requiredCount;
            
            operatorRequirements[type] = {
                equipmentType: type,
                requiredCount: requiredOperators,
                totalWorkHours: equipmentReq.adjustedHours,
                skillLevelRequired: this.getRequiredSkillLevel(type),
                processes: equipmentReq.processes.map(p => p.processName)
            };
        }

        return {
            byEquipmentType: operatorRequirements,
            summary: {
                totalOperatorsNeeded: Object.values(operatorRequirements)
                    .reduce((sum, req) => sum + req.requiredCount, 0),
                averageSkillLevel: this.calculateAverageSkillLevel(operatorRequirements)
            }
        };
    }

    /**
     * 计算时间需求
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 时间需求分析
     */
    async calculateTimeRequirements(orderData) {
        const processRequirements = await this.calculateProcessRequirements(orderData);
        
        // 串行时间（所有工序依次执行）
        const serialTime = processRequirements.reduce((total, process) => {
            return total + process.adjustedTime;
        }, 0);

        // 并行时间（可并行执行的工序）
        const parallelTime = this.calculateParallelTime(processRequirements);
        
        // 关键路径时间
        const criticalPathTime = this.calculateCriticalPath(processRequirements);
        
        // 转换为不同时间单位
        const timeInHours = serialTime / 60;
        const timeInDays = timeInHours / this.config.workingHoursPerDay;

        return {
            serialTime,           // 分钟
            parallelTime,         // 分钟
            criticalPathTime,     // 分钟
            timeInHours,          // 小时
            timeInDays,           // 工作日
            estimatedStartDate: this.calculateStartDate(orderData.requiredDate, timeInDays),
            estimatedEndDate: orderData.requiredDate,
            bufferTime: serialTime * 0.1, // 10%缓冲时间
            totalTime: serialTime
        };
    }

    /**
     * 计算并行执行时间
     * @param {Array} processRequirements 工序需求
     * @returns {number} 并行时间（分钟）
     */
    calculateParallelTime(processRequirements) {
        // 简化实现：假设某些工序可以并行
        // 实际实现需要根据工艺流程的依赖关系来计算
        const maxProcessTime = Math.max(...processRequirements.map(p => p.adjustedTime));
        const setupTimeSum = processRequirements.reduce((sum, p) => sum + p.adjustedSetupTime, 0);
        
        return maxProcessTime + setupTimeSum;
    }

    /**
     * 计算关键路径时间
     * @param {Array} processRequirements 工序需求
     * @returns {number} 关键路径时间（分钟）
     */
    calculateCriticalPath(processRequirements) {
        // 简化实现：假设所有工序都在关键路径上
        // 实际实现需要使用CPM算法
        return processRequirements.reduce((total, process) => {
            return total + process.adjustedTime;
        }, 0);
    }

    /**
     * 计算开始日期
     * @param {string} requiredDate 要求完成日期
     * @param {number} timeInDays 所需天数
     * @returns {string} 计算的开始日期
     */
    calculateStartDate(requiredDate, timeInDays) {
        const endDate = new Date(requiredDate);
        const startDate = new Date(endDate);
        
        // 减去所需的工作日数
        let daysToSubtract = Math.ceil(timeInDays);
        let currentDate = new Date(startDate);
        
        while (daysToSubtract > 0) {
            currentDate.setDate(currentDate.getDate() - 1);
            
            // 跳过周末（假设周六日不工作）
            if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
                daysToSubtract--;
            }
        }
        
        return currentDate.toISOString().split('T')[0];
    }

    /**
     * 获取设备类型所需技能等级
     * @param {string} equipmentType 设备类型
     * @returns {number} 技能等级
     */
    getRequiredSkillLevel(equipmentType) {
        const skillLevelMap = {
            'general': 2,
            'precision': 3,
            'automated': 4,
            'specialized': 5
        };
        
        return skillLevelMap[equipmentType] || 2;
    }

    /**
     * 计算平均技能等级
     * @param {Object} operatorRequirements 操作员需求
     * @returns {number} 平均技能等级
     */
    calculateAverageSkillLevel(operatorRequirements) {
        const requirements = Object.values(operatorRequirements);
        if (requirements.length === 0) return 0;
        
        const totalSkillLevel = requirements.reduce((sum, req) => {
            return sum + (req.skillLevelRequired * req.requiredCount);
        }, 0);
        
        const totalOperators = requirements.reduce((sum, req) => sum + req.requiredCount, 0);
        
        return totalOperators > 0 ? totalSkillLevel / totalOperators : 0;
    }

    /**
     * 计算实际产能
     * @param {Object} equipment 设备信息
     * @param {Object} operator 操作员信息
     * @param {Object} product 产品信息
     * @returns {number} 实际每小时产能
     */
    calculateActualCapacity(equipment, operator, product) {
        // 基础产能
        const baseCapacity = equipment.capacityPerHour || 0;
        
        // 设备效率系数
        const equipmentEfficiency = equipment.efficiencyFactor || 1.0;
        
        // 操作员效率系数
        const operatorEfficiency = operator.efficiencyFactor || 1.0;
        
        // 产品复杂度系数（可以根据产品特性调整）
        const productComplexity = product.complexityFactor || 1.0;
        
        // 计算实际产能
        const actualCapacity = baseCapacity * equipmentEfficiency * operatorEfficiency * productComplexity;
        
        return Math.max(0, actualCapacity);
    }
}

module.exports = CapacityCalculator;
