<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排程计划 - Makrite管理系统</title>
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/schedule/common.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">排程计划</h1>
                            <p class="text-gray-600 mt-1">管理生产排程计划</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button @click="refreshData"
                                    class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新
                            </button>
                            <a href="/schedule/create"
                               class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                新建排程
                            </a>
                        </div>
                    </div>
                </header>

                <!-- 筛选器 -->
                <div class="bg-white border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                            <select v-model="filters.status" @change="loadSchedules"
                                    class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">全部状态</option>
                                <option value="planned">计划中</option>
                                <option value="in_progress">执行中</option>
                                <option value="paused">已暂停</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                            <select v-model="filters.priority" @change="loadSchedules"
                                    class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">全部优先级</option>
                                <option value="low">低</option>
                                <option value="medium">中</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                            <input type="date" v-model="filters.startDate" @change="loadSchedules"
                                   class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                            <input type="date" v-model="filters.endDate" @change="loadSchedules"
                                   class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        </div>
                    </div>
                </div>

                <!-- 排程列表 -->
                <div class="p-6">
                    <div v-if="loading" class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-gray-600">加载中...</p>
                    </div>

                    <div v-else-if="schedules.length === 0" class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无排程</h3>
                        <p class="mt-1 text-sm text-gray-500">开始创建您的第一个生产排程计划</p>
                        <div class="mt-6">
                            <a href="/schedule/create"
                               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                新建排程
                            </a>
                        </div>
                    </div>

                    <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li v-for="schedule in schedules" :key="schedule.id"
                                class="px-6 py-4 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <h3 class="text-lg font-medium text-gray-900">{{ schedule.title }}</h3>
                                            <span :class="['ml-3 px-2 py-1 text-xs font-medium rounded-full', getStatusClass(schedule.status)]">
                                                {{ getStatusText(schedule.status) }}
                                            </span>
                                            <span :class="['ml-2 px-2 py-1 text-xs font-medium rounded-full', getPriorityClass(schedule.priority)]">
                                                {{ getPriorityText(schedule.priority) }}
                                            </span>
                                        </div>
                                        <div class="mt-2 text-sm text-gray-600">
                                            <p>产品: {{ schedule.productName }} | 数量: {{ schedule.quantity }}</p>
                                            <p>时间: {{ formatDateTime(schedule.startTime) }} - {{ formatDateTime(schedule.endTime) }}</p>
                                        </div>
                                        <div class="mt-2">
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-600 h-2 rounded-full" :style="{width: schedule.progress + '%'}"></div>
                                            </div>
                                            <span class="text-xs text-gray-500 mt-1">进度: {{ schedule.progress }}%</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="viewSchedule(schedule.id)"
                                                class="text-blue-600 hover:text-blue-900 text-sm">
                                            查看
                                        </button>
                                        <button @click="editSchedule(schedule.id)"
                                                class="text-green-600 hover:text-green-900 text-sm">
                                            编辑
                                        </button>
                                        <button @click="deleteSchedule(schedule.id)"
                                                class="text-red-600 hover:text-red-900 text-sm">
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <!-- 分页 -->
                    <div v-if="pagination.totalPages > 1" class="mt-6 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            显示第 {{ (pagination.page - 1) * pagination.limit + 1 }} -
                            {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，
                            共 {{ pagination.total }} 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button @click="changePage(pagination.page - 1)"
                                    :disabled="pagination.page <= 1"
                                    class="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                                上一页
                            </button>
                            <button @click="changePage(pagination.page + 1)"
                                    :disabled="pagination.page >= pagination.totalPages"
                                    class="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                                下一页
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入组件和脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/schedule/list.js"></script>
</body>
</html>
