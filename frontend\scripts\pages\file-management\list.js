/**
 * 文件管理列表页面
 * 显示和管理客户二认文件列表
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';
import FileList from '../../../components/file-management/FileList.js';

createStandardApp({
    components: {
        Sidebar,
        FileList
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        return {
            sidebarOpen
        };
    },
    requiredPermissions: ['file_view'],
    onUserLoaded: async (user) => {
        console.log('文件管理列表页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
