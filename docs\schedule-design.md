# 生产排程管理功能设计文档

## 页面设计规划

### 1. 排程总览页面 (Dashboard)
**路径**: `/schedule/dashboard`
**功能**: 
- 当日排程状态概览
- 进度统计图表
- 异常预警面板
- 快速操作入口

**UI布局**:
```
+------------------+------------------+
|   今日排程统计    |   设备状态监控    |
+------------------+------------------+
|        进度图表显示区域             |
+------------------------------------+
|        异常预警列表                |
+------------------------------------+
```

### 2. 排程计划页面 (Schedule List)
**路径**: `/schedule/list`
**功能**:
- 排程列表展示（表格视图）
- 甘特图视图切换
- 新建/编辑/删除排程
- 排程状态管理

**UI布局**:
```
+------------------------------------+
| 筛选器 | 视图切换 | [新建排程] 按钮  |
+------------------------------------+
|           排程列表/甘特图           |
+------------------------------------+
|              分页控件              |
+------------------------------------+
```

### 3. 新建/编辑排程页面
**路径**: `/schedule/create`, `/schedule/edit/:id`
**功能**:
- 排程基本信息录入
- 资源分配选择
- 时间安排设置
- 优先级设定

**表单字段**:
- 排程标题
- 产品信息
- 生产数量
- 开始/结束时间
- 分配设备
- 分配人员
- 所需物料
- 优先级

### 4. 资源管理页面
**路径**: `/schedule/resources`
**功能**:
- 设备状态监控
- 人员排班管理
- 物料库存状态

### 5. 数据分析页面
**路径**: `/schedule/reports`
**功能**:
- 排程执行效率报表
- 资源利用率分析
- 历史数据对比

## 用户交互设计

### 操作流程
1. **查看总览** → 了解当前生产状态
2. **管理排程** → 创建、编辑、删除排程计划
3. **监控资源** → 查看设备、人员、物料状态
4. **分析数据** → 查看报表和统计信息

### 权限控制
- `schedule_view`: 查看排程信息
- `schedule_create`: 创建新排程
- `schedule_edit`: 编辑排程
- `schedule_delete`: 删除排程
- `schedule_execute`: 执行排程操作
- `resource_manage`: 管理资源
- `schedule_report`: 查看报表

## 响应式设计
- 桌面端：完整功能展示
- 平板端：适配触摸操作
- 手机端：核心功能优先展示
