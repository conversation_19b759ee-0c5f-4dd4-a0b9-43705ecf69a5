/**
 * 设备产能管理页面
 * 设备产能配置和操作员技能管理
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ProductAPI from '../../api/product.js';
import EquipmentAPI from '../../api/equipment.js';
import SchedulingAPI from '../../api/scheduling.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        const { ref, onMounted, nextTick, computed } = Vue;

        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const loading = ref(false);
        const submitting = ref(false);
        const equipmentOptions = ref([]);
        const productOptions = ref([]);
        const operatorOptions = ref([]);
        const selectedEquipmentId = ref('');
        const selectedEquipment = ref(null);
        const capabilities = ref([]);
        const operatorSkills = ref([]);
        const schedulingStatus = ref([]);
        const utilizationData = ref([]);

        // 模态框状态
        const showCapabilityModal = ref(false);
        const showSkillModal = ref(false);
        const editingCapability = ref(null);
        const editingSkill = ref(null);

        // 图表引用
        const utilizationChart = ref(null);
        let utilizationChartInstance = null;

        // 表单数据
        const capabilityForm = ref({
            productId: '',
            capacityPerHour: 0,
            efficiencyFactor: 1.0,
            setupTime: 0
        });

        const skillForm = ref({
            operatorId: '',
            skillLevel: 1,
            efficiencyFactor: 1.0,
            certificationDate: ''
        });

        // 计算属性
        const utilizationRate = computed(() => {
            if (utilizationData.value.length === 0) return 0;
            const total = utilizationData.value.reduce((sum, data) => sum + data.rate, 0);
            return Math.round(total / utilizationData.value.length);
        });

        const averageEfficiency = computed(() => {
            if (capabilities.value.length === 0) return 0;
            const total = capabilities.value.reduce((sum, cap) => sum + cap.efficiencyFactor, 0);
            return total / capabilities.value.length;
        });

        // 初始化
        onMounted(async () => {
            try {
                // 获取当前用户信息
                const user = await getCurrentUser();
                if (user) {
                    currentUser.value = user;
                    isAuthenticated.value = true;
                    await loadEquipmentOptions();
                    await loadProductOptions();
                    await loadOperatorOptions();
                } else {
                    // 未登录，跳转到登录页
                    window.location.href = '/login';
                    return;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                window.location.href = '/login';
                return;
            } finally {
                hideLoading();
            }
        });

        // 加载设备选项
        async function loadEquipmentOptions() {
            try {
                const response = await EquipmentAPI.getEquipment({ limit: 1000 });
                if (response.success) {
                    equipmentOptions.value = response.data.equipment.filter(eq => eq.status === 'active');
                } else {
                    console.error('获取设备列表失败:', response.message);
                    window.showNotification('获取设备列表失败', 'error');
                }
            } catch (error) {
                console.error('加载设备选项失败:', error);
                window.showNotification('加载设备选项失败', 'error');
            }
        }

        // 加载产品选项
        async function loadProductOptions() {
            try {
                const response = await ProductAPI.getProductOptions();
                if (response.success) {
                    productOptions.value = response.data;
                } else {
                    console.error('获取产品选项失败:', response.message);
                    window.showNotification('获取产品选项失败', 'error');
                }
            } catch (error) {
                console.error('加载产品选项失败:', error);
                window.showNotification('加载产品选项失败', 'error');
            }
        }

        // 加载操作员选项
        async function loadOperatorOptions() {
            try {
                // 这里需要调用用户API获取操作员列表
                // 暂时使用模拟数据
                operatorOptions.value = [
                    { id: 'user1', name: '张三', username: 'zhangsan' },
                    { id: 'user2', name: '李四', username: 'lisi' },
                    { id: 'user3', name: '王五', username: 'wangwu' }
                ];
            } catch (error) {
                console.error('加载操作员选项失败:', error);
                window.showNotification('加载操作员选项失败', 'error');
            }
        }

        // 加载设备数据
        async function loadEquipmentData() {
            if (!selectedEquipmentId.value) {
                selectedEquipment.value = null;
                capabilities.value = [];
                operatorSkills.value = [];
                schedulingStatus.value = [];
                utilizationData.value = [];
                return;
            }

            loading.value = true;
            try {
                // 获取设备详情
                const equipment = equipmentOptions.value.find(eq => eq.id === selectedEquipmentId.value);
                selectedEquipment.value = equipment;

                // 加载设备产能配置
                await loadEquipmentCapabilities();

                // 加载操作员技能
                await loadOperatorSkills();

                // 加载利用率数据
                await loadUtilizationData();

                // 加载排程状态
                await loadSchedulingStatus();

                // 等待DOM更新后创建图表
                await nextTick();
                createUtilizationChart();

            } catch (error) {
                console.error('加载设备数据失败:', error);
                window.showNotification('加载设备数据失败', 'error');
            } finally {
                loading.value = false;
            }
        }

        // 加载设备产能配置
        async function loadEquipmentCapabilities() {
            try {
                // 这里需要调用设备产能API
                // 暂时使用模拟数据
                capabilities.value = [
                    {
                        id: 'cap1',
                        productId: 'prod1',
                        productName: '产品A',
                        productCode: 'PA001',
                        capacityPerHour: 100,
                        efficiencyFactor: 1.0,
                        setupTime: 30
                    }
                ];
            } catch (error) {
                console.error('加载设备产能配置失败:', error);
                window.showNotification('加载设备产能配置失败', 'error');
            }
        }

        // 加载操作员技能
        async function loadOperatorSkills() {
            try {
                // 这里需要调用操作员技能API
                // 暂时使用模拟数据
                operatorSkills.value = [
                    {
                        id: 'skill1',
                        operatorId: 'user1',
                        operatorName: '张三',
                        operatorUsername: 'zhangsan',
                        skillLevel: 3,
                        efficiencyFactor: 1.2,
                        certificationDate: '2024-01-15'
                    }
                ];
            } catch (error) {
                console.error('加载操作员技能失败:', error);
                window.showNotification('加载操作员技能失败', 'error');
            }
        }

        // 提交产能配置表单
        async function submitCapabilityForm() {
            if (submitting.value) return;

            // 基本验证
            if (!capabilityForm.value.productId) {
                window.showNotification('请选择产品', 'error');
                return;
            }

            if (!capabilityForm.value.capacityPerHour || capabilityForm.value.capacityPerHour <= 0) {
                window.showNotification('请输入有效的每小时产能', 'error');
                return;
            }

            submitting.value = true;

            try {
                // 这里需要调用API保存产能配置
                // 暂时模拟成功
                await new Promise(resolve => setTimeout(resolve, 1000));

                window.showNotification(
                    editingCapability.value ? '产能配置更新成功' : '产能配置创建成功', 
                    'success'
                );
                closeCapabilityModal();
                await loadEquipmentCapabilities();
            } catch (error) {
                console.error('保存产能配置失败:', error);
                window.showNotification('保存产能配置失败', 'error');
            } finally {
                submitting.value = false;
            }
        }

        // 提交技能配置表单
        async function submitSkillForm() {
            if (submitting.value) return;

            // 基本验证
            if (!skillForm.value.operatorId) {
                window.showNotification('请选择操作员', 'error');
                return;
            }

            submitting.value = true;

            try {
                // 这里需要调用API保存技能配置
                // 暂时模拟成功
                await new Promise(resolve => setTimeout(resolve, 1000));

                window.showNotification(
                    editingSkill.value ? '技能配置更新成功' : '技能配置创建成功', 
                    'success'
                );
                closeSkillModal();
                await loadOperatorSkills();
            } catch (error) {
                console.error('保存技能配置失败:', error);
                window.showNotification('保存技能配置失败', 'error');
            } finally {
                submitting.value = false;
            }
        }

        // 编辑产能配置
        function editCapability(capability) {
            editingCapability.value = capability;
            capabilityForm.value = {
                productId: capability.productId,
                capacityPerHour: capability.capacityPerHour,
                efficiencyFactor: capability.efficiencyFactor,
                setupTime: capability.setupTime
            };
            showCapabilityModal.value = true;
        }

        // 删除产能配置
        async function deleteCapability(capability) {
            if (!confirm(`确定要删除产品"${capability.productName}"的产能配置吗？`)) {
                return;
            }

            try {
                // 这里需要调用API删除产能配置
                window.showNotification('产能配置删除成功', 'success');
                await loadEquipmentCapabilities();
            } catch (error) {
                console.error('删除产能配置失败:', error);
                window.showNotification('删除产能配置失败', 'error');
            }
        }

        // 编辑技能配置
        function editSkill(skill) {
            editingSkill.value = skill;
            skillForm.value = {
                operatorId: skill.operatorId,
                skillLevel: skill.skillLevel,
                efficiencyFactor: skill.efficiencyFactor,
                certificationDate: skill.certificationDate
            };
            showSkillModal.value = true;
        }

        // 删除技能配置
        async function deleteSkill(skill) {
            if (!confirm(`确定要删除操作员"${skill.operatorName}"的技能配置吗？`)) {
                return;
            }

            try {
                // 这里需要调用API删除技能配置
                window.showNotification('技能配置删除成功', 'success');
                await loadOperatorSkills();
            } catch (error) {
                console.error('删除技能配置失败:', error);
                window.showNotification('删除技能配置失败', 'error');
            }
        }

        // 关闭产能配置模态框
        function closeCapabilityModal() {
            showCapabilityModal.value = false;
            editingCapability.value = null;
            capabilityForm.value = {
                productId: '',
                capacityPerHour: 0,
                efficiencyFactor: 1.0,
                setupTime: 0
            };
        }

        // 关闭技能配置模态框
        function closeSkillModal() {
            showSkillModal.value = false;
            editingSkill.value = null;
            skillForm.value = {
                operatorId: '',
                skillLevel: 1,
                efficiencyFactor: 1.0,
                certificationDate: ''
            };
        }

        // 获取设备状态文本
        function getEquipmentStatusText(status) {
            const statusMap = {
                'active': '正常',
                'maintenance': '维护中',
                'inactive': '停用'
            };
            return statusMap[status] || status;
        }

        // 获取技能等级样式
        function getSkillLevelClass(level) {
            const classMap = {
                1: 'bg-gray-100 text-gray-800',
                2: 'bg-blue-100 text-blue-800',
                3: 'bg-green-100 text-green-800',
                4: 'bg-yellow-100 text-yellow-800',
                5: 'bg-red-100 text-red-800'
            };
            return classMap[level] || 'bg-gray-100 text-gray-800';
        }

        // 获取技能等级文本
        function getSkillLevelText(level) {
            const textMap = {
                1: '初级',
                2: '熟练',
                3: '精通',
                4: '专家',
                5: '大师'
            };
            return textMap[level] || '未知';
        }

        // 加载利用率数据
        async function loadUtilizationData() {
            try {
                // 生成模拟利用率数据（实际应该从API获取）
                const data = [];
                const today = new Date();
                for (let i = 29; i >= 0; i--) {
                    const date = new Date(today);
                    date.setDate(date.getDate() - i);
                    data.push({
                        date: date.toISOString().split('T')[0],
                        rate: Math.floor(Math.random() * 40) + 60 // 60-100%的利用率
                    });
                }
                utilizationData.value = data;
            } catch (error) {
                console.error('加载利用率数据失败:', error);
            }
        }

        // 加载排程状态
        async function loadSchedulingStatus() {
            try {
                // 生成模拟排程状态数据（实际应该从API获取）
                const mockTasks = [
                    {
                        id: 1,
                        orderName: 'ORD001',
                        productName: '产品A',
                        quantity: 500,
                        startTime: '2024-12-20T08:00:00',
                        endTime: '2024-12-20T16:00:00',
                        status: 'in_progress',
                        progress: 65
                    },
                    {
                        id: 2,
                        orderName: 'ORD002',
                        productName: '产品B',
                        quantity: 300,
                        startTime: '2024-12-21T08:00:00',
                        endTime: '2024-12-21T14:00:00',
                        status: 'scheduled',
                        progress: 0
                    }
                ];
                schedulingStatus.value = mockTasks;
            } catch (error) {
                console.error('加载排程状态失败:', error);
            }
        }

        // 创建利用率图表
        function createUtilizationChart() {
            if (!utilizationChart.value || utilizationData.value.length === 0) return;

            const ctx = utilizationChart.value.getContext('2d');

            // 销毁现有图表
            if (utilizationChartInstance) {
                utilizationChartInstance.destroy();
            }

            const labels = utilizationData.value.map(data => {
                const date = new Date(data.date);
                return `${date.getMonth() + 1}/${date.getDate()}`;
            });
            const rates = utilizationData.value.map(data => data.rate);

            utilizationChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '利用率 (%)',
                        data: rates,
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '利用率 (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    }
                }
            });
        }

        // 刷新排程状态
        async function refreshSchedulingStatus() {
            await loadSchedulingStatus();
            window.showNotification('排程状态已刷新', 'success');
        }

        // 获取状态样式
        function getStatusClass(status) {
            const classMap = {
                'scheduled': 'bg-blue-100 text-blue-800',
                'in_progress': 'bg-yellow-100 text-yellow-800',
                'completed': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800'
            };
            return classMap[status] || 'bg-gray-100 text-gray-800';
        }

        // 获取状态文本
        function getStatusText(status) {
            const textMap = {
                'scheduled': '已排程',
                'in_progress': '进行中',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return textMap[status] || '未知';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }

        return {
            currentUser,
            isAuthenticated,
            loading,
            submitting,
            equipmentOptions,
            productOptions,
            operatorOptions,
            selectedEquipmentId,
            selectedEquipment,
            capabilities,
            operatorSkills,
            schedulingStatus,
            utilizationData,
            utilizationRate,
            averageEfficiency,
            utilizationChart,
            showCapabilityModal,
            showSkillModal,
            editingCapability,
            editingSkill,
            capabilityForm,
            skillForm,
            loadEquipmentData,
            submitCapabilityForm,
            submitSkillForm,
            editCapability,
            deleteCapability,
            editSkill,
            deleteSkill,
            closeCapabilityModal,
            closeSkillModal,
            refreshSchedulingStatus,
            getEquipmentStatusText,
            getSkillLevelClass,
            getSkillLevelText,
            getStatusClass,
            getStatusText,
            formatDate
        };
    }
}).mount('#app');
