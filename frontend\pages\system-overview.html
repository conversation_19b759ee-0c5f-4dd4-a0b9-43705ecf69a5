<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统功能展示 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <script src="/js/libs/mermaid.min.js" onerror="console.error('Mermaid本地库加载失败，请检查文件是否存在')"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        /* 现代化渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        .gradient-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        /* 现代化卡片悬停效果 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .card-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        /* Logo 样式 */
        .logo-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 8px 12px;
            transition: all 0.3s ease;
        }
        .logo-container:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .logo-image {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            transition: transform 0.3s ease;
        }
        .logo-container:hover .logo-image {
            transform: scale(1.05);
        }

        /* Logo 动画效果 */
        .logo-fade-in {
            animation: logoFadeIn 1s ease-out;
        }

        @keyframes logoFadeIn {
            0% {
                opacity: 0;
                transform: translateY(-20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 系统演进历程样式 */
        .evolution-timeline {
            position: relative;
        }

        .evolution-stage {
            opacity: 0;
            transform: translateY(30px);
            animation: stageSlideIn 0.8s ease-out forwards;
        }

        .evolution-stage:nth-child(1) { animation-delay: 0.2s; }
        .evolution-stage:nth-child(2) { animation-delay: 0.4s; }
        .evolution-stage:nth-child(3) { animation-delay: 0.6s; }

        @keyframes stageSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .timeline-node {
            position: relative;
            transition: all 0.3s ease;
        }

        .timeline-node::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .timeline-node:hover::before {
            width: 20px;
            height: 20px;
        }

        .evolution-card {
            position: relative;
            overflow: hidden;
        }

        .evolution-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s;
        }

        .evolution-card:hover::before {
            left: 100%;
        }

        .complexity-indicator {
            transition: all 0.3s ease;
        }

        .complexity-dot {
            transition: all 0.3s ease;
        }

        .evolution-card:hover .complexity-dot {
            transform: scale(1.2);
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .evolution-stage {
                margin-bottom: 2rem;
            }

            .evolution-card {
                margin: 0 1rem;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .logo-container {
                padding: 6px 8px;
                top: 1rem;
                left: 1rem;
            }
            .logo-image {
                height: 2rem !important;
            }
            .logo-text {
                font-size: 0.875rem !important;
            }
            .logo-subtitle {
                font-size: 0.625rem !important;
            }
        }

        @media (max-width: 480px) {
            .logo-container {
                padding: 4px 6px;
                top: 0.75rem;
                left: 0.75rem;
            }
            .logo-image {
                height: 1.75rem !important;
            }
            .logo-text {
                font-size: 0.75rem !important;
            }
            .logo-subtitle {
                font-size: 0.5rem !important;
            }
        }
        .card-hover:hover::before {
            left: 100%;
        }
        /* 现代化状态徽章 */
        .status-implemented {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
            position: relative;
            overflow: hidden;
        }
        .status-developing {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.39);
            position: relative;
            overflow: hidden;
        }
        .status-planned {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.39);
            position: relative;
            overflow: hidden;
        }

        /* 技术栈卡片动画 */
        .tech-card {
            position: relative;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .tech-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* 脉冲动画 */
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 渐入动画 */
        .fade-in {
            animation: fadeIn 0.8s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }
        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 延迟动画 */
        .delay-100 { animation-delay: 0.1s; }
        .delay-200 { animation-delay: 0.2s; }
        .delay-300 { animation-delay: 0.3s; }
        .delay-400 { animation-delay: 0.4s; }
        .feature-card {
            border-left: 4px solid;
        }
        .feature-implemented {
            border-left-color: #10b981;
            background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
        }
        .feature-developing {
            border-left-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb, #fefce8);
        }
        .feature-planned {
            border-left-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff, #f0f9ff);
        }
        .mermaid {
            display: flex;
            justify-content: center;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #F3F4F6;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #e5e7eb;
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .tech-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            margin: 0 auto 1rem;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        .vue-icon { background: linear-gradient(135deg, #4fc08d, #42b883); }
        .node-icon { background: linear-gradient(135deg, #68a063, #5d9c5a); }
        .sqlite-icon { background: linear-gradient(135deg, #003b57, #0f4c75); }
        .tailwind-icon { background: linear-gradient(135deg, #06b6d4, #0891b2); }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            .gradient-bg h1 {
                font-size: 2.5rem;
            }
            .gradient-bg p {
                font-size: 1rem;
            }
            .gradient-bg .flex {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* 现代化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }

        /* 交互式按钮效果 */
        .interactive-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .interactive-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }
        .interactive-btn:hover::before {
            width: 300px;
            height: 300px;
        }
    </style>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <!-- 现代化页面头部 -->
        <header class="gradient-bg text-white relative">
            <!-- Logo 左上角 -->
            <div class="absolute top-6 left-6 z-20 logo-fade-in">
                <div class="logo-container flex items-center space-x-3">
                    <img src="/logo/Makrite-logo.png" alt="Makrite Logo" class="logo-image h-12 w-auto md:h-12 sm:h-8">
                    <div class="text-left">
                        <div class="logo-subtitle text-sm font-medium md:text-sm sm:text-xs">企业管理系统</div>
                    </div>
                </div>
            </div>

            <div class="container mx-auto px-6 py-20 relative z-10">
                <div class="text-center">
                    <h1 class="text-5xl font-bold mb-6 fade-in">Makrite 企业管理系统</h1>
                    <p class="text-xl opacity-90 mb-4 fade-in delay-100">现代化的企业级管理平台，提供完整的业务流程解决方案</p>
                    <div class="flex justify-center items-center space-x-6 mb-8 fade-in delay-150">
                        <div class="flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <span class="text-sm font-medium">企业级安全</span>
                        </div>
                        <div class="flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <span class="text-sm font-medium">本地主机部署</span>
                        </div>
                        <div class="flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            <span class="text-sm font-medium">数据自主可控</span>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6 text-center fade-in delay-200 max-w-6xl mx-auto">
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation">{{ stats.totalModules }}</div>
                                <div class="text-sm opacity-80">功能模块</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-green-300">{{ stats.implementedModules }}</div>
                                <div class="text-sm opacity-80">已实现</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-blue-300">{{ stats.plannedModules }}</div>
                                <div class="text-sm opacity-80">规划中</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-purple-300">{{ stats.intelligentFeatures }}</div>
                                <div class="text-sm opacity-80">智能特性</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-yellow-300">{{ stats.monitoringFeatures }}</div>
                                <div class="text-sm opacity-80">监控特性</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-red-300">{{ stats.securityFeatures }}</div>
                                <div class="text-sm opacity-80">安全特性</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-cyan-300">{{ stats.technicalFeatures }}</div>
                                <div class="text-sm opacity-80">技术特性</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-orange-300">{{ stats.performanceOptimizations }}</div>
                                <div class="text-sm opacity-80">性能优化</div>
                            </div>
                        </div>
                        <div class="transform hover:scale-110 transition-transform duration-300">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-3xl font-bold pulse-animation text-pink-300">{{ stats.deploymentSecurity }}</div>
                                <div class="text-sm opacity-80">部署安全</div>
                            </div>
                        </div>
                    </div>

                    <!-- 交互式导航按钮 -->
                    <div class="mt-12 fade-in delay-300">
                        <button @click="scrollToSection('overview')" class="interactive-btn bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-8 py-3 rounded-full font-semibold mr-4 transition-all duration-300">
                            探索功能
                        </button>
                        <button @click="scrollToSection('tech-stack')" class="interactive-btn border-2 border-white border-opacity-50 hover:border-opacity-100 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300">
                            技术架构
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="container mx-auto px-6 py-12">
            <!-- 系统概览 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">系统概览</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-lg shadow-lg p-6 card-hover">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">模块化设计</h3>
                            <p class="text-gray-600">每个功能模块独立运行，可单独部署和维护</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg p-6 card-hover">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">权限控制</h3>
                            <p class="text-gray-600">精细化的权限管理，确保数据安全和访问控制</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg p-6 card-hover">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">高性能</h3>
                            <p class="text-gray-600">轻量级架构设计，响应速度快，用户体验优秀</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 系统演进历程 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">系统演进历程</h2>

                <!-- 演进统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                    <div class="bg-white rounded-lg shadow-lg p-6 text-center card-hover">
                        <div class="text-3xl font-bold text-blue-600 mb-2">3</div>
                        <div class="text-sm text-gray-600">主要版本</div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg p-6 text-center card-hover">
                        <div class="text-3xl font-bold text-green-600 mb-2">7</div>
                        <div class="text-sm text-gray-600">功能模块</div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg p-6 text-center card-hover">
                        <div class="text-3xl font-bold text-purple-600 mb-2">200%</div>
                        <div class="text-sm text-gray-600">性能提升</div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg p-6 text-center card-hover">
                        <div class="text-3xl font-bold text-orange-600 mb-2">4</div>
                        <div class="text-sm text-gray-600">月快速发展</div>
                    </div>
                </div>
                <div class="relative evolution-timeline">
                    <!-- 时间轴线 -->
                    <div class="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-blue-400 via-green-400 to-purple-400 h-full rounded-full hidden md:block"></div>

                    <!-- 演进阶段 -->
                    <div class="space-y-12">
                        <div v-for="(stage, index) in systemEvolution" :key="stage.version"
                             class="relative flex items-center evolution-stage"
                             :class="index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'"
                             :style="{ animationDelay: (index * 0.2 + 0.2) + 's' }">

                            <!-- 时间轴节点 -->
                            <div class="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full border-4 border-white shadow-lg z-10 hidden md:block timeline-node"
                                 :class="{
                                     'bg-blue-500': stage.color === 'blue',
                                     'bg-green-500': stage.color === 'green',
                                     'bg-purple-500': stage.color === 'purple'
                                 }">
                            </div>

                            <!-- 内容卡片 -->
                            <div class="w-full md:w-5/12 mx-auto md:mx-0"
                                 :class="index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'">
                                <div class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 evolution-card"
                                     :class="{
                                         'border-blue-500': stage.color === 'blue',
                                         'border-green-500': stage.color === 'green',
                                         'border-purple-500': stage.color === 'purple'
                                     }">

                                    <!-- 头部信息 -->
                                    <div class="flex items-center mb-4">
                                        <div class="text-3xl mr-3">{{ stage.icon }}</div>
                                        <div>
                                            <div class="flex items-center mb-1">
                                                <span class="text-xs px-2 py-1 rounded-full text-white mr-2"
                                                      :class="{
                                                          'bg-blue-500': stage.color === 'blue',
                                                          'bg-green-500': stage.color === 'green',
                                                          'bg-purple-500': stage.color === 'purple'
                                                      }">{{ stage.version }}</span>
                                                <span class="text-sm text-gray-500">{{ stage.period }}</span>
                                            </div>
                                            <h3 class="text-xl font-bold text-gray-800">{{ stage.title }}</h3>
                                        </div>
                                    </div>

                                    <!-- 描述 -->
                                    <p class="text-gray-600 mb-4">{{ stage.description }}</p>

                                    <!-- 功能模块 -->
                                    <div class="mb-4">
                                        <h4 class="text-sm font-semibold text-gray-700 mb-2">📦 核心模块</h4>
                                        <div class="flex flex-wrap gap-2">
                                            <span v-for="module in stage.modules" :key="module"
                                                  class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-full">
                                                {{ module }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 技术栈 -->
                                    <div class="mb-4">
                                        <h4 class="text-sm font-semibold text-gray-700 mb-2">🛠️ 技术栈</h4>
                                        <div class="flex flex-wrap gap-2">
                                            <span v-for="tech in stage.techStack" :key="tech"
                                                  class="text-xs px-2 py-1 rounded-full"
                                                  :class="{
                                                      'bg-blue-100 text-blue-700': stage.color === 'blue',
                                                      'bg-green-100 text-green-700': stage.color === 'green',
                                                      'bg-purple-100 text-purple-700': stage.color === 'purple'
                                                  }">
                                                {{ tech }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 主要成就 -->
                                    <div>
                                        <h4 class="text-sm font-semibold text-gray-700 mb-2">🎯 主要成就</h4>
                                        <ul class="space-y-1">
                                            <li v-for="achievement in stage.achievements" :key="achievement"
                                                class="flex items-center text-sm text-gray-600">
                                                <svg class="w-3 h-3 mr-2 flex-shrink-0"
                                                     :class="{
                                                         'text-blue-500': stage.color === 'blue',
                                                         'text-green-500': stage.color === 'green',
                                                         'text-purple-500': stage.color === 'purple'
                                                     }"
                                                     fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                {{ achievement }}
                                            </li>
                                        </ul>
                                    </div>

                                    <!-- 复杂度指示器 -->
                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-gray-500">系统复杂度</span>
                                            <div class="flex space-x-1 complexity-indicator">
                                                <div class="w-2 h-2 rounded-full complexity-dot"
                                                     :class="{
                                                         'bg-blue-500': stage.color === 'blue',
                                                         'bg-green-500': stage.color === 'green',
                                                         'bg-purple-500': stage.color === 'purple'
                                                     }"></div>
                                                <div class="w-2 h-2 rounded-full complexity-dot"
                                                     :class="{
                                                         'bg-gray-300': stage.complexity === 'low',
                                                         'bg-green-500': stage.color === 'green' && stage.complexity !== 'low',
                                                         'bg-purple-500': stage.color === 'purple'
                                                     }"></div>
                                                <div class="w-2 h-2 rounded-full complexity-dot"
                                                     :class="{
                                                         'bg-gray-300': stage.complexity !== 'high',
                                                         'bg-purple-500': stage.complexity === 'high'
                                                     }"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 技术架构特性 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">技术架构特性</h2>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div v-for="feature in technicalFeatures" :key="feature.title"
                         class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-blue-500">
                        <div class="flex items-start mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full mr-2">{{ feature.category }}</span>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ feature.title }}</h3>
                                <p class="text-gray-600 mb-4">{{ feature.description }}</p>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h4 class="text-sm font-semibold text-blue-600 mb-2">🚀 核心特性</h4>
                            <div v-for="highlight in feature.highlights" :key="highlight"
                                 class="flex items-center text-sm text-gray-700">
                                <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ highlight }}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 安全特性 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">安全特性</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div v-for="security in securityFeatures" :key="security.title"
                         class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-red-500">
                        <div class="flex items-start mb-4">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ security.title }}</h3>
                                <p class="text-gray-600 mb-3">{{ security.description }}</p>
                                <div class="text-sm text-blue-600 mb-2">
                                    <strong>实现方式:</strong> {{ security.implementation }}
                                </div>
                            </div>
                        </div>
                        <div class="space-y-1">
                            <h4 class="text-sm font-semibold text-red-600 mb-2">🔒 安全优势</h4>
                            <div v-for="benefit in security.benefits" :key="benefit"
                                 class="flex items-center text-sm text-gray-700">
                                <svg class="w-3 h-3 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                {{ benefit }}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 本地部署安全优势 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">本地部署安全优势</h2>
                <div class="bg-gradient-to-r from-red-50 to-blue-50 rounded-lg p-8 mb-8">
                    <div class="text-center mb-8">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">企业级本地化部署</h3>
                        <p class="text-gray-600 max-w-2xl mx-auto">
                            系统采用完全本地化部署方案，确保企业数据安全可控，满足最严格的安全合规要求
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-white text-xl">🔒</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-1">数据主权</h4>
                            <p class="text-sm text-gray-600">数据完全自主可控</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-white text-xl">🛡️</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-1">网络隔离</h4>
                            <p class="text-sm text-gray-600">内网独立运行</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-white text-xl">📋</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-1">合规保障</h4>
                            <p class="text-sm text-gray-600">满足行业标准</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-white text-xl">⚙️</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-1">自主运维</h4>
                            <p class="text-sm text-gray-600">企业完全掌控</p>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div v-for="security in localDeploymentSecurity" :key="security.title"
                         class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4"
                         :class="{
                             'border-red-500': security.color === 'red',
                             'border-blue-500': security.color === 'blue',
                             'border-green-500': security.color === 'green',
                             'border-purple-500': security.color === 'purple'
                         }">
                        <div class="flex items-start mb-4">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4"
                                 :class="{
                                     'bg-red-100': security.color === 'red',
                                     'bg-blue-100': security.color === 'blue',
                                     'bg-green-100': security.color === 'green',
                                     'bg-purple-100': security.color === 'purple'
                                 }">
                                <span class="text-2xl">{{ security.icon }}</span>
                            </div>
                            <div>
                                <div class="flex items-center mb-2">
                                    <span class="text-xs px-2 py-1 rounded-full text-white mr-2"
                                          :class="{
                                              'bg-red-500': security.color === 'red',
                                              'bg-blue-500': security.color === 'blue',
                                              'bg-green-500': security.color === 'green',
                                              'bg-purple-500': security.color === 'purple'
                                          }">{{ security.category }}</span>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ security.title }}</h3>
                                <p class="text-gray-600 mb-3">{{ security.description }}</p>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h4 class="text-sm font-semibold mb-2"
                                :class="{
                                    'text-red-600': security.color === 'red',
                                    'text-blue-600': security.color === 'blue',
                                    'text-green-600': security.color === 'green',
                                    'text-purple-600': security.color === 'purple'
                                }">🔐 安全特性</h4>
                            <div v-for="feature in security.features" :key="feature"
                                 class="flex items-center text-sm text-gray-700">
                                <svg class="w-3 h-3 mr-2 flex-shrink-0"
                                     :class="{
                                         'text-red-500': security.color === 'red',
                                         'text-blue-500': security.color === 'blue',
                                         'text-green-500': security.color === 'green',
                                         'text-purple-500': security.color === 'purple'
                                     }"
                                     fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ feature }}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 技术架构图 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">系统架构图</h2>
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <!-- Mermaid架构图 -->
                    <div class="mermaid" id="architecture-diagram" style="display: none;">
flowchart TB
    A[用户界面层] --> B[API网关层]
    B --> C[业务逻辑层]
    C --> D[数据访问层]
    C --> E[文件存储层]
    F[认证中间件] --> B
    G[权限中间件] --> C

    style A fill:#4fc08d
    style B fill:#68a063
    style C fill:#f59e0b
    style D fill:#003b57
    style E fill:#6b7280
    style F fill:#ef4444
    style G fill:#8b5cf6
                    </div>

                    <!-- 静态架构图 -->
                    <div id="static-architecture">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <span class="text-white font-bold">前端</span>
                                </div>
                                <h4 class="font-semibold">用户界面层</h4>
                                <p class="text-sm text-gray-600">Vue.js 3 + Tailwind CSS</p>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-yellow-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <span class="text-white font-bold">后端</span>
                                </div>
                                <h4 class="font-semibold">业务逻辑层</h4>
                                <p class="text-sm text-gray-600">Node.js + Express.js</p>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <span class="text-white font-bold">数据</span>
                                </div>
                                <h4 class="font-semibold">数据访问层</h4>
                                <p class="text-sm text-gray-600">SQLite Database</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 现代化技术栈 -->
            <section id="tech-stack" class="mb-16 py-16 bg-gradient-to-br from-gray-50 to-white">
                <div class="container mx-auto px-6">
                    <h2 class="text-3xl font-bold text-gray-800 mb-12 text-center fade-in">技术栈</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                        <div class="tech-card bg-white rounded-xl shadow-lg p-8 text-center card-hover fade-in delay-100">
                            <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 transform hover:rotate-12 transition-transform duration-300">
                                <span class="text-white text-3xl font-bold">V</span>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-3">Vue.js 3</h3>
                            <p class="text-gray-600 mb-4">前端框架</p>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                            <span class="text-sm text-gray-500 mt-2 block">95% 完成度</span>
                        </div>
                        <div class="tech-card bg-white rounded-xl shadow-lg p-8 text-center card-hover fade-in delay-200">
                            <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-700 rounded-2xl flex items-center justify-center mx-auto mb-6 transform hover:rotate-12 transition-transform duration-300">
                                <span class="text-white text-3xl font-bold">N</span>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-3">Node.js</h3>
                            <p class="text-gray-600 mb-4">后端运行时</p>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 90%"></div>
                            </div>
                            <span class="text-sm text-gray-500 mt-2 block">90% 完成度</span>
                        </div>
                        <div class="tech-card bg-white rounded-xl shadow-lg p-8 text-center card-hover fade-in delay-300">
                            <div class="w-20 h-20 bg-gradient-to-br from-blue-800 to-blue-900 rounded-2xl flex items-center justify-center mx-auto mb-6 transform hover:rotate-12 transition-transform duration-300">
                                <span class="text-white text-3xl font-bold">S</span>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-3">SQLite</h3>
                            <p class="text-gray-600 mb-4">数据库</p>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-800 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <span class="text-sm text-gray-500 mt-2 block">85% 完成度</span>
                        </div>
                        <div class="tech-card bg-white rounded-xl shadow-lg p-8 text-center card-hover fade-in delay-400">
                            <div class="w-20 h-20 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-6 transform hover:rotate-12 transition-transform duration-300">
                                <span class="text-white text-3xl font-bold">T</span>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-3">Tailwind</h3>
                            <p class="text-gray-600 mb-4">CSS框架</p>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-cyan-500 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                            <span class="text-sm text-gray-500 mt-2 block">100% 完成度</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 功能模块状态说明 -->
            <section class="mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">功能模块</h2>
                <div class="flex justify-center space-x-8 mb-8">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-gray-700">已实现</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
                        <span class="text-gray-700">开发中</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                        <span class="text-gray-700">规划中</span>
                    </div>
                </div>

                <!-- 已实现功能 -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center">已实现功能模块</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div v-for="module in implementedModules" :key="module.id"
                             class="feature-card feature-implemented bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-green-500">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" v-html="module.icon"></svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-800">{{ module.name }}</h4>
                                    <span class="status-implemented text-xs px-2 py-1 rounded-full">✅ 已实现</span>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4">{{ module.description }}</p>

                            <!-- 现状和痛点 -->
                            <div v-if="module.currentStatus" class="mb-4">
                                <h5 class="text-sm font-semibold text-red-600 mb-2">🚨 现状痛点</h5>
                                <p class="text-sm text-gray-700 mb-2">{{ module.currentStatus }}</p>
                                <div class="space-y-1">
                                    <div v-for="pain in module.painPoints" :key="pain"
                                         class="flex items-center text-xs text-red-600">
                                        <svg class="w-3 h-3 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        {{ pain }}
                                    </div>
                                </div>
                            </div>

                            <!-- 功能特性 -->
                            <div class="space-y-3">
                                <div>
                                    <h5 class="text-sm font-semibold text-green-600 mb-2">✅ 核心功能</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="feature in module.features" :key="feature"
                                              class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                            {{ feature }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="module.highlights">
                                    <h5 class="text-sm font-semibold text-blue-600 mb-2">🚀 核心亮点</h5>
                                    <div class="space-y-1">
                                        <div v-for="highlight in module.highlights" :key="highlight"
                                             class="flex items-center text-sm text-gray-700">
                                            <svg class="w-3 h-3 text-blue-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ highlight }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 开发中功能 -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center">开发中功能模块</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div v-for="module in developingModules" :key="module.id"
                             class="feature-card feature-developing bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-yellow-500">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" v-html="module.icon"></svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-800">{{ module.name }}</h4>
                                    <span class="status-developing text-xs px-2 py-1 rounded-full">开发中</span>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4">{{ module.description }}</p>

                            <!-- 进度条 -->
                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">开发进度</span>
                                    <span class="text-sm font-medium text-yellow-600">{{ module.progress }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                                         :style="{ width: module.progress + '%' }"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">{{ module.currentPhase }}</p>
                            </div>

                            <!-- 功能特性 -->
                            <div class="space-y-3">
                                <div>
                                    <h5 class="text-sm font-semibold text-yellow-600 mb-2">🎯 核心功能</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="feature in module.features" :key="feature"
                                              class="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full">
                                            {{ feature }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="module.highlights">
                                    <h5 class="text-sm font-semibold text-blue-600 mb-2">🚀 核心亮点</h5>
                                    <div class="space-y-1">
                                        <div v-for="highlight in module.highlights" :key="highlight"
                                             class="flex items-center text-sm text-gray-700">
                                            <svg class="w-3 h-3 text-blue-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ highlight }}
                                        </div>
                                    </div>
                                </div>
                                <div v-if="module.completedFeatures">
                                    <h5 class="text-sm font-semibold text-green-600 mb-2">✅ 已完成</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="completed in module.completedFeatures" :key="completed"
                                              class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                            {{ completed }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="module.inProgressFeatures">
                                    <h5 class="text-sm font-semibold text-orange-600 mb-2">🔄 进行中</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="inProgress in module.inProgressFeatures" :key="inProgress"
                                              class="text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full">
                                            {{ inProgress }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能特性展示 -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center">智能化特性</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div v-for="feature in intelligentFeatures" :key="feature.title"
                             class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-purple-500">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-800">{{ feature.title }}</h4>
                                    <span class="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">🤖 AI智能</span>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4">{{ feature.description }}</p>

                            <div class="space-y-3">
                                <div v-if="feature.algorithms">
                                    <h5 class="text-sm font-semibold text-purple-600 mb-2">🧠 算法策略</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="algorithm in feature.algorithms" :key="algorithm"
                                              class="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">
                                            {{ algorithm }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="feature.features">
                                    <h5 class="text-sm font-semibold text-blue-600 mb-2">⚙️ 核心功能</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="func in feature.features" :key="func"
                                              class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                            {{ func }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="feature.methods">
                                    <h5 class="text-sm font-semibold text-green-600 mb-2">📊 分析方法</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="method in feature.methods" :key="method"
                                              class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                            {{ method }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="feature.benefits">
                                    <h5 class="text-sm font-semibold text-orange-600 mb-2">📈 效果提升</h5>
                                    <div class="space-y-1">
                                        <div v-for="benefit in feature.benefits" :key="benefit"
                                             class="flex items-center text-sm text-gray-700">
                                            <svg class="w-3 h-3 text-orange-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ benefit }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统监控特性 -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center">系统监控特性</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div v-for="monitor in monitoringFeatures" :key="monitor.title"
                             class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-cyan-500">
                            <div class="text-center mb-4">
                                <div class="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <span class="text-xs px-2 py-1 bg-cyan-100 text-cyan-800 rounded-full">{{ monitor.category }}</span>
                                <h4 class="text-lg font-semibold text-gray-800 mt-2">{{ monitor.title }}</h4>
                            </div>

                            <div class="space-y-3">
                                <div v-if="monitor.metrics">
                                    <h5 class="text-sm font-semibold text-cyan-600 mb-2">📊 监控指标</h5>
                                    <div class="flex flex-wrap gap-1">
                                        <span v-for="metric in monitor.metrics" :key="metric"
                                              class="text-xs px-2 py-1 bg-cyan-50 text-cyan-700 rounded-full">
                                            {{ metric }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="monitor.features">
                                    <h5 class="text-sm font-semibold text-blue-600 mb-2">🔧 核心功能</h5>
                                    <div class="space-y-1">
                                        <div v-for="feature in monitor.features" :key="feature"
                                             class="flex items-center text-sm text-gray-700">
                                            <svg class="w-3 h-3 text-blue-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ feature }}
                                        </div>
                                    </div>
                                </div>
                                <div v-if="monitor.types">
                                    <h5 class="text-sm font-semibold text-orange-600 mb-2">⚠️ 告警类型</h5>
                                    <div class="flex flex-wrap gap-1">
                                        <span v-for="type in monitor.types" :key="type"
                                              class="text-xs px-2 py-1 bg-orange-50 text-orange-700 rounded-full">
                                            {{ type }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 规划中功能 -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6">规划中功能</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div v-for="module in plannedModules" :key="module.id"
                             class="feature-card feature-planned bg-white rounded-lg shadow-lg p-6 card-hover">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" v-html="module.icon"></svg>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-800">{{ module.name }}</h4>
                                    <span class="status-planned text-xs px-2 py-1 rounded-full">规划中</span>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4">{{ module.description }}</p>

                            <!-- 现状和痛点 -->
                            <div v-if="module.currentStatus" class="mb-4">
                                <h5 class="text-sm font-semibold text-red-600 mb-2">🚨 现状痛点</h5>
                                <p class="text-sm text-gray-700 mb-2">{{ module.currentStatus }}</p>
                                <div class="space-y-1">
                                    <div v-for="pain in module.painPoints" :key="pain"
                                         class="flex items-center text-xs text-red-600">
                                        <svg class="w-3 h-3 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        {{ pain }}
                                    </div>
                                </div>
                            </div>

                            <!-- 功能特性 -->
                            <div class="space-y-3">
                                <div>
                                    <h5 class="text-sm font-semibold text-blue-600 mb-2">🎯 基础功能</h5>
                                    <div class="flex flex-wrap gap-2">
                                        <span v-for="feature in module.features" :key="feature"
                                              class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                            {{ feature }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="module.plannedFeatures">
                                    <h5 class="text-sm font-semibold text-purple-600 mb-2">🚀 规划特性</h5>
                                    <div class="space-y-1">
                                        <div v-for="planned in module.plannedFeatures" :key="planned"
                                             class="flex items-center text-sm text-gray-700">
                                            <svg class="w-3 h-3 text-purple-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ planned }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 业务流程图 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">业务流程</h2>
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="mermaid" id="business-flow" style="display: none;">
flowchart LR
    A[用户登录] --> B[权限验证]
    B --> C[功能模块]
    C --> D[申请管理]
    C --> E[设备管理]
    C --> F[质量管理]
    C --> G[生产排程]
    C --> H[库存管理]
    C --> I[文档管理]

    style A fill:#4fc08d
    style B fill:#f59e0b
    style C fill:#8b5cf6
    style D fill:#10b981
    style E fill:#f59e0b
    style F fill:#f59e0b
    style G fill:#f59e0b
    style H fill:#3b82f6
    style I fill:#3b82f6
                    </div>

                    <!-- 静态流程图 -->
                    <div id="static-business-flow">
                        <div class="flex flex-wrap justify-center items-center space-x-4">
                            <div class="bg-green-500 text-white px-4 py-2 rounded-lg mb-2">用户登录</div>
                            <div class="text-gray-400">→</div>
                            <div class="bg-yellow-500 text-white px-4 py-2 rounded-lg mb-2">权限验证</div>
                            <div class="text-gray-400">→</div>
                            <div class="bg-purple-500 text-white px-4 py-2 rounded-lg mb-2">功能模块</div>
                        </div>
                        <div class="mt-6 grid grid-cols-2 md:grid-cols-5 gap-4">
                            <div class="bg-green-600 text-white px-3 py-2 rounded text-center text-sm">申请管理</div>
                            <div class="bg-red-500 text-white px-3 py-2 rounded text-center text-sm">设备管理</div>
                            <div class="bg-blue-500 text-white px-3 py-2 rounded text-center text-sm">质量管理</div>
                            <div class="bg-orange-500 text-white px-3 py-2 rounded text-center text-sm">生产排程</div>
                            <div class="bg-purple-600 text-white px-3 py-2 rounded text-center text-sm">库存管理</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 系统信息 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">系统信息</h2>
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600 mb-2">V2.0</div>
                            <div class="text-gray-600">系统版本</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600 mb-2">SQLite</div>
                            <div class="text-gray-600">数据库</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600 mb-2">JWT</div>
                            <div class="text-gray-600">认证方式</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600 mb-2">本地部署</div>
                            <div class="text-gray-600">部署方式</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 快速访问 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">快速访问</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <a href="/login" class="bg-white rounded-lg shadow-lg p-6 text-center card-hover block">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                        </div>
                        <div class="font-semibold text-gray-800">登录系统</div>
                    </a>
                    <a href="/dashboard" class="bg-white rounded-lg shadow-lg p-6 text-center card-hover block">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                        </div>
                        <div class="font-semibold text-gray-800">系统主页</div>
                    </a>
                    <a href="/new-application" class="bg-white rounded-lg shadow-lg p-6 text-center card-hover block">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <div class="font-semibold text-gray-800">新建申请</div>
                    </a>
                    <a href="/application-record" class="bg-white rounded-lg shadow-lg p-6 text-center card-hover block">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <div class="font-semibold text-gray-800">申请记录</div>
                    </a>
                </div>
            </section>

            <!-- 性能优化 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">性能优化与最佳实践</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div v-for="optimization in performanceOptimizations" :key="optimization.title"
                         class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-green-500">
                        <div class="flex items-start mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="flex items-center mb-2">
                                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full mr-2">{{ optimization.category }}</span>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ optimization.title }}</h3>
                                <div class="text-sm text-green-600 font-medium mb-3">
                                    📈 {{ optimization.impact }}
                                </div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h4 class="text-sm font-semibold text-green-600 mb-2">⚡ 优化技术</h4>
                            <div v-for="technique in optimization.techniques" :key="technique"
                                 class="flex items-center text-sm text-gray-700">
                                <svg class="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ technique }}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 开发最佳实践 -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">开发最佳实践</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div v-for="practice in developmentPractices" :key="practice.title"
                         class="bg-white rounded-lg shadow-lg p-6 card-hover border-l-4 border-purple-500">
                        <div class="flex items-start mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ practice.title }}</h3>
                                <p class="text-gray-600 mb-3">{{ practice.description }}</p>
                                <div class="text-sm text-purple-600 mb-3">
                                    <strong>示例:</strong> {{ practice.example }}
                                </div>
                            </div>
                        </div>
                        <div class="space-y-1">
                            <h4 class="text-sm font-semibold text-purple-600 mb-2">💡 实践优势</h4>
                            <div v-for="benefit in practice.benefits" :key="benefit"
                                 class="flex items-center text-sm text-gray-700">
                                <svg class="w-3 h-3 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                {{ benefit }}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页面底部 -->
        <footer class="bg-gray-800 text-white py-12">
            <div class="container mx-auto px-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Makrite 管理系统</h3>
                        <p class="text-gray-300 mb-4">现代化的企业级管理平台，提供完整的业务流程解决方案。</p>
                        <div class="flex space-x-4">
                            <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                                <span class="text-white font-bold text-sm">M</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-4">功能模块</h3>
                        <ul class="space-y-2 text-gray-300">
                            <li>申请管理系统</li>
                            <li>设备管理系统</li>
                            <li>质量管理系统</li>
                            <li>生产排程管理</li>
                            <li>库存管理系统</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-4">安全架构</h3>
                        <ul class="space-y-2 text-gray-300">
                            <li>🔒 本地主机部署，数据不出内网</li>
                            <li>🛡️ 企业级安全防护体系</li>
                            <li>🔐 JWT + bcrypt 多重认证</li>
                            <li>📋 RBAC 权限管理系统</li>
                            <li>🗄️ 本地SQLite加密存储</li>
                            <li>🚫 零外部依赖，完全离线</li>
                            <li>⚙️ 企业自主运维管控</li>
                            <li>📊 完整审计日志追踪</li>
                        </ul>
                    </div>
                </div>
                <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                    <p>&copy; 2025 Makrite 管理系统. 版本 V2.0</p>
                    <p class="text-sm mt-2">基于 Vue.js 3 + Node.js + SQLite 现代化技术栈构建</p>
                    <p class="text-xs mt-1">🔒 企业级安全 • 🏠 本地主机部署 • 🛡️ 数据自主可控 • ⚙️ 零外部依赖</p>
                </div>
            </div>
        </footer>
    </div>

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/system-overview.js"></script>
</body>
</html>
