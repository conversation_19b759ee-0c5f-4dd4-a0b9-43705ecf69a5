/**
 * 系统管理 - 用户管理页面
 * 管理系统用户账号、权限和角色设置
 */

import { createAdminApp } from '../../common/pageInit.js';
import { createUser, updateUser, getUsers } from '../../api/user.js';
import { getDepartments } from '../../api/department.js';
import Sidebar from '../../../components/common/Sidebar.js';
import UserList from '../../../components/user/UserList.js';
import PermissionManager from '../../../components/user/PermissionManager.js';
import PermissionTemplateManager from '../../../components/user/PermissionTemplateManager.js';
import DepartmentManager from '../../../components/department/DepartmentManager.js';

createAdminApp({
    components: {
        Sidebar,
        UserList,
        PermissionManager,
        PermissionTemplateManager,
        DepartmentManager
    },
    setup() {
        const { ref, reactive, onMounted, watch, computed } = Vue;

        // 页面状态
        const showUserModal = ref(false);
        const isEditing = ref(false);
        const isSubmitting = ref(false);

        // 选项卡状态
        const activeTab = ref('users');

        // 权限管理状态
        const selectedUser = ref(null);
        const usersList = ref([]);
        const isLoadingUsers = ref(false);
        const permissionFilter = ref('all'); // 'all', 'with', 'without'
        const userSearchTerm = ref('');
        const usersPermissionsMap = ref({});

        // 用户表单数据
        const userForm = reactive({
            id: '',
            code: '',
            name: '',
            email: '',
            role: '',
            department: '',
            password: '',
            confirmPassword: '',
            active: true
        });

        // 表单验证状态
        const formErrors = reactive({
            code: '',
            name: '',
            email: '',
            role: '',
            password: '',
            confirmPassword: ''
        });

        // 表单字段是否被触摸过（用于控制错误显示时机）
        const formTouched = reactive({
            code: false,
            name: false,
            email: false,
            role: false,
            password: false,
            confirmPassword: false
        });

        // 角色列表
        const roles = [
            '管理员',
            'CEO',
            '总监',
            '经理',
            '厂长',
            '普通用户'
        ];

        // 部门列表（动态获取）
        const departments = ref([]);

        // 加载部门列表
        async function loadDepartments() {
            try {
                const response = await getDepartments();
                departments.value = response.departments || [];
            } catch (error) {
                console.error('加载部门列表失败:', error);
                // 如果加载失败，使用默认部门列表
                departments.value = [
                    { name: '管理部' },
                    { name: '生产部' },
                    { name: '工程部' },
                    { name: '品管部' },
                    { name: '机电部' },
                    { name: '业务部' },
                    { name: '财务部' },
                    { name: '人事部' },
                    { name: '研发部' },
                    { name: '采购部' },
                    { name: '仓储部' }
                ];
            }
        }

        // 部门更新回调
        function onDepartmentUpdated() {
            loadDepartments();
        }

        // 页面初始化
        onMounted(() => {
            loadDepartments();

            // 监听选项卡变化
            watch(activeTab, (newTab) => {
                if (newTab === 'permissions' && usersList.value.length === 0) {
                    loadUsers();
                }
            });
        });



        // 打开添加用户模态框
        function openAddUserModal() {
            resetForm();
            isEditing.value = false;
            showUserModal.value = true;
        }

        // 打开编辑用户模态框
        function openEditUserModal(user) {
            resetForm();

            // 填充表单数据
            Object.keys(userForm).forEach(key => {
                if (key !== 'password' && key !== 'confirmPassword' && user[key] !== undefined) {
                    userForm[key] = user[key];
                }
            });

            isEditing.value = true;
            showUserModal.value = true;
        }

        // 关闭用户模态框
        function closeUserModal() {
            showUserModal.value = false;
            resetForm();
        }

        // 重置表单
        function resetForm() {
            Object.assign(userForm, {
                id: '',
                code: '',
                name: '',
                email: '',
                role: '',
                department: '',
                password: '',
                confirmPassword: '',
                active: true
            });

            // 重置错误状态
            Object.keys(formErrors).forEach(key => {
                formErrors[key] = '';
            });

            // 重置触摸状态
            Object.keys(formTouched).forEach(key => {
                formTouched[key] = false;
            });
        }

        // 验证表单字段
        function validateField(field) {
            formTouched[field] = true;

            switch (field) {
                case 'name':
                    formErrors.name = !userForm.name ? '姓名不能为空' : '';
                    break;

                case 'role':
                    formErrors.role = !userForm.role ? '请选择角色' : '';
                    break;

                case 'email':
                    if (userForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userForm.email)) {
                        formErrors.email = '请输入有效的电子邮箱';
                    } else {
                        formErrors.email = '';
                    }
                    break;

                case 'password':
                    if (!isEditing.value) {
                        // 创建用户时，密码是必填的
                        formErrors.password = !userForm.password ? '密码不能为空' :
                                             (userForm.password.length < 6 ? '密码长度至少为6个字符' : '');
                    } else if (userForm.password && userForm.password.length < 6) {
                        // 编辑用户时，如果输入了密码，则必须符合长度要求
                        formErrors.password = '密码长度至少为6个字符';
                    } else {
                        formErrors.password = '';
                    }

                    // 如果密码已验证，同时验证确认密码
                    if (formTouched.confirmPassword) {
                        validateField('confirmPassword');
                    }
                    break;

                case 'confirmPassword':
                    if (userForm.password) {
                        // 如果输入了密码，则确认密码必须匹配
                        if (userForm.password !== userForm.confirmPassword) {
                            formErrors.confirmPassword = '两次输入的密码不一致';
                        } else {
                            formErrors.confirmPassword = '';
                        }
                    } else {
                        // 如果没有输入密码，则确认密码不是必填的
                        formErrors.confirmPassword = '';
                    }
                    break;
            }

            return !formErrors[field];
        }

        // 验证整个表单
        function validateForm() {
            // 标记所有字段为已触摸
            Object.keys(formTouched).forEach(field => {
                formTouched[field] = true;
            });

            // 验证所有字段
            const nameValid = validateField('name');
            const roleValid = validateField('role');
            const emailValid = validateField('email');

            let passwordValid = true;
            let confirmPasswordValid = true;

            // 只有在创建用户或修改密码时才验证密码
            if (!isEditing.value || userForm.password) {
                passwordValid = validateField('password');
                confirmPasswordValid = validateField('confirmPassword');
            }

            return nameValid && roleValid && emailValid && passwordValid && confirmPasswordValid;
        }

        // 提交用户表单
        async function submitUserForm() {
            // 表单验证
            if (!validateForm()) {
                return;
            }

            try {
                isSubmitting.value = true;

                // 准备提交数据
                const userData = { ...userForm };

                // 编辑模式下，如果没有输入密码，则不更新密码
                if (isEditing.value && !userData.password) {
                    delete userData.password;
                }

                // 删除确认密码字段
                delete userData.confirmPassword;

                let result;
                if (isEditing.value) {
                    // 更新用户
                    result = await updateUser(userData.id, userData);
                } else {
                    // 创建用户
                    result = await createUser(userData);
                }

                if (result.success) {
                    alert(isEditing.value ? '用户更新成功！' : '用户创建成功！');
                    closeUserModal();
                    // 刷新用户列表
                    window.location.reload();
                } else {
                    alert((isEditing.value ? '更新' : '创建') + '失败: ' + result.message);
                }
            } catch (error) {
                console.error(isEditing.value ? '更新失败:' : '创建失败:', error);
                alert((isEditing.value ? '更新' : '创建') + '失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        // 加载用户列表（用于权限管理）
        async function loadUsers() {
            try {
                isLoadingUsers.value = true;
                const response = await getUsers({
                    page: 1,
                    limit: 100 // 获取足够多的用户
                });
                usersList.value = response.users;

                // 加载每个用户的权限信息
                await loadUsersPermissions();
            } catch (error) {
                console.error('加载用户列表失败:', error);
                alert('加载用户列表失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isLoadingUsers.value = false;
            }
        }

        // 加载所有用户的权限信息
        async function loadUsersPermissions() {
            try {
                // 清空权限映射
                usersPermissionsMap.value = {};

                // 为每个用户加载权限
                for (const user of usersList.value) {
                    try {
                        const { getUserPermissions } = await import('../../../scripts/api/user.js');
                        const result = await getUserPermissions(user.id);

                        if (result.success) {
                            // 存储用户权限
                            usersPermissionsMap.value[user.id] = result.permissions || [];
                        }
                    } catch (error) {
                        console.error(`加载用户 ${user.name} 的权限失败:`, error);
                        // 设置为空数组表示加载失败或无权限
                        usersPermissionsMap.value[user.id] = [];
                    }
                }
            } catch (error) {
                console.error('加载用户权限失败:', error);
            }
        }

        // 检查用户是否有权限设置
        function hasUserPermissions(user) {
            if (!user || !user.id) return false;

            const permissions = usersPermissionsMap.value[user.id];
            return permissions && permissions.length > 0;
        }

        // 筛选后的用户列表
        const filteredUsersList = computed(() => {
            // 先按权限状态筛选
            let filtered = [...usersList.value];

            if (permissionFilter.value === 'with') {
                filtered = filtered.filter(user => hasUserPermissions(user));
            } else if (permissionFilter.value === 'without') {
                filtered = filtered.filter(user => !hasUserPermissions(user));
            }

            // 再按搜索词筛选
            if (userSearchTerm.value) {
                const searchTerm = userSearchTerm.value.toLowerCase();
                filtered = filtered.filter(user =>
                    (user.name && user.name.toLowerCase().includes(searchTerm)) ||
                    (user.role && user.role.toLowerCase().includes(searchTerm))
                );
            }

            return filtered;
        });

        // 选择用户进行权限管理
        function selectUser(user) {
            selectedUser.value = user;
        }

        // 清除选中的用户，返回用户选择页面
        function clearSelectedUser() {
            selectedUser.value = null;
        }

        // 用户权限更新成功
        function onUserUpdated(updatedUser) {
            // 更新用户列表中的用户数据
            const index = usersList.value.findIndex(u => u.id === updatedUser.id);

            if (index !== -1) {
                // 使用Vue 3推荐的方式更新数组元素，确保响应式更新
                usersList.value.splice(index, 1, updatedUser);
            }

            // 更新权限映射
            if (updatedUser.permissions) {
                usersPermissionsMap.value[updatedUser.id] = updatedUser.permissions;
            }

            // 更新选中的用户
            if (selectedUser.value && selectedUser.value.id === updatedUser.id) {
                selectedUser.value = updatedUser;
            }

            // 如果更新的是当前登录用户，需要特殊处理
            const currentUserJson = sessionStorage.getItem('user');
            if (currentUserJson) {
                try {
                    const currentUser = JSON.parse(currentUserJson);
                    if (currentUser.id === updatedUser.id) {
                        console.log('当前登录用户权限已更新，触发全局状态更新');
                        // sessionStorage已经在PermissionManager中更新了，这里只需要确认
                    }
                } catch (error) {
                    console.error('解析当前用户信息失败:', error);
                }
            }
        }

        // 批量更新用户权限成功
        function onBatchUpdated() {
            // 重新加载用户列表以获取最新数据
            loadUsers();
        }

        return {
            showUserModal,
            isEditing,
            isSubmitting,
            userForm,
            formErrors,
            formTouched,
            roles,
            departments,
            activeTab,
            selectedUser,
            usersList,
            filteredUsersList,
            isLoadingUsers,
            permissionFilter,
            userSearchTerm,
            openAddUserModal,
            openEditUserModal,
            closeUserModal,
            submitUserForm,
            validateField,
            selectUser,
            clearSelectedUser,
            onUserUpdated,
            onBatchUpdated,
            onDepartmentUpdated,
            hasUserPermissions
        };
    },
    onUserLoaded: async (user) => {
        console.log('用户管理页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
