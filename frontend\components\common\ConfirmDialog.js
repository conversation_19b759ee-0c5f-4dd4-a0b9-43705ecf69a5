/**
 * 确认对话框组件
 * 用于显示确认提示信息
 */

export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '请注意'
        },
        message: {
            type: String,
            required: true
        },
        confirmText: {
            type: String,
            default: '确认'
        },
        cancelText: {
            type: String,
            default: '返回填写'
        },
        showCancel: {
            type: Boolean,
            default: true
        }
    },
    emits: ['confirm', 'cancel', 'close'],
    setup(props, { emit }) {
        const { ref, watch } = Vue;

        // 对话框引用
        const dialogRef = ref(null);

        // 监听visible变化，处理焦点
        watch(() => props.visible, (newVal) => {
            if (newVal) {
                // 延迟聚焦，确保DOM已更新
                setTimeout(() => {
                    const confirmBtn = dialogRef.value?.querySelector('.confirm-btn');
                    if (confirmBtn) {
                        confirmBtn.focus();
                    }
                }, 100);
            }
        });

        // 确认操作
        function handleConfirm() {
            emit('confirm');
        }

        // 取消操作
        function handleCancel() {
            emit('cancel');
        }

        // 关闭对话框
        function handleClose() {
            emit('close');
        }

        // 处理键盘事件
        function handleKeydown(event) {
            if (event.key === 'Escape') {
                handleCancel();
            } else if (event.key === 'Enter') {
                handleConfirm();
            }
        }

        // 处理背景点击
        function handleBackdropClick(event) {
            if (event.target === event.currentTarget) {
                handleCancel();
            }
        }

        return {
            dialogRef,
            handleConfirm,
            handleCancel,
            handleClose,
            handleKeydown,
            handleBackdropClick
        };
    },
    template: `
        <!-- 对话框遮罩 -->
        <div v-if="visible" 
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
             @click="handleBackdropClick"
             @keydown="handleKeydown"
             tabindex="-1">
            
            <!-- 对话框内容 -->
            <div ref="dialogRef"
                 class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-200 ease-out"
                 @click.stop>
                
                <!-- 对话框头部 -->
                <div class="px-6 pt-6 pb-4">
                    <h3 class="text-xl font-semibold text-gray-900 text-center">
                        {{ title }}
                    </h3>
                </div>
                
                <!-- 对话框内容 -->
                <div class="px-6 pb-6">
                    <div class="text-gray-700 text-center leading-relaxed mb-6" v-html="message">
                    </div>
                    
                    <!-- 按钮组 -->
                    <div class="flex gap-3">
                        <!-- 确认按钮 -->
                        <button @click="handleConfirm"
                                class="confirm-btn flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            {{ confirmText }}
                        </button>
                        
                        <!-- 取消按钮 -->
                        <button v-if="showCancel"
                                @click="handleCancel"
                                class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            {{ cancelText }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `
};
