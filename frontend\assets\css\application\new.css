/* 申请管理模块 - 新建申请页面样式文件 */

/* 文件输入区域悬停效果 */
#fileInputArea:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

/* 标签页激活状态 */
.tab-active {
    border-bottom: 2px solid #3b82f6;
    color: #3b82f6;
}

/* 工具提示容器 */
.tooltip-container {
    position: relative;
}

/* 工具提示内容 */
.tooltip-content {
    visibility: hidden;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.group:hover .tooltip-content {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .ml-72 {
        margin-left: 0;
    }
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    .sidebar.open {
        transform: translateX(0);
    }
    .mobile-menu-btn {
        display: block;
    }
}

/* 表单样式增强 */
input:focus, select:focus, textarea:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* 卡片悬停效果 */
.card-hover {
    transition: all 0.2s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
