/**
 * 认证路由
 * 处理用户认证相关的路由
 */

const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { authenticateJWT, authorizeRoles } = require('../middlewares/auth');

// 用户登录
router.post('/login', authController.login);

// 获取当前用户信息
router.get('/me', authenticateJWT, authController.getCurrentUser);

// 注册新用户 (仅管理员可用)
router.post('/register', authenticateJWT, authorizeRoles('admin'), authController.register);

module.exports = router;
