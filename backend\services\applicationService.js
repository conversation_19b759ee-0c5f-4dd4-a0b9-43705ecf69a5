/**
 * 申请服务
 * 处理申请相关的业务逻辑
 * 使用SQLite数据库
 */

const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const { readJsonFile, writeJsonFile, deleteFile } = require('../utils/fileSystem');
const userService = require('./userService');
const emailService = require('./emailService');
const logger = require('../utils/logger');
const applicationRepository = require('../database/applicationRepository');

/**
 * 读取所有申请
 * @returns {Array} 申请列表
 */
function getApplications() {
    try {
        return applicationRepository.findAll();
    } catch (error) {
        logger.error('获取申请列表失败:', error);
        return [];
    }
}

/**
 * 保存申请数据（保留用于兼容性）
 * @param {Array} applications - 申请列表
 * @returns {boolean} 是否成功
 */
function saveApplications(applications) {
    logger.warn('saveApplications方法已废弃，请使用具体的创建/更新方法');
    return true; // 保持兼容性
}

/**
 * 根据ID查找申请
 * @param {string} id - 申请ID
 * @returns {Object|null} 申请对象或null
 */
function getApplicationById(id) {
    try {
        const application = applicationRepository.findById(id);
        if (!application) return null;

        // 为申请书模板组织审批数据
        application.approvals = organizeApprovalsForTemplate(application);

        return application;
    } catch (error) {
        logger.error(`根据ID查找申请失败 (${id}):`, error);
        return null;
    }
}

/**
 * 为申请书模板组织审批数据
 * @param {Object} application - 申请对象
 * @returns {Object} 组织后的审批数据
 */
function organizeApprovalsForTemplate(application) {
    const approvals = {
        directors: {},
        chief: null,
        managers: {},
        ceo: null
    };

    if (!application.approvalHistory || application.approvalHistory.length === 0) {
        return approvals;
    }

    // 遍历审批历史，按角色和阶段组织数据
    application.approvalHistory.forEach(history => {
        const approvalData = {
            status: history.action === 'approve' ? 'approved' : 'rejected',
            comment: history.comment || '',
            date: history.timestamp,
            approverName: history.approverName,
            signature: history.signaturePath ? `/${history.signaturePath}` : null
        };

        // 根据审批阶段和角色分类
        switch (history.stage) {
            case 'factory_manager':
                // 厂长审批
                approvals.directors[history.approverId] = approvalData;
                break;
            case 'director':
                // 总监审批
                approvals.chief = approvalData;
                break;
            case 'manager':
                // 经理审批
                approvals.managers[history.approverId] = approvalData;
                break;
            case 'ceo':
                // CEO审批
                approvals.ceo = approvalData;
                break;
        }
    });

    return approvals;
}

/**
 * 创建新申请
 * @param {Object} applicationData - 申请数据
 * @param {Array} attachments - 附件列表
 * @param {Object} user - 当前用户
 * @returns {Promise<Object>} 创建的申请
 */
async function createApplication(applicationData, attachments, user) {
    try {
        // 处理附件
        const processedAttachments = attachments.map(file => ({
            id: uuidv4(),
            name: file.originalname,
            path: file.path,
            filename: file.filename,
            type: file.mimetype,
            size: file.size,
            uploadedAt: new Date().toISOString()
        }));

        // 生成申请编号 (YYYYMMDD-NNN)
        const today = new Date();
        const dateStr = today.getFullYear() +
            String(today.getMonth() + 1).padStart(2, '0') +
            String(today.getDate()).padStart(2, '0');

        // 查找今天的申请数量
        const todayCount = applicationRepository.getTodayApplicationCount();
        const sequence = String(todayCount + 1).padStart(3, '0');
        const applicationNumber = `${dateStr}-${sequence}`;

        // 确定申请类型和初始审批阶段
        const applicationType = applicationData.type || 'standard'; // 标准申请或其他申请
        const initialStage = applicationType === 'standard' ? 'factory_manager' :
                            (applicationData.needDirectorApproval ? 'director' : 'ceo');

        // 创建新申请
        const newApplication = {
            id: uuidv4(),
            applicationNumber,
            userId: user.id,
            applicant: applicationData.applicant,
            department: applicationData.department || '',
            date: applicationData.date,
            content: applicationData.content,
            amount: applicationData.amount || '', // 添加申请金额字段
            priority: applicationData.priority || 'normal',
            type: applicationType,
            attachments: processedAttachments,
            status: 'pending',
            currentStage: initialStage,
            needManagerApproval: false, // 总监审批通过后是否需要经理审批
            needCeoApproval: applicationType === 'standard' ? true : (applicationData.needDirectorApproval ? true : true), // 是否需要CEO审批
            selectedFactoryManagers: applicationData.selectedFactoryManagers || [], // 保存选中的厂长信息
            selectedManagers: [], // 保存选中的经理信息（由总监在审批时选择）
            approvalHistory: [], // 初始化审批历史记录
            pdfPath: applicationData.pdfPath || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // 保存到数据库
        const createdApplication = applicationRepository.create(newApplication);

        // 异步发送申请提交通知邮件（不阻塞响应）
        setImmediate(async () => {
            try {
                await emailService.sendApplicationSubmittedNotification(createdApplication);
            } catch (error) {
                logger.error('发送申请提交通知邮件失败', {
                    error: error.message,
                    applicationId: createdApplication.id
                });
                // 邮件发送失败不影响申请创建，会在后台继续重试
            }
        });

        return createdApplication;
    } catch (error) {
        logger.error('创建申请失败:', error);
        throw error;
    }
}

/**
 * 检查用户是否可以修改或删除申请
 * @param {Object} application - 申请对象
 * @param {Object} user - 当前用户
 * @param {string} action - 操作类型 ('edit' 或 'delete')
 * @returns {boolean} 是否可以执行操作
 */
function canUserModifyOrDelete(application, user, action) {
    const role = user.role.toLowerCase();

    // 管理员不受限制
    if (role === 'admin' || user.role === '管理员') {
        return true;
    }

    // 只有申请创建者可以修改/删除自己的申请
    if (application.userId !== user.id) {
        return false;
    }

    // 检查申请是否已被审批过
    if (application.approvalHistory && application.approvalHistory.length > 0) {
        // 如果有审批历史，说明已经有人审批过，不能修改/删除
        return false;
    }

    return true;
}

/**
 * 删除申请
 * @param {string} id - 申请ID
 * @param {Object} user - 当前用户
 * @returns {boolean} 是否成功
 */
function deleteApplication(id, user) {
    try {
        // 静默开始删除操作
        const application = applicationRepository.findById(id);

        if (!application) {
            throw new Error('申请不存在');
        }

        // 检查权限：使用新的权限检查函数
        const canDelete = canUserModifyOrDelete(application, user, 'delete');

        if (!canDelete) {
            if (application.userId !== user.id) {
                throw new Error('没有权限删除此申请');
            } else {
                throw new Error('申请已被审批，无法删除');
            }
        }

        // 删除相关文件
        if (application.attachments && application.attachments.length > 0) {
            application.attachments.forEach(attachment => {
                deleteFile(attachment.path);
            });
        }

        // 从数据库中删除
        const deleteResult = applicationRepository.delete(id);

        return deleteResult;
    } catch (error) {
        logger.error(`删除申请失败 (${id}):`, error);
        throw error;
    }
}

/**
 * 修改申请
 * @param {string} id - 申请ID
 * @param {Object} updateData - 更新数据
 * @param {Object} user - 当前用户
 * @param {Array} files - 新上传的文件（可选）
 * @returns {Object} 更新后的申请
 */
function updateApplication(id, updateData, user, files = []) {
    try {
        const application = applicationRepository.findById(id);

        if (!application) {
            throw new Error('申请不存在');
        }

        // 检查权限：使用新的权限检查函数
        if (!canUserModifyOrDelete(application, user, 'edit')) {
            if (application.userId !== user.id) {
                throw new Error('没有权限修改此申请');
            } else {
                throw new Error('申请已被审批，无法修改');
            }
        }

        // 更新申请数据 - 扩展允许修改的字段
        const allowedFields = [
            'content', 'amount', 'priority', 'date', 'applicant', 'department',
            'type', 'needDirectorApproval', 'selectedFactoryManagers'
        ];

        const updatedData = { ...application };
        allowedFields.forEach(field => {
            if (updateData[field] !== undefined) {
                updatedData[field] = updateData[field];
            }
        });

        // 处理附件更新 - 如果有新文件上传，则覆盖旧附件
        if (files && files.length > 0) {
            // 删除旧附件文件（如果存在）
            if (application.attachments && application.attachments.length > 0) {
                const fs = require('fs');
                const path = require('path');

                application.attachments.forEach(attachment => {
                    try {
                        if (fs.existsSync(attachment.path)) {
                            fs.unlinkSync(attachment.path);
                        }
                    } catch (error) {
                        logger.error('删除旧附件文件失败:', error);
                    }
                });
            }

            // 添加新附件
            updatedData.attachments = files.map(file => ({
                id: require('uuid').v4(),
                name: file.originalname,
                path: file.path,
                filename: file.filename,
                type: file.mimetype,
                size: file.size,
                uploadedAt: new Date().toISOString()
            }));
        }

        // 验证标准申请必须选择厂长
        if (updatedData.type === 'standard' &&
            (!updatedData.selectedFactoryManagers || updatedData.selectedFactoryManagers.length === 0)) {
            throw new Error('标准申请必须选择至少一个厂长');
        }

        // 保存更新到数据库
        return applicationRepository.update(id, updatedData);
    } catch (error) {
        logger.error(`更新申请失败 (${id}):`, error);
        throw error;
    }
}

/**
 * 查找附件
 * @param {string} attachmentId - 附件ID
 * @returns {Object|null} 附件对象和所属申请，或null
 */
function findAttachment(attachmentId) {
    try {
        const attachment = applicationRepository.findAttachmentById(attachmentId);
        if (!attachment) {
            return null;
        }

        // 获取所属申请
        const application = applicationRepository.findById(attachment.application_id);

        return {
            attachment: {
                id: attachment.id,
                name: attachment.name,
                path: attachment.path,
                filename: attachment.filename,
                type: attachment.type,
                size: attachment.size,
                uploadedAt: attachment.uploaded_at
            },
            application
        };
    } catch (error) {
        logger.error(`查找附件失败 (${attachmentId}):`, error);
        return null;
    }
}

/**
 * 获取待审核申请
 * @param {Object} user - 当前用户
 * @returns {Array} 待审核申请列表
 */
function getPendingApplications(user) {
    const applications = getApplications();
    const role = user.role.toLowerCase();

    // 根据用户角色筛选待审核申请
    const filteredApplications = applications.filter(app => {
        // 只返回状态为pending的申请
        if (app.status !== 'pending') return false;

        // 管理员可以查看所有待审核申请（但不能审批）
        if (role === 'admin' || user.role === '管理员') {
            return true;
        }

        // 根据当前审批阶段和用户角色判断
        switch (app.currentStage) {
            case 'factory_manager':
                // 厂长审批阶段：只有被选中的厂长才能看到
                if (role === 'factory_manager' || user.role === '厂长') {
                    if (app.selectedFactoryManagers && app.selectedFactoryManagers.length > 0) {
                        return app.selectedFactoryManagers.some(manager => manager.id === user.id);
                    }
                    // 兼容旧数据
                    return true;
                }
                return false;
            case 'director':
                return role === 'director' || user.role === '总监';
            case 'manager':
                // 经理审批阶段：只有被选中的经理才能看到
                if (role === 'manager' || user.role === '经理') {
                    if (app.selectedManagers && app.selectedManagers.length > 0) {
                        return app.selectedManagers.some(manager => manager.id === user.id);
                    }
                    // 兼容旧数据
                    return true;
                }
                return false;
            case 'ceo':
                return role === 'ceo' || user.role === 'CEO';
            default:
                return false;
        }
    });

    // 为待审核申请添加待审批用户信息
    return addPendingApproversToApplications(filteredApplications);
}

/**
 * 获取已审核申请
 * @param {Object} user - 当前用户
 * @returns {Array} 已审核申请列表
 */
function getApprovedApplications(user) {
    const applications = getApplications();
    const role = user.role.toLowerCase();

    let filteredApplications;
    // 管理员可以看到所有已审核申请（包括完全完成和部分完成的）
    if (role === 'admin' || user.role === '管理员') {
        filteredApplications = applications.filter(app => {
            // 管理员可以看到：
            // 1. 完全完成的申请（approved/rejected）
            // 2. 有审批历史记录的申请（表示已经有人审批过）
            return app.status === 'approved' ||
                   app.status === 'rejected' ||
                   (app.approvalHistory && app.approvalHistory.length > 0);
        });
    } else {
        // 审批人只能看到自己已经审批过的申请
        filteredApplications = applications.filter(app => {
            // 检查当前用户是否已经审批过这个申请
            if (app.approvalHistory && app.approvalHistory.length > 0) {
                return app.approvalHistory.some(history => history.approverId === user.id);
            }
            return false;
        });
    }

    // 为已审核申请添加审批状态信息
    return addPendingApproversToApplications(filteredApplications);
}

/**
 * 获取用户的申请记录
 * @param {Object} user - 当前用户
 * @returns {Array} 申请记录列表
 */
function getApplicationRecords(user) {
    const applications = getApplications();
    const role = user.role.toLowerCase();

    let filteredApplications;
    // 管理员可以看到所有申请
    if (role === 'admin' || user.role === '管理员') {
        filteredApplications = applications;
    } else {
        // 其他用户只能看到自己创建的申请
        filteredApplications = applications.filter(app => app.userId === user.id);
    }

    // 为申请记录添加待审批用户信息和操作权限信息
    const applicationsWithApprovers = addPendingApproversToApplications(filteredApplications);

    // 添加操作权限信息
    return applicationsWithApprovers.map(app => ({
        ...app,
        canEdit: canUserModifyOrDelete(app, user, 'edit'),
        canDelete: canUserModifyOrDelete(app, user, 'delete')
    }));
}

/**
 * 审批通过申请
 * @param {string} id - 申请ID
 * @param {Object} approvalData - 审批数据
 * @param {Object} user - 当前用户
 * @returns {Promise<Object>} 更新后的申请
 */
async function approveApplication(id, approvalData, user) {
    const application = applicationRepository.findById(id);

    if (!application) {
        throw new Error('申请不存在');
    }

    // 检查申请状态
    if (application.status !== 'pending') {
        throw new Error('该申请已完成审批');
    }

    // 检查当前用户是否有权限审批
    const role = user.role.toLowerCase();
    if (!canUserApprove(application, user)) {
        // 特殊处理：如果是管理员，提供更明确的错误信息
        if (role === 'admin' || user.role === '管理员') {
            throw new Error('管理员不参与审批流程，只有查看和管理权限');
        }
        throw new Error('没有权限审批此申请');
    }

    // 记录审批历史
    const approvalRecord = {
        stage: application.currentStage,
        approverId: user.id,
        approverName: user.username,
        approverRole: user.role,
        action: 'approve',
        comment: approvalData.comment || '',
        signaturePath: user.signaturePath || '',
        timestamp: new Date().toISOString()
    };

    if (!application.approvalHistory) {
        application.approvalHistory = [];
    }

    application.approvalHistory.push(approvalRecord);

    // 更新申请状态和阶段
    const nextStage = getNextStage(application, approvalData, user);

    if (nextStage === 'completed') {
        // 审批完成
        application.status = 'approved';
        application.currentStage = 'completed';
    } else {
        // 进入下一阶段
        application.currentStage = nextStage;

        // 如果总监选择了需要经理审批，保存选中的经理信息
        if (application.currentStage === 'manager' && approvalData.selectedManagers) {
            application.needManagerApproval = true;
            application.selectedManagers = approvalData.selectedManagers;
        }

        // 如果是总监审批，保存是否需要CEO审批的选择
        if (application.currentStage === 'director' && approvalData.hasOwnProperty('needCeoApproval')) {
            application.needCeoApproval = approvalData.needCeoApproval;
        }
    }

    application.updatedAt = new Date().toISOString();

    // 保存更新到数据库
    const updatedApplication = applicationRepository.update(id, application);

    // 异步发送审批状态变更通知邮件（不阻塞响应）
    setImmediate(async () => {
        try {
            await emailService.sendApprovalStatusNotification(application, user, approvalData.comment);

            // 如果申请完成且金额超过100,000，发送高金额通知
            if (application.status === 'approved' && application.currentStage === 'completed') {
                const amount = parseFloat(application.amount);
                if (!isNaN(amount) && amount > 100000) {
                    await emailService.sendHighAmountNotification(application);
                }
            }
        } catch (error) {
            logger.error('发送审批状态变更通知邮件失败', {
                error: error.message,
                applicationId: application.id
            });
            // 邮件发送失败不影响审批流程，会在后台继续重试
        }
    });

    return updatedApplication;
}

/**
 * 审批拒绝申请
 * @param {string} id - 申请ID
 * @param {Object} rejectionData - 拒绝数据
 * @param {Object} user - 当前用户
 * @returns {Promise<Object>} 更新后的申请
 */
async function rejectApplication(id, rejectionData, user) {
    const application = applicationRepository.findById(id);

    if (!application) {
        throw new Error('申请不存在');
    }

    // 检查申请状态
    if (application.status !== 'pending') {
        throw new Error('该申请已完成审批');
    }

    // 检查当前用户是否有权限审批
    if (!canUserApprove(application, user)) {
        // 特殊处理：如果是管理员，提供更明确的错误信息
        const role = user.role.toLowerCase();
        if (role === 'admin' || user.role === '管理员') {
            throw new Error('管理员不参与审批流程，只有查看和管理权限');
        }
        throw new Error('没有权限审批此申请');
    }

    // 记录审批历史
    const rejectionRecord = {
        stage: application.currentStage,
        approverId: user.id,
        approverName: user.username,
        approverRole: user.role,
        action: 'reject',
        comment: rejectionData.comment || '',
        signaturePath: user.signaturePath || '',
        timestamp: new Date().toISOString()
    };

    if (!application.approvalHistory) {
        application.approvalHistory = [];
    }

    application.approvalHistory.push(rejectionRecord);

    // 更新申请状态
    application.status = 'rejected';
    application.currentStage = 'rejected';
    application.updatedAt = new Date().toISOString();

    // 保存更新到数据库
    const updatedApplication = applicationRepository.update(id, application);

    // 异步发送申请拒绝通知邮件（不阻塞响应）
    setImmediate(async () => {
        try {
            await emailService.sendApplicationRejectedNotification(application, user, rejectionData.comment);
        } catch (error) {
            logger.error('发送申请拒绝通知邮件失败', {
                error: error.message,
                applicationId: application.id
            });
            // 邮件发送失败不影响审批流程，会在后台继续重试
        }
    });

    return updatedApplication;
}

/**
 * 检查用户是否有权限审批申请
 * @param {Object} application - 申请对象
 * @param {Object} user - 当前用户
 * @returns {boolean} 是否有权限
 */
function canUserApprove(application, user) {
    const role = user.role.toLowerCase();

    // 管理员不参与审批流程，只有管理权限
    if (role === 'admin' || user.role === '管理员') {
        return false;
    }

    // 根据当前审批阶段和用户角色判断
    switch (application.currentStage) {
        case 'factory_manager':
            // 厂长审批阶段：只有被选中的厂长才能审批
            if (role === 'factory_manager' || user.role === '厂长') {
                // 检查是否在选中的厂长列表中
                if (application.selectedFactoryManagers && application.selectedFactoryManagers.length > 0) {
                    return application.selectedFactoryManagers.some(manager => manager.id === user.id);
                }
                // 如果没有选中厂长信息（兼容旧数据），允许所有厂长审批
                return true;
            }
            return false;
        case 'director':
            return role === 'director' || user.role === '总监';
        case 'manager':
            // 经理审批阶段：只有被选中的经理才能审批
            if (role === 'manager' || user.role === '经理') {
                // 检查是否在选中的经理列表中
                if (application.selectedManagers && application.selectedManagers.length > 0) {
                    return application.selectedManagers.some(manager => manager.id === user.id);
                }
                // 如果没有选中经理信息（兼容旧数据），允许所有经理审批
                return true;
            }
            return false;
        case 'ceo':
            return role === 'ceo' || user.role === 'CEO';
        default:
            return false;
    }
}

/**
 * 检查所有选中的厂长是否都已审批完成
 * @param {Object} application - 申请对象
 * @returns {boolean} 是否所有厂长都已审批完成
 */
function areAllFactoryManagersApproved(application) {
    if (!application.selectedFactoryManagers || application.selectedFactoryManagers.length === 0) {
        return true; // 没有选中厂长，视为已完成
    }

    if (!application.approvalHistory || application.approvalHistory.length === 0) {
        return false; // 没有审批历史，肯定未完成
    }

    // 获取所有厂长审批阶段的审批记录
    const factoryManagerApprovals = application.approvalHistory.filter(
        history => history.stage === 'factory_manager' && history.action === 'approve'
    );

    // 检查每个选中的厂长是否都有审批记录
    const selectedFactoryManagerIds = application.selectedFactoryManagers.map(manager => manager.id);
    const approvedFactoryManagerIds = factoryManagerApprovals.map(approval => approval.approverId);

    return selectedFactoryManagerIds.every(managerId => approvedFactoryManagerIds.includes(managerId));
}

/**
 * 检查所有选中的经理是否都已审批完成
 * @param {Object} application - 申请对象
 * @returns {boolean} 是否所有经理都已审批完成
 */
function areAllManagersApproved(application) {
    if (!application.selectedManagers || application.selectedManagers.length === 0) {
        return true; // 没有选中经理，视为已完成
    }

    if (!application.approvalHistory || application.approvalHistory.length === 0) {
        return false; // 没有审批历史，肯定未完成
    }

    // 获取所有经理审批阶段的审批记录
    const managerApprovals = application.approvalHistory.filter(
        history => history.stage === 'manager' && history.action === 'approve'
    );

    // 检查每个选中的经理是否都有审批记录
    const selectedManagerIds = application.selectedManagers.map(manager => manager.id);
    const approvedManagerIds = managerApprovals.map(approval => approval.approverId);

    return selectedManagerIds.every(managerId => approvedManagerIds.includes(managerId));
}

/**
 * 获取下一个审批阶段
 * @param {Object} application - 申请对象
 * @param {Object} approvalData - 审批数据
 * @param {Object} user - 当前用户
 * @returns {string} 下一个审批阶段
 */
function getNextStage(application, approvalData, user) {
    // 标准申请流程: 厂长 -> 总监 -> 经理(可选) -> CEO
    // 其他申请流程: 总监(可选) -> CEO

    if (application.type === 'standard') {
        switch (application.currentStage) {
            case 'factory_manager':
                // 检查是否所有选中的厂长都已审批完成
                if (areAllFactoryManagersApproved(application)) {
                    return 'director';
                } else {
                    // 还有厂长未审批，保持在厂长审批阶段
                    return 'factory_manager';
                }
            case 'director':
                // 总监可以选择是否需要经理审批，如果选择了经理则进入经理审批阶段
                return (approvalData.selectedManagers && approvalData.selectedManagers.length > 0) ? 'manager' : 'ceo';
            case 'manager':
                // 检查是否所有选中的经理都已审批完成
                if (areAllManagersApproved(application)) {
                    return 'ceo';
                } else {
                    // 还有经理未审批，保持在经理审批阶段
                    return 'manager';
                }
            case 'ceo':
                return 'completed';
            default:
                return application.currentStage;
        }
    } else {
        // 其他申请
        switch (application.currentStage) {
            case 'director':
                // 总监可以选择是否需要CEO审批
                if (approvalData.needCeoApproval === false) {
                    return 'completed'; // 总监直接完成申请
                } else {
                    return 'ceo'; // 流转到CEO审批
                }
            case 'ceo':
                return 'completed';
            default:
                return application.currentStage;
        }
    }
}

/**
 * 获取当前阶段的待审批用户
 * @param {string} currentStage - 当前审批阶段
 * @param {Object} application - 申请对象（可选，用于厂长阶段获取选中的厂长）
 * @returns {Array} 待审批用户列表
 */
function getPendingApprovers(currentStage, application = null) {
    try {
        const users = userService.readUsers();

        // 根据当前阶段确定需要的角色
        switch (currentStage) {
            case 'factory_manager':
                // 厂长审批阶段：返回选中的厂长
                if (application && application.selectedFactoryManagers && application.selectedFactoryManagers.length > 0) {
                    // 返回选中的厂长，但需要验证这些用户仍然存在且活跃
                    return application.selectedFactoryManagers
                        .map(selectedManager => {
                            const user = users.find(u => u.id === selectedManager.id && u.active === true);
                            return user ? {
                                id: user.id,
                                name: user.username,
                                role: user.role
                            } : null;
                        })
                        .filter(user => user !== null);
                } else {
                    // 兼容旧数据：如果没有选中厂长信息，返回所有厂长
                    return users
                        .filter(user => user.role === '厂长' && user.active === true)
                        .map(user => ({
                            id: user.id,
                            name: user.username,
                            role: user.role
                        }));
                }
            case 'director':
                return users
                    .filter(user => user.role === '总监' && user.active === true)
                    .map(user => ({
                        id: user.id,
                        name: user.username,
                        role: user.role
                    }));
            case 'manager':
                // 经理审批阶段：返回选中的经理
                if (application && application.selectedManagers && application.selectedManagers.length > 0) {
                    // 返回选中的经理，但需要验证这些用户仍然存在且活跃
                    return application.selectedManagers
                        .map(selectedManager => {
                            const user = users.find(u => u.id === selectedManager.id && u.active === true);
                            return user ? {
                                id: user.id,
                                name: user.username,
                                role: user.role
                            } : null;
                        })
                        .filter(user => user !== null);
                } else {
                    // 兼容旧数据：如果没有选中经理信息，返回所有经理
                    return users
                        .filter(user => user.role === '经理' && user.active === true)
                        .map(user => ({
                            id: user.id,
                            name: user.username,
                            role: user.role
                        }));
                }
            case 'ceo':
                return users
                    .filter(user => user.role === 'CEO' && user.active === true)
                    .map(user => ({
                        id: user.id,
                        name: user.username,
                        role: user.role
                    }));
            default:
                return [];
        }
    } catch (error) {
        logger.error('获取待审批用户失败:', error);
        return [];
    }
}

/**
 * 为申请记录添加待审批用户信息
 * @param {Array} applications - 申请列表
 * @returns {Array} 包含待审批用户信息的申请列表
 */
function addPendingApproversToApplications(applications) {
    return applications.map(app => {
        // 只为待审批状态的申请添加待审批用户信息
        if (app.status === 'pending' && app.currentStage !== 'completed') {
            const pendingApprovers = getPendingApprovers(app.currentStage, app);
            return {
                ...app,
                pendingApprovers
            };
        }
        return {
            ...app,
            pendingApprovers: []
        };
    });
}

module.exports = {
    getApplications,
    saveApplications,
    getApplicationById,
    createApplication,
    updateApplication,
    deleteApplication,
    findAttachment,
    getPendingApplications,
    getApprovedApplications,
    getApplicationRecords,
    approveApplication,
    rejectApplication,
    canUserApprove,
    canUserModifyOrDelete,
    getPendingApprovers,
    addPendingApproversToApplications
};
