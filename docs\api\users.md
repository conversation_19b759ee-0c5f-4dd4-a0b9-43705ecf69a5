# 用户管理 API 文档

## 📋 概述

用户管理模块提供完整的用户生命周期管理，包括用户CRUD操作、权限管理、电子签名管理等功能。

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 查看用户列表 | `view_users` |
| 创建用户 | `create_user` |
| 编辑用户 | `edit_user` |
| 删除用户 | `delete_user` |
| 管理权限 | `manage_permissions` |

## 👥 API 接口

### 1. 获取用户列表

**GET** `/api/users`

获取用户列表，支持分页、搜索和筛选。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |
| search | string | 否 | 搜索关键词（用户名、姓名） |
| role | string | 否 | 角色筛选 |
| department | string | 否 | 部门筛选 |
| active | boolean | 否 | 状态筛选 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "user123",
      "username": "zhangsan",
      "usercode": "ZS001",
      "role": "user",
      "department": "技术部",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "active": true,
      "hasSignature": true,
      "permissions": ["new_application", "application_record"],
      "createdAt": "2025-01-01T00:00:00.000Z",
      "lastLogin": "2025-07-29T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

### 2. 获取用户详情

**GET** `/api/users/:id`

获取指定用户的详细信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 用户ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "user123",
    "username": "zhangsan",
    "usercode": "ZS001",
    "role": "user",
    "department": "技术部",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "active": true,
    "hasSignature": true,
    "signaturePath": "/uploads/signatures/ZS001_20250729.jpg",
    "permissions": ["new_application", "application_record"],
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-07-29T10:00:00.000Z",
    "lastLogin": "2025-07-29T10:30:00.000Z"
  }
}
```

### 3. 创建用户

**POST** `/api/users`

创建新用户。

#### 请求参数

```json
{
  "username": "lisi",
  "usercode": "LS001",
  "password": "123456",
  "role": "user",
  "department": "销售部",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "permissions": ["new_application", "application_record"]
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名（唯一） |
| usercode | string | 是 | 用户代码（唯一） |
| password | string | 是 | 密码 |
| role | string | 是 | 角色 |
| department | string | 否 | 部门 |
| email | string | 否 | 邮箱 |
| phone | string | 否 | 电话 |
| permissions | array | 否 | 权限数组 |

#### 响应示例

**成功响应 (201)**:
```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "id": "user124",
    "username": "lisi",
    "usercode": "LS001",
    "role": "user",
    "active": true,
    "createdAt": "2025-07-29T11:00:00.000Z"
  }
}
```

### 4. 更新用户

**PUT** `/api/users/:id`

更新用户信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 用户ID |

#### 请求参数

```json
{
  "username": "lisi_updated",
  "role": "manager",
  "department": "销售部",
  "email": "<EMAIL>",
  "phone": "13900139001",
  "active": true
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "用户更新成功",
  "data": {
    "id": "user124",
    "username": "lisi_updated",
    "role": "manager",
    "updatedAt": "2025-07-29T11:30:00.000Z"
  }
}
```

### 5. 删除用户

**DELETE** `/api/users/:id`

删除用户（软删除，设置为非活跃状态）。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 用户ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "用户删除成功"
}
```

### 6. 获取用户权限

**GET** `/api/users/:id/permissions`

获取用户的权限列表。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "userId": "user123",
    "permissions": [
      "new_application",
      "application_record",
      "pending_approval"
    ],
    "role": "user",
    "inheritedPermissions": []
  }
}
```

### 7. 更新用户权限

**PUT** `/api/users/:id/permissions`

更新用户的权限。

#### 请求参数

```json
{
  "permissions": [
    "new_application",
    "application_record",
    "pending_approval",
    "approved_applications"
  ]
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "权限更新成功",
  "data": {
    "userId": "user123",
    "permissions": [
      "new_application",
      "application_record",
      "pending_approval",
      "approved_applications"
    ]
  }
}
```

## 🖋️ 电子签名管理

### 8. 获取用户电子签名

**GET** `/api/users/:id/signature`

获取用户的电子签名（所有认证用户都可访问）。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "hasSignature": true,
  "signaturePath": "/uploads/signatures/ZS001_20250729.jpg",
  "signatureBase64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

**无签名响应 (200)**:
```json
{
  "success": true,
  "hasSignature": false,
  "message": "用户暂未上传电子签名"
}
```

### 9. 上传用户电子签名

**POST** `/api/users/:id/signature`

上传用户的电子签名图片。

#### 请求格式
- **Content-Type**: `multipart/form-data`
- **文件字段**: `signature`
- **支持格式**: JPG, PNG, GIF
- **文件大小**: 最大2MB

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "电子签名上传成功",
  "data": {
    "signaturePath": "/uploads/signatures/ZS001_20250729.jpg",
    "hasSignature": true
  }
}
```

### 10. 删除用户电子签名

**DELETE** `/api/users/:id/signature`

删除用户的电子签名。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "电子签名删除成功"
}
```

## 👥 特殊用户接口

### 11. 获取厂长列表

**GET** `/api/users/factory-managers`

获取所有厂长用户列表（用于申请创建时选择审批人）。

#### 权限要求
- `view_users` 或 `new_application`

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "fm001",
      "username": "厂长A",
      "usercode": "FM001",
      "department": "生产部",
      "hasSignature": true
    },
    {
      "id": "fm002",
      "username": "厂长B",
      "usercode": "FM002",
      "department": "制造部",
      "hasSignature": false
    }
  ]
}
```

### 12. 获取经理列表

**GET** `/api/users/managers`

获取所有经理用户列表。

#### 权限要求
- `view_users` 或 `new_application`

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "mgr001",
      "username": "经理A",
      "usercode": "MGR001",
      "department": "技术部",
      "hasSignature": true
    }
  ]
}
```

### 13. 检查用户删除状态

**GET** `/api/users/:id/deletion-status`

检查用户是否可以被删除。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "canDelete": false,
  "reason": "用户有未完成的申请，无法删除",
  "relatedData": {
    "pendingApplications": 2,
    "approvedApplications": 15
  }
}
```

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| USER_NOT_FOUND | 404 | 用户不存在 |
| USERNAME_EXISTS | 409 | 用户名已存在 |
| USERCODE_EXISTS | 409 | 用户代码已存在 |
| INVALID_ROLE | 400 | 无效的角色 |
| PERMISSION_DENIED | 403 | 权限不足 |
| SIGNATURE_UPLOAD_ERROR | 400 | 签名上传失败 |
| SIGNATURE_NOT_FOUND | 404 | 签名不存在 |

### 用户角色说明

| 角色 | 描述 | 默认权限 |
|------|------|----------|
| admin | 系统管理员 | 所有权限 |
| 厂长 | 工厂厂长 | 审批相关权限 |
| 总监 | 部门总监 | 审批相关权限 |
| 经理 | 部门经理 | 审批相关权限 |
| CEO | 首席执行官 | 最终审批权限 |
| user | 普通用户 | 基础操作权限 |

---

**更新时间**: 2025-07-29
**版本**: v1.0
