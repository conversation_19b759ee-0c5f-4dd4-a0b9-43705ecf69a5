/**
 * 质量管理文件上传中间件
 * 处理检测报告文件上传配置
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const config = require('../config');

// 创建质量报告文件存储目录
function createQualityReportDirectory() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    // 构建目录路径: uploads/quality-reports/YYYY/MM/DD
    const qualityReportsDir = path.join(config.paths.uploads, 'quality-reports');
    const yearDir = path.join(qualityReportsDir, String(year));
    const monthDir = path.join(yearDir, month);
    const dayDir = path.join(monthDir, day);
    
    // 确保目录存在
    if (!fs.existsSync(qualityReportsDir)) {
        fs.mkdirSync(qualityReportsDir, { recursive: true });
    }
    if (!fs.existsSync(yearDir)) {
        fs.mkdirSync(yearDir, { recursive: true });
    }
    if (!fs.existsSync(monthDir)) {
        fs.mkdirSync(monthDir, { recursive: true });
    }
    if (!fs.existsSync(dayDir)) {
        fs.mkdirSync(dayDir, { recursive: true });
    }
    
    return dayDir;
}

// 生成唯一文件名（保持原始文件名，但处理重名问题）
function generateUniqueFilename(directory, originalName) {
    const ext = path.extname(originalName);
    const nameWithoutExt = path.basename(originalName, ext);
    
    let filename = originalName;
    let counter = 1;
    
    // 如果文件已存在，添加数字后缀
    while (fs.existsSync(path.join(directory, filename))) {
        filename = `${nameWithoutExt}(${counter})${ext}`;
        counter++;
    }
    
    return filename;
}

// 配置质量报告文件存储
const qualityStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        try {
            const uploadDir = createQualityReportDirectory();
            cb(null, uploadDir);
        } catch (error) {
            cb(error);
        }
    },
    filename: function (req, file, cb) {
        try {
            // 获取目标目录
            const uploadDir = createQualityReportDirectory();
            // 生成唯一文件名（保持原始名称）
            const uniqueFilename = generateUniqueFilename(uploadDir, file.originalname);
            cb(null, uniqueFilename);
        } catch (error) {
            cb(error);
        }
    }
});

// 文件过滤器（与原来相同）
const qualityFileFilter = (req, file, cb) => {
    // 允许的文件扩展名
    const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.csv'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    // 允许的MIME类型
    const allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'text/csv',
        'application/csv'
    ];

    const isExtensionAllowed = allowedExtensions.includes(fileExtension);
    const isMimeTypeAllowed = allowedMimeTypes.includes(file.mimetype);

    if (isExtensionAllowed && isMimeTypeAllowed) {
        return cb(null, true);
    } else {
        cb(new Error(`不支持的文件类型！支持的格式：${allowedExtensions.join(', ')}`));
    }
};

// 配置质量报告上传
const qualityUpload = multer({
    storage: qualityStorage,
    limits: { fileSize: config.upload.maxFileSize },
    fileFilter: qualityFileFilter,
    preservePath: false,
    // 确保正确处理文件名编码
    onError: function (err, next) {
        console.error('Multer error:', err);
        next(err);
    }
});

module.exports = qualityUpload;
