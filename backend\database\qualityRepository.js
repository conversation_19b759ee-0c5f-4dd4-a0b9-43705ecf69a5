/**
 * 质量管理数据访问层
 * 使用SQLite数据库
 */

const databaseManager = require('./database');
const logger = require('../utils/logger');

class QualityRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        this.initStatements();
    }

    /**
     * 初始化预编译SQL语句
     */
    initStatements() {
        this.statements = {
            // 报告相关语句
            findAllReports: this.db.prepare(`
                SELECT 
                    qr.*,
                    u.username as uploader_name,
                    u.department as uploader_department,
                    (SELECT COUNT(*) FROM quality_report_files qrf WHERE qrf.report_id = qr.id) as file_count
                FROM quality_reports qr
                LEFT JOIN users u ON qr.uploaded_by = u.id
                ORDER BY qr.created_at DESC
            `),
            findReportById: this.db.prepare(`
                SELECT 
                    qr.*,
                    u.username as uploader_name,
                    u.department as uploader_department
                FROM quality_reports qr
                LEFT JOIN users u ON qr.uploaded_by = u.id
                WHERE qr.id = ?
            `),
            findReportByNumber: this.db.prepare(`
                SELECT 
                    qr.*,
                    u.username as uploader_name,
                    u.department as uploader_department
                FROM quality_reports qr
                LEFT JOIN users u ON qr.uploaded_by = u.id
                WHERE qr.report_number = ?
            `),
            insertReport: this.db.prepare(`
                INSERT INTO quality_reports (
                    id, report_number, title, description, test_type, test_date,
                    sample_info, test_method, test_standard, test_result, conclusion,
                    uploaded_by, uploaded_at, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            updateReport: this.db.prepare(`
                UPDATE quality_reports 
                SET title = ?, description = ?, test_type = ?, test_date = ?,
                    sample_info = ?, test_method = ?, test_standard = ?, 
                    test_result = ?, conclusion = ?, status = ?, updated_at = ?
                WHERE id = ?
            `),
            deleteReport: this.db.prepare('DELETE FROM quality_reports WHERE id = ?'),
            countReportsByDatePrefix: this.db.prepare(`
                SELECT COUNT(*) as count
                FROM quality_reports
                WHERE report_number LIKE ?
            `),
            getMaxUsedSequenceForDate: this.db.prepare(`
                SELECT COALESCE(MAX(CAST(SUBSTR(report_number, -4) AS INTEGER)), 0) as max_sequence
                FROM quality_report_numbers
                WHERE report_number LIKE ?
            `),
            insertUsedReportNumber: this.db.prepare(`
                INSERT OR IGNORE INTO quality_report_numbers (report_number, created_at)
                VALUES (?, ?)
            `),

            // 文件相关语句
            findFilesByReportId: this.db.prepare(`
                SELECT * FROM quality_report_files 
                WHERE report_id = ? 
                ORDER BY uploaded_at ASC
            `),
            findFileById: this.db.prepare(`
                SELECT qrf.*, qr.title as report_title, qr.report_number
                FROM quality_report_files qrf
                LEFT JOIN quality_reports qr ON qrf.report_id = qr.id
                WHERE qrf.id = ?
            `),
            insertFile: this.db.prepare(`
                INSERT INTO quality_report_files (
                    id, report_id, original_filename, stored_filename,
                    file_path, file_size, file_type, mime_type, uploaded_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            deleteFilesByReportId: this.db.prepare('DELETE FROM quality_report_files WHERE report_id = ?')
        };
    }

    /**
     * 获取所有报告
     */
    findAllReports() {
        try {
            return this.statements.findAllReports.all();
        } catch (error) {
            logger.error('获取所有报告失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取报告
     */
    findReportById(id) {
        try {
            return this.statements.findReportById.get(id);
        } catch (error) {
            logger.error(`根据ID获取报告失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据报告编号获取报告
     */
    findReportByNumber(reportNumber) {
        try {
            return this.statements.findReportByNumber.get(reportNumber);
        } catch (error) {
            logger.error(`根据编号获取报告失败 (${reportNumber}):`, error);
            throw error;
        }
    }

    /**
     * 创建报告
     */
    createReport(reportData) {
        try {
            const result = this.statements.insertReport.run(
                reportData.id,
                reportData.report_number,
                reportData.title,
                reportData.description,
                reportData.test_type,
                reportData.test_date,
                reportData.sample_info,
                reportData.test_method,
                reportData.test_standard,
                reportData.test_result,
                reportData.conclusion,
                reportData.uploaded_by,
                reportData.uploaded_at,
                reportData.status,
                reportData.created_at,
                reportData.updated_at
            );
            return result.changes > 0;
        } catch (error) {
            logger.error('创建报告失败:', error);
            throw error;
        }
    }

    /**
     * 更新报告
     */
    updateReport(id, reportData) {
        try {
            const result = this.statements.updateReport.run(
                reportData.title,
                reportData.description,
                reportData.test_type,
                reportData.test_date,
                reportData.sample_info,
                reportData.test_method,
                reportData.test_standard,
                reportData.test_result,
                reportData.conclusion,
                reportData.status,
                reportData.updated_at,
                id
            );
            return result.changes > 0;
        } catch (error) {
            logger.error('更新报告失败:', error);
            throw error;
        }
    }

    /**
     * 删除报告
     */
    deleteReport(id) {
        try {
            const result = this.statements.deleteReport.run(id);
            return result.changes > 0;
        } catch (error) {
            logger.error('删除报告失败:', error);
            throw error;
        }
    }

    /**
     * 统计指定日期前缀的报告数量
     */
    countReportsByDatePrefix(datePrefix) {
        try {
            const result = this.statements.countReportsByDatePrefix.get(`${datePrefix}%`);
            return result.count;
        } catch (error) {
            logger.error('统计报告数量失败:', error);
            throw error;
        }
    }

    /**
     * 获取指定日期前缀的最大已使用序号（包括已删除的）
     */
    getMaxUsedSequenceForDate(datePrefix) {
        try {
            const result = this.statements.getMaxUsedSequenceForDate.get(`${datePrefix}%`);
            return result.max_sequence || 0;
        } catch (error) {
            logger.error('获取最大已使用序号失败:', error);
            throw error;
        }
    }

    /**
     * 记录已使用的报告编号
     */
    recordUsedReportNumber(reportNumber) {
        try {
            const now = new Date().toISOString();
            this.statements.insertUsedReportNumber.run(reportNumber, now);
            return true;
        } catch (error) {
            logger.error('记录已使用报告编号失败:', error);
            throw error;
        }
    }

    /**
     * 获取报告的文件列表
     */
    findFilesByReportId(reportId) {
        try {
            return this.statements.findFilesByReportId.all(reportId);
        } catch (error) {
            logger.error(`获取报告文件失败 (${reportId}):`, error);
            throw error;
        }
    }

    /**
     * 根据ID获取文件
     */
    findFileById(fileId) {
        try {
            return this.statements.findFileById.get(fileId);
        } catch (error) {
            logger.error(`根据ID获取文件失败 (${fileId}):`, error);
            throw error;
        }
    }

    /**
     * 创建文件记录
     */
    createFile(fileData) {
        try {
            const result = this.statements.insertFile.run(
                fileData.id,
                fileData.report_id,
                fileData.original_filename,
                fileData.stored_filename,
                fileData.file_path,
                fileData.file_size,
                fileData.file_type,
                fileData.mime_type,
                fileData.uploaded_at
            );
            return result.changes > 0;
        } catch (error) {
            logger.error('创建文件记录失败:', error);
            throw error;
        }
    }

    /**
     * 删除报告的所有文件记录
     */
    deleteFilesByReportId(reportId) {
        try {
            const result = this.statements.deleteFilesByReportId.run(reportId);
            return result.changes;
        } catch (error) {
            logger.error('删除文件记录失败:', error);
            throw error;
        }
    }

    /**
     * 执行事务
     */
    transaction(callback) {
        const transaction = this.db.transaction(callback);
        return transaction();
    }
}

module.exports = new QualityRepository();
