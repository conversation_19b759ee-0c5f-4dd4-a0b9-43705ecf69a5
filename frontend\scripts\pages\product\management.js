/**
 * 产品管理页面
 * 产品信息的增删改查功能
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ProductAPI from '../../api/product.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const loading = ref(false);
        const submitting = ref(false);
        const products = ref([]);
        const showCreateModal = ref(false);
        const showEditModal = ref(false);
        const currentProduct = ref(null);

        // 分页信息
        const pagination = ref({
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0
        });

        // 筛选条件
        const filters = ref({
            keyword: '',
            category: ''
        });

        // 表单数据
        const formData = ref({
            code: '',
            name: '',
            category: '',
            unit: 'pcs',
            standardTime: 0,
            specifications: {}
        });

        // 初始化
        onMounted(async () => {
            try {
                // 获取当前用户信息
                const user = await getCurrentUser();
                if (user) {
                    currentUser.value = user;
                    isAuthenticated.value = true;
                    await loadProducts();
                } else {
                    // 未登录，跳转到登录页
                    window.location.href = '/login';
                    return;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                window.location.href = '/login';
                return;
            } finally {
                hideLoading();
            }
        });

        // 加载产品列表
        async function loadProducts() {
            loading.value = true;
            try {
                const params = {
                    page: pagination.value.page,
                    limit: pagination.value.limit,
                    ...filters.value
                };

                // 移除空值参数
                Object.keys(params).forEach(key => {
                    if (params[key] === '' || params[key] === null || params[key] === undefined) {
                        delete params[key];
                    }
                });

                const response = await ProductAPI.getProducts(params);

                if (response.success) {
                    products.value = response.data.products;
                    pagination.value.total = response.data.total;
                    pagination.value.totalPages = response.data.totalPages;
                } else {
                    console.error('获取产品列表失败:', response.message);
                    window.showNotification('获取产品列表失败', 'error');
                }
            } catch (error) {
                console.error('加载产品列表失败:', error);
                window.showNotification('加载产品列表失败', 'error');
            } finally {
                loading.value = false;
            }
        }

        // 刷新数据
        async function refreshData() {
            await loadProducts();
            window.showNotification('数据已刷新', 'success');
        }

        // 切换页面
        async function changePage(page) {
            if (page >= 1 && page <= pagination.value.totalPages) {
                pagination.value.page = page;
                await loadProducts();
            }
        }

        // 防抖搜索
        let searchTimeout;
        function debouncedSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                pagination.value.page = 1;
                loadProducts();
            }, 500);
        }

        // 查看产品
        function viewProduct(product) {
            // 跳转到产品详情页面或显示详情模态框
            window.location.href = `/product/detail/${product.id}`;
        }

        // 编辑产品
        function editProduct(product) {
            currentProduct.value = product;
            formData.value = {
                code: product.code,
                name: product.name,
                category: product.category || '',
                unit: product.unit || 'pcs',
                standardTime: product.standardTime || 0,
                specifications: product.specifications || {}
            };
            showEditModal.value = true;
        }

        // 删除产品
        async function deleteProduct(product) {
            if (!confirm(`确定要删除产品"${product.name}"吗？此操作不可恢复。`)) {
                return;
            }

            try {
                const response = await ProductAPI.deleteProduct(product.id);

                if (response.success) {
                    window.showNotification('产品删除成功', 'success');
                    await loadProducts();
                } else {
                    window.showNotification('删除失败: ' + response.message, 'error');
                }
            } catch (error) {
                console.error('删除产品失败:', error);
                window.showNotification('删除产品失败', 'error');
            }
        }

        // 提交表单
        async function submitForm() {
            if (submitting.value) return;

            // 基本验证
            if (!formData.value.code.trim()) {
                window.showNotification('请输入产品编码', 'error');
                return;
            }

            if (!formData.value.name.trim()) {
                window.showNotification('请输入产品名称', 'error');
                return;
            }

            submitting.value = true;

            try {
                let response;
                if (showCreateModal.value) {
                    response = await ProductAPI.createProduct(formData.value);
                } else {
                    response = await ProductAPI.updateProduct(currentProduct.value.id, formData.value);
                }

                if (response.success) {
                    window.showNotification(
                        showCreateModal.value ? '产品创建成功' : '产品更新成功', 
                        'success'
                    );
                    closeModals();
                    await loadProducts();
                } else {
                    window.showNotification('操作失败: ' + response.message, 'error');
                }
            } catch (error) {
                console.error('提交表单失败:', error);
                window.showNotification('操作失败', 'error');
            } finally {
                submitting.value = false;
            }
        }

        // 关闭模态框
        function closeModals() {
            showCreateModal.value = false;
            showEditModal.value = false;
            currentProduct.value = null;
            resetForm();
        }

        // 重置表单
        function resetForm() {
            formData.value = {
                code: '',
                name: '',
                category: '',
                unit: 'pcs',
                standardTime: 0,
                specifications: {}
            };
        }

        return {
            currentUser,
            isAuthenticated,
            loading,
            submitting,
            products,
            pagination,
            filters,
            formData,
            showCreateModal,
            showEditModal,
            loadProducts,
            refreshData,
            changePage,
            debouncedSearch,
            viewProduct,
            editProduct,
            deleteProduct,
            submitForm,
            closeModals
        };
    }
}).mount('#app');
