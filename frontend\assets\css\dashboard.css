/* 主页样式文件 - Dashboard CSS */

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
    display: none;
}

/* 侧边栏按钮过渡效果 */
.sidebar-btn {
    transition: background-color 0.2s;
}

/* 加载中覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #F3F4F6;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* 加载中旋转动画 */
.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e5e7eb;
    border-top: 5px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 主页英雄区域 */
.dashboard-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.dashboard-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* 统计数据网格布局 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 统计卡片样式 */
.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(229, 231, 235, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-accent, #3b82f6);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

/* 统计卡片颜色主题 */
.stat-card.blue { --card-accent: #3b82f6; }
.stat-card.green { --card-accent: #10b981; }
.stat-card.purple { --card-accent: #8b5cf6; }
.stat-card.orange { --card-accent: #f59e0b; }
.stat-card.indigo { --card-accent: #6366f1; }
.stat-card.red { --card-accent: #ef4444; }

/* 统计卡片头部 */
.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

/* 统计图标 */
.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.stat-icon::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 12px;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-icon::after {
    opacity: 1;
}

/* 统计数值 */
.stat-value {
    font-size: 2.25rem;
    font-weight: 800;
    color: #111827;
    margin-bottom: 0.25rem;
    line-height: 1;
}

/* 统计标签 */
.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

/* 统计底部信息 */
.stat-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
}

/* 统计变化指示器 */
.stat-change {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
}

.stat-change.positive {
    color: #065f46;
    background-color: #d1fae5;
}

.stat-change.negative {
    color: #991b1b;
    background-color: #fee2e2;
}

/* 快速操作区域 */
.quick-actions {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(229, 231, 235, 0.8);
    margin-bottom: 2rem;
}

/* 操作网格布局 */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 1rem;
}

/* 操作卡片 */
.action-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: white;
    text-decoration: none;
    color: inherit;
}

.action-card:hover::before {
    left: 100%;
}

/* 操作图标 */
.action-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin: 0 auto 0.75rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 内容网格布局 */
.content-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr 1fr 1fr;
    }
}

/* 内容卡片 */
.content-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(229, 231, 235, 0.8);
    height: 400px;
    display: flex;
    flex-direction: column;
}

.content-card.auto-height {
    height: fit-content;
    max-height: 400px;
}

/* 区域头部 */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
    flex-shrink: 0;
}

/* 卡片内容 */
.card-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 可滚动内容 */
.scrollable-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 滚动条样式 */
.scrollable-content::-webkit-scrollbar {
    width: 4px;
}

.scrollable-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

.scrollable-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 区域标题 */
.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-left: 0.5rem;
}

/* 区域图标 */
.section-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #6b7280;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 2rem 1rem;
    color: #6b7280;
}

.empty-state svg {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    color: #d1d5db;
}

/* 列表项间距 */
.item-list > * + * {
    margin-top: 0.75rem;
}

/* 列表项 */
.list-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.list-item:hover {
    background-color: #f9fafb;
}

/* 项目图标 */
.item-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* 项目内容 */
.item-content {
    flex: 1;
    min-width: 0;
}

/* 项目标题 */
.item-title {
    font-weight: 500;
    color: #111827;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

/* 项目副标题 */
.item-subtitle {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 项目徽章 */
.item-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    flex-shrink: 0;
}

/* 优先级点 */
.priority-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* 通知项 */
.notification-item {
    padding: 0.75rem;
    border-left: 3px solid transparent;
    border-radius: 0 8px 8px 0;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 通知优先级样式 */
.notification-item.high {
    border-left-color: #ef4444;
    background-color: rgba(254, 242, 242, 0.5);
}

.notification-item.medium {
    border-left-color: #f59e0b;
    background-color: rgba(255, 251, 235, 0.5);
}

.notification-item.low {
    border-left-color: #10b981;
    background-color: rgba(240, 253, 244, 0.5);
}
