/**
 * 维修保养记录控制器
 * 处理维修保养记录相关的HTTP请求
 */

const MaintenanceService = require('../services/maintenanceService');
const ExportService = require('../services/exportService');
const logger = require('../utils/logger');

class MaintenanceController {
    constructor() {
        this.maintenanceService = new MaintenanceService();
        this.exportService = new ExportService();
    }

    /**
     * 获取维修记录列表
     * GET /api/maintenance
     */
    async getMaintenanceRecords(req, res) {
        try {
            const {
                page = 1,
                limit = 10,
                search = '',
                equipmentId = '',
                type = '',
                status = '',
                technician = '',
                area = '',
                startDate = '',
                endDate = ''
            } = req.query;

            const options = {
                page: parseInt(page),
                limit: parseInt(limit),
                search,
                equipmentId,
                type,
                status,
                technician,
                area,
                startDate,
                endDate
            };

            const result = await this.maintenanceService.getMaintenanceRecords(options);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取维修记录列表请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取单个维修记录详情
     * GET /api/maintenance/:id
     */
    async getMaintenanceRecordById(req, res) {
        try {
            const { id } = req.params;

            const result = await this.maintenanceService.getMaintenanceRecordById(id);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '维修记录不存在' ? 404 : 500;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取维修记录详情请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 创建维修记录
     * POST /api/maintenance
     */
    async createMaintenanceRecord(req, res) {
        try {
            const recordData = req.body;
            const userId = req.user.id;

            logger.info('创建维修记录请求', { recordData, userId });

            const result = await this.maintenanceService.createMaintenanceRecord(recordData, userId);

            if (result.success) {
                res.status(201).json({
                    success: true,
                    data: result.data,
                    message: result.message
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('创建维修记录请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 更新维修记录
     * PUT /api/maintenance/:id
     */
    async updateMaintenanceRecord(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const userId = req.user.id;

            logger.info('更新维修记录请求', { recordId: id, updateData, userId });

            const result = await this.maintenanceService.updateMaintenanceRecord(id, updateData, userId);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data,
                    message: result.message
                });
            } else {
                const statusCode = result.message === '维修记录不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('更新维修记录请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 删除维修记录
     * DELETE /api/maintenance/:id
     */
    async deleteMaintenanceRecord(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            logger.info('删除维修记录请求', { recordId: id, userId });

            const result = await this.maintenanceService.deleteMaintenanceRecord(id, userId);

            if (result.success) {
                res.json({
                    success: true,
                    message: result.message
                });
            } else {
                const statusCode = result.message === '维修记录不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('删除维修记录请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取统计数据
     * GET /api/maintenance/statistics
     */
    async getStatistics(req, res) {
        try {
            const result = await this.maintenanceService.getStatistics();

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取维修记录统计数据请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取选项数据（类型、状态等）
     * GET /api/maintenance/options
     */
    async getOptions(req, res) {
        try {
            const result = await this.maintenanceService.getMaintenanceOptions();

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message
                });
            }
        } catch (error) {
            logger.error('获取维修记录选项数据请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 导出维修记录到Excel
     * GET /api/maintenance/export
     */
    async exportMaintenanceRecordsToExcel(req, res) {
        try {
            // 获取所有记录（不分页）
            const result = await this.maintenanceService.getMaintenanceRecords({});

            if (result.success) {
                const records = result.data.records || [];

                // 生成Excel文件
                const excelBuffer = await this.exportService.exportMaintenanceRecordsToExcel(records);

                // 设置响应头
                const filename = `维修保养记录_${new Date().toISOString().split('T')[0]}.xlsx`;
                res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`);
                res.setHeader('Content-Length', excelBuffer.length);

                // 发送Excel文件
                res.send(excelBuffer);

                logger.info('维修保养记录Excel导出成功', { recordCount: records.length });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message || '导出失败'
                });
            }
        } catch (error) {
            logger.error('导出维修记录Excel失败:', error);
            res.status(500).json({
                success: false,
                message: '导出维修记录Excel失败: ' + error.message
            });
        }
    }

    /**
     * 批量导入维修记录
     * POST /api/maintenance/import
     */
    async importMaintenanceRecords(req, res) {
        try {
            const { records } = req.body;
            const userId = req.user.id;

            if (!Array.isArray(records) || records.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '导入数据不能为空'
                });
            }

            const results = [];
            let successCount = 0;
            let failCount = 0;

            // 逐个处理导入记录
            for (const recordData of records) {
                try {
                    const result = await this.maintenanceService.createMaintenanceRecord(recordData, userId);
                    if (result.success) {
                        successCount++;
                        results.push({ success: true, data: result.data });
                    } else {
                        failCount++;
                        results.push({ success: false, error: result.message });
                    }
                } catch (error) {
                    failCount++;
                    results.push({ success: false, error: error.message });
                }
            }

            logger.info('批量导入维修记录完成', { 
                total: records.length, 
                success: successCount, 
                fail: failCount,
                userId 
            });

            res.json({
                success: true,
                data: {
                    total: records.length,
                    successCount,
                    failCount,
                    results
                },
                message: `导入完成：成功 ${successCount} 条，失败 ${failCount} 条`
            });
        } catch (error) {
            logger.error('批量导入维修记录请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }
}

module.exports = MaintenanceController;
