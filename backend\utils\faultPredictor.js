/**
 * 故障预测引擎
 * 基于历史维修记录进行故障预测
 */

const logger = require('./logger');

class FaultPredictor {
    constructor() {
        this.minRecordsRequired = 2; // 最少需要2次维修记录
    }

    /**
     * 预测下次故障时间
     */
    predictNextFailure(maintenanceRecords) {
        try {
            // 筛选维修记录
            const repairRecords = maintenanceRecords
                .filter(record => record.type === 'repair' || record.type === 'emergency')
                .sort((a, b) => new Date(a.maintenance_date) - new Date(b.maintenance_date));

            if (repairRecords.length < this.minRecordsRequired) {
                return {
                    canPredict: false,
                    message: `维修记录不足，至少需要${this.minRecordsRequired}次维修记录`,
                    dataQuality: 'insufficient'
                };
            }

            // 计算维修间隔
            const intervals = this.calculateIntervals(repairRecords);
            
            // 计算统计指标
            const statistics = this.calculateStatistics(intervals);
            
            // 评估数据质量
            const dataQuality = this.assessDataQuality(statistics);
            
            // 计算置信度
            const confidence = this.calculateConfidence(statistics, dataQuality);
            
            // 预测下次故障时间
            const lastRepairDate = new Date(repairRecords[repairRecords.length - 1].maintenance_date);
            const predictedDate = new Date(lastRepairDate.getTime() + statistics.avgInterval * 24 * 60 * 60 * 1000);

            return {
                canPredict: true,
                prediction: {
                    predictedDate: predictedDate.toISOString(),
                    confidence,
                    avgInterval: Math.round(statistics.avgInterval),
                    dataQuality
                },
                analysis: {
                    totalRepairs: repairRecords.length,
                    intervals: intervals.map(i => Math.round(i)),
                    avgInterval: Math.round(statistics.avgInterval),
                    standardDeviation: Math.round(statistics.standardDeviation * 10) / 10,
                    coefficientOfVariation: Math.round(statistics.coefficientOfVariation * 1000) / 1000
                }
            };
        } catch (error) {
            logger.error('故障预测失败', { error: error.message });
            return {
                canPredict: false,
                message: '预测过程出现错误: ' + error.message,
                dataQuality: 'error'
            };
        }
    }

    /**
     * 计算维修间隔（天）
     */
    calculateIntervals(repairRecords) {
        const intervals = [];
        
        for (let i = 1; i < repairRecords.length; i++) {
            const interval = (new Date(repairRecords[i].maintenance_date) - 
                            new Date(repairRecords[i-1].maintenance_date)) / (24 * 60 * 60 * 1000);
            intervals.push(interval);
        }
        
        return intervals;
    }

    /**
     * 计算统计指标
     */
    calculateStatistics(intervals) {
        const n = intervals.length;
        const sum = intervals.reduce((acc, val) => acc + val, 0);
        const avgInterval = sum / n;
        
        // 计算方差和标准差
        const variance = intervals.reduce((acc, val) => 
            acc + Math.pow(val - avgInterval, 2), 0) / n;
        const standardDeviation = Math.sqrt(variance);
        
        // 计算变异系数
        const coefficientOfVariation = standardDeviation / avgInterval;
        
        return {
            avgInterval,
            standardDeviation,
            coefficientOfVariation,
            variance
        };
    }

    /**
     * 评估数据质量
     */
    assessDataQuality(statistics) {
        const cv = statistics.coefficientOfVariation;
        
        if (cv < 0.2) return 'excellent';      // 变异系数 < 20%
        if (cv < 0.4) return 'good';           // 变异系数 < 40%
        if (cv < 0.6) return 'fair';           // 变异系数 < 60%
        if (cv < 0.8) return 'poor';           // 变异系数 < 80%
        return 'very_poor';                    // 变异系数 >= 80%
    }

    /**
     * 计算置信度
     */
    calculateConfidence(statistics, dataQuality) {
        const cv = statistics.coefficientOfVariation;
        
        // 基于变异系数计算基础置信度
        let baseConfidence;
        if (cv < 0.2) baseConfidence = 90;
        else if (cv < 0.4) baseConfidence = 70;
        else if (cv < 0.6) baseConfidence = 50;
        else if (cv < 0.8) baseConfidence = 35;
        else baseConfidence = 30;
        
        // 根据数据质量调整置信度
        const qualityMultiplier = {
            'excellent': 1.0,
            'good': 0.95,
            'fair': 0.85,
            'poor': 0.75,
            'very_poor': 0.6
        };
        
        return Math.round(baseConfidence * (qualityMultiplier[dataQuality] || 0.6));
    }
}

module.exports = FaultPredictor;
