---
type: "always_apply"
---

# 代码组织原则与开发规范

## ⚠️ 重要开发规范 ⚠️

**每次添加新功能必须遵循以下规则：**

- **严禁在单个文件中直接添加新功能代码**
- **每个新功能必须创建独立的文件**
- **所有功能模块应保持高内聚、低耦合**
- **遵循单一职责原则设计每个文件**
- **必须按照以下开发流程添加新功能：**
  1. **先设计页面**：明确UI布局、交互方式和用户体验目标
  2. **然后设计后端接口**：定义API规范、请求/响应格式和错误处理机制
  3. **开发后端接口**：实现API功能，确保安全性、性能和可靠性
  4. **最后开发前端**：基于设计好的页面和接口实现前端功能

不遵循上述规则会导致：
1. 代码臃肿难以维护
2. 功能之间相互干扰
3. 调试困难且容易引入bug
4. 团队协作效率降低

## 🔧 Bug修复原则

**修改系统代码bug应该遵循以下原则：**

### 1. 定位准确性原则
- 充分理解问题本质，不要仅针对表象
- 通过日志和调试信息精确定位问题根源
- 确认是否存在类似问题可能在其他地方同时存在

### 2. 最小侵入性原则
- 修改范围要尽可能小，仅聚焦于问题核心
- 避免重构不相关代码，减少引入新问题的风险
- 保持原有代码结构和命名规范的一致性

### 3. 完整测试验证原则
- 确保修复后的代码能覆盖所有可能的边缘情况
- 修复后进行充分测试，确保问题彻底解决
- 验证修改不会对系统其他功能产生副作用

### 4. 文档和注释原则
- 添加清晰的代码注释说明修改目的和逻辑
- 记录bug修复历史，便于后期维护
- 更新相关文档，确保团队成员了解变更

### 5. 渐进式优化原则
- 先解决核心问题，后续可考虑代码优化
- 对可能存在的性能或安全问题进行评估
- 在不破坏现有功能的前提下优化代码结构

### 6. 兼容性考虑原则
- 确保修改适用于所有支持的平台和设备
- 考虑不同用户角色和使用场景下的影响
- 保持API接口的向后兼容性

### 7. 多重防御原则
- 增加必要的错误处理和异常捕获
- 添加数据验证和合理的默认值
- 实现失败时的优雅降级策略

### 8. 有效日志原则
- 添加关键操作的日志记录，方便问题排查
- 确保错误情况下有足够详细的日志信息
- 避免过度日志导致的性能问题

## 🌍 CDN资源本地化原则

**为确保系统在全球范围内稳定部署，必须遵循以下CDN资源本地化规范：**

### 1. 禁止使用CDN依赖原则
- **严禁直接引用任何外部CDN资源**（如jsdelivr、cdnjs、unpkg等）
- 所有第三方库必须下载到本地 `frontend/js/libs/` 目录
- 避免因网络限制、DNS解析问题导致的功能失效

### 2. 本地资源管理原则
- 所有JavaScript库文件统一存放在 `frontend/js/libs/` 目录
- 保持原始文件名和版本信息，便于维护和更新
- 添加资源预加载配置，优化加载性能

### 3. 必需本地化的资源类型
- **UI框架**：Tailwind CSS、Bootstrap等样式框架
- **图表库**：Chart.js、ECharts等数据可视化库
- **PDF处理**：PDF.js、jsPDF、html2canvas等文档处理库
- **工具库**：jQuery、Lodash等常用工具库

### 4. 错误处理和降级原则
- 每个资源加载都必须有错误处理机制
- 提供友好的错误提示，说明资源加载失败原因
- 实现功能降级，确保核心功能不受影响
- 添加资源可用性检查，避免运行时错误

### 5. 动态加载优化原则
- 对于按需加载的库（如PDF处理），使用本地路径
- 添加加载状态日志，便于调试和监控
- 实现加载失败的重试机制

### 6. 版本管理和更新原则
- 记录每个本地化库的版本信息和来源
- 定期检查库的安全更新和功能更新
- 更新时确保向后兼容性，避免破坏现有功能

### 7. 国际部署兼容性原则
- 确保系统可在任何地区部署，不受网络限制
- 支持完全离线环境下的运行
- 避免依赖特定地区的网络服务

## 📝 代码示例

### 本地化配置示例

**❌ 错误：使用CDN**
```html
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
```

**✅ 正确：使用本地资源**
```html
<script src="js/libs/chart.js" onerror="console.error('Chart.js加载失败')"></script>
```

### 动态加载示例

**❌ 错误：CDN动态加载**
```javascript
script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
```

**✅ 正确：本地动态加载**
```javascript
script.src = 'js/libs/jspdf.umd.min.js';
script.onerror = () => reject(new Error('jsPDF库加载失败，请检查文件是否存在'));
```

### Vue.js 3 组件示例

**✅ 正确：使用 Composition API**
```vue
<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式状态
const count = ref(0)
const loading = ref(false)

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 方法
const increment = () => {
  count.value++
}

// 生命周期
onMounted(() => {
  console.log('组件已挂载')
})
</script>
```

**❌ 错误：混用 Options API**
```vue
<script>
export default {
  data() {
    return { count: 0 }
  },
  setup() {
    const loading = ref(false)
    return { loading }
  }
}
</script>
```

## 🎯 遵循原则的好处

遵循这些原则可以确保：
- 系统在全球任何地区都能稳定运行
- 不受CDN服务状态和网络限制影响
- 提供一致的用户体验和功能可用性
- 支持完全离线部署和使用
- 代码结构清晰，易于维护和扩展
- 团队协作效率高，减少冲突和问题

## 🔄 Vue.js 3 开发规范

**Vue.js 3 项目必须遵循以下核心规范：**

### 1. 组合式 API 规范
- **始终使用 Composition API 与 `<script setup>` 语法**
- 避免混用 Options API 和 Composition API
- 使用 `ref()` 和 `reactive()` 声明响应式状态
- 使用 `computed()` 派生计算属性
- 使用 `watch()` 和 `watchEffect()` 处理副作用

### 2. 组件设计原则
- **组件文件使用 PascalCase 命名**（如 `UserProfile.vue`）
- **目录使用 kebab-case 命名**（如 `user-management/`）
- 每个组件应遵循单一职责原则
- 将大型组件拆分为更小的子组件
- 使用 `defineProps()` 和 `defineEmits()` 声明属性和事件

### 3. 状态管理与性能优化
- 使用 Pinia 进行状态管理，避免直接使用 Vuex
- 合理使用 `shallowRef()` 和 `shallowReactive()` 优化性能
- 使用 `v-memo` 缓存大型列表渲染
- 使用 `defineAsyncComponent()` 实现组件懒加载
- 使用 VueUse 工具库提高开发效率

### 4. TypeScript 集成
- 为组件 props 定义明确的类型接口
- 使用 `defineExpose()` 显式暴露组件属性和方法
- 优先使用 `interface` 而非 `type` 定义类型
- 为复杂函数添加返回类型注解

## 🏗️ 功能模块化开发规范

**新功能开发必须严格遵循以下代码组织原则：**

### 1. 功能模块独立性原则
- **每个新功能必须创建独立的模块目录**
- **前端功能**：在 `frontend/js/` 下创建功能专用文件，配套CSS文件放在 `frontend/css/` 下
- **后端功能**：在 `backend/modules/` 下创建功能模块目录，包含完整的业务逻辑
- **页面功能**：如需新页面，在 `frontend/pages/` 下创建独立HTML文件

### 2. 文件命名和结构规范

#### 前端文件组织
```
frontend/
├── js/
│   ├── [功能名]-management.js     # 主要业务逻辑
│   ├── [功能名]-api.js           # API调用封装
│   └── [功能名]-utils.js         # 工具函数
├── css/
│   └── [功能名]-management.css   # 样式文件
└── pages/
    └── [功能名].html             # 独立页面（如需要）
```

#### 后端模块组织
```
backend/modules/
└── [功能名]/
    ├── index.js                  # 模块入口和路由定义
    ├── controller.js             # 控制器逻辑
    ├── service.js                # 业务服务层
    ├── model.js                  # 数据模型定义
    ├── validator.js              # 数据验证
    └── README.md                 # 模块说明文档
```

### 3. 模块开发流程规范

#### 第一步：需求分析和设计
1. **明确功能边界**：定义功能的输入、输出和核心业务逻辑
2. **设计数据结构**：确定需要的数据表、字段和关系
3. **设计API接口**：定义RESTful API的路径、方法和参数
4. **设计用户界面**：确定页面布局、交互方式和用户体验

#### 第二步：后端模块开发
1. **创建模块目录**：`backend/modules/[功能名]/`
2. **定义数据模型**：在 `model.js` 中定义数据结构和数据库操作
3. **实现业务服务**：在 `service.js` 中实现核心业务逻辑
4. **开发控制器**：在 `controller.js` 中处理HTTP请求和响应
5. **添加数据验证**：在 `validator.js` 中实现输入验证
6. **配置路由**：在 `index.js` 中定义API路由
7. **注册模块**：在 `backend/modules/index.js` 中注册新模块

#### 第三步：前端功能开发
1. **创建API封装**：在 `[功能名]-api.js` 中封装后端API调用
2. **开发业务逻辑**：在 `[功能名]-management.js` 中实现前端业务逻辑
3. **创建工具函数**：在 `[功能名]-utils.js` 中实现通用工具函数
4. **设计样式**：在 `[功能名]-management.css` 中定义专用样式
5. **集成到主页面**：在相应页面中引入新功能模块

#### 第四步：测试和文档
1. **功能测试**：确保所有功能正常工作
2. **边界测试**：测试异常情况和边界条件
3. **性能测试**：确保功能不影响系统性能
4. **编写文档**：在模块README.md中记录功能说明和使用方法

### 4. 代码复用和抽象原则

#### 通用功能抽象
- **工具函数**：放在 `frontend/js/utils/` 和 `backend/utils/` 目录
- **通用组件**：创建可复用的UI组件和业务组件
- **共享服务**：将通用业务逻辑抽象为共享服务

#### 避免代码重复
- **相似功能合并**：识别相似功能，抽象为通用模块
- **配置驱动**：使用配置文件驱动相似功能的差异化
- **模板模式**：为相似的业务流程创建模板

### 5. 模块依赖管理原则

#### 依赖层次规范
```
表现层 (Frontend JS)
    ↓ 只能调用
API层 (Backend Routes)
    ↓ 只能调用
业务层 (Service)
    ↓ 只能调用
数据层 (Model/Database)
```

#### 依赖注入规范
- **避免硬编码依赖**：使用依赖注入或配置文件
- **接口抽象**：定义清晰的接口，降低模块间耦合
- **循环依赖检查**：确保模块间不存在循环依赖

### 6. 错误处理和日志规范

#### 统一错误处理
- **后端错误**：使用统一的错误处理中间件
- **前端错误**：实现全局错误捕获和用户友好提示
- **错误分类**：区分业务错误、系统错误和网络错误

#### 日志记录规范
- **关键操作日志**：记录所有重要的业务操作
- **错误日志**：详细记录错误信息和堆栈跟踪
- **性能日志**：记录关键操作的执行时间

### 7. 代码质量保证

#### 代码审查检查点
- **模块独立性**：确保模块可以独立测试和部署
- **接口设计**：检查API设计是否合理和一致
- **错误处理**：确保所有异常情况都有适当处理
- **性能考虑**：检查是否存在性能瓶颈
- **安全性**：确保输入验证和权限控制

#### 文档完整性
- **API文档**：详细记录所有API接口
- **业务逻辑文档**：说明复杂业务逻辑的实现
- **部署文档**：记录模块的部署和配置要求

## 📚 相关文档

- [功能说明文档](FUNC.md)
- [系统使用指南](README.md)

---

**注意：** 所有开发人员在参与项目开发前，必须仔细阅读并严格遵循本文档中的所有规范和原则。
