/**
 * 性能监控工具
 * 监控智能排程算法的性能指标
 */

const logger = require('./logger');

/**
 * 性能监控器类
 * 收集和分析性能数据
 */
class PerformanceMonitor {
    constructor() {
        // 性能数据存储
        this.metrics = new Map();
        
        // 监控配置
        this.config = {
            enableDetailedLogging: true,
            sampleRate: 1.0,            // 采样率
            maxHistorySize: 1000,       // 最大历史记录数
            alertThresholds: {          // 告警阈值
                responseTime: 30000,    // 30秒
                memoryUsage: 500 * 1024 * 1024, // 500MB
                errorRate: 0.05         // 5%
            }
        };

        // 性能统计
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            totalResponseTime: 0,
            averageResponseTime: 0,
            maxResponseTime: 0,
            minResponseTime: Infinity
        };

        logger.info('性能监控器初始化完成');
    }

    /**
     * 开始性能测量
     * @param {string} operationName 操作名称
     * @param {Object} metadata 元数据
     * @returns {Object} 测量上下文
     */
    startMeasurement(operationName, metadata = {}) {
        const measurementId = this.generateMeasurementId();
        const startTime = process.hrtime.bigint();
        const startMemory = process.memoryUsage();

        const context = {
            id: measurementId,
            operationName,
            metadata,
            startTime,
            startMemory,
            checkpoints: []
        };

        if (this.config.enableDetailedLogging) {
            logger.debug('开始性能测量', {
                measurementId,
                operationName,
                metadata
            });
        }

        return context;
    }

    /**
     * 添加检查点
     * @param {Object} context 测量上下文
     * @param {string} checkpointName 检查点名称
     * @param {Object} data 检查点数据
     */
    addCheckpoint(context, checkpointName, data = {}) {
        const currentTime = process.hrtime.bigint();
        const elapsedTime = Number(currentTime - context.startTime) / 1000000; // 转换为毫秒

        const checkpoint = {
            name: checkpointName,
            timestamp: currentTime,
            elapsedTime,
            data
        };

        context.checkpoints.push(checkpoint);

        if (this.config.enableDetailedLogging) {
            logger.debug('性能检查点', {
                measurementId: context.id,
                checkpointName,
                elapsedTime: `${elapsedTime.toFixed(2)}ms`
            });
        }
    }

    /**
     * 结束性能测量
     * @param {Object} context 测量上下文
     * @param {boolean} success 是否成功
     * @param {Object} result 结果数据
     * @returns {Object} 性能报告
     */
    endMeasurement(context, success = true, result = {}) {
        const endTime = process.hrtime.bigint();
        const endMemory = process.memoryUsage();
        const totalTime = Number(endTime - context.startTime) / 1000000; // 毫秒

        const report = {
            id: context.id,
            operationName: context.operationName,
            metadata: context.metadata,
            success,
            totalTime,
            memoryUsage: {
                start: context.startMemory,
                end: endMemory,
                delta: {
                    rss: endMemory.rss - context.startMemory.rss,
                    heapUsed: endMemory.heapUsed - context.startMemory.heapUsed,
                    heapTotal: endMemory.heapTotal - context.startMemory.heapTotal
                }
            },
            checkpoints: context.checkpoints,
            result,
            timestamp: new Date().toISOString()
        };

        // 更新统计信息
        this.updateStats(report);

        // 存储性能数据
        this.storeMetrics(report);

        // 检查告警条件
        this.checkAlerts(report);

        if (this.config.enableDetailedLogging) {
            logger.info('性能测量完成', {
                measurementId: context.id,
                operationName: context.operationName,
                totalTime: `${totalTime.toFixed(2)}ms`,
                success,
                memoryDelta: `${(report.memoryUsage.delta.heapUsed / 1024 / 1024).toFixed(2)}MB`
            });
        }

        return report;
    }

    /**
     * 更新统计信息
     * @param {Object} report 性能报告
     */
    updateStats(report) {
        this.stats.totalRequests++;
        
        if (report.success) {
            this.stats.successfulRequests++;
        } else {
            this.stats.failedRequests++;
        }

        this.stats.totalResponseTime += report.totalTime;
        this.stats.averageResponseTime = this.stats.totalResponseTime / this.stats.totalRequests;
        this.stats.maxResponseTime = Math.max(this.stats.maxResponseTime, report.totalTime);
        this.stats.minResponseTime = Math.min(this.stats.minResponseTime, report.totalTime);
    }

    /**
     * 存储性能指标
     * @param {Object} report 性能报告
     */
    storeMetrics(report) {
        const operationMetrics = this.metrics.get(report.operationName) || {
            history: [],
            stats: {
                count: 0,
                successCount: 0,
                failureCount: 0,
                totalTime: 0,
                averageTime: 0,
                maxTime: 0,
                minTime: Infinity
            }
        };

        // 添加到历史记录
        operationMetrics.history.push(report);
        
        // 限制历史记录大小
        if (operationMetrics.history.length > this.config.maxHistorySize) {
            operationMetrics.history.shift();
        }

        // 更新操作统计
        operationMetrics.stats.count++;
        if (report.success) {
            operationMetrics.stats.successCount++;
        } else {
            operationMetrics.stats.failureCount++;
        }

        operationMetrics.stats.totalTime += report.totalTime;
        operationMetrics.stats.averageTime = operationMetrics.stats.totalTime / operationMetrics.stats.count;
        operationMetrics.stats.maxTime = Math.max(operationMetrics.stats.maxTime, report.totalTime);
        operationMetrics.stats.minTime = Math.min(operationMetrics.stats.minTime, report.totalTime);

        this.metrics.set(report.operationName, operationMetrics);
    }

    /**
     * 检查告警条件
     * @param {Object} report 性能报告
     */
    checkAlerts(report) {
        const thresholds = this.config.alertThresholds;

        // 响应时间告警
        if (report.totalTime > thresholds.responseTime) {
            logger.warn('响应时间告警', {
                operationName: report.operationName,
                actualTime: report.totalTime,
                threshold: thresholds.responseTime
            });
        }

        // 内存使用告警
        const memoryDelta = report.memoryUsage.delta.heapUsed;
        if (memoryDelta > thresholds.memoryUsage) {
            logger.warn('内存使用告警', {
                operationName: report.operationName,
                memoryDelta,
                threshold: thresholds.memoryUsage
            });
        }

        // 错误率告警
        const errorRate = this.stats.failedRequests / this.stats.totalRequests;
        if (errorRate > thresholds.errorRate) {
            logger.warn('错误率告警', {
                errorRate,
                threshold: thresholds.errorRate,
                totalRequests: this.stats.totalRequests,
                failedRequests: this.stats.failedRequests
            });
        }
    }

    /**
     * 获取性能统计
     * @param {string} operationName 操作名称（可选）
     * @returns {Object} 统计信息
     */
    getStats(operationName = null) {
        if (operationName) {
            const operationMetrics = this.metrics.get(operationName);
            return operationMetrics ? operationMetrics.stats : null;
        }

        return {
            global: this.stats,
            operations: Object.fromEntries(
                Array.from(this.metrics.entries()).map(([name, metrics]) => [
                    name,
                    metrics.stats
                ])
            )
        };
    }

    /**
     * 获取性能报告
     * @param {Object} options 选项
     * @returns {Object} 性能报告
     */
    getPerformanceReport(options = {}) {
        const {
            operationName = null,
            timeRange = null,
            includeHistory = false
        } = options;

        const report = {
            timestamp: new Date().toISOString(),
            globalStats: this.stats,
            operations: {}
        };

        for (const [name, metrics] of this.metrics) {
            if (operationName && name !== operationName) {
                continue;
            }

            const operationReport = {
                stats: metrics.stats,
                errorRate: metrics.stats.count > 0 ? 
                    metrics.stats.failureCount / metrics.stats.count : 0
            };

            if (includeHistory) {
                let history = metrics.history;
                
                if (timeRange) {
                    const startTime = new Date(Date.now() - timeRange);
                    history = history.filter(h => new Date(h.timestamp) >= startTime);
                }
                
                operationReport.history = history;
            }

            report.operations[name] = operationReport;
        }

        return report;
    }

    /**
     * 重置统计信息
     * @param {string} operationName 操作名称（可选）
     */
    reset(operationName = null) {
        if (operationName) {
            this.metrics.delete(operationName);
            logger.info('重置操作性能统计', { operationName });
        } else {
            this.metrics.clear();
            this.stats = {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                totalResponseTime: 0,
                averageResponseTime: 0,
                maxResponseTime: 0,
                minResponseTime: Infinity
            };
            logger.info('重置全部性能统计');
        }
    }

    /**
     * 生成测量ID
     * @returns {string} 测量ID
     */
    generateMeasurementId() {
        return `perf_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    }

    /**
     * 获取系统资源使用情况
     * @returns {Object} 系统资源信息
     */
    getSystemResources() {
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();

        return {
            memory: {
                rss: memoryUsage.rss,
                heapTotal: memoryUsage.heapTotal,
                heapUsed: memoryUsage.heapUsed,
                external: memoryUsage.external,
                arrayBuffers: memoryUsage.arrayBuffers
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            },
            uptime: process.uptime(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 导出性能数据
     * @param {Object} options 导出选项
     * @returns {Object} 导出数据
     */
    export(options = {}) {
        const {
            includeHistory = true,
            timeRange = null
        } = options;

        const exportData = {
            timestamp: new Date().toISOString(),
            config: this.config,
            globalStats: this.stats,
            systemResources: this.getSystemResources(),
            operations: {}
        };

        for (const [name, metrics] of this.metrics) {
            const operationData = {
                stats: metrics.stats
            };

            if (includeHistory) {
                let history = metrics.history;
                
                if (timeRange) {
                    const startTime = new Date(Date.now() - timeRange);
                    history = history.filter(h => new Date(h.timestamp) >= startTime);
                }
                
                operationData.history = history;
            }

            exportData.operations[name] = operationData;
        }

        return exportData;
    }

    /**
     * 获取当前性能指标
     * @returns {Object} 性能指标数据
     */
    async getMetrics() {
        // 计算缓存命中率（如果有缓存统计）
        let cacheHitRate = 0;
        try {
            const SchedulingCache = require('./SchedulingCache');
            const cacheStats = SchedulingCache.getStats();
            cacheHitRate = cacheStats.hitRate || 0;
        } catch (error) {
            // 如果缓存不可用，使用默认值
            cacheHitRate = 0;
        }

        // 计算错误率
        const errorRate = this.stats.totalRequests > 0
            ? this.stats.failedRequests / this.stats.totalRequests
            : 0;

        // 计算每秒请求数
        const requestsPerSecond = this.calculateRequestsPerSecond();

        return {
            totalRequests: this.stats.totalRequests,
            successfulRequests: this.stats.successfulRequests,
            failedRequests: this.stats.failedRequests,
            averageResponseTime: this.stats.averageResponseTime,
            maxResponseTime: this.stats.maxResponseTime,
            minResponseTime: this.stats.minResponseTime === Infinity ? 0 : this.stats.minResponseTime,
            errorRate: errorRate,
            requestsPerSecond: requestsPerSecond,
            cacheHitRate: cacheHitRate,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 计算每秒请求数
     * @returns {number} 每秒请求数
     */
    calculateRequestsPerSecond() {
        // 获取最近1分钟的请求数据
        const oneMinuteAgo = Date.now() - 60000;
        let recentRequests = 0;

        for (const [name, metrics] of this.metrics) {
            const recentHistory = metrics.history.filter(h =>
                new Date(h.timestamp).getTime() > oneMinuteAgo
            );
            recentRequests += recentHistory.length;
        }

        return Math.round(recentRequests / 60); // 每秒平均请求数
    }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
