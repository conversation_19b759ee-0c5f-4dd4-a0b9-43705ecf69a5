/**
 * 通知列表组件
 * 显示文件管理通知列表
 */

import { getUserNotifications, confirmNotification, fileManagementUtils } from '../../scripts/api/file-management.js';

const { createApp, ref, reactive, computed, onMounted } = Vue;

export default {
    name: 'NotificationList',
    setup() {
        // 响应式数据
        const notifications = ref([]);
        const loading = ref(false);
        const filter = ref('all'); // all, unconfirmed, confirmed

        // 计算属性
        const filteredNotifications = computed(() => {
            if (filter.value === 'all') {
                return notifications.value;
            } else if (filter.value === 'unconfirmed') {
                return notifications.value.filter(n => !n.confirmed);
            } else if (filter.value === 'confirmed') {
                return notifications.value.filter(n => n.confirmed);
            }
            return notifications.value;
        });

        const unconfirmedCount = computed(() => {
            return notifications.value.filter(n => !n.confirmed).length;
        });

        // 生命周期
        onMounted(() => {
            loadNotifications();
        });

        // 方法
        async function loadNotifications() {
            try {
                loading.value = true;
                const response = await getUserNotifications();
                if (response.success) {
                    notifications.value = response.data;
                }
            } catch (error) {
                console.error('加载通知列表失败:', error);
                alert('加载通知列表失败');
            } finally {
                loading.value = false;
            }
        }

        async function handleConfirmNotification(notification) {
            try {
                // 添加确认动画效果
                notification.confirming = true;

                const response = await confirmNotification(notification.id);
                if (response.success) {
                    // 更新本地状态
                    notification.confirmed = 1;
                    notification.confirmed_at = new Date().toISOString();
                    notification.confirming = false;

                    // 显示成功提示
                    showNotification('确认成功！', 'success');

                    // 添加成功动画效果
                    setTimeout(() => {
                        notification.justConfirmed = true;
                        setTimeout(() => {
                            notification.justConfirmed = false;
                        }, 2000);
                    }, 100);
                } else {
                    throw new Error(response.message || '确认失败');
                }
            } catch (error) {
                console.error('确认通知失败:', error);
                notification.confirming = false;
                showNotification('确认失败: ' + error.message, 'error');
            }
        }

        function showNotification(message, type = 'info') {
            // 创建通知提示
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        function getNotificationTypeText(type) {
            const typeMap = {
                'new_file': '新文件上传',
                'file_update': '文件更新'
            };
            return typeMap[type] || type;
        }

        function getNotificationIcon(type) {
            const iconMap = {
                'new_file': '📁',
                'file_update': '🔄'
            };
            return iconMap[type] || '📄';
        }

        function getNotificationPriority(notification) {
            // 根据通知类型和时间判断优先级
            const now = new Date();
            const sentAt = new Date(notification.sent_at);
            const hoursDiff = (now - sentAt) / (1000 * 60 * 60);

            if (!notification.confirmed && hoursDiff > 24) {
                return 'high'; // 超过24小时未确认
            } else if (!notification.confirmed && hoursDiff > 8) {
                return 'medium'; // 超过8小时未确认
            }
            return 'normal';
        }

        function getPriorityClass(priority) {
            const classMap = {
                'high': 'border-l-red-500 bg-red-50',
                'medium': 'border-l-orange-500 bg-orange-50',
                'normal': 'border-l-blue-500 bg-white'
            };
            return classMap[priority] || classMap.normal;
        }

        function formatTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffMs = now - date;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffHours / 24);

            if (diffDays > 0) {
                return `${diffDays}天前`;
            } else if (diffHours > 0) {
                return `${diffHours}小时前`;
            } else {
                const diffMinutes = Math.floor(diffMs / (1000 * 60));
                return diffMinutes > 0 ? `${diffMinutes}分钟前` : '刚刚';
            }
        }

        return {
            notifications,
            loading,
            filter,
            filteredNotifications,
            unconfirmedCount,
            loadNotifications,
            handleConfirmNotification,
            getNotificationTypeText,
            getNotificationIcon,
            getNotificationPriority,
            getPriorityClass,
            formatTimeAgo,
            showNotification,
            fileManagementUtils
        };
    },
    template: `
        <div class="notification-list">
            <!-- 统计信息和控制面板 -->
            <div class="gradient-bg text-white p-6 rounded-xl shadow-xl mb-8">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="mb-4 md:mb-0">
                        <div class="flex items-center space-x-3">
                            <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold">文件变更通知</h2>
                                <p class="text-blue-100 mt-1">
                                    共 {{ notifications.length }} 条通知
                                    <span v-if="unconfirmedCount > 0" class="ml-2 px-2 py-1 bg-red-500 text-white text-xs rounded-full">
                                        {{ unconfirmedCount }} 条待确认
                                    </span>
                                    <span v-else class="ml-2 px-2 py-1 bg-green-500 text-white text-xs rounded-full">
                                        全部已确认
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                        <select v-model="filter"
                                class="px-4 py-2 bg-white text-gray-900 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-300 shadow-sm">
                            <option value="all">📋 全部通知</option>
                            <option value="unconfirmed">⏰ 待确认 ({{ unconfirmedCount }})</option>
                            <option value="confirmed">✅ 已确认</option>
                        </select>

                        <button @click="loadNotifications"
                                :disabled="loading"
                                class="px-6 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors shadow-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                            <span v-if="loading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                刷新中...
                            </span>
                            <span v-else class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p class="mt-2 text-gray-600">加载中...</p>
            </div>

            <!-- 通知列表 -->
            <div v-else-if="filteredNotifications.length > 0" class="space-y-4">
                <div v-for="notification in filteredNotifications" :key="notification.id"
                     :class="[
                         'notification-card bg-white rounded-xl shadow-md border transition-all duration-300 hover:shadow-lg',
                         notification.confirmed ? 'opacity-90' : '',
                         notification.justConfirmed ? 'ring-2 ring-green-400 bg-green-50' : '',
                         getPriorityClass(getNotificationPriority(notification)),
                         getNotificationPriority(notification) === 'high' ? 'priority-high' : '',
                         getNotificationPriority(notification) === 'medium' ? 'priority-medium' : '',
                         'border-l-4'
                     ]">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <!-- 通知头部 -->
                                <div class="flex items-start space-x-4 mb-4">
                                    <div class="flex-shrink-0">
                                        <div :class="[
                                            'w-12 h-12 rounded-full flex items-center justify-center text-xl',
                                            notification.confirmed ? 'bg-green-100' :
                                            getNotificationPriority(notification) === 'high' ? 'bg-red-100' :
                                            getNotificationPriority(notification) === 'medium' ? 'bg-orange-100' : 'bg-blue-100'
                                        ]">
                                            {{ getNotificationIcon(notification.notification_type) }}
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-2">
                                            <h3 class="text-lg font-semibold text-gray-900 truncate">{{ notification.title }}</h3>
                                            <span class="text-sm text-gray-500 ml-4 flex-shrink-0">{{ formatTimeAgo(notification.sent_at) }}</span>
                                        </div>

                                        <div class="flex flex-wrap items-center gap-2 mb-3">
                                            <span :class="[
                                                'px-3 py-1 text-xs font-medium rounded-full',
                                                notification.notification_type === 'new_file' ? 'bg-blue-100 text-blue-800' : 'bg-orange-100 text-orange-800'
                                            ]">
                                                {{ getNotificationTypeText(notification.notification_type) }}
                                            </span>
                                            <span class="px-3 py-1 bg-gray-100 text-gray-700 text-xs font-mono rounded-full">
                                                {{ notification.file_number }}
                                            </span>
                                            <span v-if="getNotificationPriority(notification) === 'high'"
                                                  class="px-3 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
                                                🔥 紧急
                                            </span>
                                            <span v-else-if="getNotificationPriority(notification) === 'medium'"
                                                  class="px-3 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                                                ⚠️ 重要
                                            </span>
                                        </div>

                                        <div class="text-sm text-gray-600">
                                            <span class="font-medium">发送时间：</span>
                                            {{ fileManagementUtils.formatDateTime(notification.sent_at) }}
                                        </div>
                                    </div>
                                </div>

                                <!-- 文件详细信息 -->
                                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                            <span class="font-medium text-gray-700">客户：</span>
                                            <span class="text-gray-900">{{ notification.customer_name }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                            <span class="font-medium text-gray-700">产品：</span>
                                            <span class="text-gray-900">{{ notification.product_model }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 确认状态 -->
                                <div class="flex items-center justify-between">
                                    <div v-if="notification.confirmed" class="flex items-center space-x-2 text-green-600">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">已确认收到</div>
                                            <div class="text-xs text-gray-500">{{ fileManagementUtils.formatDateTime(notification.confirmed_at) }}</div>
                                        </div>
                                    </div>
                                    <div v-else class="flex items-center space-x-2 text-orange-600">
                                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">等待确认</div>
                                            <div class="text-xs text-gray-500">请确认已收到此文件通知</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 mt-4 pt-4 border-t border-gray-100">
                                <button v-if="!notification.confirmed"
                                        @click="handleConfirmNotification(notification)"
                                        :disabled="notification.confirming"
                                        class="flex-1 sm:flex-none px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-sm disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span v-if="notification.confirming" class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        确认中...
                                    </span>
                                    <span v-else class="flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        确认收到
                                    </span>
                                </button>

                                <a :href="'/file-list?id=' + notification.file_record_id"
                                   class="flex-1 sm:flex-none px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-sm text-center">
                                    <span class="flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        查看文件
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="bg-white rounded-xl shadow-md border p-12 text-center">
                <div class="max-w-md mx-auto">
                    <div class="w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-10 h-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                        {{ filter === 'unconfirmed' ? '🎉 全部已确认' : filter === 'confirmed' ? '暂无已确认通知' : '暂无通知' }}
                    </h3>

                    <p class="text-gray-500 mb-6">
                        {{ filter === 'unconfirmed' ? '太棒了！所有文件通知都已确认收到。' :
                           filter === 'confirmed' ? '还没有已确认的通知记录。' :
                           '还没有收到任何文件变更通知。' }}
                    </p>

                    <div v-if="filter !== 'all'" class="space-x-3">
                        <button @click="filter = 'all'"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            查看全部通知
                        </button>
                    </div>
                </div>
            </div>

            <!-- 快速操作提示 -->
            <div v-if="unconfirmedCount > 0" class="mt-6 bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-orange-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-orange-800">待处理提醒</h4>
                        <p class="text-sm text-orange-700 mt-1">
                            您还有 {{ unconfirmedCount }} 条文件通知需要确认，请及时处理以确保工作流程顺畅。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    `
};
