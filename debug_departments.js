const Database = require('better-sqlite3');
const path = require('path');

// 连接数据库
const dbPath = path.join(__dirname, 'backend', 'database', 'application_system.db');
const db = new Database(dbPath);

try {
    // 查询部门表
    console.log('查询部门表...');
    const departments = db.prepare('SELECT * FROM departments').all();
    console.log('部门数据:', departments);
    
    // 查询表结构
    console.log('\n部门表结构:');
    const tableInfo = db.prepare("PRAGMA table_info(departments)").all();
    console.log(tableInfo);
    
} catch (error) {
    console.error('查询失败:', error);
} finally {
    db.close();
}
