/**
 * 文件详情模态框组件
 * 显示文件的完整详细信息
 */

import { downloadFile, fileManagementUtils } from '../../scripts/api/file-management.js';

const { ref, computed, watch } = Vue;

export default {
    name: 'FileDetailModal',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        file: {
            type: Object,
            default: null
        }
    },
    emits: ['close'],
    setup(props, { emit }) {
        // 响应式数据
        const modalVisible = ref(false);

        // 计算属性
        const hasAttachments = computed(() => {
            return props.file && props.file.attachments && props.file.attachments.length > 0;
        });

        const totalFileSize = computed(() => {
            if (!hasAttachments.value) return 0;
            return props.file.attachments.reduce((total, attachment) => total + attachment.file_size, 0);
        });

        // 监听显示状态
        watch(() => props.show, (newVal) => {
            modalVisible.value = newVal;
            if (newVal) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });

        // 方法
        function closeModal() {
            modalVisible.value = false;
            document.body.style.overflow = '';
            emit('close');
        }

        function handleDownloadFile(attachment) {
            if (props.file && attachment) {
                downloadFile(props.file.id, attachment.id);
            }
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function getStatusText(status) {
            switch (status) {
                case 'active': return '有效';
                case 'inactive': return '无效';
                case 'archived': return '已归档';
                default: return status || '未知';
            }
        }

        function getStatusClass(status) {
            switch (status) {
                case 'active': return 'bg-green-100 text-green-800';
                case 'inactive': return 'bg-red-100 text-red-800';
                case 'archived': return 'bg-gray-100 text-gray-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        return {
            modalVisible,
            hasAttachments,
            totalFileSize,
            closeModal,
            handleDownloadFile,
            formatDate,
            getStatusText,
            getStatusClass,
            fileManagementUtils
        };
    },
    template: `
        <!-- 模态框遮罩 -->
        <div v-if="modalVisible" 
             class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
             @click.self="closeModal">
            
            <!-- 模态框内容 -->
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <h2 class="text-xl font-semibold text-gray-900">文件详情</h2>
                        <span v-if="file" class="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                            {{ file.file_number }}
                        </span>
                    </div>
                    <button @click="closeModal" 
                            class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 模态框主体 -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]" v-if="file">
                    <!-- 基本信息 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">文件标题</label>
                                    <p class="text-sm text-gray-900">{{ file.title }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">文件编号</label>
                                    <p class="text-sm text-gray-900">{{ file.file_number }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">客户名称</label>
                                    <p class="text-sm text-gray-900">{{ file.customer_name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">产品型号</label>
                                    <p class="text-sm text-gray-900">{{ file.product_model }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">版本号</label>
                                    <div class="flex items-center space-x-2">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                            V{{ file.version }}
                                        </span>
                                        <span v-if="!file.is_first_version" class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                            变更版本
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                    <span :class="getStatusClass(file.status)" class="px-2 py-1 text-xs rounded-full">
                                        {{ getStatusText(file.status) }}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">上传人员</label>
                                    <p class="text-sm text-gray-900">{{ file.uploaded_by_name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">上传时间</label>
                                    <p class="text-sm text-gray-900">{{ formatDate(file.uploaded_at) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件描述 -->
                    <div v-if="file.description" class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">文件描述</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ file.description }}</p>
                        </div>
                    </div>

                    <!-- 变更说明 -->
                    <div v-if="file.change_description && !file.is_first_version" class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">变更说明</h3>
                        <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ file.change_description }}</p>
                        </div>
                    </div>

                    <!-- 附件列表 -->
                    <div v-if="hasAttachments" class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            附件列表 
                            <span class="text-sm font-normal text-gray-500">
                                ({{ file.attachments.length }} 个文件，总大小 {{ fileManagementUtils.formatFileSize(totalFileSize) }})
                            </span>
                        </h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="space-y-3">
                                <div v-for="attachment in file.attachments" :key="attachment.id"
                                     class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                                    <div class="flex items-center space-x-3 flex-1">
                                        <span class="text-2xl">{{ fileManagementUtils.getFileTypeIcon(attachment.original_filename) }}</span>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 truncate">{{ attachment.original_filename }}</p>
                                            <p class="text-xs text-gray-500">
                                                {{ fileManagementUtils.formatFileSize(attachment.file_size) }} • 
                                                {{ attachment.mime_type }}
                                            </p>
                                        </div>
                                    </div>
                                    <button @click="handleDownloadFile(attachment)"
                                            class="ml-3 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                                        下载
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 无附件提示 -->
                    <div v-else class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">附件列表</h3>
                        <div class="bg-gray-50 rounded-lg p-8 text-center">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-gray-500">暂无附件</p>
                        </div>
                    </div>
                </div>

                <!-- 模态框底部 -->
                <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
                    <button @click="closeModal" 
                            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    `
};
