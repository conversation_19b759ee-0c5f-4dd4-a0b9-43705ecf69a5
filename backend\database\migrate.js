/**
 * 数据迁移脚本
 * 将JSON文件数据迁移到SQLite数据库
 */

const fs = require('fs');
const path = require('path');
const databaseManager = require('./database');
const logger = require('../utils/logger');

class DataMigrator {
    constructor() {
        this.db = databaseManager.getConnection();
        this.dataPath = path.join(__dirname, '..', 'data');
    }

    /**
     * 执行完整数据迁移
     */
    async migrate() {
        logger.info('开始数据迁移...');
        
        try {
            // 开始事务
            const transaction = this.db.transaction(() => {
                this.migrateUsers();
                this.migratePermissionTemplates();
                this.migrateApplications();
            });

            transaction();
            
            logger.info('数据迁移完成');
            return true;
        } catch (error) {
            logger.error('数据迁移失败:', error);
            throw error;
        }
    }

    /**
     * 迁移用户数据
     */
    migrateUsers() {
        logger.info('迁移用户数据...');
        
        const usersDir = path.join(this.dataPath, 'users');
        if (!fs.existsSync(usersDir)) {
            logger.warn('用户数据目录不存在，跳过用户迁移');
            return;
        }

        const userFiles = fs.readdirSync(usersDir).filter(file => file.endsWith('.json'));
        const insertUser = this.db.prepare(`
            INSERT OR REPLACE INTO users (
                id, usercode, username, password, role, department, email,
                active, permissions, last_login_at, last_active_at, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        let migratedCount = 0;
        
        for (const file of userFiles) {
            try {
                const filePath = path.join(usersDir, file);
                const userData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                
                insertUser.run(
                    userData.id,
                    userData.usercode,
                    userData.username,
                    userData.password,
                    userData.role,
                    userData.department || '',
                    userData.email || '',
                    userData.active !== false ? 1 : 0,
                    JSON.stringify(userData.permissions || []),
                    userData.lastLoginAt || userData.last_login_at || null,
                    userData.lastActiveAt || userData.last_active_at || null,
                    userData.createdAt || userData.created_at || new Date().toISOString(),
                    userData.updatedAt || userData.updated_at || new Date().toISOString()
                );
                
                migratedCount++;
            } catch (error) {
                logger.error(`迁移用户文件失败 ${file}:`, error);
            }
        }
        
        logger.info(`用户数据迁移完成，共迁移 ${migratedCount} 个用户`);
    }

    /**
     * 迁移权限模板数据
     */
    migratePermissionTemplates() {
        logger.info('迁移权限模板数据...');
        
        const templatesFile = path.join(this.dataPath, 'permission-templates.json');
        if (!fs.existsSync(templatesFile)) {
            logger.warn('权限模板文件不存在，跳过权限模板迁移');
            return;
        }

        try {
            const templates = JSON.parse(fs.readFileSync(templatesFile, 'utf8'));
            const insertTemplate = this.db.prepare(`
                INSERT OR REPLACE INTO permission_templates (
                    id, name, description, permissions, is_built_in, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `);

            let migratedCount = 0;
            
            for (const template of templates) {
                insertTemplate.run(
                    template.id,
                    template.name,
                    template.description || '',
                    JSON.stringify(template.permissions || []),
                    template.isBuiltIn ? 1 : 0,
                    template.createdAt || template.created_at || new Date().toISOString(),
                    template.updatedAt || template.updated_at || new Date().toISOString()
                );
                migratedCount++;
            }
            
            logger.info(`权限模板数据迁移完成，共迁移 ${migratedCount} 个模板`);
        } catch (error) {
            logger.error('迁移权限模板数据失败:', error);
        }
    }

    /**
     * 迁移申请数据
     */
    migrateApplications() {
        logger.info('迁移申请数据...');
        
        const applicationsFile = path.join(this.dataPath, 'applications.json');
        if (!fs.existsSync(applicationsFile)) {
            logger.warn('申请数据文件不存在，跳过申请迁移');
            return;
        }

        try {
            const applications = JSON.parse(fs.readFileSync(applicationsFile, 'utf8'));
            
            const insertApplication = this.db.prepare(`
                INSERT OR REPLACE INTO applications (
                    id, application_number, user_id, applicant, department, date,
                    content, amount, priority, type, status, current_stage,
                    need_manager_approval, selected_factory_managers, pdf_path,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            const insertAttachment = this.db.prepare(`
                INSERT OR REPLACE INTO application_attachments (
                    id, application_id, name, path, filename, type, size, uploaded_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `);

            const insertApprovalHistory = this.db.prepare(`
                INSERT OR REPLACE INTO approval_history (
                    application_id, stage, approver_id, approver_name, approver_role,
                    action, comment, signature_path, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            let migratedCount = 0;
            
            for (const app of applications) {
                // 插入申请主记录
                insertApplication.run(
                    app.id,
                    app.applicationNumber || app.application_number,
                    app.userId || app.user_id,
                    app.applicant,
                    app.department || '',
                    app.date,
                    app.content,
                    app.amount || '',
                    app.priority || 'normal',
                    app.type || 'standard',
                    app.status || 'pending',
                    app.currentStage || app.current_stage || '',
                    app.needManagerApproval ? 1 : 0,
                    JSON.stringify(app.selectedFactoryManagers || app.selected_factory_managers || []),
                    app.pdfPath || app.pdf_path || '',
                    app.createdAt || app.created_at || new Date().toISOString(),
                    app.updatedAt || app.updated_at || new Date().toISOString()
                );

                // 插入附件记录
                if (app.attachments && app.attachments.length > 0) {
                    for (const attachment of app.attachments) {
                        insertAttachment.run(
                            attachment.id,
                            app.id,
                            attachment.name,
                            attachment.path,
                            attachment.filename || '',
                            attachment.type || attachment.mimetype || '',
                            attachment.size || 0,
                            attachment.uploadedAt || attachment.uploaded_at || new Date().toISOString()
                        );
                    }
                }

                // 插入审批历史记录
                if (app.approvalHistory && app.approvalHistory.length > 0) {
                    for (const history of app.approvalHistory) {
                        insertApprovalHistory.run(
                            app.id,
                            history.stage,
                            history.approverId || history.approver_id,
                            history.approverName || history.approver_name,
                            history.approverRole || history.approver_role,
                            history.action,
                            history.comment || '',
                            history.signaturePath || history.signature_path || '',
                            history.timestamp
                        );
                    }
                }

                migratedCount++;
            }
            
            logger.info(`申请数据迁移完成，共迁移 ${migratedCount} 个申请`);
        } catch (error) {
            logger.error('迁移申请数据失败:', error);
        }
    }

    /**
     * 验证迁移结果
     */
    validateMigration() {
        logger.info('验证迁移结果...');
        
        try {
            const userCount = this.db.prepare('SELECT COUNT(*) as count FROM users').get().count;
            const applicationCount = this.db.prepare('SELECT COUNT(*) as count FROM applications').get().count;
            const templateCount = this.db.prepare('SELECT COUNT(*) as count FROM permission_templates').get().count;
            
            logger.info(`迁移验证结果:`);
            logger.info(`- 用户数量: ${userCount}`);
            logger.info(`- 申请数量: ${applicationCount}`);
            logger.info(`- 权限模板数量: ${templateCount}`);
            
            return { userCount, applicationCount, templateCount };
        } catch (error) {
            logger.error('验证迁移结果失败:', error);
            throw error;
        }
    }
}

module.exports = DataMigrator;
