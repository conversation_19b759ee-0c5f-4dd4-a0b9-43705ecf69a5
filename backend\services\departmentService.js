/**
 * 部门业务服务层
 * 处理部门相关的业务逻辑
 */

const departmentRepository = require('../database/departmentRepository');
const logger = require('../utils/logger');

class DepartmentService {
    /**
     * 获取所有部门
     */
    getAllDepartments() {
        try {
            return departmentRepository.findAll();
        } catch (error) {
            logger.error('获取部门列表失败:', error);
            throw new Error('获取部门列表失败');
        }
    }

    /**
     * 根据ID获取部门
     */
    getDepartmentById(id) {
        try {
            if (!id) {
                throw new Error('部门ID不能为空');
            }

            const department = departmentRepository.findById(id);
            if (!department) {
                throw new Error('部门不存在');
            }

            return department;
        } catch (error) {
            logger.error(`获取部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 创建新部门
     */
    createDepartment(departmentData) {
        try {
            // 验证必填字段
            if (!departmentData.name || !departmentData.name.trim()) {
                throw new Error('部门名称不能为空');
            }

            // 检查部门名称是否已存在
            if (departmentRepository.isNameExists(departmentData.name.trim())) {
                throw new Error('部门名称已存在');
            }

            // 创建部门
            const newDepartment = departmentRepository.create({
                name: departmentData.name.trim(),
                description: departmentData.description?.trim() || ''
            });

            logger.info(`创建部门成功: ${newDepartment.name} (ID: ${newDepartment.id})`);
            return newDepartment;
        } catch (error) {
            logger.error('创建部门失败:', error);
            throw error;
        }
    }

    /**
     * 更新部门
     */
    updateDepartment(id, departmentData) {
        try {
            if (!id) {
                throw new Error('部门ID不能为空');
            }

            // 检查部门是否存在
            const existingDepartment = departmentRepository.findById(id);
            if (!existingDepartment) {
                throw new Error('部门不存在');
            }

            // 验证必填字段
            if (!departmentData.name || !departmentData.name.trim()) {
                throw new Error('部门名称不能为空');
            }

            // 检查部门名称是否已存在（排除当前部门）
            if (departmentRepository.isNameExists(departmentData.name.trim(), id)) {
                throw new Error('部门名称已存在');
            }

            // 更新部门
            const updatedDepartment = departmentRepository.update(id, {
                name: departmentData.name.trim(),
                description: departmentData.description?.trim() || ''
            });

            logger.info(`更新部门成功: ${updatedDepartment.name} (ID: ${updatedDepartment.id})`);
            return updatedDepartment;
        } catch (error) {
            logger.error(`更新部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除部门
     */
    deleteDepartment(id) {
        try {
            if (!id) {
                throw new Error('部门ID不能为空');
            }

            // 检查部门是否存在
            const existingDepartment = departmentRepository.findById(id);
            if (!existingDepartment) {
                throw new Error('部门不存在');
            }

            // 检查是否有用户关联到此部门
            const hasUsers = departmentRepository.hasUsersInDepartment(existingDepartment.name);
            if (hasUsers) {
                throw new Error(`无法删除部门"${existingDepartment.name}"，该部门下还有用户，请先将用户转移到其他部门后再删除`);
            }

            // 执行硬删除
            const result = departmentRepository.delete(id);
            if (!result) {
                throw new Error('删除部门失败');
            }

            logger.info(`删除部门成功: ${existingDepartment.name} (ID: ${id})`);
            return true;
        } catch (error) {
            logger.error(`删除部门失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 检查部门名称是否存在
     */
    isDepartmentNameExists(name, excludeId = '') {
        try {
            if (!name || !name.trim()) {
                return false;
            }

            return departmentRepository.isNameExists(name.trim(), excludeId);
        } catch (error) {
            logger.error('检查部门名称是否存在失败:', error);
            throw new Error('检查部门名称失败');
        }
    }

    /**
     * 检查部门是否有用户关联
     */
    checkDepartmentHasUsers(departmentName) {
        try {
            if (!departmentName || !departmentName.trim()) {
                return false;
            }

            return departmentRepository.hasUsersInDepartment(departmentName.trim());
        } catch (error) {
            logger.error('检查部门用户关联失败:', error);
            throw new Error('检查部门用户关联失败');
        }
    }

    /**
     * 初始化默认部门数据
     */
    initDefaultDepartments() {
        try {
            departmentRepository.initDefaultDepartments();
            logger.info('默认部门数据初始化完成');
        } catch (error) {
            logger.error('初始化默认部门数据失败:', error);
            throw new Error('初始化默认部门数据失败');
        }
    }


}

module.exports = new DepartmentService();
