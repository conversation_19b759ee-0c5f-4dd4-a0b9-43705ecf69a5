/**
 * Toast 通知工具
 * 提供全局的非阻塞消息通知功能
 */

class ToastManager {
    constructor() {
        this.toasts = [];
        this.container = null;
        this.init();
    }

    init() {
        // 创建Toast容器
        this.container = document.createElement('div');
        this.container.id = 'toast-container';
        this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(this.container);
    }

    show(message, type = 'success', duration = 3000) {
        const toastId = Date.now() + Math.random();
        
        // 创建Toast元素
        const toast = document.createElement('div');
        toast.id = `toast-${toastId}`;
        toast.className = this.getToastClass(type);
        toast.innerHTML = this.getToastHTML(message, type, toastId);
        
        // 添加到容器
        this.container.appendChild(toast);
        
        // 触发显示动画
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
            toast.classList.add('translate-x-0', 'opacity-100');
        }, 10);
        
        // 设置自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.hide(toastId);
            }, duration);
        }
        
        return toastId;
    }

    hide(toastId) {
        const toast = document.getElementById(`toast-${toastId}`);
        if (toast) {
            // 触发隐藏动画
            toast.classList.remove('translate-x-0', 'opacity-100');
            toast.classList.add('translate-x-full', 'opacity-0');
            
            // 动画完成后移除元素
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    getToastClass(type) {
        const baseClass = 'px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full opacity-0';
        const typeClasses = {
            success: 'bg-green-500 text-white',
            error: 'bg-red-500 text-white',
            warning: 'bg-yellow-500 text-white',
            info: 'bg-blue-500 text-white'
        };
        
        return `${baseClass} ${typeClasses[type] || typeClasses.success}`;
    }

    getToastHTML(message, type, toastId) {
        const icons = {
            success: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>`,
            error: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>`,
            warning: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>`,
            info: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>`
        };

        return `
            <div class="flex items-center space-x-2">
                <div class="flex-shrink-0">${icons[type] || icons.success}</div>
                <span class="flex-1">${message}</span>
                <button onclick="toast.hide(${toastId})" class="flex-shrink-0 hover:opacity-75">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
    }

    // 便捷方法
    success(message, duration = 3000) {
        return this.show(message, 'success', duration);
    }

    error(message, duration = 5000) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration = 4000) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration = 3000) {
        return this.show(message, 'info', duration);
    }

    // 清除所有Toast
    clear() {
        const toasts = this.container.querySelectorAll('[id^="toast-"]');
        toasts.forEach(toast => {
            const toastId = toast.id.replace('toast-', '');
            this.hide(toastId);
        });
    }
}

// 创建全局实例
const toast = new ToastManager();

// 导出全局实例
export default toast;

// 也可以作为window对象的属性使用
if (typeof window !== 'undefined') {
    window.toast = toast;
}
