# 系统监控 API 文档

## 📋 概述

系统监控模块提供系统健康检查、性能监控、错误跟踪等功能，帮助管理员了解系统运行状态。

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 系统监控 | `admin` 角色 |
| 健康检查 | 所有认证用户 |
| 性能数据 | `admin` 角色 |

## 📊 API 接口

### 1. 系统健康检查

**GET** `/api/system/health`

检查系统各组件的健康状态。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "status": "healthy",
  "timestamp": "2025-07-29T12:00:00.000Z",
  "uptime": 86400,
  "components": {
    "database": {
      "status": "healthy",
      "responseTime": 5,
      "connections": {
        "active": 3,
        "idle": 7,
        "total": 10
      },
      "lastCheck": "2025-07-29T12:00:00.000Z"
    },
    "fileSystem": {
      "status": "healthy",
      "diskUsage": {
        "total": "100GB",
        "used": "45GB",
        "free": "55GB",
        "percentage": 45
      },
      "uploadsDirectory": {
        "accessible": true,
        "permissions": "rwx"
      }
    },
    "memory": {
      "status": "healthy",
      "usage": {
        "total": "8GB",
        "used": "2.5GB",
        "free": "5.5GB",
        "percentage": 31.25
      }
    },
    "cpu": {
      "status": "healthy",
      "usage": {
        "current": 15.5,
        "average": 12.3,
        "cores": 4
      }
    }
  }
}
```

### 2. 系统性能监控

**GET** `/api/system/performance`

获取系统性能指标和统计数据。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| period | string | 否 | 统计周期（1h、24h、7d、30d） |
| metrics | string | 否 | 指标类型（cpu、memory、disk、network） |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "period": "24h",
  "timestamp": "2025-07-29T12:00:00.000Z",
  "metrics": {
    "cpu": {
      "current": 15.5,
      "average": 12.3,
      "peak": 45.2,
      "history": [
        {
          "timestamp": "2025-07-29T11:00:00.000Z",
          "value": 18.5
        },
        {
          "timestamp": "2025-07-29T10:00:00.000Z",
          "value": 12.1
        }
      ]
    },
    "memory": {
      "current": 31.25,
      "average": 28.5,
      "peak": 52.1,
      "history": [
        {
          "timestamp": "2025-07-29T11:00:00.000Z",
          "value": 32.1
        }
      ]
    },
    "disk": {
      "usage": 45,
      "readSpeed": "150MB/s",
      "writeSpeed": "120MB/s",
      "iops": {
        "read": 1500,
        "write": 800
      }
    },
    "network": {
      "inbound": "50MB/s",
      "outbound": "30MB/s",
      "connections": {
        "active": 150,
        "total": 200
      }
    }
  }
}
```

### 3. API性能统计

**GET** `/api/system/api-stats`

获取API接口的性能统计数据。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "period": "24h",
  "totalRequests": 15420,
  "averageResponseTime": 125,
  "errorRate": 0.5,
  "endpoints": [
    {
      "path": "/api/applications",
      "method": "GET",
      "requests": 3500,
      "averageResponseTime": 85,
      "errorCount": 12,
      "errorRate": 0.34
    },
    {
      "path": "/api/users",
      "method": "GET", 
      "requests": 2100,
      "averageResponseTime": 65,
      "errorCount": 5,
      "errorRate": 0.24
    }
  ],
  "slowestEndpoints": [
    {
      "path": "/api/equipment/export",
      "method": "GET",
      "averageResponseTime": 2500,
      "requests": 45
    }
  ],
  "errorsByStatus": {
    "400": 25,
    "401": 18,
    "403": 12,
    "404": 8,
    "500": 3
  }
}
```

### 4. 系统日志

**GET** `/api/system/logs`

获取系统日志记录。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| level | string | 否 | 日志级别（error、warn、info、debug） |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |
| limit | number | 否 | 返回数量限制 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "log001",
      "timestamp": "2025-07-29T11:45:00.000Z",
      "level": "error",
      "message": "Database connection timeout",
      "source": "database.js:45",
      "userId": "user123",
      "ip": "*************",
      "userAgent": "Mozilla/5.0...",
      "stack": "Error: Connection timeout\n    at Database.connect...",
      "metadata": {
        "query": "SELECT * FROM users",
        "duration": 5000
      }
    },
    {
      "id": "log002",
      "timestamp": "2025-07-29T11:30:00.000Z",
      "level": "warn",
      "message": "High memory usage detected",
      "source": "monitor.js:120",
      "metadata": {
        "memoryUsage": 85.5,
        "threshold": 80
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1250,
    "totalPages": 25
  }
}
```

### 5. 错误统计

**GET** `/api/system/errors`

获取系统错误统计信息。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "period": "24h",
  "totalErrors": 45,
  "errorRate": 0.29,
  "byLevel": {
    "error": 12,
    "warn": 33
  },
  "bySource": {
    "database": 8,
    "authentication": 15,
    "fileUpload": 12,
    "api": 10
  },
  "topErrors": [
    {
      "message": "JWT token expired",
      "count": 15,
      "lastOccurred": "2025-07-29T11:45:00.000Z"
    },
    {
      "message": "File upload failed",
      "count": 12,
      "lastOccurred": "2025-07-29T11:30:00.000Z"
    }
  ],
  "trend": [
    {
      "hour": "2025-07-29T11:00:00.000Z",
      "errors": 8
    },
    {
      "hour": "2025-07-29T10:00:00.000Z", 
      "errors": 5
    }
  ]
}
```

### 6. 系统配置信息

**GET** `/api/system/config`

获取系统配置信息（敏感信息已脱敏）。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "version": "2.0.1",
    "environment": "production",
    "nodeVersion": "18.17.0",
    "platform": "linux",
    "architecture": "x64",
    "database": {
      "type": "sqlite",
      "version": "3.42.0",
      "path": "/app/database/application_system.db"
    },
    "features": {
      "emailService": true,
      "fileUpload": true,
      "pdfGeneration": true,
      "excelExport": true
    },
    "limits": {
      "maxFileSize": "10MB",
      "maxFilesPerUpload": 10,
      "sessionTimeout": "24h",
      "requestRateLimit": "100/min"
    }
  }
}
```

### 7. 清理系统缓存

**POST** `/api/system/cache/clear`

清理系统缓存。

#### 请求参数

```json
{
  "types": ["api", "files", "sessions"]
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "缓存清理成功",
  "data": {
    "cleared": {
      "api": 150,
      "files": 25,
      "sessions": 8
    },
    "totalCleared": 183
  }
}
```

## 🚨 系统告警

### 告警级别

| 级别 | 描述 | 触发条件 |
|------|------|----------|
| Critical | 严重 | 系统不可用、数据库连接失败 |
| High | 高 | CPU/内存使用率>90%、磁盘空间<10% |
| Medium | 中 | 响应时间>2s、错误率>5% |
| Low | 低 | 性能下降、警告日志增多 |

### 监控指标阈值

| 指标 | 警告阈值 | 严重阈值 |
|------|----------|----------|
| CPU使用率 | 70% | 90% |
| 内存使用率 | 80% | 95% |
| 磁盘使用率 | 80% | 95% |
| 响应时间 | 1000ms | 3000ms |
| 错误率 | 2% | 5% |

---

**更新时间**: 2025-07-29  
**版本**: v1.0
