/**
 * 设备健康度评估页面脚本
 * 处理设备健康度评估的业务逻辑
 */

import { createStandardApp } from '../../common/pageInit.js';
import EquipmentAPI from '../../api/equipment.js';

// 创建页面应用
const app = createStandardApp({
    requiredPermissions: ['equipment_health'],
    onUserLoaded: async (user) => {
        console.log('设备健康度评估页面初始化完成', user);
    },
    setup() {
        const { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } = Vue;

        // 响应式数据
        const loading = ref(false);
        const calculatingEquipment = ref(new Set()); // 正在计算健康度的设备ID集合
        const equipmentList = ref([]);
        const sidebarOpen = ref(false); // 移动端侧边栏状态
        const healthStatistics = reactive({
            overview: {
                totalEquipment: 0,
                averageHealth: 0,
                lastUpdated: null
            },
            distribution: {
                excellent: 0,
                good: 0,
                average: 0,
                poor: 0,
                dangerous: 0
            },
            warningEquipment: []
        });

        const selectedEquipment = ref(null);
        const healthDetail = ref(null);
        const showDetailModal = ref(false);
        const showStandardModal = ref(false); // 评估标准说明模态框
        const activeTab = ref('overview'); // 详情模态框的活动标签页

        // 图表相关数据
        const trendPeriod = ref('30'); // 趋势图时间周期
        const charts = reactive({
            healthDistribution: null,
            healthTrend: null,
            areaComparison: null
        });
        const chartsInitialized = ref(false); // 标记图表是否已初始化

        // 监听模态框状态，控制body滚动
        watch(showStandardModal, (newValue) => {
            if (newValue) {
                document.body.classList.add('modal-open');
            } else {
                document.body.classList.remove('modal-open');
            }
        });

        watch(showDetailModal, (newValue) => {
            if (newValue) {
                document.body.classList.add('modal-open');
            } else {
                document.body.classList.remove('modal-open');
            }
        });

        // 视图和筛选相关
        const viewMode = ref('cards'); // 'cards' 或 'table'
        const searchQuery = ref('');
        const showFilters = ref(false);
        const filters = reactive({
            area: '',
            healthLevel: '',
            status: ''
        });

        // 分页相关
        const currentPage = ref(1);
        const pageSize = ref(12); // 卡片视图每页显示数量
        const totalEquipment = ref(0);
        const totalPages = computed(() => Math.ceil(totalEquipment.value / pageSize.value));

        // 卡片视图每个厂区的分页状态
        const areaPageStates = ref(new Map()); // 存储每个厂区的当前页码
        const areaPageSize = 12; // 每个厂区每页显示的设备数量

        // 厂区管理相关
        const collapsedAreas = ref(new Set()); // 折叠的厂区
        const areaOrder = ref([]); // 厂区排序
        const isDragging = ref(false); // 是否正在拖拽
        const dragStartTime = ref(0); // 拖拽开始时间
        const longPressTimer = ref(null); // 长按定时器
        const draggedAreaIndex = ref(-1); // 被拖拽的厂区索引

        // 计算属性
        const healthLevelColor = computed(() => (score) => {
            if (score >= 90) return 'text-green-600 bg-green-100';
            if (score >= 80) return 'text-blue-600 bg-blue-100';
            if (score >= 70) return 'text-yellow-600 bg-yellow-100';
            if (score >= 60) return 'text-orange-600 bg-orange-100';
            return 'text-red-600 bg-red-100';
        });

        const healthLevelText = computed(() => (score) => {
            if (score >= 90) return '优秀';
            if (score >= 80) return '良好';
            if (score >= 70) return '一般';
            if (score >= 60) return '较差';
            return '危险';
        });

        // 按厂区分组的设备列表
        const groupedEquipmentByArea = computed(() => {
            let filtered = equipmentList.value.filter(equipment => {
                // 搜索过滤
                if (searchQuery.value) {
                    const query = searchQuery.value.toLowerCase();
                    if (!equipment.name?.toLowerCase().includes(query) &&
                        !equipment.code?.toLowerCase().includes(query) &&
                        !equipment.area?.toLowerCase().includes(query)) {
                        return false;
                    }
                }

                // 区域过滤
                if (filters.area && equipment.area !== filters.area) {
                    return false;
                }

                // 状态过滤
                if (filters.status && equipment.status !== filters.status) {
                    return false;
                }

                // 健康度等级过滤
                if (filters.healthLevel) {
                    const score = getHealthScore(equipment);
                    const level = getHealthLevelFromScore(score);
                    if (level !== filters.healthLevel) {
                        return false;
                    }
                }

                return true;
            });

            // 按厂区分组
            const grouped = {};
            filtered.forEach(equipment => {
                const area = equipment.area || '未分配厂区';
                if (!grouped[area]) {
                    grouped[area] = [];
                }
                grouped[area].push(equipment);
            });

            // 按厂区排序转换为数组
            const sortedAreas = areaOrder.value.length > 0
                ? areaOrder.value.filter(area => grouped[area])
                : Object.keys(grouped).sort();

            // 添加新出现的厂区到排序列表
            Object.keys(grouped).forEach(area => {
                if (!areaOrder.value.includes(area)) {
                    areaOrder.value.push(area);
                }
            });

            const result = sortedAreas.map(area => {
                // 按健康度分数从小到大排序（分数越小越需要关注）
                const sortedEquipment = grouped[area].sort((a, b) => {
                    const scoreA = getHealthScore(a) || 0;
                    const scoreB = getHealthScore(b) || 0;
                    return scoreA - scoreB; // 升序排列，分数小的在前
                });

                // 初始化厂区页码状态
                if (!areaPageStates.value.has(area)) {
                    areaPageStates.value.set(area, 1);
                }

                // 卡片视图分页逻辑
                const currentAreaPage = areaPageStates.value.get(area) || 1;
                const totalAreaPages = Math.ceil(sortedEquipment.length / areaPageSize);
                const startIndex = (currentAreaPage - 1) * areaPageSize;
                const endIndex = startIndex + areaPageSize;
                const paginatedEquipment = viewMode.value === 'cards'
                    ? sortedEquipment.slice(startIndex, endIndex)
                    : sortedEquipment;

                return {
                    area,
                    equipment: paginatedEquipment,
                    allEquipment: sortedEquipment, // 保存所有设备用于统计
                    count: sortedEquipment.length,
                    displayCount: paginatedEquipment.length,
                    collapsed: collapsedAreas.value.has(area),
                    averageHealth: calculateAreaAverageHealth(sortedEquipment),
                    // 分页信息
                    currentPage: currentAreaPage,
                    totalPages: totalAreaPages,
                    hasMultiplePages: totalAreaPages > 1
                };
            });

            // 更新总数
            totalEquipment.value = filtered.length;

            return result;
        });

        // 兼容原有的筛选列表（用于表格视图）
        const filteredEquipmentList = computed(() => {
            const allEquipment = groupedEquipmentByArea.value.flatMap(group => group.equipment);

            // 分页
            const start = (currentPage.value - 1) * pageSize.value;
            const end = start + pageSize.value;
            return allEquipment.slice(start, end);
        });

        // 动态获取筛选选项
        const availableAreas = computed(() => {
            const areas = [...new Set(equipmentList.value.map(eq => eq.area).filter(area => area))];
            return areas.sort();
        });

        const availableStatuses = computed(() => {
            const statuses = [...new Set(equipmentList.value.map(eq => eq.status).filter(status => status))];
            return statuses.sort();
        });

        // 状态显示映射 - 设备启用/停用状态
        const getStatusDisplayName = (status) => {
            const statusMap = {
                'active': '启用',
                'inactive': '停用'
            };
            return statusMap[status] || status;
        };

        // 方法
        const loadEquipmentList = async () => {
            try {
                loading.value = true;
                const response = await EquipmentAPI.getEquipment();
                if (response.success) {
                    equipmentList.value = response.data.equipment || [];

                    // 初始化厂区排序
                    const areas = [...new Set(equipmentList.value.map(eq => eq.area || '未分配厂区'))];
                    if (areaOrder.value.length === 0) {
                        areaOrder.value = areas.sort();
                    } else {
                        // 添加新出现的厂区
                        areas.forEach(area => {
                            if (!areaOrder.value.includes(area)) {
                                areaOrder.value.push(area);
                            }
                        });
                    }

                    // 基于实际设备列表计算统计数据，确保数据一致性
                    calculateStatisticsFromEquipmentList();
                }
            } catch (error) {
                console.error('加载设备列表失败:', error);
                showNotification('加载设备列表失败', 'error');
            } finally {
                loading.value = false;
            }
        };

        // 基于设备列表计算统计数据，确保与明细区数据完全一致
        const calculateStatisticsFromEquipmentList = () => {
            try {
                const equipmentWithHealth = equipmentList.value.filter(eq => eq.healthScore !== null && eq.healthScore !== undefined);

                // 初始化统计数据
                const distribution = {
                    excellent: 0,
                    good: 0,
                    average: 0,
                    poor: 0,
                    dangerous: 0
                };

                let totalScore = 0;
                const warningEquipment = [];

                // 遍历设备列表计算统计
                equipmentWithHealth.forEach(equipment => {
                    const score = equipment.healthScore;
                    totalScore += score;

                    // 根据分数确定等级并统计分布
                    let level = '';
                    if (score >= 90) {
                        distribution.excellent++;
                        level = '优秀';
                    } else if (score >= 80) {
                        distribution.good++;
                        level = '良好';
                    } else if (score >= 70) {
                        distribution.average++;
                        level = '一般';
                    } else if (score >= 60) {
                        distribution.poor++;
                        level = '较差';
                    } else {
                        distribution.dangerous++;
                        level = '危险';
                    }

                    // 收集需要关注的设备（分数低于70）
                    if (score < 70) {
                        warningEquipment.push({
                            equipmentId: equipment.id,
                            equipmentName: equipment.name,
                            equipmentCode: equipment.code,
                            area: equipment.area,
                            totalScore: score,
                            level: level
                        });
                    }
                });

                // 更新统计数据
                Object.assign(healthStatistics, {
                    overview: {
                        totalEquipment: equipmentWithHealth.length, // 只统计有健康度的设备
                        averageHealth: equipmentWithHealth.length > 0 ? Math.round(totalScore / equipmentWithHealth.length) : 0,
                        lastUpdated: new Date().toISOString()
                    },
                    distribution,
                    warningEquipment: warningEquipment.sort((a, b) => a.totalScore - b.totalScore)
                });

                console.log('统计数据已基于设备列表更新:', healthStatistics);

                // 设置所有厂区默认为折叠状态
                const areas = [...new Set(equipmentList.value.map(eq => eq.area).filter(area => area))];
                collapsedAreas.value = new Set(areas);
                console.log('所有厂区已设置为折叠状态:', areas);

                // 只有在图表已初始化的情况下才更新图表
                if (chartsInitialized.value) {
                    nextTick(() => {
                        updateAllCharts();
                    });
                }
            } catch (error) {
                console.error('计算统计数据失败:', error);
            }
        };

        const loadHealthStatistics = async () => {
            try {
                // 优先使用基于设备列表的计算结果，确保数据一致性
                if (equipmentList.value.length > 0) {
                    calculateStatisticsFromEquipmentList();
                    return;
                }

                // 如果设备列表为空，尝试从API获取
                const response = await EquipmentAPI.getHealthStatistics();
                if (response.success) {
                    Object.assign(healthStatistics, response.data);
                }
            } catch (error) {
                console.error('加载健康度统计失败:', error);
                // 如果设备列表有数据，基于设备列表计算
                if (equipmentList.value.length > 0) {
                    calculateStatisticsFromEquipmentList();
                } else {
                    // 否则使用空的默认数据
                    Object.assign(healthStatistics, {
                        overview: {
                            totalEquipment: 0,
                            averageHealth: 0,
                            lastUpdated: new Date().toISOString()
                        },
                        distribution: {
                            excellent: 0,
                            good: 0,
                            average: 0,
                            poor: 0,
                            dangerous: 0
                        },
                        warningEquipment: []
                    });
                }
            }
        };

        const calculateEquipmentHealth = async (equipmentId) => {
            try {
                // 添加到正在计算的设备集合中
                calculatingEquipment.value.add(equipmentId);

                const response = await EquipmentAPI.calculateHealth(equipmentId);
                if (response.success) {
                    showNotification('健康度计算完成', 'success');
                    // 重新加载设备列表以获取最新的健康度数据，统计数据会自动重新计算
                    await loadEquipmentList();
                    return response.data;
                }
            } catch (error) {
                console.error('计算健康度失败:', error);
                showNotification('计算健康度失败', 'error');
            } finally {
                // 从正在计算的设备集合中移除
                calculatingEquipment.value.delete(equipmentId);
            }
        };

        const showEquipmentDetail = async (equipment) => {
            try {
                selectedEquipment.value = equipment;
                loading.value = true;
                activeTab.value = 'overview'; // 重置到概览标签页

                const response = await EquipmentAPI.getHealthAssessment(equipment.id);
                if (response.success) {
                    healthDetail.value = response.data;
                    showDetailModal.value = true;
                }
            } catch (error) {
                console.error('获取设备健康度详情失败:', error);
                showNotification('获取设备详情失败', 'error');
            } finally {
                loading.value = false;
            }
        };

        // 维修记录统计处理函数
        const getMaintenanceStatsByType = (stats) => {
            const typeMap = new Map();
            stats.forEach(stat => {
                if (typeMap.has(stat.type)) {
                    typeMap.set(stat.type, typeMap.get(stat.type) + stat.count);
                } else {
                    typeMap.set(stat.type, stat.count);
                }
            });
            return Array.from(typeMap.entries()).map(([type, count]) => ({ type, count }));
        };

        const getMaintenanceStatsBySeverity = (stats) => {
            const severityMap = new Map();
            stats.forEach(stat => {
                if (stat.severity_level) {
                    if (severityMap.has(stat.severity_level)) {
                        severityMap.set(stat.severity_level, severityMap.get(stat.severity_level) + stat.count);
                    } else {
                        severityMap.set(stat.severity_level, stat.count);
                    }
                }
            });
            return Array.from(severityMap.entries()).map(([severity, count]) => ({ severity, count }));
        };

        const getMaintenanceTypeName = (type) => {
            const typeNames = {
                'maintenance': '保养',
                'repair': '维修',
                'preventive': '预防性维护',
                'emergency': '紧急维修'
            };
            return typeNames[type] || type;
        };

        const getMaintenanceTypeColor = (type) => {
            const colors = {
                'maintenance': 'text-green-600',
                'repair': 'text-red-600',
                'preventive': 'text-blue-600',
                'emergency': 'text-orange-600'
            };
            return colors[type] || 'text-gray-600';
        };

        const getSeverityName = (severity) => {
            const severityNames = {
                'severe': '严重',
                'moderate': '一般',
                'minor': '轻微'
            };
            return severityNames[severity] || severity;
        };

        const getSeverityColor = (severity) => {
            const colors = {
                'severe': 'text-red-600',
                'moderate': 'text-orange-600',
                'minor': 'text-yellow-600'
            };
            return colors[severity] || 'text-gray-600';
        };

        const getLatestMaintenanceDate = (stats) => {
            const latestDate = stats.reduce((latest, stat) => {
                if (stat.latest_date && (!latest || new Date(stat.latest_date) > new Date(latest))) {
                    return stat.latest_date;
                }
                return latest;
            }, null);
            return latestDate ? new Date(latestDate).toLocaleString() : '无记录';
        };

        // 根据设备数量获取健康度圆点大小
        const getHealthDotSize = (count, total) => {
            if (count === 0) return 'w-2 h-2'; // 无设备时显示小圆点

            const ratio = count / total;
            if (ratio >= 0.5) return 'w-4 h-4'; // 50%以上显示大圆点
            if (ratio >= 0.3) return 'w-3.5 h-3.5'; // 30-50%显示中大圆点
            if (ratio >= 0.1) return 'w-3 h-3'; // 10-30%显示中等圆点
            return 'w-2.5 h-2.5'; // 10%以下显示小圆点
        };

        const batchCalculateHealth = async () => {
            try {
                loading.value = true;
                const equipmentIds = equipmentList.value.map(eq => eq.id);

                const response = await EquipmentAPI.batchCalculateHealth(equipmentIds);
                if (response.success) {
                    showNotification(`批量计算完成，成功: ${response.data.summary.successful}，失败: ${response.data.summary.failed}`, 'success');
                    // 重新加载设备列表以获取最新的健康度数据，统计数据会自动重新计算
                    await loadEquipmentList();
                }
            } catch (error) {
                console.error('批量计算失败:', error);
                showNotification('批量计算失败', 'error');
            } finally {
                loading.value = false;
            }
        };

        // 图表相关方法
        const initHealthDistributionChart = () => {
            const ctx = document.getElementById('healthDistributionChart');
            if (!ctx) {
                console.warn('健康度分布图表canvas元素未找到');
                return;
            }

            // 确保画布没有被其他图表使用
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            // 销毁现有图表
            if (charts.healthDistribution) {
                charts.healthDistribution.destroy();
                charts.healthDistribution = null;
            }

            const data = {
                labels: ['优秀', '良好', '一般', '较差', '危险'],
                datasets: [{
                    data: [
                        healthStatistics.distribution.excellent,
                        healthStatistics.distribution.good,
                        healthStatistics.distribution.average,
                        healthStatistics.distribution.poor,
                        healthStatistics.distribution.dangerous
                    ],
                    backgroundColor: [
                        '#10b981', // 绿色 - 优秀
                        '#3b82f6', // 蓝色 - 良好
                        '#f59e0b', // 黄色 - 一般
                        '#f97316', // 橙色 - 较差
                        '#ef4444'  // 红色 - 危险
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverBorderWidth: 4,
                    hoverBorderColor: '#ffffff',
                    // 移除hoverOffset，改用更稳定的悬停效果
                    hoverBackgroundColor: [
                        '#059669', // 优秀 - 更深的绿色
                        '#2563eb', // 良好 - 更深的蓝色
                        '#d97706', // 一般 - 更深的黄色
                        '#ea580c', // 较差 - 更深的橙色
                        '#dc2626'  // 危险 - 更深的红色
                    ]
                }]
            };

            const config = {
                type: 'doughnut',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '65%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            usePointStyle: true,
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                    return `${value}台设备 (${percentage}%)`;
                                }
                            }
                        }
                    },
                    // 添加平滑的动画效果
                    animation: {
                        animateRotate: true,
                        animateScale: true,
                        duration: 1000,
                        easing: 'easeOutQuart'
                    },
                    // 添加交互效果
                    interaction: {
                        intersect: false,
                        mode: 'point'
                    },
                    // 添加悬停效果
                    onHover: (event, activeElements) => {
                        event.native.target.style.cursor = activeElements.length > 0 ? 'pointer' : 'default';
                    }
                }
            };

            charts.healthDistribution = new Chart(ctx, config);
            console.log('健康度分布图表初始化成功');
        };

        const initHealthTrendChart = () => {
            const ctx = document.getElementById('healthTrendChart');
            if (!ctx) {
                console.warn('健康度趋势图表canvas元素未找到');
                return;
            }

            // 确保画布没有被其他图表使用
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            // 销毁现有图表
            if (charts.healthTrend) {
                charts.healthTrend.destroy();
                charts.healthTrend = null;
            }

            // 基于真实设备数据生成趋势数据
            const days = parseInt(trendPeriod.value);
            const labels = [];
            const data = [];
            const today = new Date();

            // 获取当前平均健康度作为基准
            const currentAvgHealth = healthStatistics.overview.averageHealth || 75;

            for (let i = days - 1; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));

                // 基于设备实际状况生成合理的历史趋势
                // 考虑设备老化、维修记录等因素
                let healthValue = currentAvgHealth;

                // 根据时间距离调整健康度（越久远的数据健康度可能略高，因为设备会老化）
                const dayOffset = i;
                const ageingFactor = dayOffset * 0.1; // 每天老化0.1分
                healthValue = Math.min(100, healthValue + ageingFactor);

                // 根据设备维修记录添加一些波动
                const maintenanceImpact = equipmentList.value.filter(eq => {
                    // 检查设备是否有维修记录
                    if (!eq.maintenance_stats || !Array.isArray(eq.maintenance_stats) || eq.maintenance_stats.length === 0) {
                        return false;
                    }

                    // 获取最近的维修日期
                    const latestMaintenanceDate = eq.maintenance_stats.reduce((latest, stat) => {
                        if (stat.latest_date && (!latest || new Date(stat.latest_date) > new Date(latest))) {
                            return stat.latest_date;
                        }
                        return latest;
                    }, null);

                    if (!latestMaintenanceDate) return false;

                    const maintenanceDate = new Date(latestMaintenanceDate);
                    const checkDate = new Date(today);
                    checkDate.setDate(checkDate.getDate() - i);
                    return Math.abs(maintenanceDate - checkDate) < 7 * 24 * 60 * 60 * 1000; // 7天内
                }).length;

                // 维修活动会提升健康度
                if (maintenanceImpact > 0) {
                    healthValue += maintenanceImpact * 2;
                }

                // 确保数值在合理范围内
                data.push(Math.max(50, Math.min(100, healthValue)));
            }

            const chartData = {
                labels: labels,
                datasets: [{
                    label: '平均健康度',
                    data: data,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            };

            const config = {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '分';
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `健康度: ${context.parsed.y.toFixed(1)}分`;
                                }
                            }
                        }
                    }
                }
            };

            charts.healthTrend = new Chart(ctx, config);
            console.log('健康度趋势图表初始化成功');
        };

        const initAreaComparisonChart = () => {
            const ctx = document.getElementById('areaComparisonChart');
            if (!ctx) {
                console.warn('厂区对比图表canvas元素未找到');
                return;
            }

            // 确保画布没有被其他图表使用
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            // 销毁现有图表
            if (charts.areaComparison) {
                charts.areaComparison.destroy();
                charts.areaComparison = null;
            }

            // 从设备列表中计算各厂区的健康度
            const areaHealthMap = new Map();
            equipmentList.value.forEach(equipment => {
                const area = equipment.area || '未知厂区';
                const healthScore = getHealthScore(equipment);

                if (healthScore !== null) {
                    if (!areaHealthMap.has(area)) {
                        areaHealthMap.set(area, { total: 0, count: 0 });
                    }
                    const areaData = areaHealthMap.get(area);
                    areaData.total += healthScore;
                    areaData.count += 1;
                }
            });

            const labels = [];
            const data = [];
            const backgroundColors = [];
            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#f97316'];

            let colorIndex = 0;
            areaHealthMap.forEach((areaData, area) => {
                const averageHealth = areaData.total / areaData.count;
                labels.push(area);
                data.push(averageHealth.toFixed(1));
                backgroundColors.push(colors[colorIndex % colors.length]);
                colorIndex++;
            });

            const chartData = {
                labels: labels,
                datasets: [{
                    label: '平均健康度',
                    data: data,
                    backgroundColor: backgroundColors.map(color => color + '80'), // 添加透明度
                    borderColor: backgroundColors,
                    borderWidth: 2,
                    borderRadius: {
                        topLeft: 6,
                        topRight: 6,
                        bottomLeft: 0,
                        bottomRight: 0
                    },
                    borderSkipped: false,
                    // 添加悬停效果
                    hoverBackgroundColor: backgroundColors,
                    hoverBorderColor: backgroundColors.map(color => color),
                    hoverBorderWidth: 3,
                    // 设置柱子最小高度
                    minBarLength: 2
                }]
            };

            const config = {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    // 设置柱状图宽度
                    categoryPercentage: 0.5, // 控制整个类别的宽度
                    barPercentage: 0.5, // 控制单个柱子的宽度
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)',
                                drawBorder: false
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '分';
                                },
                                font: {
                                    size: 12
                                },
                                color: '#6b7280'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 12
                                },
                                color: '#6b7280',
                                maxRotation: 0, // 防止标签旋转
                                minRotation: 0
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: false,
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    return `平均健康度: ${context.parsed.y}分`;
                                }
                            }
                        }
                    },
                    // 添加动画效果
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    },
                    // 添加悬停效果
                    onHover: (event, activeElements) => {
                        event.native.target.style.cursor = activeElements.length > 0 ? 'pointer' : 'default';
                    }
                }
            };

            charts.areaComparison = new Chart(ctx, config);
            console.log('厂区对比图表初始化成功');
        };

        const updateTrendChart = () => {
            initHealthTrendChart();
        };

        // 销毁所有图表
        const destroyAllCharts = () => {
            if (charts.healthDistribution) {
                charts.healthDistribution.destroy();
                charts.healthDistribution = null;
            }
            if (charts.healthTrend) {
                charts.healthTrend.destroy();
                charts.healthTrend = null;
            }
            if (charts.areaComparison) {
                charts.areaComparison.destroy();
                charts.areaComparison = null;
            }
        };

        const initAllCharts = async () => {
            console.log('开始初始化所有图表...');

            // 检查Chart.js是否可用
            if (typeof Chart === 'undefined') {
                console.error('Chart.js库未加载，无法初始化图表');
                return;
            }

            // 使用nextTick确保DOM元素已渲染
            await nextTick();
            setTimeout(() => {
                console.log('执行图表初始化...');
                initHealthDistributionChart();
                initHealthTrendChart();
                initAreaComparisonChart();
                chartsInitialized.value = true; // 标记图表已初始化
                console.log('图表初始化完成');
            }, 200);
        };

        const updateAllCharts = async () => {
            console.log('更新所有图表...');
            await nextTick();
            setTimeout(() => {
                initHealthDistributionChart();
                initAreaComparisonChart();
                console.log('图表更新完成');
            }, 100);
        };

        // 工具方法
        const isCalculating = (equipmentId) => {
            return calculatingEquipment.value.has(equipmentId);
        };

        const getHealthScore = (equipment) => {
            // 从设备数据中获取健康度分数
            if (equipment.healthScore !== undefined) {
                return equipment.healthScore;
            }
            if (equipment.health_score !== undefined) {
                return equipment.health_score;
            }

            // 如果没有健康度数据，返回null而不是随机数
            return null;
        };

        const getHealthLevel = (score) => {
            if (score === null || score === undefined) return '未评估';
            if (score >= 90) return '优秀';
            if (score >= 80) return '良好';
            if (score >= 70) return '一般';
            if (score >= 60) return '较差';
            return '危险';
        };

        const getHealthLevelFromScore = (score) => {
            if (!score) return '';
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 70) return 'average';
            if (score >= 60) return 'poor';
            return 'dangerous';
        };

        const getHealthScoreColor = (score) => {
            if (score === null || score === undefined) return 'text-gray-400';
            if (score >= 90) return 'text-green-600';
            if (score >= 80) return 'text-blue-600';
            if (score >= 70) return 'text-yellow-600';
            if (score >= 60) return 'text-orange-600';
            return 'text-red-600';
        };

        const getHealthBarColor = (score) => {
            if (score === null || score === undefined) return 'bg-gray-300';
            if (score >= 90) return 'bg-green-500';
            if (score >= 80) return 'bg-blue-500';
            if (score >= 70) return 'bg-yellow-500';
            if (score >= 60) return 'bg-orange-500';
            return 'bg-red-500';
        };

        const getHealthIndicatorColor = (score) => {
            if (score === null || score === undefined) return 'bg-gray-400';
            if (score >= 90) return 'bg-green-500';
            if (score >= 80) return 'bg-blue-500';
            if (score >= 70) return 'bg-yellow-500';
            if (score >= 60) return 'bg-orange-500';
            return 'bg-red-500';
        };

        const getStatusText = (status) => {
            const statusMap = {
                'active': '启用',
                'inactive': '停用'
            };
            return statusMap[status] || '未知';
        };

        const getStatusBadgeColor = (status) => {
            const colorMap = {
                'active': 'bg-green-100 text-green-800',
                'inactive': 'bg-red-100 text-red-800'
            };
            return colorMap[status] || 'bg-gray-100 text-gray-800';
        };

        const getStatusIndicatorColor = (status) => {
            const colorMap = {
                'active': 'bg-green-500',
                'inactive': 'bg-red-500'
            };
            return colorMap[status] || 'bg-gray-500';
        };

        const formatDate = (dateString) => {
            if (!dateString) return '';
            try {
                return new Date(dateString).toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        };

        // 筛选和分页方法
        const resetFilters = () => {
            filters.area = '';
            filters.healthLevel = '';
            filters.status = '';
            searchQuery.value = '';
            currentPage.value = 1;
        };

        const sortBy = (field) => {
            // 实现排序逻辑
            console.log('排序字段:', field);
        };

        const previousPage = () => {
            if (currentPage.value > 1) {
                currentPage.value--;
            }
        };

        const nextPage = () => {
            if (currentPage.value < totalPages.value) {
                currentPage.value++;
            }
        };

        const goToPage = (page) => {
            if (page >= 1 && page <= totalPages.value) {
                currentPage.value = page;
            }
        };

        // 展开/折叠全部功能
        const expandAll = () => {
            collapsedAreas.value.clear();
        };

        const collapseAll = () => {
            const allAreas = groupedEquipmentByArea.value.map(group => group.area);
            allAreas.forEach(area => {
                collapsedAreas.value.add(area);
            });
        };

        // 检查是否所有区域都已展开
        const isAllExpanded = computed(() => {
            const totalAreas = groupedEquipmentByArea.value.length;
            const collapsedCount = collapsedAreas.value.size;
            return totalAreas > 0 && collapsedCount === 0;
        });

        // 检查是否所有区域都已折叠
        const isAllCollapsed = computed(() => {
            const totalAreas = groupedEquipmentByArea.value.length;
            const collapsedCount = collapsedAreas.value.size;
            return totalAreas > 0 && collapsedCount === totalAreas;
        });

        // 厂区分页控制函数
        const goToAreaPage = (area, page) => {
            const areaGroup = groupedEquipmentByArea.value.find(group => group.area === area);
            if (areaGroup && page >= 1 && page <= areaGroup.totalPages) {
                areaPageStates.value.set(area, page);
            }
        };

        const previousAreaPage = (area) => {
            const currentPage = areaPageStates.value.get(area) || 1;
            if (currentPage > 1) {
                goToAreaPage(area, currentPage - 1);
            }
        };

        const nextAreaPage = (area) => {
            const areaGroup = groupedEquipmentByArea.value.find(group => group.area === area);
            const currentPage = areaPageStates.value.get(area) || 1;
            if (areaGroup && currentPage < areaGroup.totalPages) {
                goToAreaPage(area, currentPage + 1);
            }
        };

        // 厂区管理方法
        const calculateAreaAverageHealth = (equipmentList) => {
            if (!equipmentList || equipmentList.length === 0) return 0;

            // 只计算有健康度分数的设备
            const validScores = equipmentList
                .map(equipment => getHealthScore(equipment))
                .filter(score => score !== null && score !== undefined);

            if (validScores.length === 0) return 0;

            const total = validScores.reduce((sum, score) => sum + score, 0);
            return Math.round(total / validScores.length);
        };

        const toggleAreaCollapse = (area) => {
            if (collapsedAreas.value.has(area)) {
                collapsedAreas.value.delete(area);
            } else {
                collapsedAreas.value.add(area);
            }
        };

        const getAreaHealthColor = (averageHealth) => {
            if (averageHealth >= 90) return 'text-green-600 bg-green-50 border-green-200';
            if (averageHealth >= 80) return 'text-blue-600 bg-blue-50 border-blue-200';
            if (averageHealth >= 70) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            if (averageHealth >= 60) return 'text-orange-600 bg-orange-50 border-orange-200';
            return 'text-red-600 bg-red-50 border-red-200';
        };

        // 长按拖拽相关方法
        const handleAreaMouseDown = (event, index) => {
            event.preventDefault();
            dragStartTime.value = Date.now();
            draggedAreaIndex.value = index;

            // 判断是否为触摸事件
            const isTouch = event.type === 'touchstart';

            // 添加移动和松开事件监听
            const handleMove = (e) => {
                if (isDragging.value) {
                    try {
                        const clientX = isTouch ? e.touches[0]?.clientX : e.clientX;
                        const clientY = isTouch ? e.touches[0]?.clientY : e.clientY;

                        if (clientX !== undefined && clientY !== undefined) {
                            handleAreaDragMove({ clientX, clientY });
                        }
                    } catch (error) {
                        console.error('拖拽移动事件处理错误:', error);
                    }
                }
            };

            const handleEnd = (e) => {
                handleAreaMouseUp();
                if (isTouch) {
                    document.removeEventListener('touchmove', handleMove);
                    document.removeEventListener('touchend', handleEnd);
                } else {
                    document.removeEventListener('mousemove', handleMove);
                    document.removeEventListener('mouseup', handleEnd);
                }
            };

            if (isTouch) {
                document.addEventListener('touchmove', handleMove, { passive: false });
                document.addEventListener('touchend', handleEnd);
            } else {
                document.addEventListener('mousemove', handleMove);
                document.addEventListener('mouseup', handleEnd);
            }

            longPressTimer.value = setTimeout(() => {
                if (!isDragging.value) {
                    startDragging(index);
                }
            }, 500); // 500ms 长按触发
        };

        const handleAreaMouseUp = () => {
            if (longPressTimer.value) {
                clearTimeout(longPressTimer.value);
                longPressTimer.value = null;
            }

            if (!isDragging.value && Date.now() - dragStartTime.value < 500) {
                // 短按，切换折叠状态
                const area = groupedEquipmentByArea.value[draggedAreaIndex.value]?.area;
                if (area) {
                    toggleAreaCollapse(area);
                }
            }

            if (isDragging.value) {
                stopDragging();
            }
        };

        const handleAreaDragMove = (coords) => {
            if (!isDragging.value) return;

            try {
                // 清除所有拖拽悬停效果
                document.querySelectorAll('.area-header').forEach(header => {
                    header.classList.remove('drag-hover');
                });

                // 获取鼠标位置下的厂区元素
                const elements = document.elementsFromPoint(coords.clientX, coords.clientY);
                const areaHeader = elements.find(el => el.classList.contains('area-header'));

                if (areaHeader && !areaHeader.classList.contains('dragging')) {
                    const allAreaHeaders = document.querySelectorAll('.area-header');
                    const targetIndex = Array.from(allAreaHeaders).indexOf(areaHeader);

                    if (targetIndex !== -1 && targetIndex !== draggedAreaIndex.value) {
                        // 添加悬停效果
                        areaHeader.classList.add('drag-hover');

                        // 执行拖拽排序
                        handleAreaDragOver(targetIndex);
                    }
                }
            } catch (error) {
                console.error('拖拽移动处理错误:', error);
            }
        };

        const startDragging = (index) => {
            isDragging.value = true;
            draggedAreaIndex.value = index;
            document.body.style.userSelect = 'none';
            document.body.style.cursor = 'grabbing';

            // 添加拖拽样式到当前厂区
            const allAreaHeaders = document.querySelectorAll('.area-header');
            if (allAreaHeaders[index]) {
                allAreaHeaders[index].classList.add('dragging');
            }

            console.log('开始拖拽厂区，索引:', index);
            showNotification('拖拽模式已启用，拖拽到目标位置后松开', 'info');
        };

        const stopDragging = () => {
            isDragging.value = false;
            draggedAreaIndex.value = -1;
            document.body.style.userSelect = '';
            document.body.style.cursor = '';

            // 清除所有拖拽效果
            document.querySelectorAll('.area-header').forEach(header => {
                header.classList.remove('drag-hover', 'dragging');
            });

            showNotification('厂区排序已保存', 'success');
        };

        const handleAreaDragOver = (targetIndex) => {
            if (!isDragging.value || draggedAreaIndex.value === -1) return;

            if (targetIndex !== draggedAreaIndex.value && targetIndex >= 0) {
                // 重新排序厂区
                const currentGroups = groupedEquipmentByArea.value;

                // 确保索引有效
                if (draggedAreaIndex.value >= currentGroups.length || targetIndex >= currentGroups.length) {
                    return;
                }

                const draggedArea = currentGroups[draggedAreaIndex.value]?.area;
                const targetArea = currentGroups[targetIndex]?.area;

                if (draggedArea && targetArea && draggedArea !== targetArea) {
                    const newOrder = [...areaOrder.value];

                    // 在排序数组中找到对应位置
                    const draggedOrderIndex = newOrder.indexOf(draggedArea);
                    const targetOrderIndex = newOrder.indexOf(targetArea);

                    if (draggedOrderIndex !== -1 && targetOrderIndex !== -1) {
                        // 移除被拖拽的项
                        newOrder.splice(draggedOrderIndex, 1);
                        // 插入到新位置
                        newOrder.splice(targetOrderIndex, 0, draggedArea);

                        areaOrder.value = newOrder;
                        draggedAreaIndex.value = targetIndex;
                    }
                }
            }
        };

        // 页面初始化逻辑
        onMounted(async () => {
            console.log('设备健康度评估页面组件已挂载');
            await loadEquipmentList();
            // 统计数据现在会在 loadEquipmentList 完成后自动计算，确保数据一致性

            // 延迟初始化图表，确保所有DOM元素都已渲染
            setTimeout(async () => {
                await initAllCharts();
            }, 500);
        });

        // 页面卸载时清理图表
        onUnmounted(() => {
            destroyAllCharts();
        });

        return {
            loading,
            equipmentList,
            healthStatistics,
            selectedEquipment,
            healthDetail,
            showDetailModal,
            showStandardModal,
            activeTab,
            healthLevelColor,
            healthLevelText,
            loadEquipmentList,
            loadHealthStatistics,
            calculateEquipmentHealth,
            showEquipmentDetail,
            batchCalculateHealth,
            isCalculating,
            // 视图和筛选
            viewMode,
            searchQuery,
            showFilters,
            filters,
            filteredEquipmentList,
            // 厂区管理
            groupedEquipmentByArea,
            collapsedAreas,
            areaOrder,
            isDragging,
            draggedAreaIndex,
            toggleAreaCollapse,
            getAreaHealthColor,
            handleAreaMouseDown,
            handleAreaMouseUp,
            handleAreaDragOver,
            expandAll,
            collapseAll,
            isAllExpanded,
            isAllCollapsed,
            // 筛选选项
            availableAreas,
            availableStatuses,
            getStatusDisplayName,
            // 分页
            currentPage,
            pageSize,
            totalEquipment,
            totalPages,
            goToPage,
            // 厂区分页
            areaPageStates,
            goToAreaPage,
            previousAreaPage,
            nextAreaPage,
            // 工具方法
            getHealthScore,
            getHealthLevel,
            getHealthScoreColor,
            getHealthBarColor,
            getHealthIndicatorColor,
            getStatusText,
            getStatusBadgeColor,
            getStatusIndicatorColor,
            formatDate,
            resetFilters,
            sortBy,
            previousPage,
            nextPage,
            // 侧边栏控制
            sidebarOpen,
            toggleSidebar: () => sidebarOpen.value = !sidebarOpen.value,
            closeSidebar: () => sidebarOpen.value = false,
            // 详情数据处理
            getMaintenanceStatsByType,
            getMaintenanceStatsBySeverity,
            getMaintenanceTypeName,
            getMaintenanceTypeColor,
            getSeverityName,
            getSeverityColor,
            getLatestMaintenanceDate,
            getHealthDotSize,
            // 图表相关
            trendPeriod,
            updateTrendChart,
            initAllCharts,
            updateAllCharts
        };
    }
});

// 配置Vue以抑制特定警告
app.config.warnHandler = (msg, instance, trace) => {
    // 抑制关于script和style标签的警告
    if (msg.includes('Tags with side effect') || msg.includes('<script>') || msg.includes('<style>')) {
        return;
    }
    console.warn(msg, instance, trace);
};

// 确保DOM加载完成后再挂载Vue应用
document.addEventListener('DOMContentLoaded', () => {
    const appElement = document.getElementById('app');
    if (appElement) {
        app.mount(appElement);
    } else {
        console.error('找不到#app元素');
    }
});
