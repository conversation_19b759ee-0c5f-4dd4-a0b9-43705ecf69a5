<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备产能管理 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/equipment/capacity.css">

</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">设备产能管理</h1>
                            <p class="text-gray-600 mt-1">配置设备的产品生产能力和操作员技能</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <select v-model="selectedEquipmentId"
                                    @change="loadEquipmentData"
                                    class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">选择设备</option>
                                <option v-for="equipment in equipmentOptions" :key="equipment.id" :value="equipment.id">
                                    {{ equipment.name }} ({{ equipment.code }})
                                </option>
                            </select>
                        </div>
                    </div>
                </header>

                <!-- 设备信息 -->
                <div v-if="selectedEquipment" class="px-6 py-4 bg-blue-50 border-b border-blue-200">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <h2 class="text-lg font-medium text-blue-900">{{ selectedEquipment.name }}</h2>
                            <p class="text-blue-700">设备编码: {{ selectedEquipment.code }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-blue-700">状态: {{ getEquipmentStatusText(selectedEquipment.status) }}</p>
                            <p class="text-sm text-blue-700">位置: {{ selectedEquipment.location || '未设置' }}</p>
                        </div>
                    </div>
                </div>

                <!-- 主要内容 -->
                <div class="p-6">
                    <div v-if="!selectedEquipmentId" class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">请选择设备</h3>
                        <p class="mt-1 text-sm text-gray-500">选择一个设备来配置其产能和操作员信息</p>
                    </div>

                    <div v-else class="space-y-8">
                        <!-- 产能概览统计 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="p-3 bg-blue-100 rounded-lg">
                                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">总产能配置</p>
                                        <p class="text-2xl font-bold text-gray-900">{{ capabilities.length }}</p>
                                        <p class="text-sm text-gray-500">个产品</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="p-3 bg-green-100 rounded-lg">
                                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">当前利用率</p>
                                        <p class="text-2xl font-bold text-gray-900">{{ utilizationRate }}%</p>
                                        <p class="text-sm text-gray-500">本周平均</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="p-3 bg-yellow-100 rounded-lg">
                                        <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">平均效率</p>
                                        <p class="text-2xl font-bold text-gray-900">{{ averageEfficiency.toFixed(2) }}</p>
                                        <p class="text-sm text-gray-500">效率系数</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow p-6">
                                <div class="flex items-center">
                                    <div class="p-3 bg-purple-100 rounded-lg">
                                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">操作员技能</p>
                                        <p class="text-2xl font-bold text-gray-900">{{ operatorSkills.length }}</p>
                                        <p class="text-sm text-gray-500">个技能配置</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 产能利用率图表 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">产能利用率趋势</h3>
                                <p class="text-sm text-gray-600 mt-1">过去30天的设备产能利用情况</p>
                            </div>
                            <div class="p-6">
                                <div class="h-64">
                                    <canvas ref="utilizationChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 智能排程集成 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">智能排程状态</h3>
                                        <p class="text-sm text-gray-600 mt-1">当前设备在智能排程系统中的使用情况</p>
                                    </div>
                                    <button @click="refreshSchedulingStatus"
                                            class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50">
                                        刷新状态
                                    </button>
                                </div>
                            </div>
                            <div class="p-6">
                                <div v-if="schedulingStatus.length === 0" class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">暂无排程任务</h3>
                                    <p class="mt-1 text-sm text-gray-500">当前设备没有分配的排程任务</p>
                                </div>
                                <div v-else class="space-y-4">
                                    <div v-for="task in schedulingStatus" :key="task.id"
                                         class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-medium text-gray-900">{{ task.orderName }}</h4>
                                                <p class="text-sm text-gray-600">产品: {{ task.productName }} | 数量: {{ task.quantity }}</p>
                                                <p class="text-sm text-gray-500">预计时间: {{ formatDate(task.startTime) }} - {{ formatDate(task.endTime) }}</p>
                                            </div>
                                            <div class="text-right">
                                                <span :class="['px-2 py-1 text-xs font-medium rounded-full', getStatusClass(task.status)]">
                                                    {{ getStatusText(task.status) }}
                                                </span>
                                                <p class="text-sm text-gray-500 mt-1">进度: {{ task.progress }}%</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 产品产能配置 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-medium text-gray-900">产品产能配置</h3>
                                    <button @click="showCapabilityModal = true"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        添加产品产能
                                    </button>
                                </div>
                            </div>

                            <div class="p-6">
                                <div v-if="capabilities.length === 0" class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">暂无产能配置</h3>
                                    <p class="mt-1 text-sm text-gray-500">开始配置第一个产品的生产能力吧</p>
                                </div>

                                <div v-else class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">每小时产能</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">效率系数</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">准备时间</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="capability in capabilities" :key="capability.id">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">{{ capability.productName }}</div>
                                                        <div class="text-sm text-gray-500">{{ capability.productCode }}</div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ capability.capacityPerHour }} 件/小时
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ capability.efficiencyFactor }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ capability.setupTime }} 分钟
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <button @click="editCapability(capability)" class="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                                                    <button @click="deleteCapability(capability)" class="text-red-600 hover:text-red-900">删除</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 操作员技能配置 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-medium text-gray-900">操作员技能配置</h3>
                                    <button @click="showSkillModal = true"
                                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        添加操作员技能
                                    </button>
                                </div>
                            </div>

                            <div class="p-6">
                                <div v-if="operatorSkills.length === 0" class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">暂无操作员技能</h3>
                                    <p class="mt-1 text-sm text-gray-500">开始配置操作员的设备技能吧</p>
                                </div>

                                <div v-else class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作员</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技能等级</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">效率系数</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认证日期</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="skill in operatorSkills" :key="skill.id">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">{{ skill.operatorName }}</div>
                                                        <div class="text-sm text-gray-500">{{ skill.operatorUsername }}</div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                          :class="getSkillLevelClass(skill.skillLevel)">
                                                        {{ getSkillLevelText(skill.skillLevel) }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ skill.efficiencyFactor }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ skill.certificationDate ? formatDate(skill.certificationDate) : '-' }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <button @click="editSkill(skill)" class="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                                                    <button @click="deleteSkill(skill)" class="text-red-600 hover:text-red-900">删除</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品产能配置模态框 -->
        <div v-if="showCapabilityModal" class="modal-overlay" @click.self="closeCapabilityModal">
            <div class="modal-content w-full max-w-md p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">
                        {{ editingCapability ? '编辑产品产能' : '添加产品产能' }}
                    </h2>
                    <button @click="closeCapabilityModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitCapabilityForm">
                    <div class="space-y-4">
                        <div v-if="!editingCapability">
                            <label class="block text-sm font-medium text-gray-700 mb-2">产品 *</label>
                            <select v-model="capabilityForm.productId"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择产品</option>
                                <option v-for="product in productOptions" :key="product.id" :value="product.id">
                                    {{ product.label }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">每小时产能 *</label>
                            <input v-model.number="capabilityForm.capacityPerHour"
                                   type="number"
                                   min="1"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">效率系数</label>
                            <input v-model.number="capabilityForm.efficiencyFactor"
                                   type="number"
                                   step="0.1"
                                   min="0.1"
                                   max="2.0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">准备时间（分钟）</label>
                            <input v-model.number="capabilityForm.setupTime"
                                   type="number"
                                   min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="mt-6 flex items-center justify-end space-x-4">
                        <button type="button"
                                @click="closeCapabilityModal"
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                :disabled="submitting"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                            {{ submitting ? '保存中...' : '保存' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作员技能配置模态框 -->
        <div v-if="showSkillModal" class="modal-overlay" @click.self="closeSkillModal">
            <div class="modal-content w-full max-w-md p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">
                        {{ editingSkill ? '编辑操作员技能' : '添加操作员技能' }}
                    </h2>
                    <button @click="closeSkillModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitSkillForm">
                    <div class="space-y-4">
                        <div v-if="!editingSkill">
                            <label class="block text-sm font-medium text-gray-700 mb-2">操作员 *</label>
                            <select v-model="skillForm.operatorId"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择操作员</option>
                                <option v-for="operator in operatorOptions" :key="operator.id" :value="operator.id">
                                    {{ operator.name }} ({{ operator.username }})
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">技能等级 *</label>
                            <select v-model.number="skillForm.skillLevel"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="1">1级 - 初级</option>
                                <option value="2">2级 - 熟练</option>
                                <option value="3">3级 - 精通</option>
                                <option value="4">4级 - 专家</option>
                                <option value="5">5级 - 大师</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">效率系数</label>
                            <input v-model.number="skillForm.efficiencyFactor"
                                   type="number"
                                   step="0.1"
                                   min="0.1"
                                   max="2.0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">认证日期</label>
                            <input v-model="skillForm.certificationDate"
                                   type="date"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="mt-6 flex items-center justify-end space-x-4">
                        <button type="button"
                                @click="closeSkillModal"
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                :disabled="submitting"
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50">
                            {{ submitting ? '保存中...' : '保存' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script src="/js/libs/chart.umd.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/equipment/capacity.js"></script>
</body>
</html>
