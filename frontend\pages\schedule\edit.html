<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑排程 - Makrite管理系统</title>
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/schedule/common.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">编辑排程</h1>
                            <p class="text-gray-600 mt-1">修改排程计划信息</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <a href="/schedule/list"
                               class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                                返回列表
                            </a>
                        </div>
                    </div>
                </header>

                <!-- 加载状态 -->
                <div v-if="loading" class="p-6">
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-gray-600">加载中...</p>
                    </div>
                </div>

                <!-- 表单内容 -->
                <div v-else-if="formData.id" class="p-6">
                    <div class="max-w-4xl mx-auto">
                        <div class="bg-white shadow rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">排程信息</h3>
                            </div>
                            <form @submit.prevent="submitForm" class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- 排程标题 -->
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">排程标题 *</label>
                                        <input type="text" v-model="formData.title" required
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入排程标题">
                                    </div>

                                    <!-- 产品ID -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">产品ID *</label>
                                        <input type="text" v-model="formData.productId" required
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入产品ID">
                                    </div>

                                    <!-- 产品名称 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">产品名称 *</label>
                                        <input type="text" v-model="formData.productName" required
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入产品名称">
                                    </div>

                                    <!-- 生产数量 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">生产数量 *</label>
                                        <input type="number" v-model="formData.quantity" required min="1"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入生产数量">
                                    </div>

                                    <!-- 优先级 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                                        <select v-model="formData.priority"
                                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="low">低</option>
                                            <option value="medium">中</option>
                                            <option value="high">高</option>
                                            <option value="urgent">紧急</option>
                                        </select>
                                    </div>

                                    <!-- 开始时间 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">开始时间 *</label>
                                        <input type="datetime-local" v-model="formData.startTime" required
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <!-- 结束时间 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">结束时间 *</label>
                                        <input type="datetime-local" v-model="formData.endTime" required
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <!-- 状态 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                                        <select v-model="formData.status"
                                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="planned">计划中</option>
                                            <option value="in_progress">执行中</option>
                                            <option value="paused">已暂停</option>
                                            <option value="completed">已完成</option>
                                            <option value="cancelled">已取消</option>
                                        </select>
                                    </div>

                                    <!-- 进度 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">进度 (%)</label>
                                        <input type="number" v-model="formData.progress" min="0" max="100"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="0-100">
                                    </div>

                                    <!-- 备注 -->
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                                        <textarea v-model="formData.notes" rows="3"
                                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                  placeholder="请输入备注信息"></textarea>
                                    </div>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="mt-8 flex justify-end space-x-4">
                                    <a href="/schedule/list"
                                       class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                        取消
                                    </a>
                                    <button type="submit" :disabled="submitting"
                                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                                        {{ submitting ? '保存中...' : '保存修改' }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 错误状态 -->
                <div v-else class="p-6">
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">排程不存在</h3>
                        <p class="mt-1 text-sm text-gray-500">请检查排程ID是否正确</p>
                        <div class="mt-6">
                            <a href="/schedule/list"
                               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                返回排程列表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入组件和脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/schedule/edit.js"></script>
</body>
</html>
