/**
 * 个人设置页面逻辑
 */

import { getCurrentUser } from '../../../scripts/api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import PasswordChangeForm from '../../../components/user/PasswordChangeForm.js';
import SignatureUploadForm from '../../../components/user/SignatureUploadForm.js';

const { createApp, ref, onMounted, watch } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar,
        PasswordChangeForm,
        SignatureUploadForm
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const activeTab = ref('password'); // 默认显示密码修改选项卡

        // 初始化
        onMounted(() => {
            checkAuth();
            // 确保加载指示器被隐藏
            hideLoading();

            // 从URL参数中获取选项卡
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab');
            if (tab === 'signature') {
                activeTab.value = 'signature';
            }
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
            }
        }

        // 用户信息更新成功
        function onUserUpdated(updatedUser) {
            currentUser.value = updatedUser;
            alert('个人设置更新成功！');
        }

        // 监听选项卡变化，更新URL
        function updateUrlWithTab() {
            const url = new URL(window.location);
            url.searchParams.set('tab', activeTab.value);
            window.history.replaceState({}, '', url);
        }

        // 监听选项卡变化
        watch(activeTab, (newTab) => {
            updateUrlWithTab();
        });

        return {
            currentUser,
            isAuthenticated,
            activeTab,
            onUserUpdated
        };
    }
}).mount('#app');
