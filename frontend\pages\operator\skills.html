<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作员技能管理 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/operator/common.css">

</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 md:ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <button @click="toggleSidebar" class="md:hidden mr-4 text-gray-600 hover:text-gray-900">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">操作员技能管理</h1>
                                <p class="text-gray-600 mt-1">管理操作员的设备操作技能和效率评估</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button @click="showSkillModal = true"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                添加技能记录
                            </button>
                        </div>
                    </div>
                </header>

                <!-- 筛选和搜索 -->
                <div class="px-6 py-4 bg-white border-b border-gray-200">
                    <div class="flex items-center space-x-4">
                        <div class="flex-1">
                            <select v-model="selectedOperatorId"
                                    @change="loadOperatorSkills"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">选择操作员</option>
                                <option v-for="operator in operatorOptions" :key="operator.id" :value="operator.id">
                                    {{ operator.name }} ({{ operator.username }})
                                </option>
                            </select>
                        </div>
                        <div>
                            <select v-model="selectedEquipmentId"
                                    @change="loadOperatorSkills"
                                    class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">所有设备</option>
                                <option v-for="equipment in equipmentOptions" :key="equipment.id" :value="equipment.id">
                                    {{ equipment.name }}
                                </option>
                            </select>
                        </div>
                        <button @click="refreshData"
                                class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                            刷新
                        </button>
                    </div>
                </div>

                <!-- 技能列表 -->
                <div class="p-6">
                    <div v-if="loading" class="text-center py-8">
                        <div class="loading-spinner mx-auto"></div>
                        <p class="mt-4 text-gray-600">加载中...</p>
                    </div>

                    <div v-else-if="skills.length === 0" class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无技能记录</h3>
                        <p class="mt-1 text-sm text-gray-500">开始添加操作员技能记录吧</p>
                    </div>

                    <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li v-for="skill in skills" :key="skill.id"
                                class="px-6 py-4 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <h3 class="text-lg font-medium text-gray-900">{{ skill.operatorName }}</h3>
                                            <span class="ml-3 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                {{ skill.operatorUsername }}
                                            </span>
                                            <span :class="['ml-2 px-2 py-1 text-xs font-medium rounded-full', getSkillLevelClass(skill.skillLevel)]">
                                                {{ getSkillLevelText(skill.skillLevel) }}
                                            </span>
                                        </div>
                                        <div class="mt-2 text-sm text-gray-600">
                                            <p>设备: {{ skill.equipmentName }}</p>
                                            <p>效率系数: {{ skill.efficiencyFactor }}</p>
                                            <p v-if="skill.certificationDate">认证日期: {{ formatDate(skill.certificationDate) }}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="editSkill(skill)"
                                                class="px-3 py-1 text-sm text-green-600 hover:text-green-800">
                                            编辑
                                        </button>
                                        <button @click="deleteSkill(skill)"
                                                class="px-3 py-1 text-sm text-red-600 hover:text-red-800">
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技能记录模态框 -->
        <div v-if="showSkillModal" class="modal-overlay" @click.self="closeSkillModal">
            <div class="modal-content w-full max-w-2xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">
                        {{ editingSkill ? '编辑技能记录' : '添加技能记录' }}
                    </h2>
                    <button @click="closeSkillModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitSkillForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">操作员 *</label>
                            <select v-model="skillForm.operatorId"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择操作员</option>
                                <option v-for="operator in operatorOptions" :key="operator.id" :value="operator.id">
                                    {{ operator.name }} ({{ operator.username }})
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备 *</label>
                            <select v-model="skillForm.equipmentId"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择设备</option>
                                <option v-for="equipment in equipmentOptions" :key="equipment.id" :value="equipment.id">
                                    {{ equipment.name }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">技能等级 *</label>
                            <select v-model.number="skillForm.skillLevel"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option :value="1">1级 - 初级</option>
                                <option :value="2">2级 - 熟练</option>
                                <option :value="3">3级 - 精通</option>
                                <option :value="4">4级 - 专家</option>
                                <option :value="5">5级 - 大师</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">效率系数</label>
                            <input v-model.number="skillForm.efficiencyFactor"
                                   type="number"
                                   step="0.1"
                                   min="0.1"
                                   max="2.0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">认证日期</label>
                            <input v-model="skillForm.certificationDate"
                                   type="date"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="mt-6 flex items-center justify-end space-x-4">
                        <button type="button"
                                @click="closeSkillModal"
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                :disabled="submitting"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                            {{ submitting ? '保存中...' : '保存' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 移动端侧边栏遮罩 -->
        <div v-if="sidebarOpen" @click="closeSidebar" class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"></div>
    </div>

    <!-- JavaScript -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/operator/skills.js"></script>
</body>
</html>
