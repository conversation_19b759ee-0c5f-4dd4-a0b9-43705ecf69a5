/**
 * 智能排程页面
 * 智能排程方案生成和管理功能
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import SchedulingAPI from '../../api/scheduling.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const loading = ref(false);
        const submitting = ref(false);
        const statistics = ref({});
        const schedulePlans = ref([]);
        const showOrderModal = ref(false);
        const selectedPlan = ref(null);

        // 表单数据
        const orderForm = ref({
            id: '',
            productId: '',
            quantity: 1,
            requiredDate: '',
            priority: 'normal',
            customerName: ''
        });

        // 初始化
        onMounted(async () => {
            try {
                // 获取当前用户信息
                const user = await getCurrentUser();
                if (user) {
                    currentUser.value = user;
                    isAuthenticated.value = true;
                    await loadStatistics();
                } else {
                    // 未登录，跳转到登录页
                    window.location.href = '/login';
                    return;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                window.location.href = '/login';
                return;
            } finally {
                hideLoading();
            }
        });

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await SchedulingAPI.getStatistics();
                if (response.success) {
                    statistics.value = response.data;
                } else {
                    console.error('获取统计信息失败:', response.message);
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
                // 使用默认统计数据
                statistics.value = {
                    totalOrders: 0,
                    scheduledOrders: 0,
                    averageLeadTime: 0,
                    resourceUtilization: { equipment: 0 }
                };
            }
        }

        // 创建排程方案
        async function createSchedulePlans() {
            if (submitting.value) return;

            // 基本验证
            if (!orderForm.value.id) {
                window.showNotification('请输入订单ID', 'error');
                return;
            }

            if (!orderForm.value.productId) {
                window.showNotification('请输入产品ID', 'error');
                return;
            }

            if (!orderForm.value.quantity || orderForm.value.quantity <= 0) {
                window.showNotification('请输入有效的订单数量', 'error');
                return;
            }

            if (!orderForm.value.requiredDate) {
                window.showNotification('请选择要求交期', 'error');
                return;
            }

            // 检查交期是否合理
            const requiredDate = new Date(orderForm.value.requiredDate);
            const today = new Date();
            if (requiredDate <= today) {
                window.showNotification('要求交期必须晚于当前日期', 'error');
                return;
            }

            submitting.value = true;

            try {
                const response = await SchedulingAPI.createPlans(orderForm.value);
                
                if (response.success) {
                    window.showNotification('排程方案生成成功', 'success');
                    
                    // 显示生成的方案
                    schedulePlans.value = response.data.plans.map(plan => ({
                        ...plan,
                        orderId: orderForm.value.id,
                        selected: false
                    }));
                    
                    closeOrderModal();
                    await loadStatistics(); // 刷新统计信息
                } else {
                    window.showNotification(response.message || '生成排程方案失败', 'error');
                }
            } catch (error) {
                console.error('创建排程方案失败:', error);
                window.showNotification('创建排程方案失败', 'error');
            } finally {
                submitting.value = false;
            }
        }

        // 选择方案
        function selectPlan(plan) {
            // 取消其他方案的选中状态
            schedulePlans.value.forEach(p => p.selected = false);
            
            // 选中当前方案
            plan.selected = true;
            selectedPlan.value = plan;
        }

        // 查看方案详情
        function viewPlanDetails(plan) {
            // 跳转到交期预测页面查看详细信息
            const orderId = plan.orderId || orderForm.value.id;
            const detailUrl = `/scheduling/delivery-prediction?orderId=${orderId}&planId=${plan.id}`;
            window.location.href = detailUrl;
        }

        // 确认方案
        async function confirmPlan(plan) {
            if (!confirm(`确定要选择"${plan.name}"作为最终排程方案吗？`)) {
                return;
            }

            try {
                const response = await SchedulingAPI.selectPlan(plan.orderId, plan.id);
                
                if (response.success) {
                    window.showNotification('排程方案确认成功', 'success');
                    
                    // 更新方案状态
                    plan.confirmed = true;
                    
                    // 可以跳转到生产计划页面
                    // window.location.href = `/production/plan/${plan.orderId}`;
                } else {
                    window.showNotification(response.message || '确认方案失败', 'error');
                }
            } catch (error) {
                console.error('确认方案失败:', error);
                window.showNotification('确认方案失败', 'error');
            }
        }

        // 关闭订单模态框
        function closeOrderModal() {
            showOrderModal.value = false;
            orderForm.value = {
                id: '',
                productId: '',
                quantity: 1,
                requiredDate: '',
                priority: 'normal',
                customerName: ''
            };
        }

        // 获取风险等级样式
        function getRiskClass(riskLevel) {
            const classMap = {
                'low': 'risk-low',
                'medium': 'risk-medium',
                'high': 'risk-high'
            };
            return classMap[riskLevel] || 'risk-low';
        }

        // 获取风险等级文本
        function getRiskText(riskLevel) {
            const textMap = {
                'low': '低风险',
                'medium': '中风险',
                'high': '高风险'
            };
            return textMap[riskLevel] || '未知';
        }

        // 获取优先级样式
        function getPriorityClass(priority) {
            const classMap = {
                'low': 'priority-low',
                'normal': 'priority-medium',
                'high': 'priority-high'
            };
            return classMap[priority] || 'priority-medium';
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const textMap = {
                'low': '低优先级',
                'normal': '普通',
                'high': '高优先级'
            };
            return textMap[priority] || '普通';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }

        // 格式化数字
        function formatNumber(number, decimals = 0) {
            if (typeof number !== 'number') return '0';
            return number.toFixed(decimals);
        }

        // 生成示例订单ID
        function generateOrderId() {
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.random().toString(36).substr(2, 4).toUpperCase();
            orderForm.value.id = `ORD${timestamp}${random}`;
        }

        // 生成示例产品ID
        function generateProductId() {
            const products = ['PROD001', 'PROD002', 'PROD003', 'PROD004', 'PROD005'];
            orderForm.value.productId = products[Math.floor(Math.random() * products.length)];
        }

        // 设置默认交期（当前日期后15天）
        function setDefaultDate() {
            const date = new Date();
            date.setDate(date.getDate() + 15);
            orderForm.value.requiredDate = date.toISOString().split('T')[0];
        }

        // 填充示例数据
        function fillExampleData() {
            generateOrderId();
            generateProductId();
            orderForm.value.quantity = Math.floor(Math.random() * 1000) + 100;
            setDefaultDate();
            orderForm.value.customerName = '示例客户';
            orderForm.value.priority = ['low', 'normal', 'high'][Math.floor(Math.random() * 3)];
        }

        // 对比所有方案
        function compareAllPlans() {
            if (schedulePlans.value.length < 2) {
                window.showNotification('至少需要2个方案才能进行对比', 'warning');
                return;
            }

            const orderId = schedulePlans.value[0]?.orderId;
            if (!orderId) {
                window.showNotification('缺少订单信息', 'error');
                return;
            }

            const planIds = schedulePlans.value.map(plan => plan.id).join(',');
            const compareUrl = `/scheduling/compare?orderId=${orderId}&planIds=${planIds}`;

            window.location.href = compareUrl;
        }

        return {
            currentUser,
            isAuthenticated,
            loading,
            submitting,
            statistics,
            schedulePlans,
            showOrderModal,
            selectedPlan,
            orderForm,
            createSchedulePlans,
            selectPlan,
            viewPlanDetails,
            confirmPlan,
            closeOrderModal,
            getRiskClass,
            getRiskText,
            getPriorityClass,
            getPriorityText,
            formatDate,
            formatNumber,
            fillExampleData,
            compareAllPlans
        };
    }
}).mount('#app');
