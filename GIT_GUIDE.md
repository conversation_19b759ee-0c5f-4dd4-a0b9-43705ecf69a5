# Git 版本管理指南

## 📋 概述

本项目使用Git进行版本管理，遵循规范化的提交流程和分支管理策略。

## 🌿 分支管理策略

### 主要分支

- **main**: 主分支，包含生产环境代码
- **develop**: 开发分支，包含最新开发代码
- **feature/***: 功能分支，用于开发新功能
- **hotfix/***: 热修复分支，用于紧急修复生产问题
- **release/***: 发布分支，用于准备新版本发布

### 分支命名规范

```
feature/功能名称        # 例: feature/user-management
hotfix/修复描述         # 例: hotfix/login-bug-fix
release/版本号          # 例: release/v2.1.0
```

## 📝 提交规范

### 提交消息格式

```
<类型>: <简短描述>

<详细描述>

<相关问题>
```

### 提交类型

- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 提交示例

```bash
feat: 添加质量管理模块

- 实现检测报告上传功能
- 添加报告列表和详情页面
- 集成权限控制系统

Closes #45
```

## 🔄 工作流程

### 1. 开发新功能

```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 开发完成后提交
git add .
git commit -m "feat: 添加新功能"

# 推送到远程仓库
git push origin feature/new-feature

# 创建Pull Request合并到develop
```

### 2. 修复Bug

```bash
# 从main分支创建热修复分支
git checkout main
git pull origin main
git checkout -b hotfix/bug-description

# 修复完成后提交
git add .
git commit -m "fix: 修复登录问题"

# 推送并合并到main和develop
git push origin hotfix/bug-description
```

### 3. 发布版本

```bash
# 从develop创建发布分支
git checkout develop
git pull origin develop
git checkout -b release/v2.1.0

# 更新版本号和文档
git add .
git commit -m "chore: 准备v2.1.0发布"

# 合并到main并打标签
git checkout main
git merge release/v2.1.0
git tag -a v2.1.0 -m "Release version 2.1.0"
git push origin main --tags
```

## 🏷️ 版本标签

### 版本号规范

使用语义化版本控制 (Semantic Versioning):

```
主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)
```

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 标签示例

```bash
# 创建标签
git tag -a v2.1.0 -m "Release version 2.1.0"

# 推送标签
git push origin --tags

# 查看标签
git tag -l
```

## 🔍 常用命令

### 基本操作

```bash
# 查看状态
git status

# 查看提交历史
git log --oneline --graph

# 查看分支
git branch -a

# 切换分支
git checkout branch-name

# 创建并切换分支
git checkout -b new-branch
```

### 同步操作

```bash
# 拉取最新代码
git pull origin main

# 推送代码
git push origin branch-name

# 同步所有分支
git fetch --all
```

### 撤销操作

```bash
# 撤销工作区修改
git checkout -- file-name

# 撤销暂存区修改
git reset HEAD file-name

# 撤销最后一次提交
git reset --soft HEAD~1
```

## 🛡️ 预提交检查

系统配置了预提交钩子，会自动检查：

1. **敏感信息检查**: 防止提交密码、密钥等敏感信息
2. **文件大小检查**: 防止提交过大的文件
3. **代码格式检查**: 运行ESLint等代码检查工具

如需跳过检查，使用：
```bash
git commit --no-verify -m "提交消息"
```

## 📊 项目历史

### 重要里程碑

- **v1.0.0**: 初始版本发布
- **v2.0.0**: 数据库迁移到SQLite
- **v2.1.0**: 添加质量管理模块

## 🤝 协作规范

### Pull Request 规范

1. **标题**: 简洁描述变更内容
2. **描述**: 详细说明变更原因和内容
3. **测试**: 确保所有测试通过
4. **审查**: 至少一人审查后合并

### 代码审查要点

- 代码质量和规范
- 功能完整性
- 安全性检查
- 性能影响
- 文档更新

## 📚 参考资源

- [Git官方文档](https://git-scm.com/doc)
- [语义化版本控制](https://semver.org/lang/zh-CN/)
- [约定式提交](https://www.conventionalcommits.org/zh-hans/)
