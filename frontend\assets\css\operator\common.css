/* 操作员模块通用样式文件 - Operator Common CSS */

/* 此文件包含操作员模块的通用样式 */
/* 基础样式继承自 /assets/css/common.css */

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

/* 技能等级样式 */
.skill-level-1 { @apply bg-gray-100 text-gray-800; }
.skill-level-2 { @apply bg-blue-100 text-blue-800; }
.skill-level-3 { @apply bg-green-100 text-green-800; }
.skill-level-4 { @apply bg-yellow-100 text-yellow-800; }
.skill-level-5 { @apply bg-red-100 text-red-800; }
