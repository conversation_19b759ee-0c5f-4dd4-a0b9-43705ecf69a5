/**
 * 排程方案对比卡片组件
 * 用于显示和对比不同的排程方案
 */

export default {
    name: 'PlanComparisonCard',
    props: {
        plan: {
            type: Object,
            required: true
        },
        selected: {
            type: Boolean,
            default: false
        },
        showActions: {
            type: Boolean,
            default: true
        }
    },
    emits: ['select', 'view-details', 'confirm'],
    template: `
        <div :class="[
            'plan-card bg-white border-2 rounded-lg p-6 cursor-pointer transition-all duration-200',
            selected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
        ]" @click="$emit('select', plan)">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-lg font-medium text-gray-900">{{ plan.name }}</h4>
                <span :class="['px-2 py-1 text-xs font-medium rounded-full', getRiskClass(plan.riskAssessment?.level)]">
                    {{ getRiskText(plan.riskAssessment?.level) }}
                </span>
            </div>

            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">预计交期:</span>
                    <span class="text-sm font-medium">{{ formatDate(plan.finalPrediction?.deliveryDate) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">按时概率:</span>
                    <span class="text-sm font-medium">{{ Math.round((plan.finalPrediction?.onTimeProb || 0) * 100) }}%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">效率评分:</span>
                    <span class="text-sm font-medium">{{ (plan.metrics?.efficiency || 0).toFixed(2) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">成本估算:</span>
                    <span class="text-sm font-medium">¥{{ (plan.metrics?.cost || 0).toLocaleString() }}</span>
                </div>
            </div>

            <!-- 进度条 -->
            <div class="mt-4 space-y-2">
                <div>
                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                        <span>按时概率</span>
                        <span>{{ Math.round((plan.finalPrediction?.onTimeProb || 0) * 100) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" 
                             :style="{ width: (plan.finalPrediction?.onTimeProb * 100) + '%' }"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                        <span>效率评分</span>
                        <span>{{ (plan.metrics?.efficiency || 0).toFixed(2) }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" 
                             :style="{ width: (plan.metrics?.efficiency * 100) + '%' }"></div>
                    </div>
                </div>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-xs text-gray-500">{{ plan.description }}</p>
            </div>

            <div v-if="selected && showActions" class="mt-4 flex space-x-2" @click.stop>
                <button @click="$emit('view-details', plan)"
                        class="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    查看详情
                </button>
                <button @click="$emit('confirm', plan)"
                        class="flex-1 px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700">
                    确认方案
                </button>
            </div>
        </div>
    `,
    methods: {
        getRiskClass(riskLevel) {
            const classMap = {
                'low': 'bg-green-100 text-green-800',
                'medium': 'bg-yellow-100 text-yellow-800',
                'high': 'bg-red-100 text-red-800'
            };
            return classMap[riskLevel] || 'bg-gray-100 text-gray-800';
        },

        getRiskText(riskLevel) {
            const textMap = {
                'low': '低风险',
                'medium': '中风险',
                'high': '高风险'
            };
            return textMap[riskLevel] || '未知';
        },

        formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }
    }
};
