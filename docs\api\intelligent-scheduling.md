# 智能排产系统 API 文档

## 📋 概述

智能排产系统提供了完整的API接口，支持AI智能排产计算、产能数据管理、性能监控等功能。采用5种优化策略算法，提供多方案对比和智能决策支持。

## 🌐 基础信息

- **Base URL**: `http://localhost:5050/api`
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

## 🔐 认证

所有API请求都需要在请求头中包含有效的JWT令牌：

```http
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 查看排产方案 | `schedule_view` |
| 创建排产方案 | `schedule_create` |
| 编辑排产方案 | `schedule_edit` |
| 删除排产方案 | `schedule_delete` |
| 执行排产 | `schedule_execute` |

## 🤖 智能排产 API

### 1. 生成智能排产方案

**POST** `/api/scheduling/generate`

基于AI算法生成多种优化策略的排产方案，支持5种优化策略。

#### 请求参数

```json
{
  "orderId": "ORD20250729001",
  "productId": "PROD001",
  "quantity": 1000,
  "requiredDate": "2025-12-31",
  "priority": "normal",
  "customerName": "客户A",
  "constraints": {
    "maxDeliveryDate": "2025-12-31",
    "priorityLevel": "normal",
    "specialRequirements": [],
    "preferredEquipment": [],
    "excludedEquipment": []
  },
  "options": {
    "strategies": ["earliest_completion", "high_efficiency", "load_balanced"],
    "includeRiskAssessment": true,
    "includeResourceAnalysis": true
  }
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "orderId": "ORD20250729001",
    "generatedAt": "2025-07-29T10:00:00.000Z",
    "totalPlans": 5,
    "plans": [
      {
        "id": "plan_earliest_20250729_001",
        "strategy": "earliest_completion",
        "name": "最早完成方案",
        "description": "优先考虑交期，选择最高产能设备组合",
        "priority": 1,
        "schedule": {
          "startDate": "2025-08-01",
          "endDate": "2025-08-10",
          "totalDays": 9,
          "workingHours": 72
        },
        "resources": {
          "equipment": [
            {
              "id": "EQ001",
              "name": "数控机床A",
              "type": "数控机床",
              "allocatedHours": 36,
              "efficiency": 0.95
            }
          ],
          "operators": [
            {
              "id": "OP001",
              "name": "张师傅",
              "skillLevel": 5,
              "allocatedHours": 36
            }
          ]
        },
        "metrics": {
          "efficiency": 0.92,
          "estimatedCost": 8500,
          "resourceUtilization": 0.88,
          "qualityScore": 0.95
        },
        "deliveryPrediction": {
          "deliveryDate": "2025-08-10",
          "onTimeProb": 0.98,
          "riskLevel": "low",
          "confidence": 0.95
        },
        "riskAssessment": {
          "overallRisk": "low",
          "factors": [
            {
              "type": "equipment_failure",
              "probability": 0.05,
              "impact": "medium"
            }
          ]
        }
      },
      {
        "id": "plan_efficiency_20250729_001",
        "strategy": "high_efficiency",
        "name": "高效率方案",
        "description": "最大化生产效率，优化设备和人员配置",
        "priority": 2,
        "schedule": {
          "startDate": "2025-08-01",
          "endDate": "2025-08-12",
          "totalDays": 11,
          "workingHours": 80
        },
        "metrics": {
          "efficiency": 0.98,
          "estimatedCost": 7800,
          "resourceUtilization": 0.95,
          "qualityScore": 0.97
        },
        "deliveryPrediction": {
          "deliveryDate": "2025-08-12",
          "onTimeProb": 0.92,
          "riskLevel": "low"
        }
      }
    ],
    "comparison": {
      "bestForTime": "plan_earliest_20250729_001",
      "bestForCost": "plan_efficiency_20250729_001",
      "bestForQuality": "plan_efficiency_20250729_001",
      "recommended": "plan_earliest_20250729_001"
    },
    "metadata": {
      "algorithm": "IntelligentScheduler",
      "version": "2.0.1",
      "processingTime": 1250,
      "dataQuality": "excellent"
    }
  }
}
```

### 2. 获取排产方案列表

**GET** `/api/scheduling/plans`

获取所有排产方案列表，支持分页和筛选。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |
| status | string | 否 | 状态筛选 |
| strategy | string | 否 | 策略筛选 |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "plan_001",
      "orderId": "ORD20250729001",
      "strategy": "earliest_completion",
      "name": "最早完成方案",
      "status": "active",
      "createdAt": "2025-07-29T10:00:00.000Z",
      "schedule": {
        "startDate": "2025-08-01",
        "endDate": "2025-08-10"
      },
      "metrics": {
        "efficiency": 0.92,
        "estimatedCost": 8500
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

### 3. 获取排产方案详情

**GET** `/api/scheduling/plans/:id`

获取指定排产方案的详细信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 方案ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "plan_001",
    "orderId": "ORD20250729001",
    "strategy": "earliest_completion",
    "name": "最早完成方案",
    "status": "active",
    "schedule": {
      "startDate": "2025-08-01",
      "endDate": "2025-08-10",
      "totalDays": 9,
      "workingHours": 72
    },
    "resources": {
      "equipment": [...],
      "operators": [...]
    },
    "metrics": {
      "efficiency": 0.92,
      "estimatedCost": 8500,
      "resourceUtilization": 0.88
    },
    "deliveryPrediction": {
      "deliveryDate": "2025-08-10",
      "onTimeProb": 0.98,
      "riskLevel": "low"
    },
    "createdAt": "2025-07-29T10:00:00.000Z",
    "updatedAt": "2025-07-29T10:30:00.000Z"
  }
}
```

### 4. 优化排产方案

**POST** `/api/scheduling/optimize`

对现有排产方案进行优化调整。

#### 请求参数

```json
{
  "planId": "plan_001",
  "optimizationGoals": ["minimize_cost", "maximize_efficiency"],
  "constraints": {
    "maxDeliveryDate": "2025-08-15",
    "budgetLimit": 10000,
    "preferredEquipment": ["EQ001", "EQ002"]
  }
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "originalPlan": {
      "id": "plan_001",
      "metrics": {
        "efficiency": 0.92,
        "estimatedCost": 8500
      }
    },
    "optimizedPlan": {
      "id": "plan_001_optimized",
      "metrics": {
        "efficiency": 0.95,
        "estimatedCost": 7800
      },
      "improvements": {
        "costReduction": 700,
        "efficiencyGain": 0.03,
        "timeReduction": 0.5
      }
    },
    "optimizationReport": {
      "changes": [
        "调整设备分配，提高利用率",
        "优化操作员排班，减少空闲时间"
      ],
      "riskAssessment": "优化后风险等级保持在低水平"
    }
  }
}
```

### 5. 获取排产分析数据

**GET** `/api/scheduling/analysis`

获取排产系统的分析统计数据。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| period | string | 否 | 统计周期（week、month、quarter、year） |
| startDate | string | 否 | 开始日期 (YYYY-MM-DD) |
| endDate | string | 否 | 结束日期 (YYYY-MM-DD) |
| strategy | string | 否 | 策略筛选 |
| status | string | 否 | 状态筛选 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalPlans": 150,
      "activePlans": 45,
      "completedPlans": 80,
      "cancelledPlans": 25,
      "averageEfficiency": 0.89,
      "averageCost": 7850,
      "onTimeDeliveryRate": 0.92
    },
    "strategyDistribution": {
      "earliest_completion": {
        "count": 45,
        "percentage": 30,
        "avgEfficiency": 0.88,
        "avgCost": 8200
      },
      "high_efficiency": {
        "count": 35,
        "percentage": 23.3,
        "avgEfficiency": 0.95,
        "avgCost": 7500
      },
      "load_balanced": {
        "count": 30,
        "percentage": 20,
        "avgEfficiency": 0.87,
        "avgCost": 7800
      },
      "cost_optimized": {
        "count": 25,
        "percentage": 16.7,
        "avgEfficiency": 0.82,
        "avgCost": 7200
      },
      "low_risk": {
        "count": 15,
        "percentage": 10,
        "avgEfficiency": 0.85,
        "avgCost": 8000
      }
    },
    "performanceMetrics": {
      "resourceUtilization": {
        "equipment": 0.85,
        "operators": 0.78,
        "overall": 0.82
      },
      "qualityMetrics": {
        "averageQualityScore": 0.94,
        "defectRate": 0.02,
        "reworkRate": 0.03
      },
      "timeMetrics": {
        "averageLeadTime": 12.5,
        "averageSetupTime": 2.3,
        "averageProcessingTime": 8.7
      }
    },
    "trends": {
      "efficiency": {
        "current": 0.89,
        "previous": 0.86,
        "change": 0.03,
        "trend": "increasing"
      },
      "cost": {
        "current": 7850,
        "previous": 8200,
        "change": -350,
        "trend": "decreasing"
      }
    },
    "recommendations": [
      {
        "type": "strategy_optimization",
        "priority": "high",
        "description": "建议增加高效率方案的使用比例",
        "expectedImprovement": "效率提升5%，成本降低8%"
      }
    ]
  }
}
```

## 📊 产能数据 API

### 1. 获取产能数据总览

**GET** `/api/capacity/overview`

获取系统整体产能数据概览。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| date | string | 否 | 查询日期 (YYYY-MM-DD) |
| factory | string | 否 | 厂区筛选 |
| includeHistory | boolean | 否 | 是否包含历史趋势 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "timestamp": "2025-07-29T12:00:00.000Z",
  "data": {
    "overview": {
      "totalCapacity": 2400,
      "availableCapacity": 1680,
      "utilizationRate": 0.72,
      "efficiency": 0.89
    },
    "equipment": {
      "total": 25,
      "available": 20,
      "maintenance": 3,
      "offline": 2,
      "avgUtilization": 0.75
    },
    "operators": {
      "total": 50,
      "available": 35,
      "onLeave": 8,
      "training": 7,
      "avgEfficiency": 0.88
    },
    "bottlenecks": [
      {
        "type": "equipment",
        "resource": "EQ005",
        "utilizationRate": 0.95,
        "impact": "high"
      }
    ]
  }
}
```

### 2. 获取设备产能配置

**GET** `/api/capacity/equipment/:id`

获取指定设备的详细产能配置信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 设备ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "EQ001",
    "equipmentCode": "EQ-001",
    "name": "数控机床A",
    "type": "数控机床",
    "factory": "第一厂区",
    "capacity": {
      "maxHoursPerDay": 16,
      "maxUnitsPerHour": 50,
      "efficiencyFactor": 0.85,
      "currentLoad": 0.6
    },
    "status": "active",
    "maintenance": {
      "nextScheduled": "2025-08-15",
      "duration": 4,
      "type": "preventive"
    },
    "operators": [
      {
        "operatorId": "OP001",
        "name": "张师傅",
        "skillLevel": 5,
        "efficiencyFactor": 1.1,
        "certifications": ["数控操作证"]
      }
    ],
    "performance": {
      "actualOutput": 720,
      "plannedOutput": 800,
      "qualityRate": 0.96,
      "downtime": 2.5
    }
  }
}
```

### 3. 更新设备产能配置

**PUT** `/api/capacity/equipment/:id`

更新设备的产能配置信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 设备ID |

#### 请求参数

```json
{
  "capacity": {
    "maxHoursPerDay": 18,
    "maxUnitsPerHour": 55,
    "efficiencyFactor": 0.9
  },
  "status": "active",
  "maintenance": {
    "nextScheduled": "2025-09-15",
    "duration": 6,
    "type": "preventive"
  }
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "设备产能配置更新成功",
  "data": {
    "id": "EQ001",
    "capacity": {
      "maxHoursPerDay": 18,
      "maxUnitsPerHour": 55,
      "efficiencyFactor": 0.9
    },
    "updatedAt": "2025-07-29T12:30:00.000Z"
  }
}
```

### 4. 获取操作员产能信息

**GET** `/api/capacity/operators/:id`

获取指定操作员的技能和产能信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 操作员ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "OP001",
    "usercode": "OP001",
    "name": "张师傅",
    "department": "生产部",
    "skillLevel": 5,
    "skills": ["数控操作", "质量检验", "设备维护"],
    "capacity": {
      "maxHoursPerDay": 8,
      "efficiency": 1.1,
      "currentLoad": 0.7,
      "availableHours": 2.4
    },
    "certifications": [
      {
        "name": "数控操作证",
        "level": "高级",
        "expiryDate": "2026-12-31",
        "status": "valid"
      }
    ],
    "currentAssignments": [
      {
        "equipmentId": "EQ001",
        "equipmentName": "数控机床A",
        "startTime": "08:00",
        "endTime": "16:00",
        "taskType": "生产操作"
      }
    ],
    "performance": {
      "productivityScore": 92,
      "qualityScore": 95,
      "attendanceRate": 0.98,
      "trainingHours": 40
    },
    "availability": {
      "status": "available",
      "nextShift": "2025-07-30T08:00:00.000Z",
      "vacationDays": 5
    }
  }
}
```

### 4. 计算产能需求

**POST** `/capacity-data/calculate-requirements`

根据订单信息计算产能需求。

#### 请求参数

```json
{
  "productId": "PROD001",
  "quantity": 1000,
  "requiredDate": "2024-12-31",
  "processFlow": [
    {
      "id": "process1",
      "name": "原料准备",
      "standardTime": 30,
      "setupTime": 10
    }
  ]
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "baseCapacity": {
      "requiredQuantity": 1000,
      "adjustedQuantity": 1020,
      "baseProductionHours": 510
    },
    "equipmentRequirements": {
      "byType": {
        "general": {
          "requiredCount": 2,
          "totalHours": 255,
          "adjustedHours": 268
        }
      }
    },
    "operatorRequirements": {
      "byEquipmentType": {
        "general": {
          "requiredCount": 2,
          "skillLevelRequired": 2
        }
      }
    },
    "timeRequirements": {
      "totalTime": 510,
      "timeInDays": 8.5,
      "estimatedStartDate": "2024-12-20",
      "estimatedEndDate": "2024-12-31"
    }
  }
}
```

## 性能监控 API

### 1. 获取性能统计

**GET** `/performance/stats`

获取系统性能统计信息。

#### 查询参数

- `operation` (可选): 指定操作名称

#### 响应示例

```json
{
  "success": true,
  "data": {
    "global": {
      "totalRequests": 1250,
      "successfulRequests": 1200,
      "failedRequests": 50,
      "averageResponseTime": 1250.5,
      "maxResponseTime": 5000,
      "minResponseTime": 200
    },
    "operations": {
      "generateSchedulePlans": {
        "count": 150,
        "successCount": 145,
        "failureCount": 5,
        "averageTime": 2500.8,
        "maxTime": 8000,
        "minTime": 800
      }
    }
  }
}
```

### 2. 获取缓存统计

**GET** `/performance/cache`

获取缓存系统的统计信息。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "hits": 850,
    "misses": 150,
    "sets": 200,
    "size": 180,
    "hitRate": 0.85,
    "memoryUsage": 52428800
  }
}
```

### 3. 系统健康检查

**GET** `/performance/health`

检查系统健康状态。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-12-01T10:00:00.000Z",
    "uptime": 86400,
    "memory": {
      "rss": 134217728,
      "heapTotal": 67108864,
      "heapUsed": 45088768,
      "external": 2097152
    },
    "performance": {
      "global": {
        "totalRequests": 1250,
        "averageResponseTime": 1250.5
      }
    }
  }
}
```

## 🚨 错误处理

### 标准错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "SCHEDULING_ERROR",
  "details": {
    "field": "具体错误信息",
    "code": "INVALID_PARAMETER"
  },
  "timestamp": "2025-07-29T12:00:00.000Z"
}
```

### 智能排产特定错误码

| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| INSUFFICIENT_CAPACITY | 400 | 产能不足 | 调整订单量或延长交期 |
| EQUIPMENT_UNAVAILABLE | 400 | 设备不可用 | 选择其他设备或调整时间 |
| OPERATOR_UNAVAILABLE | 400 | 操作员不可用 | 分配其他操作员或调整班次 |
| INVALID_CONSTRAINTS | 400 | 约束条件无效 | 检查约束参数设置 |
| OPTIMIZATION_FAILED | 500 | 优化算法失败 | 简化约束条件重试 |
| PLAN_NOT_FOUND | 404 | 排产方案不存在 | 检查方案ID是否正确 |
| RESOURCE_CONFLICT | 409 | 资源冲突 | 调整资源分配 |

### 常见HTTP状态码

| 状态码 | 描述 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理响应数据 |
| 400 | 请求参数错误 | 检查请求参数格式和值 |
| 401 | 未认证 | 检查JWT令牌是否有效 |
| 403 | 权限不足 | 确认用户具有相应权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 429 | 请求频率超限 | 降低请求频率 |
| 500 | 服务器错误 | 联系技术支持 |

## ⚡ 性能优化建议

### 请求优化
- **批量操作**: 使用批量API减少请求次数
- **缓存策略**: 合理使用缓存减少重复计算
- **分页查询**: 大数据量查询使用分页参数
- **字段选择**: 只请求必要的数据字段

### 算法优化
- **约束简化**: 避免过于复杂的约束条件
- **数据预处理**: 确保输入数据质量
- **增量更新**: 使用增量更新而非全量重算
- **并行处理**: 利用多核处理能力

## 🔧 开发工具

### API测试示例 (curl)

```bash
# 生成排产方案
curl -X POST http://localhost:5050/api/scheduling/generate \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "ORD20250729001",
    "productId": "PROD001",
    "quantity": 1000,
    "requiredDate": "2025-12-31",
    "priority": "normal"
  }'

# 获取方案列表
curl -X GET "http://localhost:5050/api/scheduling/plans?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取产能数据
curl -X GET http://localhost:5050/api/capacity/overview \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 算法策略说明

### 5种优化策略

| 策略 | 目标 | 适用场景 | 优势 |
|------|------|----------|------|
| earliest_completion | 最早完成 | 紧急订单 | 交期保证 |
| high_efficiency | 高效率 | 常规生产 | 资源利用最大化 |
| load_balanced | 负载均衡 | 多设备协调 | 避免瓶颈 |
| cost_optimized | 成本优化 | 成本敏感订单 | 降低生产成本 |
| low_risk | 低风险 | 重要客户 | 稳定性保证 |

---

**更新时间**: 2025-07-29
**版本**: v2.0.1
**技术支持**: [<EMAIL>](mailto:<EMAIL>)
- 单个请求最大大小: 10MB
- 请求超时时间: 30秒

## 版本控制

API版本通过URL路径进行控制：

- 当前版本: `v1` (默认)
- 访问方式: `/api/v1/scheduling/plans`

## 示例代码

### JavaScript (Fetch)

```javascript
// 创建排程方案
const response = await fetch('/api/scheduling/plans', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    id: 'ORD20241201001',
    productId: 'PROD001',
    quantity: 1000,
    requiredDate: '2024-12-31',
    priority: 'normal'
  })
});

const result = await response.json();
console.log(result);
```

### cURL

```bash
# 创建排程方案
curl -X POST http://localhost:5050/api/scheduling/plans \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "ORD20241201001",
    "productId": "PROD001",
    "quantity": 1000,
    "requiredDate": "2024-12-31",
    "priority": "normal"
  }'
```

## 更新日志

### v1.0.0 (2024-12-01)
- 初始版本发布
- 支持智能排程计算
- 支持产能数据管理
- 支持性能监控

---

更多详细信息请参考各个模块的具体文档。
