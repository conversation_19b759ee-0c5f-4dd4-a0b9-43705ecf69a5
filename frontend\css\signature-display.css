/**
 * 电子签名显示样式
 * 用于申请书模板中的签名显示组件
 */

/* 签名容器基础样式 */
.signature-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
    min-height: 80px;
}

/* 审批意见样式 */
.approval-comment {
    font-size: 14px;
    color: #333;
    text-align: center;
    margin-bottom: 8px;
    font-weight: 500;
    line-height: 1.4;
    word-wrap: break-word;
}

/* 签名显示区域 */
.signature-display-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 60px;
}

/* 签名包装器 */
.signature-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

/* 签名图片基础样式 */
.signature-image {
    display: block;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    object-fit: contain;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* 签名图片尺寸 */
.signature-small {
    max-width: 80px;
    max-height: 40px;
}

.signature-normal {
    max-width: 120px;
    max-height: 60px;
}

.signature-large {
    max-width: 160px;
    max-height: 80px;
}

/* 签名信息样式 */
.signature-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    font-size: 12px;
    color: #666;
}

.approver-name {
    font-weight: 600;
    color: #333;
}

.approval-time {
    font-size: 11px;
    color: #888;
}

/* 无签名状态样式 */
.no-signature {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 60px;
}

.signature-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
    padding: 10px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.no-signature-text {
    font-size: 11px;
    color: #999;
    font-style: italic;
}

/* 加载状态样式 */
.signature-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    color: #666;
}

.loading-text {
    font-size: 12px;
    color: #888;
}

/* 多签名布局 */
.multiple-signatures {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
}

.multiple-signatures .signature-container {
    flex: 0 0 auto;
    min-width: 140px;
    max-width: 180px;
}

/* 打印样式优化 */
@media print {
    .signature-image {
        border: 1px solid #000;
        background-color: #fff;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .approval-comment {
        color: #000 !important;
    }
    
    .signature-info {
        color: #333 !important;
    }
    
    .approver-name {
        color: #000 !important;
    }
    
    .approval-time {
        color: #666 !important;
    }
    
    .no-signature-text {
        color: #666 !important;
    }
    
    .signature-placeholder {
        border: 1px dashed #666;
        background-color: #f5f5f5;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .signature-container {
        min-height: 70px;
    }
    
    .signature-normal {
        max-width: 100px;
        max-height: 50px;
    }
    
    .signature-large {
        max-width: 120px;
        max-height: 60px;
    }
    
    .approval-comment {
        font-size: 13px;
    }
    
    .signature-info {
        font-size: 11px;
    }
    
    .multiple-signatures .signature-container {
        min-width: 120px;
        max-width: 150px;
    }
}

/* PDF生成优化 */
.pdf-generating .signature-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}

/* 高DPI显示优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .signature-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}
