<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新建申请 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/application-template.css">
    <link rel="stylesheet" href="/css/signature-display.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/application/new.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button class="mobile-menu-btn fixed top-4 left-4 z-50 p-2 rounded-md bg-white shadow-md text-gray-700 md:hidden" @click="toggleSidebar">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :class="{'open': sidebarOpen}"></sidebar>

        <div class="flex-1 ml-72 p-4 md:p-6 lg:p-8">
            <div class="max-w-5xl mx-auto">
                <!-- 页面标题 -->
                <div class="mb-6">
                    <div class="flex items-center mb-3">
                        <h1 class="text-2xl font-bold text-gray-800 mr-3">
                            {{ applicationType === 'standard' ? '新建标准申请' : '新建其他申请' }}
                        </h1>
                        <!-- 流程说明图标 -->
                        <div class="relative group">
                            <svg class="w-5 h-5 text-blue-500 cursor-help hover:text-blue-600 transition-colors"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <!-- Tooltip -->
                            <div class="absolute left-6 top-0 invisible group-hover:visible opacity-0 group-hover:opacity-100
                                        transition-all duration-200 z-50 w-80 bg-white text-gray-800 text-sm rounded-lg
                                        shadow-xl p-4 border border-gray-200">
                                <div class="font-semibold mb-3 text-gray-900">
                                    {{ applicationType === 'standard' ? '标准申请审批流程：' : '其他申请审批流程：' }}
                                </div>
                                <div v-if="applicationType === 'standard'" class="space-y-2">
                                    <div class="flex items-center">
                                        <span class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 font-medium">1</span>
                                        <span class="text-gray-700">厂长审批</span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 font-medium">2</span>
                                        <span class="text-gray-700">总监审批</span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 font-medium">3</span>
                                        <span class="text-gray-700">经理审批（可选）</span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 font-medium">4</span>
                                        <span class="text-gray-700">CEO最终审批</span>
                                    </div>
                                </div>
                                <div v-else class="space-y-2">
                                    <div class="flex items-center">
                                        <span class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs mr-3 font-medium">1</span>
                                        <span class="text-gray-700">总监审批（可选）</span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs mr-3 font-medium">2</span>
                                        <span class="text-gray-700">CEO最终审批</span>
                                    </div>
                                </div>
                                <!-- 箭头指示器 -->
                                <div class="absolute top-3 -left-2 w-0 h-0 border-t-4 border-b-4 border-r-4
                                            border-transparent border-r-white drop-shadow-sm"></div>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        {{ applicationType === 'standard'
                            ? '请填写标准申请信息，系统将按照标准流程进行审批。'
                            : '请填写其他申请信息，您可以选择是否需要总监审批。'
                        }}
                    </p>
                </div>

                <!-- 申请表单 -->
                <application-form
                    :user="currentUser"
                    :application-type="applicationType"
                    @submitted="onApplicationSubmitted">
                </application-form>
            </div>
        </div>
    </div>

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/application/new.js"></script>
</body>
</html>
