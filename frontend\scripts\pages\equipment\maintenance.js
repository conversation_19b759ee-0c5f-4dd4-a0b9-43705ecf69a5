/**
 * 维修/保养记录页面脚本
 * 处理设备维修和保养记录的业务逻辑
 */

import { createStandardApp } from '/scripts/common/pageInit.js';
import { logUserInfo, debugLog } from '/scripts/common/debugUtils.js';
import {
    exportMaintenanceRecordsToExcel,
    downloadExcelFile
} from '/scripts/api/maintenance.js';
import MaintenanceList from '/components/maintenance/MaintenanceList.js';
import MaintenanceForm from '/components/maintenance/MaintenanceForm.js';

// 创建页面应用
createStandardApp({
    requiredPermissions: ['equipment_maintenance'],
    components: {
        MaintenanceList,
        MaintenanceForm
    },
    onUserLoaded: async (user) => {
        logUserInfo('维修/保养记录页面初始化完成', user);
    },
    setup() {
        const { ref, reactive, onMounted } = Vue;

        // 响应式数据
        const loading = ref(false);

        // 表单状态
        const showForm = ref(false);
        const showDetail = ref(false);
        const selectedRecord = ref(null);
        const isEdit = ref(false);
        const refreshTrigger = ref(0);
        const isExporting = ref(false);
        const refreshing = ref(false);

        // 批量删除相关
        const maintenanceListRef = ref(null);
        const selectedRecords = ref([]);



        /**
         * 显示创建表单
         */
        const showCreateForm = () => {
            selectedRecord.value = null;
            isEdit.value = false;
            showForm.value = true;
        };

        /**
         * 处理编辑记录
         */
        const handleEdit = (record) => {
            selectedRecord.value = record;
            isEdit.value = true;
            showForm.value = true;
        };

        /**
         * 处理查看记录
         */
        const handleView = (record) => {
            selectedRecord.value = record;
            showDetail.value = true;
        };

        /**
         * 关闭表单
         */
        const closeForm = () => {
            showForm.value = false;
            selectedRecord.value = null;
            isEdit.value = false;
        };

        /**
         * 关闭详情
         */
        const closeDetail = () => {
            showDetail.value = false;
            selectedRecord.value = null;
        };

        /**
         * 处理表单成功
         */
        const handleFormSuccess = (data) => {
            closeForm();
            // 刷新列表
            refreshTrigger.value++;
        };

        /**
         * 导出记录到Excel
         */
        const exportRecords = async () => {
            try {
                isExporting.value = true;
                const blob = await exportMaintenanceRecordsToExcel();

                // 生成文件名
                const today = new Date().toISOString().split('T')[0];
                const filename = `维修保养记录_${today}.xlsx`;

                downloadExcelFile(blob, filename);

                if (window.showToast) {
                    window.showToast('Excel导出成功', 'success');
                }
            } catch (error) {
                console.error('导出Excel失败:', error);
                if (window.showToast) {
                    window.showToast('导出Excel失败', 'error');
                }
            } finally {
                isExporting.value = false;
            }
        };

        /**
         * 显示导入对话框
         */
        const showImportDialog = () => {
            if (window.showToast) {
                window.showToast('导入功能开发中', 'info');
            }
        };

        /**
         * 刷新数据
         */
        const refreshData = async () => {
            try {
                refreshing.value = true;
                refreshTrigger.value++;
                if (window.showToast) {
                    window.showToast('数据刷新成功', 'success');
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
                if (window.showToast) {
                    window.showToast('刷新数据失败', 'error');
                }
            } finally {
                refreshing.value = false;
            }
        };

        /**
         * 批量删除记录
         */
        const batchDeleteRecords = () => {
            if (selectedRecords.value.length === 0) {
                if (window.showToast) {
                    window.showToast('请先选择要删除的记录', 'warning');
                }
                return;
            }

            if (maintenanceListRef.value && maintenanceListRef.value.batchDeleteRecords) {
                maintenanceListRef.value.batchDeleteRecords();
            }
        };

        /**
         * 处理选择变化
         */
        const handleSelectionChange = (selected) => {
            selectedRecords.value = selected;
        };

        /**
         * 获取故障程度样式
         */
        const getSeverityClass = (severity) => {
            const severityMap = {
                'low': 'bg-green-100 text-green-800',
                'medium': 'bg-yellow-100 text-yellow-800',
                'high': 'bg-orange-100 text-orange-800',
                'critical': 'bg-red-100 text-red-800'
            };
            return severityMap[severity] || 'bg-gray-100 text-gray-800';
        };

        /**
         * 获取故障程度标签
         */
        const getSeverityLabel = (severity) => {
            const severityMap = {
                'low': '轻微',
                'medium': '中等',
                'high': '严重',
                'critical': '紧急'
            };
            return severityMap[severity] || severity;
        };

        // 页面初始化逻辑
        onMounted(() => {
            debugLog('维修/保养记录页面组件已挂载');
        });

        return {
            loading,
            showForm,
            showDetail,
            selectedRecord,
            isEdit,
            refreshTrigger,
            isExporting,
            refreshing,
            maintenanceListRef,
            selectedRecords,
            showCreateForm,
            handleEdit,
            handleView,
            closeForm,
            closeDetail,
            handleFormSuccess,
            exportRecords,
            showImportDialog,
            refreshData,
            batchDeleteRecords,
            handleSelectionChange,
            getSeverityClass,
            getSeverityLabel
        };
    }
}).mount('#app');
