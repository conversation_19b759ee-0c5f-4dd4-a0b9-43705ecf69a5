/**
 * 质量管理路由
 * 定义检测报告相关的API路由
 */

const express = require('express');
const router = express.Router();
const qualityController = require('../controllers/qualityController');
const userController = require('../controllers/userController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');
const qualityUpload = require('../middlewares/qualityUpload');

// 获取用户列表（用于邮件通知选择）
router.get('/users',
    authenticateJWT,
    checkPermission('quality_upload'), // 需要上传权限才能选择用户
    userController.getUsersForNotification
);

// 获取检测报告列表
router.get('/',
    authenticateJWT,
    checkPermission('quality_view'),
    qualityController.getReports
);

// 创建检测报告（支持文件上传）
router.post('/',
    authenticateJWT,
    checkPermission('quality_upload'),
    qualityUpload.array('files', 10), // 最多上传10个文件
    qualityController.createReport
);

// 根据ID获取检测报告详情
router.get('/:id',
    authenticateJWT,
    checkPermission('quality_view'),
    qualityController.getReportById
);

// 根据报告编号获取检测报告
router.get('/number/:reportNumber',
    authenticateJWT,
    checkPermission('quality_view'),
    qualityController.getReportByNumber
);

// 更新检测报告
router.put('/:id',
    authenticateJWT,
    checkPermission('quality_upload'), // 需要上传权限才能修改
    qualityController.updateReport
);

// 删除检测报告
router.delete('/:id',
    authenticateJWT,
    checkPermission('quality_manage'), // 需要管理权限才能删除
    qualityController.deleteReport
);

// 下载检测报告文件
router.get('/files/:fileId/download',
    authenticateJWT,
    checkPermission('quality_download'),
    qualityController.downloadFile
);

module.exports = router;
