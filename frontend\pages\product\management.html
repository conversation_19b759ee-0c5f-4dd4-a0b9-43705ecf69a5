<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品管理 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/product/management.css">

</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">产品管理</h1>
                            <p class="text-gray-600 mt-1">管理产品信息和生产工艺流程</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button @click="showCreateModal = true"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                新建产品
                            </button>
                        </div>
                    </div>
                </header>

                <!-- 筛选和搜索 -->
                <div class="px-6 py-4 bg-white border-b border-gray-200">
                    <div class="flex items-center space-x-4">
                        <div class="flex-1">
                            <input v-model="filters.keyword"
                                   @input="debouncedSearch"
                                   type="text"
                                   placeholder="搜索产品编码或名称..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <select v-model="filters.category"
                                    @change="loadProducts"
                                    class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">所有类别</option>
                                <option value="原材料">原材料</option>
                                <option value="半成品">半成品</option>
                                <option value="成品">成品</option>
                            </select>
                        </div>
                        <button @click="refreshData"
                                class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                            刷新
                        </button>
                    </div>
                </div>

                <!-- 产品列表 -->
                <div class="p-6">
                    <div v-if="loading" class="text-center py-8">
                        <div class="loading-spinner mx-auto"></div>
                        <p class="mt-4 text-gray-600">加载中...</p>
                    </div>

                    <div v-else-if="products.length === 0" class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无产品</h3>
                        <p class="mt-1 text-sm text-gray-500">开始创建第一个产品吧</p>
                    </div>

                    <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li v-for="product in products" :key="product.id"
                                class="px-6 py-4 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <h3 class="text-lg font-medium text-gray-900">{{ product.name }}</h3>
                                            <span class="ml-3 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                {{ product.code }}
                                            </span>
                                            <span v-if="product.category" 
                                                  class="ml-2 px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                {{ product.category }}
                                            </span>
                                        </div>
                                        <div class="mt-2 text-sm text-gray-600">
                                            <p>计量单位: {{ product.unit }}</p>
                                            <p v-if="product.standardTime > 0">标准工时: {{ product.standardTime }} 分钟</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="viewProduct(product)"
                                                class="px-3 py-1 text-sm text-blue-600 hover:text-blue-800">
                                            查看
                                        </button>
                                        <button @click="editProduct(product)"
                                                class="px-3 py-1 text-sm text-green-600 hover:text-green-800">
                                            编辑
                                        </button>
                                        <button @click="deleteProduct(product)"
                                                class="px-3 py-1 text-sm text-red-600 hover:text-red-800">
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <!-- 分页 -->
                    <div v-if="pagination.totalPages > 1" class="mt-6 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            显示第 {{ (pagination.page - 1) * pagination.limit + 1 }} 到 
                            {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，
                            共 {{ pagination.total }} 条记录
                        </div>
                        <div class="flex items-center space-x-2">
                            <button @click="changePage(pagination.page - 1)"
                                    :disabled="pagination.page <= 1"
                                    class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                上一页
                            </button>
                            <span class="px-3 py-1 text-sm">
                                第 {{ pagination.page }} / {{ pagination.totalPages }} 页
                            </span>
                            <button @click="changePage(pagination.page + 1)"
                                    :disabled="pagination.page >= pagination.totalPages"
                                    class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                下一页
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建/编辑产品模态框 -->
        <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click.self="closeModals">
            <div class="modal-content w-full max-w-2xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">
                        {{ showCreateModal ? '新建产品' : '编辑产品' }}
                    </h2>
                    <button @click="closeModals" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">产品编码 *</label>
                            <input v-model="formData.code"
                                   type="text"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">产品名称 *</label>
                            <input v-model="formData.name"
                                   type="text"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">产品类别</label>
                            <select v-model="formData.category"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择类别</option>
                                <option value="原材料">原材料</option>
                                <option value="半成品">半成品</option>
                                <option value="成品">成品</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">计量单位</label>
                            <input v-model="formData.unit"
                                   type="text"
                                   placeholder="如：pcs, kg, m等"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">标准工时（分钟）</label>
                            <input v-model.number="formData.standardTime"
                                   type="number"
                                   min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="mt-6 flex items-center justify-end space-x-4">
                        <button type="button"
                                @click="closeModals"
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                :disabled="submitting"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                            {{ submitting ? '保存中...' : '保存' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/product/management.js"></script>
</body>
</html>
