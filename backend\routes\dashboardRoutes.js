/**
 * 主页路由
 * 定义主页相关的API路由
 */

const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const { authenticateJWT } = require('../middlewares/auth');

/**
 * 获取主页统计数据
 * GET /api/dashboard/stats
 * 权限: 需要登录
 */
router.get('/stats',
    authenticateJWT,
    dashboardController.getStats.bind(dashboardController)
);

/**
 * 获取最近活动
 * GET /api/dashboard/activities
 * 权限: 需要登录
 */
router.get('/activities',
    authenticateJWT,
    dashboardController.getActivities.bind(dashboardController)
);

/**
 * 获取通知信息
 * GET /api/dashboard/notifications
 * 权限: 需要登录
 */
router.get('/notifications',
    authenticateJWT,
    dashboardController.getNotifications.bind(dashboardController)
);

/**
 * 获取待处理任务
 * GET /api/dashboard/pending-tasks
 * 权限: 需要登录
 */
router.get('/pending-tasks',
    authenticateJWT,
    dashboardController.getPendingTasks.bind(dashboardController)
);

/**
 * 获取快速操作菜单
 * GET /api/dashboard/quick-actions
 * 权限: 需要登录
 */
router.get('/quick-actions',
    authenticateJWT,
    dashboardController.getQuickActions.bind(dashboardController)
);

/**
 * 获取系统健康状态
 * GET /api/dashboard/health
 * 权限: 需要登录
 */
router.get('/health',
    authenticateJWT,
    dashboardController.getSystemHealth.bind(dashboardController)
);

module.exports = router;
