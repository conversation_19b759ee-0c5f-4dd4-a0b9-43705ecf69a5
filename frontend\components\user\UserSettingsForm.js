/**
 * 个人设置表单组件
 * 处理用户个人设置的显示和更新
 */

import { updateUserSettings, changePassword, uploadSignature, uploadUserSignature } from '../../scripts/api/user.js';

export default {
    props: {
        user: Object
    },
    emits: ['updated'],
    setup(props, { emit }) {
        const { ref, reactive, computed, watch } = Vue;

        // 表单数据
        const formData = reactive({
            username: '',
            usercode: '',
            department: '',
            email: ''
        });

        // 密码表单
        const passwordForm = reactive({
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
        });

        // 签名上传相关
        const fileInput = ref(null);
        const selectedFile = ref(null);
        const dragOver = ref(false);
        const signaturePreview = ref('');

        // 加载状态
        const isSubmitting = ref(false);
        const isChangingPassword = ref(false);
        const isUploadingSignature = ref(false);

        // 密码错误
        const passwordError = computed(() => {
            if (passwordForm.newPassword && passwordForm.newPassword.length < 6) {
                return '新密码长度至少为6个字符';
            }
            if (passwordForm.newPassword && passwordForm.confirmPassword &&
                passwordForm.newPassword !== passwordForm.confirmPassword) {
                return '两次输入的密码不一致';
            }
            return '';
        });

        // 监听用户数据变化
        watch(() => props.user, (newUser) => {
            if (newUser) {
                formData.username = newUser.username || '';
                formData.usercode = newUser.usercode || '';
                formData.department = newUser.department || '';
                formData.email = newUser.email || '';

                // 如果用户有签名，显示预览
                if (newUser.hasSignature && newUser.signaturePath) {
                    // 使用正确的路径格式，添加时间戳防止缓存
                    signaturePreview.value = `/${newUser.signaturePath}?t=${new Date().getTime()}`;
                }
            }
        }, { immediate: true });

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            processFile(file);
        }

        // 处理文件拖放
        function handleFileDrop(event) {
            dragOver.value = false;
            const file = event.dataTransfer.files[0];
            processFile(file);
        }

        // 处理文件
        function processFile(file) {
            if (!file) return;

            // 验证文件类型
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'];
            if (!validTypes.includes(file.type)) {
                alert('请上传有效的图片文件（JPG/PNG/GIF/BMP）');
                return;
            }

            // 验证文件大小
            if (file.size > 2 * 1024 * 1024) {
                alert('文件大小不能超过2MB');
                return;
            }

            selectedFile.value = file;

            // 创建预览
            const reader = new FileReader();
            reader.onload = (e) => {
                signaturePreview.value = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // 移除文件
        function removeFile() {
            selectedFile.value = null;
            // 恢复原始签名预览，添加时间戳防止缓存
            signaturePreview.value = props.user?.hasSignature ? `/${props.user.signaturePath}?t=${new Date().getTime()}` : '';
            if (fileInput.value) {
                fileInput.value.value = '';
            }
        }

        // 更新个人设置
        async function submitSettings() {
            try {
                isSubmitting.value = true;

                const result = await updateUserSettings(formData);

                if (result.success) {
                    emit('updated', result.user);
                } else {
                    alert('更新个人设置失败: ' + result.message);
                }
            } catch (error) {
                console.error('更新失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('更新失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                isSubmitting.value = false;
            }
        }

        // 修改密码
        async function submitPasswordChange() {
            if (passwordError.value) {
                alert(passwordError.value);
                return;
            }

            try {
                isChangingPassword.value = true;

                const result = await changePassword(
                    passwordForm.currentPassword,
                    passwordForm.newPassword
                );

                if (result.success) {
                    alert('密码修改成功！');
                    // 清空密码表单
                    passwordForm.currentPassword = '';
                    passwordForm.newPassword = '';
                    passwordForm.confirmPassword = '';
                } else {
                    alert('密码修改失败: ' + result.message);
                }
            } catch (error) {
                console.error('密码修改失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('密码修改失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                isChangingPassword.value = false;
            }
        }

        // 上传签名
        async function submitSignatureUpload() {
            if (!selectedFile.value) {
                alert('请先选择签名图片');
                return;
            }

            try {
                isUploadingSignature.value = true;

                const result = await uploadSignature(selectedFile.value);

                if (result.success) {
                    alert('签名上传成功！');
                    emit('updated', result.user);
                    selectedFile.value = null;
                    if (fileInput.value) {
                        fileInput.value.value = '';
                    }
                } else {
                    alert('签名上传失败: ' + result.message);
                }
            } catch (error) {
                console.error('签名上传失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('签名上传失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                isUploadingSignature.value = false;
            }
        }

        return {
            formData,
            passwordForm,
            passwordError,
            fileInput,
            selectedFile,
            dragOver,
            signaturePreview,
            isSubmitting,
            isChangingPassword,
            isUploadingSignature,
            handleFileSelect,
            handleFileDrop,
            removeFile,
            submitSettings,
            submitPasswordChange,
            submitSignatureUpload
        };
    },
    template: `
        <div class="space-y-8">
            <!-- 基本信息 -->
            <section>
                <h3 class="text-lg font-medium text-gray-800 mb-4">基本信息</h3>
                <form @submit.prevent="submitSettings" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-gray-700 mb-2">姓名</label>
                            <input type="text" v-model="formData.username" required
                                   class="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:border-blue-400 text-gray-700">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">用户代码</label>
                            <input type="text" v-model="formData.usercode" required disabled
                                   class="w-full p-2 border border-gray-200 rounded-md bg-gray-50 text-gray-500">
                            <p class="text-xs text-gray-500 mt-1">用户代码不可修改</p>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">部门</label>
                            <input type="text" v-model="formData.department"
                                   class="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:border-blue-400 text-gray-700">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">邮箱</label>
                            <input type="email" v-model="formData.email"
                                   class="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:border-blue-400 text-gray-700">
                        </div>
                    </div>
                    <div>
                        <button type="submit"
                                :disabled="isSubmitting"
                                class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50">
                            {{ isSubmitting ? '保存中...' : '保存设置' }}
                        </button>
                    </div>
                </form>
            </section>

            <!-- 修改密码 -->
            <section>
                <h3 class="text-lg font-medium text-gray-800 mb-4">修改密码</h3>
                <form @submit.prevent="submitPasswordChange" class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-2">当前密码</label>
                        <input type="password" v-model="passwordForm.currentPassword" required
                               class="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:border-blue-400 text-gray-700">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">新密码</label>
                        <input type="password" v-model="passwordForm.newPassword" required
                               class="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:border-blue-400 text-gray-700">
                        <p class="text-xs text-gray-500 mt-1">密码长度至少为6个字符</p>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">确认新密码</label>
                        <input type="password" v-model="passwordForm.confirmPassword" required
                               class="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:border-blue-400 text-gray-700">
                    </div>
                    <div v-if="passwordError" class="text-red-500 text-sm">
                        {{ passwordError }}
                    </div>
                    <div>
                        <button type="submit"
                                :disabled="isChangingPassword || passwordError"
                                class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50">
                            {{ isChangingPassword ? '修改中...' : '修改密码' }}
                        </button>
                    </div>
                </form>
            </section>

            <!-- 电子签名 -->
            <section>
                <h3 class="text-lg font-medium text-gray-800 mb-4">电子签名</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-2">当前签名</label>
                        <div v-if="signaturePreview" class="border border-gray-200 rounded-md p-4 bg-gray-50 max-w-xs">
                            <img :src="signaturePreview" alt="签名预览" class="max-h-32">
                        </div>
                        <div v-else class="border border-gray-200 rounded-md p-4 bg-gray-50 text-gray-500 max-w-xs">
                            暂无签名
                        </div>
                    </div>

                    <div>
                        <label class="block text-gray-700 mb-2">上传新签名</label>
                        <input type="file" ref="fileInput" @change="handleFileSelect"
                               class="hidden"
                               accept=".jpg,.jpeg,.png,.gif,.bmp">
                        <div id="signatureInputArea"
                             class="text-center cursor-pointer py-4 border border-dashed border-gray-200 rounded-md max-w-md"
                             @click="fileInput.click()"
                             @dragover.prevent="dragOver = true"
                             @dragleave.prevent="dragOver = false"
                             @drop.prevent="handleFileDrop"
                             :class="{'border-blue-400': dragOver}">
                            <svg class="w-8 h-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <p class="text-gray-500 text-sm">点击选择文件或拖拽文件到此区域</p>
                            <p class="text-xs text-gray-400 mt-1">支持格式：JPG/PNG/GIF/BMP（最大2MB）</p>
                        </div>

                        <div v-if="selectedFile" class="mt-4 flex items-center justify-between p-2 bg-gray-50 rounded-md max-w-md">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span class="text-sm text-gray-700">{{ selectedFile.name }}</span>
                                <span class="text-xs text-gray-400">{{ (selectedFile.size/1024/1024).toFixed(2) }}MB</span>
                            </div>
                            <button @click.prevent="removeFile"
                                    class="text-gray-400 hover:text-red-500 text-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div>
                        <button @click="submitSignatureUpload"
                                :disabled="isUploadingSignature || !selectedFile"
                                class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50">
                            {{ isUploadingSignature ? '上传中...' : '上传签名' }}
                        </button>
                    </div>
                </div>
            </section>
        </div>
    `
};
