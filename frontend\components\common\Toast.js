/**
 * Toast 通知组件
 * 用于显示非阻塞的成功、错误、警告等消息
 */

export default {
    props: {
        message: {
            type: String,
            required: true
        },
        type: {
            type: String,
            default: 'success', // success, error, warning, info
            validator: value => ['success', 'error', 'warning', 'info'].includes(value)
        },
        duration: {
            type: Number,
            default: 3000 // 3秒后自动消失
        },
        show: {
            type: Boolean,
            default: false
        }
    },
    emits: ['close'],
    setup(props, { emit }) {
        const { ref, watch, onMounted } = Vue;
        
        const visible = ref(props.show);
        let timer = null;

        // 监听show属性变化
        watch(() => props.show, (newValue) => {
            visible.value = newValue;
            if (newValue && props.duration > 0) {
                // 设置自动关闭定时器
                clearTimeout(timer);
                timer = setTimeout(() => {
                    close();
                }, props.duration);
            }
        });

        function close() {
            visible.value = false;
            clearTimeout(timer);
            emit('close');
        }

        // 获取Toast样式类
        function getToastClass() {
            const baseClass = 'fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform';
            const typeClasses = {
                success: 'bg-green-500 text-white',
                error: 'bg-red-500 text-white',
                warning: 'bg-yellow-500 text-white',
                info: 'bg-blue-500 text-white'
            };
            
            const visibilityClass = visible.value ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0';
            
            return `${baseClass} ${typeClasses[props.type]} ${visibilityClass}`;
        }

        // 获取图标
        function getIcon() {
            const icons = {
                success: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>`,
                error: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>`,
                warning: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>`,
                info: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>`
            };
            return icons[props.type];
        }

        return {
            visible,
            close,
            getToastClass,
            getIcon
        };
    },
    template: `
        <div v-if="visible" :class="getToastClass()">
            <div class="flex items-center space-x-2">
                <div v-html="getIcon()"></div>
                <span>{{ message }}</span>
                <button @click="close" class="ml-2 hover:opacity-75">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `
};
