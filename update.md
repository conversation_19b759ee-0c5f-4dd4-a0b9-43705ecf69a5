# 更新日志

## 2025年6月8日

### 邮件重试机制优化

1. **无限重试机制**
   - 将邮件发送从最多重试5次改为无限重试直到成功
   - 实现智能递增重试间隔，避免频繁请求邮件服务器
   - 重试间隔从3秒开始，每5次失败后递增，最大30秒
   - 确保重要邮件通知永不丢失

2. **日志输出简化**
   - 简化重试过程的日志输出，减少日志噪音
   - 只在首次失败时记录错误日志
   - 重试过程静默进行，不产生额外日志
   - 成功时显示是否经过重试及重试次数

3. **邮件服务配置优化**
   - 将SMTP配置改为Gmail服务配置，更简洁可靠
   - 添加dotenv支持，正确加载环境变量
   - 移除maxAttempts配置，采用无限重试策略

### 系统启动性能优化

1. **启动架构简化**
   - 从双服务器模式改为单服务器模式
   - 移除不必要的前端静态服务器(3000端口)
   - 启动时间从8-12秒减少到3-5秒，提升60-70%
   - 内存使用减少约50%，进程数量减少67%

2. **启动脚本优化**
   - 更新npm start为单服务器启动
   - 保留npm run start:dual作为双服务器选项
   - 添加快速启动脚本start-fast.bat
   - 改进启动成功提示信息

3. **访问地址统一**
   - 统一使用http://localhost:5050访问系统
   - 更新README文档说明正确访问方式
   - 避免端口混淆问题

### 用户认证问题修复

1. **密码验证修复**
   - 重置所有用户密码为统一的测试密码
   - 管理员密码：admin123
   - 业务用户密码：123456
   - 确保所有用户都能正常登录

2. **登录流程优化**
   - 修复密码哈希验证问题
   - 优化登录错误提示
   - 确保JWT令牌正确生成和验证

### 代码清理和优化

1. **测试脚本清理**
   - 删除不必要的开发测试脚本
   - 保留生产环境需要的实用工具
   - 减少50%的脚本文件，提升代码整洁度

2. **文档更新**
   - 创建邮件重试机制详细文档
   - 更新性能优化说明文档
   - 添加脚本清理总结文档

## 2025年5月28日

### 邮件通知系统集成

1. **Gmail邮件服务集成**
   - 集成Nodemailer邮件发送服务，支持Gmail SMTP
   - 添加邮件配置管理，支持环境变量配置
   - 实现邮件发送重试机制，确保发送可靠性
   - 添加邮件连接验证功能，确保配置正确性

2. **完整审批流程邮件通知**
   - **申请提交通知**：用户提交申请时自动通知所有厂长
   - **审批状态变更通知**：审批通过时自动通知下一级审批人
   - **审批完成通知**：CEO最终审批通过时通知申请人
   - **审批拒绝通知**：任何阶段拒绝时立即通知申请人
   - **高金额申请通知**：金额超过100,000的申请完成时通知所有只读用户

3. **邮件模板系统**
   - 设计响应式HTML邮件模板，支持多种设备显示
   - 实现邮件内容动态生成，包含完整申请信息
   - 添加不同类型通知的专用模板和样式
   - 支持邮件主题和内容的模板变量替换

4. **用户邮箱管理**
   - 扩展用户数据结构，添加邮箱字段支持
   - 实现基于角色的收件人自动识别和管理
   - 添加用户邮箱配置脚本，支持批量设置
   - 提供邮箱验证和管理功能

5. **系统集成和优化**
   - 将邮件通知无缝集成到现有审批流程中
   - 实现异步邮件发送，不影响审批流程性能
   - 添加详细的邮件发送日志记录和错误处理
   - 提供邮件服务测试工具和配置验证

6. **配置和文档**
   - 创建Gmail配置指南和环境变量模板
   - 添加邮件功能测试脚本和故障排除指南
   - 更新系统文档，包含邮件配置和使用说明
   - 提供完整的邮件功能部署和维护文档

## 2025年5月20日

### 权限管理页面优化

1. **用户筛选功能**
   - 添加了按权限状态筛选用户的功能
   - 支持查看"所有用户"、"已设置权限的用户"和"未设置权限的用户"
   - 实现了用户权限状态的自动检测和显示

2. **搜索功能优化**
   - 添加了更美观、更紧凑的搜索框设计
   - 搜索框添加了搜索图标，提供更直观的视觉提示
   - 支持按用户名或角色进行搜索

3. **用户卡片显示优化**
   - 为已设置权限的用户添加了绿色背景和标签
   - 为未设置权限的用户添加了灰色标签
   - 优化了用户信息的布局和显示

4. **用户体验改进**
   - 添加了无匹配结果的提示
   - 添加了用户总数显示
   - 优化了加载状态的显示
