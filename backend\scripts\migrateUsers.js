/**
 * 用户数据迁移脚本
 * 将用户数据从单一JSON文件迁移到独立文件
 */

const fs = require('fs');
const path = require('path');
const { readJsonFile, writeJsonFile, ensureDirectoryExists } = require('../utils/fileSystem');
const config = require('../config');
const userService = require('../services/userService');

/**
 * 生成10位纯数字唯一ID
 * @returns {string} 10位数字UID
 */
function generateId() {
    // 生成10位纯数字ID
    const min = 1000000000; // 10位数的最小值
    const max = 9999999999; // 10位数的最大值
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 检查ID是否已存在
 * @param {string} id - 用户ID
 * @param {Set} existingIds - 已存在的ID集合
 * @returns {boolean} 是否存在
 */
function isIdExists(id, existingIds) {
    return existingIds.has(id);
}

/**
 * 迁移用户数据
 */
async function migrateUsers() {
    console.log('开始迁移用户数据...');
    
    // 确保用户目录存在
    const usersDir = config.paths.users;
    ensureDirectoryExists(usersDir);
    console.log(`用户目录: ${usersDir}`);
    
    // 读取旧的用户数据
    const oldUsersFile = config.files.users;
    if (!fs.existsSync(oldUsersFile)) {
        console.log(`旧用户文件不存在: ${oldUsersFile}`);
        return;
    }
    
    const users = readJsonFile(oldUsersFile, []);
    console.log(`读取到 ${users.length} 个用户`);
    
    if (users.length === 0) {
        console.log('没有用户数据需要迁移');
        return;
    }
    
    // 跟踪已存在的ID
    const existingIds = new Set();
    
    // 迁移每个用户
    for (const user of users) {
        // 生成新的10位数字ID
        let newId;
        do {
            newId = generateId().toString();
        } while (isIdExists(newId, existingIds));
        
        existingIds.add(newId);
        
        // 记录ID映射
        const oldId = user.id;
        user.id = newId;
        
        console.log(`迁移用户: ${user.username} (${oldId} -> ${newId})`);
        
        // 保存到新文件
        const userFile = path.join(usersDir, `${newId}.json`);
        writeJsonFile(userFile, user);
    }
    
    // 备份旧文件
    const backupFile = `${oldUsersFile}.bak`;
    fs.copyFileSync(oldUsersFile, backupFile);
    console.log(`旧用户文件已备份为: ${backupFile}`);
    
    // 删除旧文件
    fs.unlinkSync(oldUsersFile);
    console.log(`旧用户文件已删除: ${oldUsersFile}`);
    
    console.log('用户数据迁移完成!');
}

// 执行迁移
migrateUsers().catch(err => {
    console.error('迁移失败:', err);
    process.exit(1);
});
