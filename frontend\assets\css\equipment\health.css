/* 设备管理模块 - 设备健康度评估页面样式文件 */

/* 卡片悬停效果 */
.equipment-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.equipment-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 健康度进度条动画 */
.health-progress {
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 状态指示器脉冲动画 */
.status-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* 筛选面板滑入动画 */
.filter-panel {
    transition: all 0.3s ease-in-out;
    transform: translateY(-10px);
    opacity: 0;
}

.filter-panel.show {
    transform: translateY(0);
    opacity: 1;
}

/* 卡片网格响应式 */
@media (max-width: 640px) {
    .equipment-cards {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    .equipment-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1025px) and (max-width: 1280px) {
    .equipment-cards {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1281px) {
    .equipment-cards {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 厂区头部样式 */
.area-header {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
}

.area-header:hover:not(.dragging) {
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.area-header.dragging {
    transform: scale(1.02);
    box-shadow: 0 20px 40px -8px rgba(59, 130, 246, 0.4), 0 8px 16px -4px rgba(0, 0, 0, 0.1);
    z-index: 50 !important;
    position: relative;
    border: 2px solid #3b82f6;
    background-color: rgba(59, 130, 246, 0.08) !important;
    opacity: 0.9;
}

/* 拖拽时的脉冲边框效果 */
.area-header.dragging::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid #3b82f6;
    border-radius: inherit;
    animation: dragPulse 1.5s infinite;
    pointer-events: none;
}

@keyframes dragPulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.02);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 拖拽时的光标 */
.cursor-grabbing {
    cursor: grabbing !important;
}

.cursor-grabbing * {
    cursor: grabbing !important;
}

/* 拖拽状态样式 */
.area-group.drag-over {
    border: 2px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.05);
}

/* 拖拽悬停效果 */
.area-header.drag-hover {
    border: 2px dashed #10b981 !important;
    background-color: rgba(16, 185, 129, 0.1) !important;
    transform: translateY(-2px);
}

.area-header.drag-hover::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #3b82f6);
    border-radius: 2px;
    animation: pulse 1s infinite;
}

/* 拖拽时禁用过渡效果 */
.area-header.dragging {
    transition: none !important; /* 拖拽时禁用过渡，避免延迟 */
}

/* 折叠动画 */
.area-content {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.area-content.collapsed {
    max-height: 0;
    opacity: 0;
    margin-top: 0;
    margin-bottom: 0;
}

.area-content.expanded {
    max-height: 5000px;
    opacity: 1;
}

/* 长按反馈 */
.area-header.long-pressing {
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(0.98);
}

/* 拖拽提示 */
.drag-hint {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 1000;
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; transform: translateY(-10px); }
    10%, 90% { opacity: 1; transform: translateY(0); }
}

/* 模态框样式 */
.modal-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

/* 模态框打开时禁用body滚动 */
body:has(.modal-container) {
    overflow: hidden;
}

/* 兼容性备选方案 */
body.modal-open {
    overflow: hidden;
}
