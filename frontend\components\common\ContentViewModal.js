/**
 * 内容查看弹出框组件
 * 用于显示完整的申请内容或其他长文本内容
 */

const { ref, computed, onMounted, onUnmounted } = Vue;

export default {
    name: 'ContentViewModal',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '查看内容'
        },
        content: {
            type: String,
            default: ''
        },
        maxWidth: {
            type: String,
            default: 'max-w-2xl'
        }
    },
    emits: ['close'],
    setup(props, { emit }) {
        // 关闭弹出框
        function close() {
            emit('close');
        }

        // 处理ESC键关闭
        function handleKeydown(event) {
            if (event.key === 'Escape' && props.visible) {
                close();
            }
        }

        // 处理点击外部关闭
        function handleBackdropClick(event) {
            if (event.target === event.currentTarget) {
                close();
            }
        }

        // 复制内容到剪贴板
        async function copyContent() {
            try {
                await navigator.clipboard.writeText(props.content);
                if (window.showNotification) {
                    window.showNotification('内容已复制到剪贴板', 'success');
                } else {
                    alert('内容已复制到剪贴板');
                }
            } catch (error) {
                console.error('复制失败:', error);
                if (window.showNotification) {
                    window.showNotification('复制失败', 'error');
                } else {
                    alert('复制失败');
                }
            }
        }

        // 监听键盘事件
        onMounted(() => {
            document.addEventListener('keydown', handleKeydown);
        });

        onUnmounted(() => {
            document.removeEventListener('keydown', handleKeydown);
        });

        // 计算内容是否为空
        const isEmpty = computed(() => {
            return !props.content || props.content.trim() === '';
        });

        // 计算内容行数（用于调整文本框高度）
        const contentLines = computed(() => {
            if (!props.content) return 1;
            return Math.min(Math.max(props.content.split('\n').length, 3), 20);
        });

        return {
            close,
            handleBackdropClick,
            copyContent,
            isEmpty,
            contentLines
        };
    },
    template: `
        <!-- 弹出框遮罩 -->
        <div v-if="visible" 
             @click="handleBackdropClick"
             class="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            
            <!-- 弹出框内容 -->
            <div :class="['bg-white rounded-2xl shadow-2xl w-full', maxWidth]"
                 @click.stop>
                
                <!-- 头部 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-900">{{ title }}</h3>
                            <p class="text-sm text-gray-600">查看完整内容</p>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="flex items-center space-x-2">
                        <!-- 复制按钮 -->
                        <button @click="copyContent"
                                :disabled="isEmpty"
                                class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                title="复制内容">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                        
                        <!-- 关闭按钮 -->
                        <button @click="close"
                                class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                                title="关闭">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="p-6">
                    <div v-if="isEmpty" class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <p class="text-gray-500 text-lg">暂无内容</p>
                        <p class="text-gray-400 text-sm mt-1">该项目没有填写内容</p>
                    </div>
                    
                    <div v-else class="bg-gray-50 rounded-xl p-4">
                        <textarea readonly
                                  :value="content"
                                  :rows="contentLines"
                                  class="w-full bg-transparent border-none resize-none focus:outline-none text-gray-800 leading-relaxed"
                                  style="font-family: inherit;"></textarea>
                    </div>
                </div>
                
                <!-- 底部 -->
                <div class="flex justify-end p-6 border-t border-gray-200 bg-gray-50 rounded-b-2xl">
                    <button @click="close"
                            class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    `
};
