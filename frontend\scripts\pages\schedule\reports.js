/**
 * 数据分析页面逻辑
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const selectedPeriod = ref('month');
        const reportData = ref({
            completionRate: 85.6,
            avgExecutionTime: 7.2,
            equipmentUtilization: 78.9,
            personnelEfficiency: 82.3,
            statusDistribution: [
                { status: 'completed', label: '已完成', count: 45, percentage: 56.3 },
                { status: 'in_progress', label: '执行中', count: 18, percentage: 22.5 },
                { status: 'planned', label: '计划中', count: 12, percentage: 15.0 },
                { status: 'cancelled', label: '已取消', count: 5, percentage: 6.2 }
            ],
            dailyTrend: [
                { date: '12-20', completed: 8, percentage: 80 },
                { date: '12-21', completed: 12, percentage: 100 },
                { date: '12-22', completed: 6, percentage: 60 },
                { date: '12-23', completed: 15, percentage: 100 },
                { date: '12-24', completed: 9, percentage: 75 },
                { date: '12-25', completed: 11, percentage: 92 },
                { date: '12-26', completed: 7, percentage: 58 }
            ],
            scheduleDetails: [
                { id: 1, title: '产品A生产排程', plannedHours: 8, actualHours: 7.5, efficiency: 106.7, status: 'completed' },
                { id: 2, title: '产品B生产排程', plannedHours: 10, actualHours: 12, efficiency: 83.3, status: 'completed' },
                { id: 3, title: '产品C生产排程', plannedHours: 6, actualHours: 5.8, efficiency: 103.4, status: 'completed' },
                { id: 4, title: '产品D生产排程', plannedHours: 8, actualHours: 0, efficiency: 0, status: 'in_progress' },
                { id: 5, title: '产品E生产排程', plannedHours: 12, actualHours: 0, efficiency: 0, status: 'planned' }
            ]
        });

        // 初始化
        onMounted(async () => {
            try {
                await checkAuth();
                loadReportData();
            } finally {
                // 确保加载指示器被隐藏
                hideLoading();
            }
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
                throw error; // 重新抛出错误以便onMounted能够捕获
            }
        }

        // 加载报表数据
        function loadReportData() {
            // 这里应该根据selectedPeriod.value从API加载真实数据
            // 目前使用模拟数据
            console.log('加载报表数据，时间段:', selectedPeriod.value);

            // 模拟不同时间段的数据变化
            switch (selectedPeriod.value) {
                case 'week':
                    reportData.value.completionRate = 88.2;
                    reportData.value.avgExecutionTime = 6.8;
                    break;
                case 'month':
                    reportData.value.completionRate = 85.6;
                    reportData.value.avgExecutionTime = 7.2;
                    break;
                case 'quarter':
                    reportData.value.completionRate = 82.4;
                    reportData.value.avgExecutionTime = 7.8;
                    break;
                case 'year':
                    reportData.value.completionRate = 79.1;
                    reportData.value.avgExecutionTime = 8.1;
                    break;
            }
        }

        // 刷新数据
        function refreshData() {
            loadReportData();
            if (window.showNotification) {
                window.showNotification('数据已刷新', 'success');
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusClasses = {
                completed: 'bg-green-100 text-green-800',
                in_progress: 'bg-blue-100 text-blue-800',
                planned: 'bg-yellow-100 text-yellow-800',
                cancelled: 'bg-red-100 text-red-800'
            };
            return statusClasses[status] || 'bg-gray-100 text-gray-800';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                completed: '已完成',
                in_progress: '执行中',
                planned: '计划中',
                cancelled: '已取消'
            };
            return statusMap[status] || status;
        }

        // 获取效率样式类
        function getEfficiencyClass(efficiency) {
            if (efficiency >= 100) {
                return 'text-green-600';
            } else if (efficiency >= 80) {
                return 'text-yellow-600';
            } else {
                return 'text-red-600';
            }
        }

        // 获取状态颜色类
        function getStatusColor(status) {
            const statusColors = {
                completed: 'bg-green-500',
                in_progress: 'bg-blue-500',
                planned: 'bg-yellow-500',
                cancelled: 'bg-red-500'
            };
            return statusColors[status] || 'bg-gray-500';
        }

        // 格式化百分比
        function formatPercentage(value) {
            return value.toFixed(1) + '%';
        }

        // 格式化小时数
        function formatHours(hours) {
            return hours.toFixed(1) + 'h';
        }

        return {
            currentUser,
            isAuthenticated,
            selectedPeriod,
            reportData,
            loadReportData,
            refreshData,
            getStatusClass,
            getStatusText,
            getStatusColor,
            getEfficiencyClass,
            formatPercentage,
            formatHours
        };
    }
}).mount('#app');
