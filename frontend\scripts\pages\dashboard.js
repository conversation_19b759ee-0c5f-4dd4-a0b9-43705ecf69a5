/**
 * 主页页面逻辑
 */

import { createStandardApp } from '../common/pageInit.js';
import { logUserInfo } from '../common/debugUtils.js';
import DashboardAPI from '../api/dashboard.js';

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

// 创建页面应用
createStandardApp({
    onUserLoaded: async (user) => {
        // 使用安全的调试工具
        logUserInfo('主页初始化完成', user);
    },
    setup() {
        const { ref, onMounted, computed } = Vue;

        // 响应式数据
        const stats = ref({});
        const activities = ref([]);
        const notifications = ref([]);
        const pendingTasks = ref([]);
        const quickActions = ref([]);
        const loading = ref(true);
        const refreshing = ref(false);

        // 计算属性
        const systemHealthColor = computed(() => {
            const memoryUsage = stats.value.system?.memoryUsage || 0;
            if (memoryUsage > 80) return 'text-red-600';
            if (memoryUsage > 60) return 'text-yellow-600';
            return 'text-green-600';
        });

        const systemHealthText = computed(() => {
            const memoryUsage = stats.value.system?.memoryUsage || 0;
            if (memoryUsage > 80) return '系统负载高';
            if (memoryUsage > 60) return '系统负载中';
            return '系统正常';
        });

        // 初始化
        onMounted(async () => {
            await loadDashboardData();
            hideLoading();

            // 设置定时刷新
            setInterval(refreshData, 30000); // 30秒刷新一次
        });

        // 加载主页数据
        async function loadDashboardData() {
            try {
                loading.value = true;

                // 并行加载所有数据
                const [statsRes, activitiesRes, notificationsRes, tasksRes, actionsRes] = await Promise.all([
                    DashboardAPI.getStats(),
                    DashboardAPI.getActivities(8),
                    DashboardAPI.getNotifications(5),
                    DashboardAPI.getPendingTasks(),
                    DashboardAPI.getQuickActions()
                ]);

                if (statsRes.success) {
                    stats.value = statsRes.data;
                }

                if (activitiesRes.success) {
                    activities.value = activitiesRes.data;
                }

                if (notificationsRes.success) {
                    notifications.value = notificationsRes.data;
                }

                if (tasksRes.success) {
                    pendingTasks.value = tasksRes.data;
                }

                if (actionsRes.success) {
                    quickActions.value = actionsRes.data;
                }

            } catch (error) {
                console.error('加载主页数据失败:', error);
                if (window.showNotification) {
                    window.showNotification('加载数据失败，请刷新页面重试', 'error');
                }
            } finally {
                loading.value = false;
            }
        }

        // 刷新数据
        async function refreshData() {
            if (refreshing.value) return;

            try {
                refreshing.value = true;
                const statsRes = await DashboardAPI.getStats();
                if (statsRes.success) {
                    stats.value = statsRes.data;
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
            } finally {
                refreshing.value = false;
            }
        }

        // 手动刷新
        async function handleRefresh() {
            await loadDashboardData();
            if (window.showNotification) {
                window.showNotification('数据已刷新', 'success');
            }
        }

        // 工具函数
        function formatDate(date) {
            return new Intl.DateTimeFormat('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            }).format(date);
        }

        // 根据当前时间获取问候语
        function getGreeting() {
            const hour = new Date().getHours();

            if (hour >= 5 && hour < 12) {
                return '早上好';
            } else if (hour >= 12 && hour < 18) {
                return '下午好';
            } else if (hour >= 18 && hour < 22) {
                return '晚上好';
            } else {
                return '夜深了';
            }
        }

        function formatTime(timeString) {
            const time = new Date(timeString);
            const now = new Date();
            const diff = now - time;

            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
            if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
            return `${Math.floor(diff / 86400000)}天前`;
        }

        function hasPermission(permission) {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            return user.permissions && user.permissions.includes(permission);
        }

        function getPriorityColor(priority) {
            switch (priority) {
                case 'high': return 'bg-red-500';
                case 'medium': return 'bg-yellow-500';
                case 'low': return 'bg-green-500';
                default: return 'bg-gray-500';
            }
        }

        function getActivityIconClass(type) {
            switch (type) {
                case 'application': return 'bg-blue-100 text-blue-600';
                case 'quality': return 'bg-purple-100 text-purple-600';
                case 'equipment': return 'bg-green-100 text-green-600';
                case 'schedule': return 'bg-orange-100 text-orange-600';
                default: return 'bg-gray-100 text-gray-600';
            }
        }

        function getActivityIcon(type) {
            const icons = {
                application: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
                quality: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
                equipment: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>',
                schedule: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h8a2 2 0 012 2v4m0 0V7a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h8z"></path>'
            };
            return icons[type] || icons.application;
        }

        function getActionIcon(iconName) {
            const icons = {
                plus: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>',
                cog: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>',
                document: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
                calendar: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h8a2 2 0 012 2v4m0 0V7a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h8z"></path>',
                lightning: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>',
                users: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>'
            };
            return icons[iconName] || icons.plus;
        }

        function getStatusClass(status) {
            switch (status) {
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'approved': return 'bg-green-100 text-green-800';
                case 'rejected': return 'bg-red-100 text-red-800';
                case 'completed': return 'bg-blue-100 text-blue-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusText(status) {
            const statusMap = {
                pending: '待审核',
                approved: '已通过',
                rejected: '已拒绝',
                completed: '已完成',
                in_progress: '进行中',
                planned: '已计划'
            };
            return statusMap[status] || status;
        }

        return {
            stats,
            activities,
            notifications,
            pendingTasks,
            quickActions,
            loading,
            refreshing,
            systemHealthColor,
            systemHealthText,
            formatDate,
            formatTime,
            getGreeting,
            hasPermission,
            getPriorityColor,
            getActivityIconClass,
            getActivityIcon,
            getActionIcon,
            getStatusClass,
            getStatusText,
            handleRefresh
        };
    }
}).mount('#app');
