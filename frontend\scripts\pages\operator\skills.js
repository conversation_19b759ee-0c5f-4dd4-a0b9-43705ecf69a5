/**
 * 操作员技能管理页面
 * 操作员技能评估和管理功能
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import CapacityAPI from '../../api/capacity.js';
import EquipmentAPI from '../../api/equipment.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const sidebarOpen = ref(false);
        const loading = ref(false);
        const submitting = ref(false);
        const operatorOptions = ref([]);
        const equipmentOptions = ref([]);
        const selectedOperatorId = ref('');
        const selectedEquipmentId = ref('');
        const skills = ref([]);
        const showSkillModal = ref(false);
        const editingSkill = ref(null);

        // 表单数据
        const skillForm = ref({
            operatorId: '',
            equipmentId: '',
            skillLevel: 1,
            efficiencyFactor: 1.0,
            certificationDate: ''
        });

        // 初始化
        onMounted(async () => {
            try {
                // 获取当前用户信息
                const user = await getCurrentUser();
                if (user) {
                    currentUser.value = user;
                    isAuthenticated.value = true;
                    await loadOperatorOptions();
                    await loadEquipmentOptions();
                } else {
                    // 未登录，跳转到登录页
                    window.location.href = '/login';
                    return;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                window.location.href = '/login';
                return;
            } finally {
                hideLoading();
            }
        });

        // 加载操作员选项
        async function loadOperatorOptions() {
            try {
                // 这里需要调用用户API获取操作员列表
                // 暂时使用模拟数据
                operatorOptions.value = [
                    { id: 'user1', name: '张三', username: 'zhangsan' },
                    { id: 'user2', name: '李四', username: 'lisi' },
                    { id: 'user3', name: '王五', username: 'wangwu' },
                    { id: 'user4', name: '赵六', username: 'zhaoliu' },
                    { id: 'user5', name: '钱七', username: 'qianqi' }
                ];
            } catch (error) {
                console.error('加载操作员选项失败:', error);
                window.showNotification('加载操作员选项失败', 'error');
                // 保持模拟数据，避免页面崩溃
            }
        }

        // 加载设备选项
        async function loadEquipmentOptions() {
            try {
                const response = await EquipmentAPI.getEquipment({ limit: 1000 });
                if (response.success) {
                    equipmentOptions.value = response.data.equipment.filter(eq => eq.status === 'active');
                } else {
                    console.error('获取设备列表失败:', response.message);
                    window.showNotification('获取设备列表失败', 'error');
                }
            } catch (error) {
                console.error('加载设备选项失败:', error);
                window.showNotification('加载设备选项失败', 'error');
                // 使用空数组避免页面崩溃
                equipmentOptions.value = [];
            }
        }

        // 加载操作员技能
        async function loadOperatorSkills() {
            if (!selectedOperatorId.value) {
                skills.value = [];
                return;
            }

            loading.value = true;
            try {
                const response = await CapacityAPI.getOperatorSkills(selectedOperatorId.value);
                if (response.success) {
                    let skillList = response.data;
                    
                    // 如果选择了特定设备，进行筛选
                    if (selectedEquipmentId.value) {
                        skillList = skillList.filter(skill => skill.equipmentId === selectedEquipmentId.value);
                    }
                    
                    skills.value = skillList;
                } else {
                    console.error('获取操作员技能失败:', response.message);
                    window.showNotification('获取操作员技能失败', 'error');
                    skills.value = [];
                }
            } catch (error) {
                console.error('加载操作员技能失败:', error);
                window.showNotification('加载操作员技能失败', 'error');
                skills.value = [];
            } finally {
                loading.value = false;
            }
        }

        // 提交技能表单
        async function submitSkillForm() {
            if (submitting.value) return;

            // 基本验证
            if (!skillForm.value.operatorId) {
                window.showNotification('请选择操作员', 'error');
                return;
            }

            if (!skillForm.value.equipmentId) {
                window.showNotification('请选择设备', 'error');
                return;
            }

            submitting.value = true;

            try {
                let response;
                if (editingSkill.value) {
                    // 更新技能记录
                    response = await CapacityAPI.updateOperatorSkill(editingSkill.value.id, skillForm.value);
                } else {
                    // 创建新技能记录
                    response = await CapacityAPI.createOperatorSkill(skillForm.value);
                }

                if (response.success) {
                    window.showNotification(
                        editingSkill.value ? '技能记录更新成功' : '技能记录创建成功', 
                        'success'
                    );
                    closeSkillModal();
                    await loadOperatorSkills();
                } else {
                    window.showNotification(response.message || '操作失败', 'error');
                }
            } catch (error) {
                console.error('保存技能记录失败:', error);
                window.showNotification('保存技能记录失败', 'error');
            } finally {
                submitting.value = false;
            }
        }

        // 编辑技能记录
        function editSkill(skill) {
            editingSkill.value = skill;
            skillForm.value = {
                operatorId: skill.operatorId,
                equipmentId: skill.equipmentId,
                skillLevel: skill.skillLevel,
                efficiencyFactor: skill.efficiencyFactor,
                certificationDate: skill.certificationDate || ''
            };
            showSkillModal.value = true;
        }

        // 删除技能记录
        async function deleteSkill(skill) {
            if (!confirm(`确定要删除操作员"${skill.operatorName}"在设备"${skill.equipmentName}"上的技能记录吗？`)) {
                return;
            }

            try {
                const response = await CapacityAPI.deleteOperatorSkill(skill.id);
                if (response.success) {
                    window.showNotification('技能记录删除成功', 'success');
                    await loadOperatorSkills();
                } else {
                    window.showNotification(response.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除技能记录失败:', error);
                window.showNotification('删除技能记录失败', 'error');
            }
        }

        // 关闭技能模态框
        function closeSkillModal() {
            showSkillModal.value = false;
            editingSkill.value = null;
            skillForm.value = {
                operatorId: '',
                equipmentId: '',
                skillLevel: 1,
                efficiencyFactor: 1.0,
                certificationDate: ''
            };
        }



        // 刷新数据
        async function refreshData() {
            await loadOperatorOptions();
            await loadEquipmentOptions();
            if (selectedOperatorId.value) {
                await loadOperatorSkills();
            }
        }

        // 获取技能等级样式
        function getSkillLevelClass(level) {
            const classMap = {
                1: 'skill-level-1',
                2: 'skill-level-2',
                3: 'skill-level-3',
                4: 'skill-level-4',
                5: 'skill-level-5'
            };
            return classMap[level] || 'skill-level-1';
        }

        // 获取技能等级文本
        function getSkillLevelText(level) {
            const textMap = {
                1: '初级',
                2: '熟练',
                3: '精通',
                4: '专家',
                5: '大师'
            };
            return textMap[level] || '未知';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }

        // 侧边栏控制
        function toggleSidebar() {
            sidebarOpen.value = !sidebarOpen.value;
        }

        function closeSidebar() {
            sidebarOpen.value = false;
        }

        return {
            currentUser,
            isAuthenticated,
            sidebarOpen,
            loading,
            submitting,
            operatorOptions,
            equipmentOptions,
            selectedOperatorId,
            selectedEquipmentId,
            skills,
            showSkillModal,
            editingSkill,
            skillForm,
            loadOperatorSkills,
            submitSkillForm,
            editSkill,
            deleteSkill,
            closeSkillModal,
            refreshData,
            getSkillLevelClass,
            getSkillLevelText,
            formatDate,
            toggleSidebar,
            closeSidebar
        };
    }
}).mount('#app');
