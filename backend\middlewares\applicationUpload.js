/**
 * 申请文件上传中间件
 * 处理申请系统的文件上传配置
 * 目录结构：backend/uploads/applications/YYYY/MM/DD/用户ID_文件名
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const logger = require('../utils/logger');

/**
 * 创建申请文件存储目录
 * 目录结构：backend/uploads/applications/YYYY/MM/DD/
 * @param {string} userId - 用户ID
 */
function createApplicationDirectory(userId = 'default') {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    const uploadDir = path.join(
        process.cwd(),
        'backend',
        'uploads',
        'applications',
        year.toString(),
        month,
        day
    );

    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
        logger.info(`创建申请文件目录: ${uploadDir}`);
    }

    return uploadDir;
}

/**
 * 生成唯一文件名，避免重复
 * @param {string} uploadDir - 上传目录
 * @param {string} originalName - 原始文件名
 * @param {string} userId - 用户ID
 * @returns {string} 唯一文件名
 */
function generateUniqueFilename(uploadDir, originalName, userId) {
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext);
    
    // 清理文件名，移除不安全的字符
    const safeBaseName = baseName
        .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows不允许的字符
        .replace(/\s+/g, '_')           // 替换空格为下划线
        .trim();
    
    // 使用用户ID作为前缀
    let filename = `${userId}_${safeBaseName}${ext}`;
    let counter = 1;
    
    // 如果文件已存在，添加数字后缀
    while (fs.existsSync(path.join(uploadDir, filename))) {
        filename = `${userId}_${safeBaseName}_${counter}${ext}`;
        counter++;
    }
    
    return filename;
}

// 配置申请文件存储
const applicationStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        try {
            // 从请求中获取用户ID
            const userId = req.user ? req.user.id : 'anonymous';
            const uploadDir = createApplicationDirectory(userId);

            // 将目录信息存储到请求对象中，供后续使用
            req.uploadDir = uploadDir;
            req.uploadUserId = userId;

            cb(null, uploadDir);
        } catch (error) {
            logger.error('创建申请文件上传目录失败:', error);
            cb(error);
        }
    },
    filename: function (req, file, cb) {
        try {
            const userId = req.uploadUserId || (req.user ? req.user.id : 'anonymous');
            const uploadDir = req.uploadDir || createApplicationDirectory(userId);
            
            // 生成唯一文件名（保持原始名称，添加用户ID前缀）
            const uniqueFilename = generateUniqueFilename(uploadDir, file.originalname, userId);
            cb(null, uniqueFilename);
        } catch (error) {
            logger.error('生成申请文件名失败:', error);
            cb(error);
        }
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = config.upload.allowedTypes;
    
    if (allowedTypes.test(file.mimetype) || allowedTypes.test(path.extname(file.originalname).toLowerCase())) {
        cb(null, true);
    } else {
        const error = new Error('不支持的文件类型');
        error.code = 'INVALID_FILE_TYPE';
        cb(error, false);
    }
};

// 创建申请文件上传中间件
const applicationUpload = multer({
    storage: applicationStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: config.upload.maxFileSize, // 10MB
        files: 10 // 最多10个文件
    },
    // 确保正确处理中文文件名
    preservePath: false,
    // 设置字段名编码
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB for field data
});

// 错误处理中间件
function handleUploadError(error, req, res, next) {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: '文件大小超过限制（最大10MB）'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                message: '文件数量超过限制（最多10个文件）'
            });
        }
        if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
                success: false,
                message: '意外的文件字段'
            });
        }
    }
    
    if (error && error.code === 'INVALID_FILE_TYPE') {
        return res.status(400).json({
            success: false,
            message: error.message
        });
    }
    
    logger.error('申请文件上传错误:', error);
    return res.status(500).json({
        success: false,
        message: '文件上传失败'
    });
}

module.exports = {
    applicationUpload,
    handleUploadError,
    createApplicationDirectory,
    generateUniqueFilename
};
