/**
 * 前端性能监控组件
 * 监控页面性能、API请求和用户体验指标
 */

import { PerformanceMonitor, MemoryMonitor } from '../../scripts/common/utils.js';
import { cachedGet } from '../../scripts/api/config.js';

export default {
    name: 'PerformanceMonitor',
    template: `
        <div v-if="showMonitor" class="fixed bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 max-w-sm z-50 border">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-gray-800">性能监控</h3>
                <button @click="toggleMonitor" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-2 text-xs">
                <!-- 页面性能 -->
                <div class="flex justify-between">
                    <span class="text-gray-600">页面加载:</span>
                    <span :class="getPerformanceClass(pageLoadTime)">{{ pageLoadTime }}ms</span>
                </div>
                
                <!-- 内存使用 -->
                <div class="flex justify-between" v-if="memoryUsage.supported">
                    <span class="text-gray-600">内存使用:</span>
                    <span :class="getMemoryClass(memoryUsage.usedMB)">{{ memoryUsage.usedMB }}MB</span>
                </div>
                
                <!-- API性能 -->
                <div class="flex justify-between">
                    <span class="text-gray-600">API平均:</span>
                    <span :class="getPerformanceClass(avgApiTime)">{{ avgApiTime }}ms</span>
                </div>
                
                <!-- 缓存命中率 -->
                <div class="flex justify-between">
                    <span class="text-gray-600">缓存命中:</span>
                    <span :class="getCacheClass(cacheHitRate)">{{ cacheHitRate }}%</span>
                </div>
                
                <!-- 错误计数 -->
                <div class="flex justify-between" v-if="errorCount > 0">
                    <span class="text-gray-600">错误数:</span>
                    <span class="text-red-600">{{ errorCount }}</span>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2 mt-3">
                    <button @click="clearCache" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                        清理缓存
                    </button>
                    <button @click="exportData" class="text-xs bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600">
                        导出数据
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 切换按钮 -->
        <button v-if="!showMonitor" @click="toggleMonitor" 
                class="fixed bottom-4 right-4 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 z-50">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
        </button>
    `,
    
    data() {
        return {
            showMonitor: false,
            pageLoadTime: 0,
            memoryUsage: { supported: false },
            avgApiTime: 0,
            cacheHitRate: 0,
            errorCount: 0,
            apiRequests: [],
            updateInterval: null
        };
    },
    
    mounted() {
        this.initializeMonitoring();
        this.startPerformanceTracking();
    },
    
    beforeUnmount() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    },
    
    methods: {
        /**
         * 初始化性能监控
         */
        initializeMonitoring() {
            // 监控页面加载性能
            this.measurePageLoadTime();
            
            // 监控API请求
            this.interceptAPIRequests();
            
            // 监控错误
            this.monitorErrors();
            
            // 定期更新数据
            this.updateInterval = setInterval(() => {
                this.updateMetrics();
            }, 2000);
        },
        
        /**
         * 测量页面加载时间
         */
        measurePageLoadTime() {
            if (performance.timing) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                this.pageLoadTime = loadTime;
            } else if (performance.getEntriesByType) {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation) {
                    this.pageLoadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart);
                }
            }
        },
        
        /**
         * 拦截API请求进行性能监控
         */
        interceptAPIRequests() {
            // 监控axios请求
            if (window.axios && window.axios.interceptors) {
                window.axios.interceptors.response.use(
                    (response) => {
                        this.recordAPIRequest(response);
                        return response;
                    },
                    (error) => {
                        this.recordAPIError(error);
                        return Promise.reject(error);
                    }
                );
            }
        },
        
        /**
         * 记录API请求性能
         */
        recordAPIRequest(response) {
            const duration = response.duration || 0;
            const fromCache = response.headers && response.headers['x-cache'] === 'HIT';
            
            this.apiRequests.push({
                url: response.config.url,
                duration,
                fromCache,
                timestamp: Date.now()
            });
            
            // 只保留最近50个请求
            if (this.apiRequests.length > 50) {
                this.apiRequests = this.apiRequests.slice(-50);
            }
        },
        
        /**
         * 记录API错误
         */
        recordAPIError(error) {
            this.errorCount++;
            console.warn('API请求错误:', error);
        },
        
        /**
         * 监控JavaScript错误
         */
        monitorErrors() {
            window.addEventListener('error', (event) => {
                this.errorCount++;
                console.error('JavaScript错误:', event.error);
            });
            
            window.addEventListener('unhandledrejection', (event) => {
                this.errorCount++;
                console.error('未处理的Promise拒绝:', event.reason);
            });
        },
        
        /**
         * 更新性能指标
         */
        updateMetrics() {
            // 更新内存使用情况
            this.memoryUsage = MemoryMonitor.getUsage();
            
            // 计算平均API响应时间
            if (this.apiRequests.length > 0) {
                const totalTime = this.apiRequests.reduce((sum, req) => sum + req.duration, 0);
                this.avgApiTime = Math.round(totalTime / this.apiRequests.length);
            }
            
            // 计算缓存命中率
            if (this.apiRequests.length > 0) {
                const cacheHits = this.apiRequests.filter(req => req.fromCache).length;
                this.cacheHitRate = Math.round((cacheHits / this.apiRequests.length) * 100);
            }
            
            // 检查内存使用情况
            MemoryMonitor.suggestGC();
        },
        
        /**
         * 开始性能跟踪
         */
        startPerformanceTracking() {
            // 监控长任务
            if ('PerformanceObserver' in window) {
                try {
                    const observer = new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            if (entry.duration > 50) { // 超过50ms的长任务
                                console.warn(`长任务检测: ${entry.name} - ${entry.duration.toFixed(2)}ms`);
                            }
                        }
                    });
                    observer.observe({ entryTypes: ['longtask'] });
                } catch (e) {
                    // 某些浏览器可能不支持longtask
                }
            }
        },
        
        /**
         * 切换监控面板显示
         */
        toggleMonitor() {
            this.showMonitor = !this.showMonitor;
        },
        
        /**
         * 清理缓存
         */
        async clearCache() {
            try {
                // 清理API缓存
                if (window.apiCache) {
                    window.apiCache.clear();
                }
                
                // 清理浏览器缓存（如果支持）
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                }
                
                alert('缓存已清理');
            } catch (error) {
                console.error('清理缓存失败:', error);
                alert('清理缓存失败');
            }
        },
        
        /**
         * 导出性能数据
         */
        exportData() {
            const data = {
                timestamp: new Date().toISOString(),
                pageLoadTime: this.pageLoadTime,
                memoryUsage: this.memoryUsage,
                avgApiTime: this.avgApiTime,
                cacheHitRate: this.cacheHitRate,
                errorCount: this.errorCount,
                apiRequests: this.apiRequests,
                performanceStats: PerformanceMonitor.getStats()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance-data-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        },
        
        /**
         * 获取性能指标的CSS类
         */
        getPerformanceClass(time) {
            if (time < 100) return 'text-green-600';
            if (time < 500) return 'text-yellow-600';
            return 'text-red-600';
        },
        
        /**
         * 获取内存使用的CSS类
         */
        getMemoryClass(usage) {
            if (usage < 50) return 'text-green-600';
            if (usage < 100) return 'text-yellow-600';
            return 'text-red-600';
        },
        
        /**
         * 获取缓存命中率的CSS类
         */
        getCacheClass(rate) {
            if (rate > 80) return 'text-green-600';
            if (rate > 50) return 'text-yellow-600';
            return 'text-red-600';
        }
    }
};
