/**
 * 排程计划页面逻辑
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ScheduleAPI from '../../api/schedule.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const schedules = ref([]);
        const loading = ref(false);
        const filters = ref({
            status: '',
            priority: '',
            startDate: '',
            endDate: ''
        });
        const pagination = ref({
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0
        });

        // 初始化
        onMounted(async () => {
            try {
                await checkAuth();
            } finally {
                // 确保加载指示器被隐藏
                hideLoading();
            }
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
                await loadSchedules();
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
                throw error; // 重新抛出错误以便onMounted能够捕获
            }
        }

        // 加载排程列表
        async function loadSchedules() {
            loading.value = true;
            try {
                const params = {
                    page: pagination.value.page,
                    limit: pagination.value.limit,
                    ...filters.value
                };

                // 移除空值参数
                Object.keys(params).forEach(key => {
                    if (params[key] === '' || params[key] === null || params[key] === undefined) {
                        delete params[key];
                    }
                });

                const response = await ScheduleAPI.getSchedules(params);

                if (response.success) {
                    schedules.value = response.data.schedules;
                    pagination.value.total = response.data.total;
                    pagination.value.totalPages = Math.ceil(response.data.total / pagination.value.limit);
                } else {
                    console.error('获取排程列表失败:', response.message);
                    window.showNotification('获取排程列表失败', 'error');
                }
            } catch (error) {
                console.error('加载排程列表失败:', error);
                window.showNotification('加载排程列表失败', 'error');
            } finally {
                loading.value = false;
            }
        }

        // 刷新数据
        async function refreshData() {
            await loadSchedules();
            window.showNotification('数据已刷新', 'success');
        }

        // 切换页面
        async function changePage(page) {
            if (page >= 1 && page <= pagination.value.totalPages) {
                pagination.value.page = page;
                await loadSchedules();
            }
        }

        // 查看排程
        function viewSchedule(scheduleId) {
            window.location.href = `/schedule/edit/${scheduleId}`;
        }

        // 编辑排程
        function editSchedule(scheduleId) {
            window.location.href = `/schedule/edit/${scheduleId}`;
        }

        // 删除排程
        async function deleteSchedule(scheduleId) {
            if (!confirm('确定要删除这个排程吗？')) {
                return;
            }

            try {
                const response = await ScheduleAPI.deleteSchedule(scheduleId);
                if (response.success) {
                    window.showNotification('排程删除成功', 'success');
                    await loadSchedules();
                } else {
                    window.showNotification('删除失败: ' + response.message, 'error');
                }
            } catch (error) {
                console.error('删除排程失败:', error);
                window.showNotification('删除排程失败', 'error');
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusClasses = {
                planned: 'status-planned',
                in_progress: 'status-in_progress',
                paused: 'status-paused',
                completed: 'status-completed',
                cancelled: 'status-cancelled'
            };
            return statusClasses[status] || 'status-planned';
        }

        // 获取优先级样式类
        function getPriorityClass(priority) {
            const priorityClasses = {
                low: 'priority-low',
                medium: 'priority-medium',
                high: 'priority-high',
                urgent: 'priority-urgent'
            };
            return priorityClasses[priority] || 'priority-medium';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                planned: '计划中',
                in_progress: '执行中',
                paused: '已暂停',
                completed: '已完成',
                cancelled: '已取消'
            };
            return statusMap[status] || status;
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityMap = {
                low: '低',
                medium: '中',
                high: '高',
                urgent: '紧急'
            };
            return priorityMap[priority] || priority;
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }

        // 格式化日期时间（兼容性函数）
        function formatDateTime(dateTimeString) {
            return formatDate(dateTimeString);
        }

        return {
            currentUser,
            isAuthenticated,
            schedules,
            loading,
            filters,
            pagination,
            loadSchedules,
            refreshData,
            changePage,
            viewSchedule,
            editSchedule,
            deleteSchedule,
            getStatusClass,
            getPriorityClass,
            getStatusText,
            getPriorityText,
            formatDate,
            formatDateTime
        };
    }
}).mount('#app');
