/**
 * 设备产能控制器
 * 处理设备产能相关的HTTP请求
 */

const CapacityService = require('../services/capacityService');
const logger = require('../utils/logger');

class CapacityController {
    constructor() {
        this.capacityService = new CapacityService();
    }

    /**
     * 获取设备的产能配置
     * GET /api/equipment/:equipmentId/capabilities
     */
    async getEquipmentCapabilities(req, res) {
        try {
            const { equipmentId } = req.params;

            const result = await this.capacityService.getEquipmentCapabilities(equipmentId);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取设备产能配置请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 创建设备产能记录
     * POST /api/equipment/capabilities
     */
    async createEquipmentCapability(req, res) {
        try {
            const capabilityData = req.body;
            const userId = req.user.id;

            // 基础数据验证
            if (!capabilityData.equipmentId || !capabilityData.productId || !capabilityData.capacityPerHour) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必要的产能配置信息'
                });
            }

            const result = await this.capacityService.createEquipmentCapability(capabilityData, userId);

            if (result.success) {
                res.status(201).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('创建设备产能记录请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 更新设备产能
     * PUT /api/equipment/capabilities/:id
     */
    async updateEquipmentCapability(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const userId = req.user.id;

            const result = await this.capacityService.updateEquipmentCapability(id, updateData, userId);

            if (result.success) {
                res.json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '设备产能记录不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('更新设备产能请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取产品的可用设备
     * GET /api/products/:productId/equipment
     */
    async getAvailableEquipmentForProduct(req, res) {
        try {
            const { productId } = req.params;

            const result = await this.capacityService.getAvailableEquipmentForProduct(productId);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取产品可用设备请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取设备的操作员技能
     * GET /api/equipment/:equipmentId/operator-skills
     */
    async getEquipmentOperatorSkills(req, res) {
        try {
            const { equipmentId } = req.params;

            const result = await this.capacityService.getEquipmentOperatorSkills(equipmentId);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取设备操作员技能请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 创建操作员技能记录
     * POST /api/operator-skills
     */
    async createOperatorSkill(req, res) {
        try {
            const skillData = req.body;
            const userId = req.user.id;

            // 基础数据验证
            if (!skillData.operatorId || !skillData.equipmentId || !skillData.skillLevel) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必要的技能信息'
                });
            }

            const result = await this.capacityService.createOperatorSkill(skillData, userId);

            if (result.success) {
                res.status(201).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('创建操作员技能记录请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取操作员的技能列表
     * GET /api/operators/:operatorId/skills
     */
    async getOperatorSkills(req, res) {
        try {
            const { operatorId } = req.params;

            const result = await this.capacityService.getOperatorSkills(operatorId);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取操作员技能列表请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取设备的操作员列表
     * GET /api/equipment/:equipmentId/operators
     */
    async getEquipmentOperators(req, res) {
        try {
            const { equipmentId } = req.params;

            const result = await this.capacityService.getEquipmentOperators(equipmentId);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取设备操作员列表请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 创建设备操作员关联
     * POST /api/equipment-operators
     */
    async createEquipmentOperator(req, res) {
        try {
            const relationData = req.body;
            const userId = req.user.id;

            // 基础数据验证
            if (!relationData.equipmentId || !relationData.operatorId) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必要的关联信息'
                });
            }

            const result = await this.capacityService.createEquipmentOperator(relationData, userId);

            if (result.success) {
                res.status(201).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('创建设备操作员关联请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }
}

module.exports = CapacityController;
