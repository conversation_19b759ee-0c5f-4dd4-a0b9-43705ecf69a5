/**
 * 用户管理API
 * 处理用户相关的API请求
 */

import { API_URL, getAuthHeaders, apiCache } from './config.js';

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.role - 角色过滤
 * @param {string} params.department - 部门过滤
 * @param {string} params.status - 状态过滤
 * @returns {Promise<Object>} 用户列表和分页信息
 */
export async function getUsers(params = {}) {
    try {
        // 添加时间戳防止缓存
        const queryParams = {
            ...params,
            _t: Date.now()
        };

        const response = await axios.get(`${API_URL}/users`, {
            params: queryParams,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取用户列表失败:', error);
        throw error;
    }
}

/**
 * 获取厂长用户列表
 * @returns {Promise<Object>} 厂长用户列表
 */
export async function getFactoryManagers() {
    try {
        const response = await axios.get(`${API_URL}/users/factory-managers`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取厂长用户列表失败:', error);
        throw error;
    }
}

/**
 * 获取经理用户列表
 * @returns {Promise<Object>} 经理用户列表
 */
export async function getManagers() {
    try {
        const response = await axios.get(`${API_URL}/users/managers`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取经理用户列表失败:', error);
        throw error;
    }
}

/**
 * 创建新用户
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} 创建结果
 */
export async function createUser(userData) {
    try {
        const response = await axios.post(`${API_URL}/users`, userData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('创建用户失败:', error);
        throw error;
    }
}

/**
 * 更新用户
 * @param {string} id - 用户ID
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} 更新结果
 */
export async function updateUser(id, userData) {
    try {
        const response = await axios.put(`${API_URL}/users/${id}`, userData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('更新用户失败:', error);
        throw error;
    }
}

/**
 * 检查用户删除状态
 * @param {string} id - 用户ID
 * @returns {Promise<Object>} 检查结果
 */
export async function checkUserDeletionStatus(id) {
    try {
        const response = await axios.get(`${API_URL}/users/${id}/deletion-status`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('检查用户删除状态失败:', error);
        throw error;
    }
}

/**
 * 删除用户
 * @param {string} id - 用户ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteUser(id) {
    try {
        const response = await axios.delete(`${API_URL}/users/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除用户失败:', error);
        throw error;
    }
}

/**
 * 导出用户数据为CSV
 * @returns {Promise<void>} 导出结果
 */
export async function exportUsersToCSV() {
    try {
        const response = await axios.get(`${API_URL}/users/export`, {
            headers: getAuthHeaders(),
            responseType: 'blob'
        });

        // 创建下载链接，确保使用UTF-8编码
        const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'users.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('导出用户数据失败:', error);
        throw error;
    }
}

/**
 * 导入CSV用户数据
 * @param {File} file - CSV文件
 * @returns {Promise<Object>} 导入结果
 */
export async function importUsersFromCSV(file) {
    try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await axios.post(`${API_URL}/users/import`, formData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'multipart/form-data'
            }
        });

        return response.data;
    } catch (error) {
        console.error('导入用户数据失败:', error);
        throw error;
    }
}

/**
 * 上传用户电子签名
 * @param {string} id - 用户ID
 * @param {File} file - 签名文件
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadUserSignature(id, file) {
    try {
        const formData = new FormData();
        formData.append('signature', file);

        const response = await axios.post(`${API_URL}/users/${id}/signature`, formData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'multipart/form-data'
            }
        });

        return response.data;
    } catch (error) {
        console.error('上传电子签名失败:', error);
        throw error;
    }
}

/**
 * 上传当前用户电子签名
 * @param {File} file - 签名文件
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadSignature(file) {
    try {
        const formData = new FormData();
        formData.append('signature', file);

        const response = await axios.post(`${API_URL}/users/signature`, formData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'multipart/form-data'
            }
        });

        return response.data;
    } catch (error) {
        console.error('上传签名失败:', error);
        throw error;
    }
}

/**
 * 删除用户电子签名
 * @param {string} id - 用户ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteSignature(id) {
    try {
        const response = await axios.delete(`${API_URL}/users/${id}/signature`, {
            headers: getAuthHeaders()
        });

        return response.data;
    } catch (error) {
        console.error('删除电子签名失败:', error);
        throw error;
    }
}

/**
 * 获取用户电子签名信息
 * @param {string} id - 用户ID
 * @returns {Promise<Object>} 电子签名信息
 */
export async function getSignature(id) {
    try {
        const response = await axios.get(`${API_URL}/users/${id}/signature`, {
            headers: getAuthHeaders()
        });

        // 后端现在总是返回200状态码，并在响应中指明用户是否有签名
        return response.data;
    } catch (error) {
        // 保留对旧版API的兼容性
        if (error.response && error.response.status === 404) {
            return {
                success: true,
                hasSignature: false,
                message: '用户没有电子签名'
            };
        }
        console.error('获取电子签名失败:', error);
        throw error;
    }
}

/**
 * 更新用户设置
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} 更新结果
 */
export async function updateUserSettings(userData) {
    try {
        const response = await axios.put(`${API_URL}/users/settings`, userData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('更新用户设置失败:', error);
        throw error;
    }
}

/**
 * 修改密码
 * @param {string} currentPassword - 当前密码
 * @param {string} newPassword - 新密码
 * @returns {Promise<Object>} 修改结果
 */
export async function changePassword(currentPassword, newPassword) {
    try {
        const response = await axios.put(`${API_URL}/users/password`, {
            currentPassword,
            newPassword
        }, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('修改密码失败:', error);
        throw error;
    }
}

/**
 * 获取用户权限
 * @param {string} id - 用户ID
 * @returns {Promise<Object>} 用户权限
 */
export async function getUserPermissions(id, forceRefresh = false) {
    try {
        const config = {
            headers: getAuthHeaders()
        };

        // 如果需要强制刷新，添加时间戳参数避免缓存
        if (forceRefresh) {
            config.params = { _t: Date.now() };
        }

        const response = await axios.get(`${API_URL}/users/${id}/permissions`, config);
        return response.data;
    } catch (error) {
        console.error('获取用户权限失败:', error);
        throw error;
    }
}

/**
 * 更新用户权限
 * @param {string} id - 用户ID
 * @param {Array} permissions - 权限列表
 * @returns {Promise<Object>} 更新结果
 */
export async function updateUserPermissions(id, permissions) {
    try {
        const response = await axios.put(`${API_URL}/users/${id}/permissions`, {
            permissions
        }, {
            headers: getAuthHeaders()
        });

        // 清除相关的API缓存，确保下次获取最新数据
        const userPermissionsCacheKey = apiCache.generateKey(`${API_URL}/users/${id}/permissions`, {});
        const userDetailsCacheKey = apiCache.generateKey(`${API_URL}/users/${id}`, {});
        apiCache.delete(userPermissionsCacheKey);
        apiCache.delete(userDetailsCacheKey);

        return response.data;
    } catch (error) {
        console.error('更新用户权限失败:', error);
        throw error;
    }
}

// ==================== 权限模板相关API ====================

/**
 * 获取所有权限模板
 * @returns {Promise<Object>} 权限模板列表
 */
export async function getPermissionTemplates() {
    try {
        const response = await axios.get(`${API_URL}/permission-templates`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取权限模板列表失败:', error);
        throw error;
    }
}

/**
 * 根据ID获取权限模板
 * @param {string} id - 模板ID
 * @returns {Promise<Object>} 权限模板
 */
export async function getPermissionTemplateById(id) {
    try {
        const response = await axios.get(`${API_URL}/permission-templates/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取权限模板失败:', error);
        throw error;
    }
}

/**
 * 创建权限模板
 * @param {Object} templateData - 模板数据
 * @returns {Promise<Object>} 创建结果
 */
export async function createPermissionTemplate(templateData) {
    try {
        const response = await axios.post(`${API_URL}/permission-templates`, templateData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('创建权限模板失败:', error);
        throw error;
    }
}

/**
 * 更新权限模板
 * @param {string} id - 模板ID
 * @param {Object} templateData - 模板数据
 * @returns {Promise<Object>} 更新结果
 */
export async function updatePermissionTemplate(id, templateData) {
    try {
        const response = await axios.put(`${API_URL}/permission-templates/${id}`, templateData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('更新权限模板失败:', error);
        throw error;
    }
}

/**
 * 删除权限模板
 * @param {string} id - 模板ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deletePermissionTemplate(id) {
    try {
        const response = await axios.delete(`${API_URL}/permission-templates/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除权限模板失败:', error);
        throw error;
    }
}
