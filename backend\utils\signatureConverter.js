/**
 * 电子签名转换工具
 * 实现文件到Base64的转换和图片处理
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const logger = require('./logger');

/**
 * 验证Base64图片格式
 * @param {string} base64String - Base64字符串
 * @returns {boolean} 是否为有效的Base64图片
 */
function validateBase64Image(base64String) {
    if (!base64String || typeof base64String !== 'string') {
        return false;
    }

    // 检查Base64格式
    const base64Regex = /^data:image\/(jpeg|jpg|png|gif);base64,/;
    if (!base64Regex.test(base64String)) {
        return false;
    }

    // 检查Base64数据长度
    const base64Data = base64String.split(',')[1];
    if (!base64Data || base64Data.length < 100) {
        return false;
    }

    return true;
}

/**
 * 将图片文件转换为Base64
 * @param {string} filePath - 图片文件路径
 * @returns {Promise<string>} Base64字符串
 */
async function fileToBase64(filePath) {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            throw new Error('签名文件不存在');
        }

        // 读取文件
        const fileBuffer = fs.readFileSync(filePath);
        
        // 使用sharp进行图片处理和优化
        const processedBuffer = await sharp(fileBuffer)
            .resize(300, 150, { 
                fit: 'inside',
                withoutEnlargement: true 
            })
            .jpeg({ 
                quality: 90,
                progressive: true 
            })
            .toBuffer();

        // 转换为Base64
        const base64String = `data:image/jpeg;base64,${processedBuffer.toString('base64')}`;
        
        return base64String;
    } catch (error) {
        logger.error('文件转Base64失败:', error);
        throw error;
    }
}

/**
 * 将Base64字符串保存为文件
 * @param {string} base64String - Base64字符串
 * @param {string} outputPath - 输出文件路径
 * @returns {Promise<void>}
 */
async function base64ToFile(base64String, outputPath) {
    try {
        // 验证Base64格式
        if (!validateBase64Image(base64String)) {
            throw new Error('无效的Base64图片格式');
        }

        // 提取Base64数据
        const base64Data = base64String.split(',')[1];
        const buffer = Buffer.from(base64Data, 'base64');

        // 确保目录存在
        const dir = path.dirname(outputPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 保存文件
        fs.writeFileSync(outputPath, buffer);
        
        logger.info(`Base64转文件成功: ${outputPath}`);
    } catch (error) {
        logger.error('Base64转文件失败:', error);
        throw error;
    }
}

/**
 * 压缩和优化Base64图片
 * @param {string} base64String - 原始Base64字符串
 * @param {Object} options - 压缩选项
 * @returns {Promise<string>} 优化后的Base64字符串
 */
async function compressBase64Image(base64String, options = {}) {
    try {
        // 默认选项
        const defaultOptions = {
            maxWidth: 300,
            maxHeight: 150,
            quality: 90
        };
        const opts = { ...defaultOptions, ...options };

        // 验证输入
        if (!validateBase64Image(base64String)) {
            throw new Error('无效的Base64图片格式');
        }

        // 提取Base64数据并转换为Buffer
        const base64Data = base64String.split(',')[1];
        const inputBuffer = Buffer.from(base64Data, 'base64');

        // 使用sharp进行压缩和优化
        const processedBuffer = await sharp(inputBuffer)
            .resize(opts.maxWidth, opts.maxHeight, { 
                fit: 'inside',
                withoutEnlargement: true 
            })
            .jpeg({ 
                quality: opts.quality,
                progressive: true 
            })
            .toBuffer();

        // 转换回Base64
        const compressedBase64 = `data:image/jpeg;base64,${processedBuffer.toString('base64')}`;
        
        return compressedBase64;
    } catch (error) {
        logger.error('Base64图片压缩失败:', error);
        throw error;
    }
}

/**
 * 批量转换签名文件为Base64
 * @param {string} signaturesDir - 签名文件目录
 * @returns {Promise<Object>} 转换结果映射
 */
async function batchConvertSignatures(signaturesDir) {
    try {
        const results = {};
        
        if (!fs.existsSync(signaturesDir)) {
            logger.warn('签名目录不存在:', signaturesDir);
            return results;
        }

        const files = fs.readdirSync(signaturesDir);
        const imageFiles = files.filter(file => 
            /\.(png|jpg|jpeg|gif)$/i.test(file)
        );

        for (const file of imageFiles) {
            try {
                const filePath = path.join(signaturesDir, file);
                const base64 = await fileToBase64(filePath);
                
                // 从文件名提取用户代码
                const userCode = file.split('_')[0];
                results[userCode] = base64;
                
                logger.info(`转换签名成功: ${file} -> ${userCode}`);
            } catch (error) {
                logger.error(`转换签名失败: ${file}`, error);
            }
        }

        return results;
    } catch (error) {
        logger.error('批量转换签名失败:', error);
        throw error;
    }
}

/**
 * 获取签名的多重保障机制
 * @param {Object} user - 用户对象
 * @param {string} signaturesDir - 签名文件目录
 * @returns {Promise<string|null>} Base64签名或null
 */
async function getSignatureWithFallback(user, signaturesDir) {
    try {
        // 1. 优先使用用户数据中的Base64签名
        if (user.signatureBase64 && validateBase64Image(user.signatureBase64)) {
            return user.signatureBase64;
        }

        // 2. 如果有签名文件路径，尝试转换
        if (user.signaturePath) {
            const fullPath = path.join(process.cwd(), user.signaturePath);
            if (fs.existsSync(fullPath)) {
                const base64 = await fileToBase64(fullPath);
                return base64;
            }
        }

        // 3. 尝试从签名目录查找文件
        if (signaturesDir && user.usercode) {
            const files = fs.readdirSync(signaturesDir);
            const userSignatureFile = files.find(file => 
                file.startsWith(user.usercode + '_')
            );
            
            if (userSignatureFile) {
                const filePath = path.join(signaturesDir, userSignatureFile);
                const base64 = await fileToBase64(filePath);
                return base64;
            }
        }

        return null;
    } catch (error) {
        logger.error('获取签名失败:', error);
        return null;
    }
}

module.exports = {
    validateBase64Image,
    fileToBase64,
    base64ToFile,
    compressBase64Image,
    batchConvertSignatures,
    getSignatureWithFallback
};
