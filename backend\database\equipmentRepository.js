/**
 * 设备数据访问层
 * 处理设备相关的数据库操作
 */

const logger = require('../utils/logger');

class EquipmentRepository {
    constructor(database) {
        this.db = database.getConnection();
        this.initStatements();
    }

    /**
     * 初始化预编译语句
     */
    initStatements() {
        this.statements = {
            // 设备相关语句
            findAll: this.db.prepare('SELECT * FROM equipment ORDER BY created_at DESC'),
            findById: this.db.prepare('SELECT * FROM equipment WHERE id = ?'),
            findByCode: this.db.prepare('SELECT * FROM equipment WHERE code = ?'),
            findByArea: this.db.prepare('SELECT * FROM equipment WHERE area = ?'),
            insert: this.db.prepare(`
                INSERT INTO equipment (
                    id, code, name, area, location, responsible,
                    manufacture_date, status, specifications, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            update: this.db.prepare(`
                UPDATE equipment SET
                    code = ?, name = ?, area = ?, location = ?, responsible = ?,
                    manufacture_date = ?, status = ?, specifications = ?, updated_at = ?
                WHERE id = ?
            `),
            delete: this.db.prepare('DELETE FROM equipment WHERE id = ?'),

            // 厂区相关语句
            findAllFactories: this.db.prepare('SELECT * FROM factories ORDER BY created_at DESC'),
            findFactoryById: this.db.prepare('SELECT * FROM factories WHERE id = ?'),
            findFactoryByName: this.db.prepare('SELECT * FROM factories WHERE name = ?'),
            insertFactory: this.db.prepare(`
                INSERT INTO factories (id, name, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            `),
            updateFactory: this.db.prepare(`
                UPDATE factories SET name = ?, description = ?, updated_at = ?
                WHERE id = ?
            `),
            deleteFactory: this.db.prepare('DELETE FROM factories WHERE id = ?'),

            // 统计相关语句
            countByArea: this.db.prepare('SELECT area, COUNT(*) as count FROM equipment GROUP BY area'),
            countByStatus: this.db.prepare('SELECT status, COUNT(*) as count FROM equipment GROUP BY status'),
            countTotal: this.db.prepare('SELECT COUNT(*) as count FROM equipment')
        };
    }

    /**
     * 获取所有设备（包含最新健康度数据）
     */
    findAll() {
        try {
            // 获取设备基本信息和最新健康度数据
            const query = `
                SELECT
                    e.*,
                    eh.total_score as healthScore,
                    eh.health_level as healthLevel,
                    eh.assessment_date as lastAssessment,
                    eh.age_score,
                    eh.repair_frequency_score,
                    eh.fault_severity_score,
                    eh.maintenance_score
                FROM equipment e
                LEFT JOIN (
                    SELECT
                        equipment_id,
                        total_score,
                        health_level,
                        assessment_date,
                        age_score,
                        repair_frequency_score,
                        fault_severity_score,
                        maintenance_score,
                        ROW_NUMBER() OVER (PARTITION BY equipment_id ORDER BY assessment_date DESC) as rn
                    FROM equipment_health
                ) eh ON e.id = eh.equipment_id AND eh.rn = 1
                ORDER BY e.created_at DESC
            `;

            const equipment = this.db.prepare(query).all();
            return equipment.map(eq => ({
                ...eq,
                specifications: JSON.parse(eq.specifications || '{}'),
                healthMetrics: eq.age_score ? {
                    age: eq.age_score,
                    repair: eq.repair_frequency_score,
                    fault: eq.fault_severity_score,
                    maintenance: eq.maintenance_score
                } : null
            }));
        } catch (error) {
            logger.error('获取设备列表失败:', error);
            throw error;
        }
    }

    /**
     * 分页获取设备列表
     * @param {Object} options - 查询选项
     * @param {number} options.page - 页码
     * @param {number} options.limit - 每页数量
     * @param {string} options.search - 搜索关键词
     * @param {string} options.area - 厂区过滤
     * @param {string} options.status - 状态过滤
     * @param {string} options.location - 位置过滤
     * @param {string} options.responsible - 负责人过滤
     * @returns {Object} 分页结果
     */
    findAllWithPagination(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                search = '',
                area = '',
                status = '',
                location = '',
                responsible = ''
            } = options;

            // 构建WHERE条件
            const conditions = [];
            const params = [];

            if (search) {
                conditions.push('(code LIKE ? OR name LIKE ? OR location LIKE ? OR responsible LIKE ?)');
                const searchParam = `%${search}%`;
                params.push(searchParam, searchParam, searchParam, searchParam);
            }

            if (area) {
                conditions.push('area = ?');
                params.push(area);
            }

            if (status) {
                conditions.push('status = ?');
                params.push(status);
            }

            if (location) {
                conditions.push('location = ?');
                params.push(location);
            }

            if (responsible) {
                conditions.push('responsible = ?');
                params.push(responsible);
            }

            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

            // 获取总数
            const countStmt = this.db.prepare(`SELECT COUNT(*) as total FROM equipment ${whereClause}`);
            const { total } = countStmt.get(...params);

            // 获取分页数据（包含健康度信息）
            const offset = (page - 1) * limit;
            const dataQuery = `
                SELECT
                    e.*,
                    eh.total_score as healthScore,
                    eh.health_level as healthLevel,
                    eh.assessment_date as lastAssessment,
                    eh.age_score,
                    eh.repair_frequency_score,
                    eh.fault_severity_score,
                    eh.maintenance_score
                FROM equipment e
                LEFT JOIN (
                    SELECT
                        equipment_id,
                        total_score,
                        health_level,
                        assessment_date,
                        age_score,
                        repair_frequency_score,
                        fault_severity_score,
                        maintenance_score,
                        ROW_NUMBER() OVER (PARTITION BY equipment_id ORDER BY assessment_date DESC) as rn
                    FROM equipment_health
                ) eh ON e.id = eh.equipment_id AND eh.rn = 1
                ${whereClause}
                ORDER BY e.created_at DESC
                LIMIT ? OFFSET ?
            `;

            const dataStmt = this.db.prepare(dataQuery);
            const rows = dataStmt.all(...params, limit, offset);
            const equipment = rows.map(eq => ({
                ...eq,
                specifications: JSON.parse(eq.specifications || '{}'),
                healthMetrics: eq.age_score ? {
                    age: eq.age_score,
                    repair: eq.repair_frequency_score,
                    fault: eq.fault_severity_score,
                    maintenance: eq.maintenance_score
                } : null
            }));

            return {
                equipment,
                total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(total / limit)
            };
        } catch (error) {
            logger.error('分页获取设备列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取设备
     */
    findById(id) {
        try {
            const equipment = this.statements.findById.get(id);
            if (equipment) {
                equipment.specifications = JSON.parse(equipment.specifications || '{}');
            }
            return equipment;
        } catch (error) {
            logger.error('根据ID获取设备失败:', error);
            throw error;
        }
    }

    /**
     * 根据设备编号获取设备
     */
    findByCode(code) {
        try {
            const equipment = this.statements.findByCode.get(code);
            if (equipment) {
                equipment.specifications = JSON.parse(equipment.specifications || '{}');
            }
            return equipment;
        } catch (error) {
            logger.error('根据编号获取设备失败:', error);
            throw error;
        }
    }

    /**
     * 根据厂区获取设备
     */
    findByArea(area) {
        try {
            const equipment = this.statements.findByArea.all(area);
            return equipment.map(eq => ({
                ...eq,
                specifications: JSON.parse(eq.specifications || '{}')
            }));
        } catch (error) {
            logger.error('根据厂区获取设备失败:', error);
            throw error;
        }
    }

    /**
     * 创建设备
     */
    create(equipmentData) {
        try {
            const now = new Date().toISOString();

            this.statements.insert.run(
                equipmentData.id,
                equipmentData.code,
                equipmentData.name,
                equipmentData.area,
                equipmentData.location,
                equipmentData.responsible,
                equipmentData.manufacture_date,
                equipmentData.status || 'active',
                JSON.stringify(equipmentData.specifications || {}),
                now,
                now
            );

            return this.findById(equipmentData.id);
        } catch (error) {
            logger.error('创建设备失败:', error);
            throw error;
        }
    }

    /**
     * 更新设备
     */
    update(id, equipmentData) {
        try {
            const now = new Date().toISOString();

            this.statements.update.run(
                equipmentData.code,
                equipmentData.name,
                equipmentData.area,
                equipmentData.location,
                equipmentData.responsible,
                equipmentData.manufacture_date,
                equipmentData.status,
                JSON.stringify(equipmentData.specifications || {}),
                now,
                id
            );

            return this.findById(id);
        } catch (error) {
            logger.error('更新设备失败:', error);
            throw error;
        }
    }

    /**
     * 删除设备
     */
    delete(id) {
        try {
            logger.info('执行设备删除SQL', { equipmentId: id });
            const result = this.statements.delete.run(id);
            logger.info('设备删除SQL执行结果', { equipmentId: id, changes: result.changes, lastInsertRowid: result.lastInsertRowid });

            const success = result.changes > 0;
            if (success) {
                logger.info('设备删除成功', { equipmentId: id });
            } else {
                logger.warn('设备删除失败，没有行被影响', { equipmentId: id });
            }

            return success;
        } catch (error) {
            logger.error('删除设备失败:', { equipmentId: id, error: error.message });
            throw error;
        }
    }

    /**
     * 获取所有厂区
     */
    findAllFactories() {
        try {
            return this.statements.findAllFactories.all();
        } catch (error) {
            logger.error('获取厂区列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取厂区
     */
    findFactoryById(id) {
        try {
            return this.statements.findFactoryById.get(id);
        } catch (error) {
            logger.error('根据ID获取厂区失败:', error);
            throw error;
        }
    }

    /**
     * 根据名称获取厂区
     */
    findFactoryByName(name) {
        try {
            return this.statements.findFactoryByName.get(name);
        } catch (error) {
            logger.error('根据名称获取厂区失败:', error);
            throw error;
        }
    }

    /**
     * 创建厂区
     */
    createFactory(factoryData) {
        try {
            const now = new Date().toISOString();

            this.statements.insertFactory.run(
                factoryData.id,
                factoryData.name,
                factoryData.description || '',
                now,
                now
            );

            return this.findFactoryById(factoryData.id);
        } catch (error) {
            logger.error('创建厂区失败:', error);
            throw error;
        }
    }

    /**
     * 更新厂区
     */
    updateFactory(id, factoryData) {
        try {
            const now = new Date().toISOString();

            this.statements.updateFactory.run(
                factoryData.name,
                factoryData.description || '',
                now,
                id
            );

            return this.findFactoryById(id);
        } catch (error) {
            logger.error('更新厂区失败:', error);
            throw error;
        }
    }

    /**
     * 删除厂区
     */
    deleteFactory(id) {
        try {
            const result = this.statements.deleteFactory.run(id);
            return result.changes > 0;
        } catch (error) {
            logger.error('删除厂区失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备统计信息
     */
    getStatistics() {
        try {
            const totalResult = this.statements.countTotal.get();
            const areaStats = this.statements.countByArea.all();
            const statusStats = this.statements.countByStatus.all();

            return {
                total: totalResult.count,
                byArea: areaStats,
                byStatus: statusStats
            };
        } catch (error) {
            logger.error('获取设备统计失败:', error);
            throw error;
        }
    }

    /**
     * 获取筛选选项
     */
    getFilterOptions() {
        try {
            // 获取所有不同的位置
            const locations = this.db.prepare(`
                SELECT DISTINCT location, COUNT(*) as count
                FROM equipment
                WHERE location IS NOT NULL AND location != ''
                GROUP BY location
                ORDER BY location
            `).all();

            // 获取所有不同的负责人
            const responsibles = this.db.prepare(`
                SELECT DISTINCT responsible, COUNT(*) as count
                FROM equipment
                WHERE responsible IS NOT NULL AND responsible != ''
                GROUP BY responsible
                ORDER BY responsible
            `).all();

            // 获取所有不同的厂区
            const areas = this.db.prepare(`
                SELECT DISTINCT area, COUNT(*) as count
                FROM equipment
                WHERE area IS NOT NULL AND area != ''
                GROUP BY area
                ORDER BY area
            `).all();

            // 获取所有不同的状态
            const statuses = this.db.prepare(`
                SELECT DISTINCT status, COUNT(*) as count
                FROM equipment
                WHERE status IS NOT NULL AND status != ''
                GROUP BY status
                ORDER BY status
            `).all();

            return {
                locations,
                responsibles,
                areas,
                statuses
            };
        } catch (error) {
            logger.error('获取筛选选项失败:', error);
            throw error;
        }
    }
}

module.exports = EquipmentRepository;
