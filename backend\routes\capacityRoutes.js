/**
 * 设备产能路由
 * 定义设备产能相关的API端点
 */

const express = require('express');
const router = express.Router();
const CapacityController = require('../controllers/capacityController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

const capacityController = new CapacityController();

/**
 * 设备产能相关路由
 */

/**
 * 获取设备的产能配置
 * GET /api/equipment/:equipmentId/capabilities
 * 权限: equipment_view
 */
router.get('/equipment/:equipmentId/capabilities',
    authenticateJWT,
    checkPermission('equipment_view'),
    capacityController.getEquipmentCapabilities.bind(capacityController)
);

/**
 * 创建设备产能记录
 * POST /api/equipment/capabilities
 * 权限: equipment_edit
 */
router.post('/equipment/capabilities',
    authenticateJWT,
    checkPermission('equipment_edit'),
    capacityController.createEquipmentCapability.bind(capacityController)
);

/**
 * 更新设备产能
 * PUT /api/equipment/capabilities/:id
 * 权限: equipment_edit
 */
router.put('/equipment/capabilities/:id',
    authenticateJWT,
    checkPermission('equipment_edit'),
    capacityController.updateEquipmentCapability.bind(capacityController)
);

/**
 * 获取产品的可用设备
 * GET /api/products/:productId/equipment
 * 权限: schedule_view 或 product_view
 */
router.get('/products/:productId/equipment',
    authenticateJWT,
    checkPermission(['schedule_view', 'product_view']),
    capacityController.getAvailableEquipmentForProduct.bind(capacityController)
);

/**
 * 操作员技能相关路由
 */

/**
 * 获取设备的操作员技能
 * GET /api/equipment/:equipmentId/operator-skills
 * 权限: equipment_view
 */
router.get('/equipment/:equipmentId/operator-skills',
    authenticateJWT,
    checkPermission('equipment_view'),
    capacityController.getEquipmentOperatorSkills.bind(capacityController)
);

/**
 * 创建操作员技能记录
 * POST /api/operator-skills
 * 权限: user_edit
 */
router.post('/operator-skills',
    authenticateJWT,
    checkPermission('user_edit'),
    capacityController.createOperatorSkill.bind(capacityController)
);

/**
 * 获取操作员的技能列表
 * GET /api/operators/:operatorId/skills
 * 权限: user_view
 */
router.get('/operators/:operatorId/skills',
    authenticateJWT,
    checkPermission('user_view'),
    capacityController.getOperatorSkills.bind(capacityController)
);

/**
 * 设备操作员关联相关路由
 */

/**
 * 获取设备的操作员列表
 * GET /api/equipment/:equipmentId/operators
 * 权限: equipment_view
 */
router.get('/equipment/:equipmentId/operators',
    authenticateJWT,
    checkPermission('equipment_view'),
    capacityController.getEquipmentOperators.bind(capacityController)
);

/**
 * 创建设备操作员关联
 * POST /api/equipment-operators
 * 权限: equipment_edit
 */
router.post('/equipment-operators',
    authenticateJWT,
    checkPermission('equipment_edit'),
    capacityController.createEquipmentOperator.bind(capacityController)
);

module.exports = router;
