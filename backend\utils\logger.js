/**
 * 日志工具模块
 * 使用 Winston 库实现的日志系统
 *
 * 遵循有效日志原则:
 * 1. 添加关键操作的日志记录，方便问题排查
 * 2. 确保错误情况下有足够详细的日志信息
 * 3. 避免过度日志导致的性能问题
 */

const winston = require('winston');
const { createLogger, format, transports } = winston;
const { combine, timestamp, printf, colorize, json, ms } = format;
const path = require('path');
const fs = require('fs');
const DailyRotateFile = require('winston-daily-rotate-file');
const config = require('../config');
const crypto = require('crypto');

// 确保日志目录存在
const logDir = path.join(config.paths.data, 'logs');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
}

/**
 * 生成唯一请求ID
 * @returns {string} 请求ID
 */
function generateRequestId() {
    return crypto.randomBytes(8).toString('hex');
}

/**
 * 日志采样决策
 * @param {Object} info - 日志信息
 * @returns {boolean} 是否记录日志
 */
function shouldLogSample(info) {
    // 错误级别始终记录
    if (info.level === 'error' || info.level === 'warn') {
        return true;
    }

    // HTTP请求采样 - 只记录一部分常规请求
    if (info.level === 'http') {
        // 静态资源请求采样率低
        if (info.url && (
            info.url.endsWith('.js') ||
            info.url.endsWith('.css') ||
            info.url.endsWith('.png') ||
            info.url.endsWith('.jpg') ||
            info.url.endsWith('.ico')
        )) {
            // 静态资源只记录1%
            return Math.random() < 0.01;
        }

        // 健康检查请求采样率更低
        if (info.url && info.url.includes('/health')) {
            // 健康检查只记录0.1%
            return Math.random() < 0.001;
        }

        // 其他HTTP请求采样10%
        return Math.random() < 0.1;
    }

    // 调试日志在生产环境采样
    if (info.level === 'debug' && process.env.NODE_ENV === 'production') {
        return Math.random() < 0.05; // 5%的采样率
    }

    // 其他级别全部记录
    return true;
}

// 采样格式
const samplingFormat = format((info) => {
    return shouldLogSample(info) ? info : false;
});

// 自定义日志格式 - 详细版（用于文件日志）
const detailedFormat = printf(({ level, message, timestamp, requestId, duration, ...metadata }) => {
    let meta = '';
    if (Object.keys(metadata).length > 0) {
        meta = JSON.stringify(metadata);
    }

    let reqId = requestId ? `[${requestId}]` : '';
    let durationStr = duration ? `(${duration})` : '';

    return `[${timestamp}] [${level.toUpperCase()}] ${reqId} ${durationStr}: ${message} ${meta}`;
});

// 控制台日志过滤器
const consoleFilter = format((info) => {
    // 过滤掉健康检查请求
    if (info.url && info.url.includes('/health')) {
        return false;
    }

    // 过滤掉静态资源请求
    if (info.url && info.url.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i)) {
        return false;
    }

    // 过滤掉一些不必要的系统日志
    if (info.message && (
        info.message.includes('日志轮转') ||
        info.message.includes('成功读取文件') ||
        info.message.includes('成功写入文件')
    )) {
        return false;
    }

    return info;
});

// 简洁版控制台日志格式
const simpleConsoleFormat = printf(({ level, message, method, url, status }) => {
    // 提取HTTP请求的简洁信息
    if (message.startsWith('HTTP ')) {
        // 如果有状态码，显示状态码
        const statusStr = status ? ` [${status}]` : '';
        // 例如: "GET /api/users [200]"
        return `${message.replace('HTTP ', '')}${statusStr}`;
    }

    // 对于错误，保留更多信息
    if (level === 'error' || level === 'warn') {
        return `[${level.toUpperCase()}] ${message}`;
    }

    // 其他日志尽量简化
    return message;
});

// 控制台日志格式（带颜色，简洁版）
const consoleFormat = combine(
    consoleFilter(), // 先过滤不需要的日志
    colorize(),
    simpleConsoleFormat
);

// 文件日志格式（详细版）
const fileFormat = combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    ms(), // 添加执行时间
    samplingFormat(), // 添加采样
    json()
);

// 详细的控制台日志格式（用于调试，通常不启用）
const detailedConsoleFormat = combine(
    colorize(),
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    ms(), // 添加执行时间
    detailedFormat
);

// 定义日志级别
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
};

// 创建日志轮转传输器
const createFileTransport = (level) => {
    return new DailyRotateFile({
        level,
        dirname: logDir,
        filename: `%DATE%-${level}.log`,
        datePattern: 'YYYY-MM-DD',
        maxSize: config.logging.file.maxSize || '20m',
        maxFiles: config.logging.file.maxDays || '14d',
        format: fileFormat,
        zippedArchive: true, // 启用压缩归档
        // 添加钩子在轮转后压缩旧日志
        hooks: {
            rotate: (oldFilename, newFilename) => {
                console.log(`日志轮转: ${oldFilename} -> ${newFilename}`);
            }
        }
    });
};

// 创建日志记录器
const logger = createLogger({
    levels: logLevels,
    format: fileFormat,
    transports: [
        // 错误日志文件
        createFileTransport('error'),
        // 所有日志文件
        createFileTransport('info'),
        // 控制台输出
        new transports.Console({
            // 在生产环境只显示警告和错误，在开发环境显示到info级别，但不显示debug级别
            level: process.env.NODE_ENV === 'production' ? 'warn' : 'info',
            // 根据环境变量选择日志格式（详细或简洁）
            format: combine(
                format((info) => {
                    // 过滤掉debug级别的日志，不在控制台显示
                    if (info.level === 'debug') {
                        return false;
                    }
                    return info;
                })(),
                process.env.VERBOSE_LOGS === 'true' ? detailedConsoleFormat : consoleFormat
            ),
        }),
    ],
    exitOnError: false,
});

// 添加HTTP请求日志传输器
logger.httpTransport = createFileTransport('http');
logger.add(logger.httpTransport);

/**
 * 记录HTTP请求日志
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {number} responseTime - 响应时间(ms)
 */
logger.logHttpRequest = (req, res, responseTime) => {
    const { method, url, headers, body, params, query } = req;
    const userAgent = headers['user-agent'];
    const contentLength = res.getHeader('content-length');

    // 生成或获取请求ID
    const requestId = req.requestId || generateRequestId();

    // 判断是否为API请求
    const isApiRequest = url.startsWith('/api');

    // 判断是否为静态资源请求
    const isStaticRequest = url.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i);

    // 根据请求类型和状态码确定日志级别
    let logLevel = 'http';
    if (res.statusCode >= 500) {
        logLevel = 'error';
    } else if (res.statusCode >= 400) {
        logLevel = 'warn';
    } else if (isApiRequest) {
        logLevel = 'info';
    } else if (isStaticRequest) {
        logLevel = 'debug';
    }

    // 构建日志信息
    const logInfo = {
        requestId,
        method,
        url,
        status: res.statusCode,
        responseTime: `${responseTime}ms`,
        duration: responseTime,
        contentLength,
        userAgent,
        ip: req.ip || req.connection.remoteAddress,
        referer: headers.referer || headers.referrer,
        // 只对API请求记录详细参数
        params: isApiRequest ? params : undefined,
        query: isApiRequest ? query : undefined,
        // 不记录敏感信息，如密码
        body: isApiRequest && body ? JSON.stringify(sanitizeBody(body)) : undefined,
        user: req.user ? { id: req.user.id, username: req.user.username, role: req.user.role } : null,
    };

    // 记录日志
    logger[logLevel](`HTTP ${method} ${url}`, logInfo);
};

/**
 * 记录应用操作日志
 * @param {string} action - 操作类型
 * @param {Object} user - 用户信息
 * @param {Object} details - 操作详情
 * @param {boolean} success - 是否成功
 * @param {string} requestId - 请求ID
 */
logger.logAction = (action, user, details, success = true, requestId = null) => {
    // 如果没有提供requestId，则生成一个新的
    const reqId = requestId || generateRequestId();

    // 构建用户信息
    const userInfo = user ? {
        id: user.id,
        username: user.username,
        role: user.role,
        department: user.department,
        usercode: user.usercode
    } : null;

    // 记录操作日志
    logger.info(`用户操作: ${action}`, {
        requestId: reqId,
        action,
        user: userInfo,
        details,
        success,
        timestamp: new Date().toISOString(),
        // 添加业务相关信息
        businessContext: details.businessContext,
        // 添加操作目标
        target: details.target,
        // 添加操作前后的状态
        previousState: details.previousState,
        newState: details.newState
    });

    // 如果操作失败，同时记录一条警告日志
    if (!success) {
        logger.warn(`操作失败: ${action}`, {
            requestId: reqId,
            action,
            user: userInfo,
            reason: details.reason || '未知原因',
            timestamp: new Date().toISOString()
        });
    }

    return reqId;
};

/**
 * 记录系统事件
 * @param {string} event - 事件类型
 * @param {Object} details - 事件详情
 * @param {string} requestId - 请求ID
 */
logger.logSystem = (event, details, requestId = null) => {
    // 如果没有提供requestId，则生成一个新的
    const reqId = requestId || generateRequestId();

    logger.info(`系统事件: ${event}`, {
        requestId: reqId,
        event,
        details,
        timestamp: new Date().toISOString(),
        // 添加系统状态信息
        systemState: {
            memory: process.memoryUsage(),
            uptime: process.uptime()
        }
    });

    return reqId;
};

/**
 * 记录错误
 * @param {string} message - 错误消息
 * @param {Error} error - 错误对象
 * @param {Object} context - 上下文信息
 * @param {string} requestId - 请求ID
 */
logger.logError = (message, error, context = {}, requestId = null) => {
    // 如果没有提供requestId，则生成一个新的
    const reqId = requestId || generateRequestId();

    // 构建错误信息
    const errorInfo = error ? {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        // 添加更多错误属性
        ...Object.getOwnPropertyNames(error)
            .filter(prop => prop !== 'stack' && prop !== 'message' && prop !== 'name')
            .reduce((obj, prop) => {
                try {
                    obj[prop] = error[prop];
                } catch (e) {
                    // 忽略无法序列化的属性
                }
                return obj;
            }, {})
    } : null;

    // 记录错误日志
    logger.error(message, {
        requestId: reqId,
        error: errorInfo,
        context,
        timestamp: new Date().toISOString(),
        // 添加系统状态信息
        systemState: {
            memory: process.memoryUsage(),
            uptime: process.uptime()
        }
    });

    return reqId;
};

/**
 * 清理请求体中的敏感信息
 * @param {Object} body - 请求体
 * @returns {Object} 清理后的请求体
 */
function sanitizeBody(body) {
    if (!body) return {};

    const sanitized = { ...body };

    // 从配置中获取敏感字段列表
    const sensitiveFields = config.logging.sensitiveFields || [
        'password', 'token', 'secret', 'authorization',
        'apiKey', 'api_key', 'key', 'credential', 'pwd',
        'passphrase', 'pin', 'code', 'otp'
    ];

    // 递归清理对象中的敏感字段
    function sanitizeObject(obj) {
        if (!obj || typeof obj !== 'object') return obj;

        const result = Array.isArray(obj) ? [...obj] : { ...obj };

        for (const key in result) {
            // 检查字段名是否包含敏感词
            const isSensitive = sensitiveFields.some(field =>
                key.toLowerCase().includes(field.toLowerCase())
            );

            if (isSensitive) {
                // 敏感字段替换为[REDACTED]
                result[key] = '[REDACTED]';
            } else if (typeof result[key] === 'object') {
                // 递归处理嵌套对象
                result[key] = sanitizeObject(result[key]);
            }
        }

        return result;
    }

    return sanitizeObject(sanitized);
}

/**
 * 记录性能指标
 * @param {string} operation - 操作名称
 * @param {number} duration - 持续时间(ms)
 * @param {Object} metadata - 额外元数据
 * @param {string} requestId - 请求ID
 */
logger.logPerformance = (operation, duration, metadata = {}, requestId = null) => {
    const reqId = requestId || generateRequestId();

    // 根据持续时间确定日志级别
    let level = 'debug';
    if (duration > 1000) { // 超过1秒
        level = 'warn';
    } else if (duration > 500) { // 超过500毫秒
        level = 'info';
    }

    logger[level](`性能: ${operation}`, {
        requestId: reqId,
        operation,
        duration: `${duration}ms`,
        ...metadata,
        timestamp: new Date().toISOString()
    });

    return reqId;
};

/**
 * 记录业务事件
 * @param {string} category - 业务类别
 * @param {string} event - 事件名称
 * @param {Object} data - 事件数据
 * @param {Object} user - 用户信息
 * @param {string} requestId - 请求ID
 */
logger.logBusinessEvent = (category, event, data = {}, user = null, requestId = null) => {
    const reqId = requestId || generateRequestId();

    logger.info(`业务事件: ${category}.${event}`, {
        requestId: reqId,
        category,
        event,
        data: sanitizeBody(data),
        user: user ? {
            id: user.id,
            username: user.username,
            role: user.role
        } : null,
        timestamp: new Date().toISOString()
    });

    return reqId;
};

// 导出日志模块
module.exports = logger;
