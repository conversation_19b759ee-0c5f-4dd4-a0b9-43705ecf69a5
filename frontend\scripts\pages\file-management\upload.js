/**
 * 文件管理上传页面
 * 处理客户二认文件的上传功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';
import FileUploadForm from '../../../components/file-management/FileUploadForm.js';

createStandardApp({
    components: {
        Sidebar,
        FileUploadForm
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);
        const isSubmitting = ref(false);

        // 处理表单提交
        async function handleSubmit(formData) {
            try {
                isSubmitting.value = true;
                
                // 表单提交逻辑在FileUploadForm组件内部处理
                console.log('文件上传表单提交');
            } catch (error) {
                console.error('文件上传失败:', error);
            } finally {
                isSubmitting.value = false;
            }
        }

        // 处理取消操作
        function handleCancel() {
            if (confirm('确定要取消上传吗？未保存的数据将丢失。')) {
                window.location.href = '/file-list';
            }
        }

        return {
            sidebarOpen,
            isSubmitting,
            handleSubmit,
            handleCancel
        };
    },
    requiredPermissions: ['file_upload'],
    onUserLoaded: async (user) => {
        console.log('文件管理上传页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
