/**
 * 日志查看工具
 * 提供日志文件读取和分析功能
 */

const fs = require('fs');
const path = require('path');
const config = require('../config');
const logger = require('./logger');

/**
 * 获取所有日志文件列表
 * @returns {Array} 日志文件列表
 */
function getLogFiles() {
    try {
        const logDir = config.paths.logs;
        if (!fs.existsSync(logDir)) {
            return [];
        }
        
        const files = fs.readdirSync(logDir)
            .filter(file => file.endsWith('.log'))
            .map(file => ({
                name: file,
                path: path.join(logDir, file),
                size: fs.statSync(path.join(logDir, file)).size,
                created: fs.statSync(path.join(logDir, file)).birthtime,
                modified: fs.statSync(path.join(logDir, file)).mtime
            }))
            .sort((a, b) => b.modified - a.modified);
            
        return files;
    } catch (error) {
        logger.error('获取日志文件列表失败', { error: error.message, stack: error.stack });
        return [];
    }
}

/**
 * 读取日志文件内容
 * @param {string} fileName - 日志文件名
 * @param {number} limit - 最大行数限制
 * @param {number} skip - 跳过的行数
 * @returns {Array} 日志条目数组
 */
function readLogFile(fileName, limit = 100, skip = 0) {
    try {
        const filePath = path.join(config.paths.logs, fileName);
        if (!fs.existsSync(filePath)) {
            return [];
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n')
            .filter(line => line.trim())
            .map(line => {
                try {
                    return JSON.parse(line);
                } catch (e) {
                    return { message: line, level: 'unknown', timestamp: new Date().toISOString() };
                }
            })
            .slice(skip, skip + limit);
            
        return lines;
    } catch (error) {
        logger.error('读取日志文件失败', { error: error.message, stack: error.stack, fileName });
        return [];
    }
}

/**
 * 搜索日志文件
 * @param {string} fileName - 日志文件名
 * @param {string} searchTerm - 搜索关键词
 * @param {number} limit - 最大行数限制
 * @returns {Array} 匹配的日志条目
 */
function searchLogFile(fileName, searchTerm, limit = 100) {
    try {
        const filePath = path.join(config.paths.logs, fileName);
        if (!fs.existsSync(filePath)) {
            return [];
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n')
            .filter(line => line.trim() && line.toLowerCase().includes(searchTerm.toLowerCase()))
            .map(line => {
                try {
                    return JSON.parse(line);
                } catch (e) {
                    return { message: line, level: 'unknown', timestamp: new Date().toISOString() };
                }
            })
            .slice(0, limit);
            
        return lines;
    } catch (error) {
        logger.error('搜索日志文件失败', { error: error.message, stack: error.stack, fileName, searchTerm });
        return [];
    }
}

/**
 * 删除日志文件
 * @param {string} fileName - 日志文件名
 * @returns {boolean} 是否成功
 */
function deleteLogFile(fileName) {
    try {
        const filePath = path.join(config.paths.logs, fileName);
        if (!fs.existsSync(filePath)) {
            return true;
        }
        
        fs.unlinkSync(filePath);
        logger.info(`删除日志文件: ${fileName}`);
        return true;
    } catch (error) {
        logger.error('删除日志文件失败', { error: error.message, stack: error.stack, fileName });
        return false;
    }
}

/**
 * 清理过期日志文件
 * @param {number} days - 保留天数
 * @returns {number} 删除的文件数量
 */
function cleanupOldLogs(days = 30) {
    try {
        const logDir = config.paths.logs;
        if (!fs.existsSync(logDir)) {
            return 0;
        }
        
        const now = new Date();
        const cutoffDate = new Date(now.setDate(now.getDate() - days));
        
        const files = fs.readdirSync(logDir)
            .filter(file => file.endsWith('.log'))
            .map(file => ({
                name: file,
                path: path.join(logDir, file),
                modified: fs.statSync(path.join(logDir, file)).mtime
            }))
            .filter(file => file.modified < cutoffDate);
            
        let deletedCount = 0;
        files.forEach(file => {
            try {
                fs.unlinkSync(file.path);
                deletedCount++;
            } catch (e) {
                logger.error(`删除过期日志文件失败: ${file.name}`, { error: e.message });
            }
        });
        
        logger.info(`清理过期日志文件完成`, { days, deletedCount, totalFiles: files.length });
        return deletedCount;
    } catch (error) {
        logger.error('清理过期日志文件失败', { error: error.message, stack: error.stack, days });
        return 0;
    }
}

module.exports = {
    getLogFiles,
    readLogFile,
    searchLogFile,
    deleteLogFile,
    cleanupOldLogs
};
