/**
 * 添加文件管理权限脚本
 * 为所有admin用户添加文件管理相关权限
 */

const databaseManager = require('../database/database');
const logger = require('../utils/logger');

async function addFileManagementPermissions() {
    try {
        const db = databaseManager.getConnection();
        
        // 文件管理相关权限
        const filePermissions = [
            'file_upload',
            'file_view', 
            'file_download',
            'file_manage',
            'file_confirm'
        ];
        
        // 获取所有admin用户
        const adminUsers = db.prepare(`
            SELECT id, username, permissions 
            FROM users 
            WHERE role = 'admin' AND active = 1
        `).all();
        
        console.log(`找到 ${adminUsers.length} 个admin用户`);
        
        for (const user of adminUsers) {
            let currentPermissions = [];
            
            try {
                currentPermissions = user.permissions ? JSON.parse(user.permissions) : [];
            } catch (error) {
                console.warn(`用户 ${user.username} 的权限格式错误，重置为空数组`);
                currentPermissions = [];
            }
            
            // 添加文件管理权限（如果还没有的话）
            let hasNewPermissions = false;
            for (const permission of filePermissions) {
                if (!currentPermissions.includes(permission)) {
                    currentPermissions.push(permission);
                    hasNewPermissions = true;
                }
            }
            
            if (hasNewPermissions) {
                // 更新用户权限
                const updateStmt = db.prepare(`
                    UPDATE users 
                    SET permissions = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                `);
                
                updateStmt.run(JSON.stringify(currentPermissions), user.id);
                
                console.log(`✅ 已为用户 ${user.username} 添加文件管理权限`);
                console.log(`   新权限列表: ${currentPermissions.join(', ')}`);
            } else {
                console.log(`ℹ️  用户 ${user.username} 已有所有文件管理权限`);
            }
        }
        
        // 同时检查并添加权限模板
        console.log('\n检查权限模板...');
        
        const templateExists = db.prepare(`
            SELECT COUNT(*) as count 
            FROM permission_templates 
            WHERE id = 'file_manager'
        `).get();
        
        if (templateExists.count === 0) {
            const insertTemplate = db.prepare(`
                INSERT INTO permission_templates (id, name, description, permissions, created_at, updated_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `);
            
            insertTemplate.run(
                'file_manager',
                '文件管理员权限',
                '适用于文件管理人员的权限配置',
                JSON.stringify(['file_upload', 'file_view', 'file_download', 'file_manage', 'file_confirm', 'user_settings'])
            );
            
            console.log('✅ 已创建文件管理员权限模板');
        } else {
            console.log('ℹ️  文件管理员权限模板已存在');
        }
        
        console.log('\n🎉 文件管理权限添加完成！');
        
        logger.info('文件管理权限添加完成', {
            adminUsersCount: adminUsers.length,
            permissions: filePermissions
        });
        
    } catch (error) {
        console.error('❌ 添加文件管理权限失败:', error);
        logger.error('添加文件管理权限失败', {
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    addFileManagementPermissions()
        .then(() => {
            console.log('脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = addFileManagementPermissions;
