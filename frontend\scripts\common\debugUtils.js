/**
 * 安全调试工具
 * 提供生产环境安全的调试输出功能
 */

// 检测是否为开发环境
const isDevelopment = () => {
    // 检查多种开发环境标识
    return window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1' ||
           window.location.hostname.includes('dev') ||
           window.location.port === '3000' ||
           window.location.port === '5050' ||
           localStorage.getItem('debug') === 'true';
};

/**
 * 安全的用户信息日志输出
 * 只显示非敏感信息
 * @param {string} message - 日志消息
 * @param {Object} user - 用户对象
 */
export function logUserInfo(message, user) {
    if (!user) {
        console.log(message + ' - 用户信息为空');
        return;
    }

    if (isDevelopment()) {
        // 开发环境显示更多信息，但仍然过滤敏感数据
        const safeUser = {
            username: user.username,
            role: user.role,
            department: user.department,
            active: user.active,
            permissionCount: user.permissions ? user.permissions.length : 0
        };
        console.log(message, safeUser);
    } else {
        // 生产环境只显示基本信息
        console.log(`${message} - 用户: ${user.username}, 角色: ${user.role}`);
    }
}

/**
 * 安全的调试日志
 * 只在开发环境输出详细信息
 * @param {string} message - 日志消息
 * @param {any} data - 要输出的数据
 */
export function debugLog(message, data = null) {
    if (isDevelopment()) {
        if (data !== null) {
            console.log(message, data);
        } else {
            console.log(message);
        }
    }
}

/**
 * 安全的错误日志
 * 在所有环境都输出，但过滤敏感信息
 * @param {string} message - 错误消息
 * @param {Error|Object} error - 错误对象
 */
export function logError(message, error = null) {
    if (error) {
        if (isDevelopment()) {
            console.error(message, error);
        } else {
            // 生产环境只显示错误消息，不显示堆栈信息
            console.error(`${message}: ${error.message || error}`);
        }
    } else {
        console.error(message);
    }
}

/**
 * 安全的警告日志
 * @param {string} message - 警告消息
 * @param {any} data - 相关数据
 */
export function logWarning(message, data = null) {
    if (isDevelopment() && data !== null) {
        console.warn(message, data);
    } else {
        console.warn(message);
    }
}

/**
 * 过滤敏感信息的对象清理器
 * @param {Object} obj - 要清理的对象
 * @returns {Object} 清理后的对象
 */
export function sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') return obj;

    const sensitiveFields = [
        'password', 'token', 'secret', 'authorization',
        'apiKey', 'api_key', 'key', 'credential', 'pwd',
        'passphrase', 'pin', 'code', 'otp', 'permissions',
        'id', 'usercode', 'email', 'lastLoginAt', 'lastActiveAt',
        'createdAt', 'updatedAt'
    ];

    const sanitized = Array.isArray(obj) ? [...obj] : { ...obj };

    for (const key in sanitized) {
        const isSensitive = sensitiveFields.some(field =>
            key.toLowerCase().includes(field.toLowerCase())
        );

        if (isSensitive) {
            if (key === 'permissions' && Array.isArray(sanitized[key])) {
                sanitized[key] = `[${sanitized[key].length} permissions]`;
            } else {
                sanitized[key] = '[REDACTED]';
            }
        } else if (typeof sanitized[key] === 'object') {
            sanitized[key] = sanitizeObject(sanitized[key]);
        }
    }

    return sanitized;
}

// 导出开发环境检测函数
export { isDevelopment };
