/**
 * 新建申请页面逻辑
 */

import { getCurrentUser } from '../../../scripts/api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ApplicationForm from '../../../components/application/ApplicationForm.js';

const { createApp, ref, onMounted, watch } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar,
        ApplicationForm
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const applicationType = ref('standard'); // 默认显示标准申请
        // 从localStorage获取侧边栏状态，默认为false（桌面端展开，移动端折叠）
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);
        const isMobile = ref(window.innerWidth < 768); // 是否为移动设备

        // 监听窗口大小变化
        function handleResize() {
            isMobile.value = window.innerWidth < 768;
            if (!isMobile.value) {
                sidebarOpen.value = false; // 在桌面模式下关闭移动侧边栏
            }
        }

        // 切换侧边栏
        function toggleSidebar() {
            sidebarOpen.value = !sidebarOpen.value;
            // 保存状态到localStorage
            localStorage.setItem('sidebarOpen', sidebarOpen.value.toString());
        }

        // 初始化
        onMounted(() => {
            checkAuth();
            // 确保加载指示器被隐藏
            hideLoading();

            // 从URL参数中获取申请类型
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type');
            if (type === 'other') {
                applicationType.value = 'other';
            }

            // 设置页面标题
            document.title = applicationType.value === 'standard' ?
                '新建标准申请 - 管理系统' :
                '新建其他申请 - 管理系统';

            // 添加窗口大小变化监听
            window.addEventListener('resize', handleResize);
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
            }
        }

        // 申请提交成功
        function onApplicationSubmitted() {
            // 提交成功后跳转到申请记录页面
            window.location.href = '/application-record';
        }

        return {
            currentUser,
            isAuthenticated,
            applicationType,
            sidebarOpen,
            isMobile,
            toggleSidebar,
            onApplicationSubmitted
        };
    }
}).mount('#app');
