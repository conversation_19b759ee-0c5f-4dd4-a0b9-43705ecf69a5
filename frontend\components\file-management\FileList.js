/**
 * 文件列表组件
 * 显示文件管理列表
 */

import { getFileRecords, getFileById, deleteFileRecord, downloadFile, fileManagementUtils } from '../../scripts/api/file-management.js';
import FileDetailModal from './FileDetailModal.js';

const { createApp, ref, reactive, computed, onMounted } = Vue;

export default {
    name: 'FileList',
    components: {
        FileDetailModal
    },
    setup() {
        // 响应式数据
        const files = ref([]);
        const loading = ref(false);
        const searchQuery = ref('');
        const selectedCustomer = ref('');
        const selectedStatus = ref('');
        const currentPage = ref(1);
        const itemsPerPage = ref(10);

        // 详情模态框相关
        const showDetailModal = ref(false);
        const selectedFile = ref(null);

        // 计算属性
        const filteredFiles = computed(() => {
            let result = files.value;

            // 搜索过滤
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                result = result.filter(file => 
                    file.title.toLowerCase().includes(query) ||
                    file.file_number.toLowerCase().includes(query) ||
                    file.customer_name.toLowerCase().includes(query) ||
                    file.product_model.toLowerCase().includes(query)
                );
            }

            // 客户过滤
            if (selectedCustomer.value) {
                result = result.filter(file => file.customer_name === selectedCustomer.value);
            }

            return result;
        });

        const paginatedFiles = computed(() => {
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return filteredFiles.value.slice(start, end);
        });

        const totalPages = computed(() => {
            return Math.ceil(filteredFiles.value.length / itemsPerPage.value);
        });

        const customers = computed(() => {
            const customerSet = new Set(files.value.map(file => file.customer_name));
            return Array.from(customerSet).sort();
        });

        // 生命周期
        onMounted(() => {
            loadFiles();
        });

        // 方法
        async function loadFiles() {
            try {
                loading.value = true;
                const response = await getFileRecords();
                if (response.success) {
                    files.value = response.data;
                }
            } catch (error) {
                console.error('加载文件列表失败:', error);
                alert('加载文件列表失败');
            } finally {
                loading.value = false;
            }
        }

        async function viewFileDetail(file) {
            try {
                // 获取完整的文件详情（包括附件列表）
                const response = await getFileById(file.id);

                if (response.success) {
                    selectedFile.value = response.data;
                    showDetailModal.value = true;
                } else {
                    throw new Error(response.message || '获取文件详情失败');
                }
            } catch (error) {
                console.error('获取文件详情失败:', error);
                alert('获取文件详情失败: ' + error.message);
            }
        }

        function closeDetailModal() {
            showDetailModal.value = false;
            selectedFile.value = null;
        }

        function handleDownloadFile(file, attachment) {
            downloadFile(file.id, attachment.id);
        }

        async function deleteFile(file) {
            if (!confirm(`确定要删除文件 "${file.title}" 吗？`)) {
                return;
            }

            try {
                const response = await deleteFileRecord(file.id);
                if (response.success) {
                    alert('文件删除成功');
                    await loadFiles();
                } else {
                    throw new Error(response.message || '删除失败');
                }
            } catch (error) {
                console.error('删除文件失败:', error);
                alert('删除文件失败: ' + error.message);
            }
        }

        function resetFilters() {
            searchQuery.value = '';
            selectedCustomer.value = '';
            selectedStatus.value = '';
            currentPage.value = 1;
        }

        function goToPage(page) {
            if (page >= 1 && page <= totalPages.value) {
                currentPage.value = page;
            }
        }

        return {
            files,
            loading,
            searchQuery,
            selectedCustomer,
            selectedStatus,
            currentPage,
            itemsPerPage,
            filteredFiles,
            paginatedFiles,
            totalPages,
            customers,
            showDetailModal,
            selectedFile,
            loadFiles,
            viewFileDetail,
            closeDetailModal,
            handleDownloadFile,
            deleteFile,
            resetFilters,
            goToPage,
            fileManagementUtils
        };
    },
    template: `
        <div class="file-list">
            <!-- 搜索和过滤区域 -->
            <div class="bg-white p-6 rounded-lg shadow border mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
                    <div class="flex-1">
                        <input type="text" v-model="searchQuery" 
                               placeholder="搜索文件标题、编号、客户名称或产品型号..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="flex space-x-4">
                        <select v-model="selectedCustomer" 
                                class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">所有客户</option>
                            <option v-for="customer in customers" :key="customer" :value="customer">
                                {{ customer }}
                            </option>
                        </select>
                        
                        <button @click="resetFilters"
                                class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                            重置
                        </button>
                        
                        <button @click="loadFiles"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            刷新
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p class="mt-2 text-gray-600">加载中...</p>
            </div>

            <!-- 文件列表 -->
            <div v-else-if="paginatedFiles.length > 0" class="space-y-4">
                <div v-for="file in paginatedFiles" :key="file.id"
                     class="bg-white p-6 rounded-lg shadow border hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">{{ file.title }}</h3>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    V{{ file.version }}
                                </span>
                                <span v-if="!file.is_first_version" class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                    变更版本
                                </span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                                <div>
                                    <span class="font-medium">文件编号：</span>
                                    <span>{{ file.file_number }}</span>
                                </div>
                                <div>
                                    <span class="font-medium">客户名称：</span>
                                    <span>{{ file.customer_name }}</span>
                                </div>
                                <div>
                                    <span class="font-medium">产品型号：</span>
                                    <span>{{ file.product_model }}</span>
                                </div>
                                <div>
                                    <span class="font-medium">上传时间：</span>
                                    <span>{{ fileManagementUtils.formatDateTime(file.uploaded_at) }}</span>
                                </div>
                            </div>
                            
                            <div v-if="file.description" class="text-sm text-gray-600 mb-3">
                                <span class="font-medium">描述：</span>
                                <span>{{ file.description }}</span>
                            </div>
                            
                            <div v-if="!file.is_first_version && file.change_description" class="text-sm text-gray-600 mb-3">
                                <span class="font-medium">变更内容：</span>
                                <span class="text-orange-600">{{ file.change_description }}</span>
                            </div>
                            
                            <!-- 附件列表 -->
                            <div v-if="file.attachments && file.attachments.length > 0" class="mt-3">
                                <span class="text-sm font-medium text-gray-700">附件 ({{ file.attachments.length }})：</span>
                                <div class="flex flex-wrap gap-2 mt-1">
                                    <button v-for="attachment in file.attachments" :key="attachment.id"
                                            @click="handleDownloadFile(file, attachment)"
                                            class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm">
                                        <span class="mr-1">{{ fileManagementUtils.getFileTypeIcon(attachment.original_filename) }}</span>
                                        {{ attachment.original_filename }}
                                        <span class="ml-1 text-xs text-gray-500">({{ fileManagementUtils.formatFileSize(attachment.file_size) }})</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="flex flex-col space-y-2 ml-4">
                            <button @click="viewFileDetail(file)"
                                    class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                                查看详情
                            </button>
                            <button @click="deleteFile(file)"
                                    class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors">
                                删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">暂无文件</h3>
                <p class="mt-1 text-sm text-gray-500">还没有上传任何文件</p>
            </div>

            <!-- 分页 -->
            <div v-if="totalPages > 1" class="flex items-center justify-between mt-6">
                <div class="text-sm text-gray-700">
                    显示第 {{ (currentPage - 1) * itemsPerPage + 1 }} - {{ Math.min(currentPage * itemsPerPage, filteredFiles.length) }} 条，
                    共 {{ filteredFiles.length }} 条记录
                </div>
                
                <div class="flex space-x-2">
                    <button @click="goToPage(currentPage - 1)" :disabled="currentPage === 1"
                            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>
                    
                    <button v-for="page in Math.min(totalPages, 5)" :key="page"
                            @click="goToPage(page)"
                            :class="[
                                'px-3 py-1 border rounded text-sm',
                                page === currentPage 
                                    ? 'bg-blue-600 text-white border-blue-600' 
                                    : 'border-gray-300 hover:bg-gray-50'
                            ]">
                        {{ page }}
                    </button>
                    
                    <button @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages"
                            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>
                </div>
            </div>

            <!-- 文件详情模态框 -->
            <file-detail-modal
                :show="showDetailModal"
                :file="selectedFile"
                @close="closeDetailModal">
            </file-detail-modal>
        </div>
    `
};
