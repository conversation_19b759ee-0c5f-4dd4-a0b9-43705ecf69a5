/**
 * 数据库查询优化器
 * 提供查询性能监控、优化建议和批量操作支持
 */

const logger = require('./logger');

class QueryOptimizer {
    constructor() {
        this.queryStats = new Map();
        this.slowQueries = [];
        this.maxSlowQueries = 100;
        this.slowQueryThreshold = 100; // 100ms
    }

    /**
     * 记录查询性能
     */
    recordQuery(sql, duration, params = []) {
        const queryKey = this.normalizeQuery(sql);
        
        // 更新统计信息
        if (!this.queryStats.has(queryKey)) {
            this.queryStats.set(queryKey, {
                sql: queryKey,
                count: 0,
                totalDuration: 0,
                avgDuration: 0,
                minDuration: Infinity,
                maxDuration: 0,
                lastExecuted: null
            });
        }

        const stats = this.queryStats.get(queryKey);
        stats.count++;
        stats.totalDuration += duration;
        stats.avgDuration = stats.totalDuration / stats.count;
        stats.minDuration = Math.min(stats.minDuration, duration);
        stats.maxDuration = Math.max(stats.maxDuration, duration);
        stats.lastExecuted = new Date();

        // 记录慢查询
        if (duration > this.slowQueryThreshold) {
            this.recordSlowQuery(sql, duration, params);
        }
    }

    /**
     * 记录慢查询
     */
    recordSlowQuery(sql, duration, params) {
        const slowQuery = {
            sql,
            duration,
            params: params.slice(0, 10), // 只保留前10个参数
            timestamp: new Date(),
            stack: new Error().stack
        };

        this.slowQueries.unshift(slowQuery);
        
        // 限制慢查询记录数量
        if (this.slowQueries.length > this.maxSlowQueries) {
            this.slowQueries = this.slowQueries.slice(0, this.maxSlowQueries);
        }

        logger.warn(`慢查询检测: ${duration.toFixed(2)}ms`, {
            sql: this.normalizeQuery(sql),
            duration,
            params: params.slice(0, 5)
        });
    }

    /**
     * 标准化查询语句（移除参数值）
     */
    normalizeQuery(sql) {
        return sql
            .replace(/\s+/g, ' ')
            .replace(/\$\d+/g, '?')
            .replace(/'[^']*'/g, '?')
            .replace(/\d+/g, '?')
            .trim();
    }

    /**
     * 包装查询执行以进行性能监控
     */
    wrapQuery(statement, sql) {
        const originalAll = statement.all;
        const originalGet = statement.get;
        const originalRun = statement.run;

        statement.all = (...params) => {
            const start = performance.now();
            try {
                const result = originalAll.apply(statement, params);
                const duration = performance.now() - start;
                this.recordQuery(sql, duration, params);
                return result;
            } catch (error) {
                const duration = performance.now() - start;
                this.recordQuery(sql, duration, params);
                throw error;
            }
        };

        statement.get = (...params) => {
            const start = performance.now();
            try {
                const result = originalGet.apply(statement, params);
                const duration = performance.now() - start;
                this.recordQuery(sql, duration, params);
                return result;
            } catch (error) {
                const duration = performance.now() - start;
                this.recordQuery(sql, duration, params);
                throw error;
            }
        };

        statement.run = (...params) => {
            const start = performance.now();
            try {
                const result = originalRun.apply(statement, params);
                const duration = performance.now() - start;
                this.recordQuery(sql, duration, params);
                return result;
            } catch (error) {
                const duration = performance.now() - start;
                this.recordQuery(sql, duration, params);
                throw error;
            }
        };

        return statement;
    }

    /**
     * 批量插入优化
     */
    createBatchInsert(db, tableName, columns) {
        const placeholders = columns.map(() => '?').join(', ');
        const sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
        const statement = db.prepare(sql);

        return {
            /**
             * 执行批量插入
             */
            execute: (rows) => {
                const start = performance.now();
                const transaction = db.transaction(() => {
                    for (const row of rows) {
                        statement.run(...row);
                    }
                });

                try {
                    const result = transaction();
                    const duration = performance.now() - start;
                    this.recordQuery(`BATCH INSERT INTO ${tableName}`, duration, [`${rows.length} rows`]);
                    return result;
                } catch (error) {
                    const duration = performance.now() - start;
                    this.recordQuery(`BATCH INSERT INTO ${tableName}`, duration, [`${rows.length} rows`]);
                    throw error;
                }
            },

            /**
             * 执行批量插入（忽略冲突）
             */
            executeIgnore: (rows) => {
                const ignoreSql = sql.replace('INSERT INTO', 'INSERT OR IGNORE INTO');
                const ignoreStatement = db.prepare(ignoreSql);
                
                const start = performance.now();
                const transaction = db.transaction(() => {
                    for (const row of rows) {
                        ignoreStatement.run(...row);
                    }
                });

                try {
                    const result = transaction();
                    const duration = performance.now() - start;
                    this.recordQuery(`BATCH INSERT OR IGNORE INTO ${tableName}`, duration, [`${rows.length} rows`]);
                    return result;
                } catch (error) {
                    const duration = performance.now() - start;
                    this.recordQuery(`BATCH INSERT OR IGNORE INTO ${tableName}`, duration, [`${rows.length} rows`]);
                    throw error;
                }
            }
        };
    }

    /**
     * 批量更新优化
     */
    createBatchUpdate(db, tableName, setColumns, whereColumns) {
        const setClause = setColumns.map(col => `${col} = ?`).join(', ');
        const whereClause = whereColumns.map(col => `${col} = ?`).join(' AND ');
        const sql = `UPDATE ${tableName} SET ${setClause} WHERE ${whereClause}`;
        const statement = db.prepare(sql);

        return {
            execute: (rows) => {
                const start = performance.now();
                const transaction = db.transaction(() => {
                    for (const row of rows) {
                        statement.run(...row);
                    }
                });

                try {
                    const result = transaction();
                    const duration = performance.now() - start;
                    this.recordQuery(`BATCH UPDATE ${tableName}`, duration, [`${rows.length} rows`]);
                    return result;
                } catch (error) {
                    const duration = performance.now() - start;
                    this.recordQuery(`BATCH UPDATE ${tableName}`, duration, [`${rows.length} rows`]);
                    throw error;
                }
            }
        };
    }

    /**
     * 获取查询统计信息
     */
    getQueryStats() {
        const stats = Array.from(this.queryStats.values())
            .sort((a, b) => b.totalDuration - a.totalDuration)
            .slice(0, 20); // 返回前20个最耗时的查询

        return {
            totalQueries: Array.from(this.queryStats.values()).reduce((sum, stat) => sum + stat.count, 0),
            uniqueQueries: this.queryStats.size,
            slowQueries: this.slowQueries.length,
            topQueries: stats
        };
    }

    /**
     * 获取慢查询列表
     */
    getSlowQueries(limit = 10) {
        return this.slowQueries.slice(0, limit);
    }

    /**
     * 生成优化建议
     */
    generateOptimizationSuggestions() {
        const suggestions = [];
        const stats = Array.from(this.queryStats.values());

        // 检查频繁执行的查询
        const frequentQueries = stats
            .filter(stat => stat.count > 100)
            .sort((a, b) => b.count - a.count);

        frequentQueries.forEach(stat => {
            if (stat.avgDuration > 50) {
                suggestions.push({
                    type: 'INDEX_SUGGESTION',
                    query: stat.sql,
                    reason: `查询执行 ${stat.count} 次，平均耗时 ${stat.avgDuration.toFixed(2)}ms`,
                    suggestion: '考虑为相关字段添加索引'
                });
            }
        });

        // 检查慢查询
        const recentSlowQueries = this.slowQueries.slice(0, 10);
        recentSlowQueries.forEach(slowQuery => {
            suggestions.push({
                type: 'SLOW_QUERY',
                query: this.normalizeQuery(slowQuery.sql),
                reason: `查询耗时 ${slowQuery.duration.toFixed(2)}ms`,
                suggestion: '检查查询逻辑和索引使用'
            });
        });

        // 检查N+1查询问题
        const possibleN1Queries = stats.filter(stat => 
            stat.count > 50 && 
            (stat.sql.includes('SELECT') && stat.sql.includes('WHERE'))
        );

        possibleN1Queries.forEach(stat => {
            suggestions.push({
                type: 'N_PLUS_1_WARNING',
                query: stat.sql,
                reason: `可能存在N+1查询问题，执行了 ${stat.count} 次`,
                suggestion: '考虑使用JOIN或批量查询优化'
            });
        });

        return suggestions;
    }

    /**
     * 重置统计信息
     */
    reset() {
        this.queryStats.clear();
        this.slowQueries = [];
        logger.info('查询统计信息已重置');
    }
}

// 创建全局查询优化器实例
const queryOptimizer = new QueryOptimizer();

module.exports = {
    QueryOptimizer,
    queryOptimizer
};
