# 侧边栏导航配置文档

## 概述

本文档详细说明了智能排程系统中所有页面在侧边栏中的组织结构和导航配置。

## 侧边栏菜单结构

### 🔧 设备管理

设备相关功能，包含智能排程相关的产能管理：

#### 1. 设备信息 (`/equipment/info`)
- **功能**: 设备基本信息管理

#### 2. 设备维护 (`/equipment/maintenance`)
- **功能**: 设备维护记录管理

#### 3. 设备健康 (`/equipment/health`)
- **功能**: 设备健康状态监控

#### 4. 设备产能 (`/equipment/capacity`) (增强)
- **功能**: 设备产能配置和操作员技能管理
- **增强特性**:
  - 产能利用率分析
  - 智能排程集成状态
  - 产能利用率趋势图表
  - 性能统计概览

### 📅 生产排程管理 (重新组织)

生产排程管理现在是一个统一的一级菜单，包含传统排程、智能排程和产品管理功能：

#### 一级子菜单：

##### 1. 排程概览 (`/schedule/dashboard`)
- **功能**: 排程总体概览和统计
- **权限**: 需要 `schedule_view` 权限

##### 2. 排程列表 (`/schedule/list`)
- **功能**: 查看和管理所有排程
- **权限**: 需要 `schedule_view` 权限

#### 二级子菜单：

##### 🤖 智能排程管理
包含所有智能排程相关功能：

###### 1. 智能排程 (`/scheduling/intelligent`)
- **文件位置**: `frontend/pages/scheduling/intelligent.html`
- **脚本文件**: `frontend/scripts/pages/scheduling/intelligent.js`
- **功能**: 智能排程方案生成和管理
- **权限**: 需要 `scheduling_manage` 权限或管理员权限
- **特性**:
  - 订单信息输入
  - 多种优化策略的方案生成
  - 方案选择和确认

###### 2. 算法调优 (`/admin/algorithm-tuning`)
- **文件位置**: `frontend/pages/admin/algorithm-tuning.html`
- **脚本文件**: `frontend/scripts/pages/admin/algorithm-tuning.js`
- **功能**: 智能排程算法性能优化和参数调整
- **权限**: 仅管理员可访问
- **特性**:
  - 调优统计概览
  - 调优参数配置
  - 性能历史趋势图表
  - 基准测试
  - 自动调优执行

##### � 产品管理
包含所有产品相关功能：

###### 1. 产品信息管理 (`/product/management`)
- **文件位置**: `frontend/pages/product/management.html`
- **功能**: 产品基本信息管理
- **权限**: 需要 `product_view` 权限

###### 2. 工艺流程管理 (`/product/processes`)
- **文件位置**: `frontend/pages/product/processes.html`
- **功能**: 产品工艺流程管理
- **权限**: 需要 `product_edit` 权限

###### 3. 操作员技能管理 (`/operator/skills`)
- **文件位置**: `frontend/pages/operator/skills.html`
- **功能**: 操作员技能评估和管理
- **权限**: 需要 `operator_skill_manage` 权限

#### 一级子菜单（续）：

##### 3. 资源管理 (`/schedule/resources`)
- **功能**: 管理排程相关资源
- **权限**: 需要 `schedule_manage` 权限

##### 4. 数据分析 (`/schedule/reports`)
- **功能**: 排程数据分析和报告
- **权限**: 需要 `schedule_view` 权限

### 📦 产品管理

产品相关功能：

#### 1. 产品管理 (`/product/management`)
- **功能**: 产品信息管理

#### 2. 工艺流程 (`/product/processes`)
- **功能**: 产品工艺流程管理

## 导航逻辑

### 菜单展开逻辑

侧边栏会根据当前页面路径自动展开相应的菜单：

```javascript
// 生产排程管理菜单展开条件（包含所有相关页面）
if (currentPath.includes('schedule') ||
    currentPath.includes('scheduling') ||
    currentPath.includes('product') ||
    currentPath.includes('admin/algorithm-tuning')) {
    scheduleManagementExpanded.value = true;
}

// 智能排程子菜单展开条件
if (currentPath.includes('scheduling') || currentPath.includes('admin/algorithm-tuning')) {
    scheduleManagementExpanded.value = true;
    intelligentSchedulingExpanded.value = true;
}

// 产品管理子菜单展开条件
if (currentPath.includes('product')) {
    scheduleManagementExpanded.value = true;
    productManagementExpanded.value = true;
}

// 设备管理菜单展开条件
if (currentPath.includes('equipment')) {
    equipmentManagementExpanded.value = true;
}
```

### 权限控制

不同页面有不同的权限要求：

#### 生产排程管理
- **排程概览/列表**: 需要 `schedule_view` 权限
- **智能排程相关页面**: 需要 `scheduling_manage` 权限或管理员权限
- **算法调优页面**: 仅管理员可访问
- **产品管理页面**: 需要相应的产品管理权限（`product_view`, `product_edit`等）
- **操作员技能管理**: 需要 `operator_skill_manage` 权限
- **资源管理**: 需要 `resource_manage` 权限
- **数据分析**: 需要 `schedule_view` 权限

#### 其他模块
- **设备管理页面**: 需要相应的设备管理权限
- **申请管理页面**: 需要相应的申请管理权限
- **质量管理页面**: 需要相应的质量管理权限

## 页面间导航关系

### 生产排程管理工作流

#### 智能排程工作流
1. **生产排程管理** → **智能排程管理** → **智能排程** → **方案对比** → **交期预测** → **确认方案**
2. **生产排程管理** → **智能排程管理** → **智能排程** → **查看详情** → **交期预测**
3. **生产排程管理** → **智能排程管理** → **方案对比** → **选择方案** → **确认方案**

#### 产品管理工作流
1. **生产排程管理** → **产品管理** → **产品信息管理** → **工艺流程管理**
2. **生产排程管理** → **产品管理** → **操作员技能管理** → **技能评估**

#### 管理员工作流
1. **生产排程管理** → **智能排程管理** → **算法调优** → **执行调优** → **查看性能历史**
2. **生产排程管理** → **智能排程管理** → **算法调优** → **参数配置** → **基准测试**

#### 传统排程工作流
1. **生产排程管理** → **排程概览** → **排程列表** → **资源管理**
2. **生产排程管理** → **排程列表** → **数据分析** → **报告查看**

## 测试验证

可以使用以下命令测试所有页面的可访问性：

```bash
# 测试侧边栏导航
npm run test:navigation

# 完整集成测试
npm run test:integration
```

## 文件结构

```
frontend/
├── pages/
│   ├── scheduling/                 # 智能排程页面
│   │   ├── intelligent.html       # 智能排程
│   │   ├── compare.html           # 方案对比
│   │   └── delivery-prediction.html # 交期预测
│   ├── admin/                     # 管理员页面
│   │   └── algorithm-tuning.html  # 算法调优
│   ├── schedule/                  # 传统排程页面
│   │   ├── dashboard.html
│   │   ├── list.html
│   │   ├── create.html (已升级)
│   │   ├── resources.html
│   │   └── reports.html
│   ├── equipment/                 # 设备管理页面
│   │   ├── info.html
│   │   ├── maintenance.html
│   │   ├── health.html
│   │   └── capacity.html (已增强)
│   └── product/                   # 产品管理页面
│       ├── management.html
│       └── processes.html
├── scripts/pages/
│   ├── scheduling/                # 智能排程脚本
│   │   ├── intelligent.js
│   │   ├── compare.js
│   │   └── delivery-prediction.js
│   ├── admin/                     # 管理员脚本
│   │   └── algorithm-tuning.js
│   └── ...                       # 其他页面脚本
└── components/
    ├── common/
    │   └── Sidebar.js             # 侧边栏组件 (已更新)
    └── scheduling/                # 智能排程组件
        ├── PlanComparisonCard.js
        ├── DeliveryPredictionChart.js
        ├── ProductionTimeline.js
        ├── RiskAssessment.js
        └── CapacityUtilization.js
```

## 注意事项

1. **权限检查**: 所有页面都会在加载时检查用户权限
2. **菜单状态**: 侧边栏会记住用户的菜单展开状态
3. **响应式设计**: 所有页面都支持移动端访问
4. **错误处理**: 页面加载失败时会有适当的错误提示
5. **性能优化**: 使用了懒加载和缓存机制

## 更新历史

- **2024-12-20**: 新增智能排程管理菜单和相关页面
- **2024-12-20**: 增强设备产能管理页面
- **2024-12-20**: 升级传统排程创建页面为智能排程界面
- **2024-12-20**: 新增算法调优管理页面
