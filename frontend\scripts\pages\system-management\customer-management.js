/**
 * 系统管理 - 客户管理页面
 * 管理文件管理系统中的客户信息
 */

import { createAdminApp } from '../../common/pageInit.js';
import { getCustomers, createCustomer, updateCustomer, toggleCustomerStatus as toggleCustomerStatusAPI, deleteCustomer as deleteCustomerAPI } from '../../api/file-management.js';
import Sidebar from '../../../components/common/Sidebar.js';
import CustomerFormModal from '../../../components/system-management/CustomerFormModal.js';

createAdminApp({
    components: {
        Sidebar,
        CustomerFormModal
    },
    setup() {
        const { ref, reactive, computed, onMounted, watch } = Vue;
        
        // 页面状态
        const customers = ref([]);
        const loading = ref(false);
        const searchQuery = ref('');
        const statusFilter = ref('');
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        
        // 模态框状态
        const showAddModal = ref(false);
        const showEditModal = ref(false);
        const editingCustomer = ref(null);
        
        // 计算属性
        const filteredCustomers = computed(() => {
            let result = customers.value;
            
            // 搜索过滤
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                result = result.filter(customer => 
                    customer.customer_name.toLowerCase().includes(query) ||
                    (customer.contact_person && customer.contact_person.toLowerCase().includes(query)) ||
                    (customer.contact_email && customer.contact_email.toLowerCase().includes(query)) ||
                    (customer.description && customer.description.toLowerCase().includes(query))
                );
            }
            
            // 状态过滤
            if (statusFilter.value) {
                const isActive = statusFilter.value === 'active';
                result = result.filter(customer => customer.active === isActive);
            }
            
            return result;
        });
        
        const paginatedCustomers = computed(() => {
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return filteredCustomers.value.slice(start, end);
        });
        
        const totalPages = computed(() => {
            return Math.ceil(filteredCustomers.value.length / itemsPerPage.value);
        });
        
        // 生命周期
        onMounted(() => {
            loadCustomers();
        });
        
        // 监听搜索和筛选变化，重置页码
        watch([searchQuery, statusFilter], () => {
            currentPage.value = 1;
        });
        
        // 方法
        async function loadCustomers() {
            try {
                loading.value = true;
                const response = await getCustomers();
                if (response.success) {
                    customers.value = response.data || [];
                } else {
                    throw new Error(response.message || '获取客户列表失败');
                }
            } catch (error) {
                console.error('加载客户列表失败:', error);
                if (error.response && error.response.status === 403) {
                    alert('权限不足，无法访问客户管理功能');
                } else {
                    alert('加载客户列表失败: ' + error.message);
                }
                customers.value = [];
            } finally {
                loading.value = false;
            }
        }
        
        function editCustomer(customer) {
            editingCustomer.value = { ...customer };
            showEditModal.value = true;
        }
        
        async function handleSaveCustomer(customerData) {
            try {
                let response;
                let isEdit = showEditModal.value;

                if (isEdit) {
                    // 更新客户
                    response = await updateCustomer(editingCustomer.value.id, customerData);
                } else {
                    // 创建新客户
                    response = await createCustomer(customerData);
                }

                if (response.success) {
                    const message = isEdit ? '客户更新成功！' : '客户创建成功！';

                    if (isEdit) {
                        // 更新操作：直接更新列表中的数据，避免重新加载
                        const index = customers.value.findIndex(c => c.id === response.data.id);
                        if (index !== -1) {
                            // 使用splice确保Vue能检测到变化
                            customers.value.splice(index, 1, response.data);
                        }
                    } else {
                        // 创建操作：添加到列表开头并重置到第一页
                        customers.value = [response.data, ...customers.value];
                        currentPage.value = 1;
                    }

                    // 关闭模态框
                    closeModal();

                    // 显示成功消息
                    alert(message);
                } else {
                    throw new Error(response.message || '保存客户失败');
                }
            } catch (error) {
                console.error('保存客户失败:', error);
                alert('保存客户失败: ' + error.message);
            }
        }
        
        async function toggleCustomerStatus(customer) {
            const action = customer.active ? '停用' : '启用';
            if (!confirm(`确定要${action}客户 "${customer.customer_name}" 吗？`)) {
                return;
            }

            try {
                const response = await toggleCustomerStatusAPI(customer.id);
                if (response.success) {
                    alert(`客户${action}成功！`);

                    // 直接更新列表中的客户状态，避免重新加载
                    const index = customers.value.findIndex(c => c.id === customer.id);
                    if (index !== -1) {
                        customers.value.splice(index, 1, response.data);
                    }
                } else {
                    throw new Error(response.message || `${action}客户失败`);
                }
            } catch (error) {
                console.error(`${action}客户失败:`, error);
                alert(`${action}客户失败: ` + error.message);
            }
        }

        async function deleteCustomer(customer) {
            if (!confirm(`确定要删除客户 "${customer.customer_name}" 吗？\n\n注意：如果该客户下有文件记录，将无法删除。`)) {
                return;
            }

            try {
                const response = await deleteCustomerAPI(customer.id);
                if (response.success) {
                    alert('客户删除成功！');

                    // 从列表中移除已删除的客户
                    const index = customers.value.findIndex(c => c.id === customer.id);
                    if (index !== -1) {
                        customers.value.splice(index, 1);
                    }

                    // 如果当前页没有数据了，回到上一页
                    if (paginatedCustomers.value.length === 0 && currentPage.value > 1) {
                        currentPage.value--;
                    }
                } else {
                    throw new Error(response.message || '删除客户失败');
                }
            } catch (error) {
                console.error('删除客户失败:', error);
                alert('删除客户失败: ' + error.message);
            }
        }
        
        function closeModal() {
            showAddModal.value = false;
            showEditModal.value = false;
            editingCustomer.value = null;
        }
        
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        return {
            customers,
            loading,
            searchQuery,
            statusFilter,
            currentPage,
            itemsPerPage,
            showAddModal,
            showEditModal,
            editingCustomer,
            filteredCustomers,
            paginatedCustomers,
            totalPages,
            loadCustomers,
            editCustomer,
            handleSaveCustomer,
            toggleCustomerStatus,
            deleteCustomer,
            closeModal,
            formatDate
        };
    },
    onUserLoaded: async (user) => {
        console.log('客户管理页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
