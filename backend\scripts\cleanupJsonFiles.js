#!/usr/bin/env node

/**
 * 清理原始JSON数据文件脚本
 * 在确认SQLite迁移成功后，删除原始JSON文件
 */

const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

function backupAndDeleteJsonFiles() {
    const dataDir = path.join(__dirname, '..', 'data');
    const backupDir = path.join(dataDir, 'json_backup_' + Date.now());
    
    console.log('='.repeat(60));
    console.log('清理原始JSON数据文件');
    console.log('='.repeat(60));

    try {
        // 创建备份目录
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
            console.log(`创建备份目录: ${backupDir}`);
        }

        const filesToBackup = [
            'applications.json',
            'permission-templates.json',
            'users.json.bak'
        ];

        const directoriesToBackup = [
            'users'
        ];

        let backedUpCount = 0;
        let deletedCount = 0;

        // 备份和删除文件
        for (const fileName of filesToBackup) {
            const filePath = path.join(dataDir, fileName);
            if (fs.existsSync(filePath)) {
                const backupPath = path.join(backupDir, fileName);
                
                // 复制到备份目录
                fs.copyFileSync(filePath, backupPath);
                console.log(`备份文件: ${fileName}`);
                backedUpCount++;
                
                // 删除原文件
                fs.unlinkSync(filePath);
                console.log(`删除文件: ${fileName}`);
                deletedCount++;
            }
        }

        // 备份和删除目录
        for (const dirName of directoriesToBackup) {
            const dirPath = path.join(dataDir, dirName);
            if (fs.existsSync(dirPath)) {
                const backupDirPath = path.join(backupDir, dirName);
                
                // 递归复制目录
                copyDirectory(dirPath, backupDirPath);
                console.log(`备份目录: ${dirName}`);
                backedUpCount++;
                
                // 删除原目录
                removeDirectory(dirPath);
                console.log(`删除目录: ${dirName}`);
                deletedCount++;
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('JSON文件清理完成！');
        console.log('='.repeat(60));
        console.log(`备份项目数: ${backedUpCount}`);
        console.log(`删除项目数: ${deletedCount}`);
        console.log(`备份位置: ${backupDir}`);
        console.log('\n注意：备份文件已保存，如需恢复可从备份目录中获取。');

        logger.info('JSON文件清理完成', {
            backedUpCount,
            deletedCount,
            backupDir
        });

    } catch (error) {
        console.error('清理JSON文件失败:', error);
        logger.error('清理JSON文件失败:', error);
        throw error;
    }
}

/**
 * 递归复制目录
 */
function copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }

    const entries = fs.readdirSync(src, { withFileTypes: true });

    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);

        if (entry.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

/**
 * 递归删除目录
 */
function removeDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
        const entries = fs.readdirSync(dirPath, { withFileTypes: true });

        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);

            if (entry.isDirectory()) {
                removeDirectory(fullPath);
            } else {
                fs.unlinkSync(fullPath);
            }
        }

        fs.rmdirSync(dirPath);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    // 添加确认提示
    console.log('警告：此操作将删除所有原始JSON数据文件！');
    console.log('请确保SQLite数据库迁移已成功完成。');
    console.log('');
    console.log('如果您确定要继续，请在5秒内按 Ctrl+C 取消，否则将自动执行清理。');
    
    setTimeout(() => {
        try {
            backupAndDeleteJsonFiles();
        } catch (error) {
            console.error('脚本执行失败:', error);
            process.exit(1);
        }
    }, 5000);
}

module.exports = backupAndDeleteJsonFiles;
