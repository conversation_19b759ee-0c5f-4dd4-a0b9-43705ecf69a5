/**
 * 申请书模板样式
 * 包括A4纸张样式、表格样式、响应式设计和打印样式
 */

/* 申请书模板样式 */
.application-template {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #000;
    font-family: SimSun, serif;
    height: 100%;
}

.application-template.mobile {
    font-size: 14px;
}

.application-template.mobile-small {
    font-size: 12px;
}

.application-template th, .application-template td {
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
}

.application-template .title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    padding: 15px;
    height: 10%;
}

.application-template .label {
    width: 15%;
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
}

.application-template .content {
    height: 200px;
    vertical-align: top;
    text-align: left;
    padding: 10px;
}

.application-template .applicant {
    text-align: right;
    padding-right: 50px;
    height: 5%;
}

.application-template .approval {
    min-height: 100px;
    vertical-align: top;
    height: 15%;
}

/* A4纸张样式 */
.a4-page {
    width: 210mm;
    height: 297mm;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 10mm;
    box-sizing: border-box;
}

/* 审批容器样式 */
.approval-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.single-approval {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.approval-comment {
    font-size: 14px;
    color: #333;
    text-align: center;
    margin-bottom: 5px;
    font-weight: 500;
}

/* 审批签名区域样式 */
.approval-signatures-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 80px;
    gap: 10px;
}

/* 多签名布局 */
.multiple-signatures {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
}

.multiple-signatures .signature-container {
    flex: 0 0 auto;
    min-width: 120px;
    max-width: 160px;
}

/* 签名容器样式 */
.signature-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
}

/* 签名包装器 */
.signature-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

/* 签名图片样式 */
.signature-image {
    display: block;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    object-fit: contain;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

.signature-normal {
    max-width: 120px;
    max-height: 60px;
}

/* 签名信息样式 */
.signature-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    font-size: 12px;
    color: #666;
}

.approver-name {
    font-weight: 600;
    color: #333;
}

.approval-time {
    font-size: 11px;
    color: #888;
}

/* 无签名占位符 */
.no-signature {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 60px;
}

.signature-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
    padding: 10px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.no-signature-text {
    font-size: 11px;
    color: #999;
    font-style: italic;
}

.signature-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

/* 签名相关样式 */
.signature-image {
    max-width: 120px;
    max-height: 60px;
    margin-bottom: 5px;
}

.approval-date {
    font-size: 12px;
    color: #333;
    text-align: center;
    font-weight: 500;
}

.no-signature {
    color: #666;
    font-style: italic;
    font-size: 12px;
    margin-bottom: 5px;
    font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .a4-page {
        width: 100%;
        height: auto;
        padding: 10mm;
        transform-origin: top center;
        transform: scale(0.9);
        margin: 0 auto;
        box-sizing: border-box;
        aspect-ratio: 1 / 1.414; /* A4纸张比例 */
    }

    .application-template {
        width: 100%;
        height: 100%;
        font-size: 14px;
        table-layout: fixed;
    }

    .application-template .title {
        font-size: 20px;
        padding: 10px;
        height: 10%;
    }

    .application-template .content {
        height: 40%;
        min-height: 180px;
    }

    .application-template .approval {
        min-height: 90px;
        height: 15%;
    }

    .signature-image {
        max-width: 100px;
        max-height: 50px;
    }
}

@media (max-width: 480px) {
    .a4-page {
        transform: scale(0.7);
        padding: 8mm;
        aspect-ratio: 1 / 1.414;
    }

    .application-template {
        font-size: 12px;
    }

    .application-template .title {
        font-size: 18px;
        padding: 8px;
    }

    .application-template .content {
        height: 40%;
        min-height: 160px;
    }

    .application-template .approval {
        min-height: 80px;
        height: 15%;
    }

    .signature-image {
        max-width: 80px;
        max-height: 40px;
    }
}

/* 打印样式 */
@media print {
    @page {
        size: A4;
        margin: 0;
    }

    body * {
        visibility: hidden;
    }

    #applicationTemplateModal, #applicationTemplateModal * {
        visibility: visible;
    }

    #applicationTemplateModal {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: white;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        overflow: visible;
    }

    .a4-page {
        box-shadow: none;
        margin: 0;
        padding: 10mm;
        width: 210mm;
        height: 297mm;
        overflow: hidden;
        page-break-after: always;
    }

    .application-template {
        page-break-inside: avoid;
        width: 100%;
        height: 100%;
        border-collapse: collapse;
    }

    .print-hint, .template-actions {
        display: none !important;
    }
}

/* 模态框样式优化 */
#applicationTemplateModal {
    backdrop-filter: blur(2px);
}

#applicationTemplateModal .bg-white {
    max-height: 95vh;
    overflow: hidden;
}

#applicationTemplateModal .overflow-auto {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
}

#applicationTemplateModal .overflow-auto::-webkit-scrollbar {
    width: 6px;
}

#applicationTemplateModal .overflow-auto::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
}

#applicationTemplateModal .overflow-auto::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

#applicationTemplateModal .overflow-auto::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* 按钮样式优化 */
.template-actions button {
    transition: all 0.2s ease-in-out;
    font-weight: 500;
    min-width: 80px;
}

.template-actions button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.template-actions button:active {
    transform: translateY(0);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
