/**
 * 主页重定向脚本
 * 根据用户角色重定向到不同的默认首页
 */

// 检查当前路径
if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
    // 获取当前用户信息
    const userJson = sessionStorage.getItem('user');

    if (userJson) {
        try {
            const user = JSON.parse(userJson);
            const role = user.role ? user.role.toLowerCase() : '';
            const permissions = user.permissions || [];

            // 所有用户登录后都跳转到主页
            window.location.href = '/dashboard';
        } catch (error) {
            console.error('解析用户信息失败:', error);
            // 默认重定向到申请记录页面
            window.location.href = '/application-record';
        }
    } else {
        // 未登录用户重定向到登录页面
        window.location.href = '/login';
    }
}
