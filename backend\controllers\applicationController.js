/**
 * 申请控制器
 * 处理申请相关的请求
 */

const fs = require('fs');
const applicationService = require('../services/applicationService');

/**
 * 获取所有申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getAllApplications(req, res) {
    try {
        // 设置强制不缓存的响应头
        res.set({
            'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        });

        const applications = applicationService.getApplications();
        res.json(applications);
    } catch (error) {
        console.error('获取申请列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取申请列表失败: ' + error.message
        });
    }
}

/**
 * 获取单个申请详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getApplicationById(req, res) {
    try {
        const application = applicationService.getApplicationById(req.params.id);

        if (!application) {
            return res.status(404).json({
                success: false,
                message: '申请不存在'
            });
        }

        res.json(application);
    } catch (error) {
        console.error('获取申请详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取申请详情失败: ' + error.message
        });
    }
}

/**
 * 创建新申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function createApplication(req, res) {
    try {
        const { applicant, department, date, content, amount, priority, type, needDirectorApproval, selectedFactoryManagers } = req.body;

        // 验证必填字段
        if (!applicant || !date || !content) {
            return res.status(400).json({
                success: false,
                message: '缺少必填字段'
            });
        }

        // 解析选中的厂长信息
        let parsedFactoryManagers = [];
        if (selectedFactoryManagers) {
            try {
                parsedFactoryManagers = JSON.parse(selectedFactoryManagers);
            } catch (error) {
                console.error('解析厂长选择数据失败:', error);
                return res.status(400).json({
                    success: false,
                    message: '厂长选择数据格式错误'
                });
            }
        }

        // 验证标准申请必须选择厂长
        if (type === 'standard' && (!parsedFactoryManagers || parsedFactoryManagers.length === 0)) {
            return res.status(400).json({
                success: false,
                message: '标准申请必须选择至少一个厂长'
            });
        }

        const newApplication = await applicationService.createApplication(
            {
                applicant,
                department,
                date,
                content,
                amount, // 添加申请金额字段
                priority,
                type,
                needDirectorApproval: needDirectorApproval === 'true' || needDirectorApproval === true,
                selectedFactoryManagers: parsedFactoryManagers
            },
            req.files || [],
            req.user
        );

        res.status(201).json({
            success: true,
            id: newApplication.id
        });
    } catch (error) {
        console.error('创建申请失败:', error);
        res.status(500).json({
            success: false,
            message: '创建申请失败: ' + error.message
        });
    }
}

/**
 * 修改申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function updateApplication(req, res) {
    try {
        // 解析选中的厂长信息（如果有）
        let parsedFactoryManagers = [];
        if (req.body.selectedFactoryManagers) {
            try {
                parsedFactoryManagers = JSON.parse(req.body.selectedFactoryManagers);
            } catch (error) {
                console.error('解析厂长选择数据失败:', error);
                return res.status(400).json({
                    success: false,
                    message: '厂长选择数据格式错误'
                });
            }
        }

        // 准备更新数据
        const updateData = {
            ...req.body,
            selectedFactoryManagers: parsedFactoryManagers
        };

        const application = applicationService.updateApplication(
            req.params.id,
            updateData,
            req.user,
            req.files || []
        );

        res.json({
            success: true,
            message: '修改申请成功',
            application
        });
    } catch (error) {
        console.error('修改申请失败:', error);

        if (error.message === '申请不存在') {
            return res.status(404).json({
                success: false,
                message: error.message
            });
        }

        if (error.message === '没有权限修改此申请' || error.message === '申请已被审批，无法修改') {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: '修改申请失败: ' + error.message
        });
    }
}

/**
 * 删除申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function deleteApplication(req, res) {
    try {
        await applicationService.deleteApplication(req.params.id, req.user);
        res.json({ success: true });
    } catch (error) {
        // 静默处理删除错误，只记录到日志

        if (error.message === '申请不存在') {
            return res.status(404).json({
                success: false,
                message: error.message
            });
        }

        if (error.message === '没有权限删除此申请' || error.message === '申请已被审批，无法删除') {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: '删除申请失败: ' + error.message
        });
    }
}

/**
 * 下载附件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function downloadAttachment(req, res) {
    try {
        const result = applicationService.findAttachment(req.params.id);

        if (!result) {
            return res.status(404).json({
                success: false,
                message: '附件不存在'
            });
        }

        const { attachment } = result;

        // 检查文件是否存在
        if (!fs.existsSync(attachment.path)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        // 设置响应头，确保中文文件名正确显示
        const filename = attachment.name;
        const encodedFilename = encodeURIComponent(filename);

        // 设置Content-Disposition头，支持中文文件名
        res.setHeader('Content-Disposition',
            `attachment; filename="${filename}"; filename*=UTF-8''${encodedFilename}`);
        res.setHeader('Content-Type', attachment.type || 'application/octet-stream');
        res.setHeader('Cache-Control', 'no-cache');

        // 发送文件
        res.sendFile(attachment.path);

        // 记录下载日志到文件，不在终端显示
        logger.info(`申请附件下载成功: ${attachment.name}`, {
            attachmentId: req.params.id,
            userId: req.user.id
        });
    } catch (error) {
        logger.error('下载附件失败:', error);
        res.status(500).json({
            success: false,
            message: '下载附件失败: ' + error.message
        });
    }
}

/**
 * 获取待审核申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getPendingApplications(req, res) {
    try {
        // 设置强制不缓存的响应头
        res.set({
            'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        });

        const applications = applicationService.getPendingApplications(req.user);
        res.json(applications);
    } catch (error) {
        console.error('获取待审核申请列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取待审核申请列表失败: ' + error.message
        });
    }
}

/**
 * 获取已审核申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getApprovedApplications(req, res) {
    try {
        // 设置强制不缓存的响应头
        res.set({
            'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        });

        const applications = applicationService.getApprovedApplications(req.user);
        res.json(applications);
    } catch (error) {
        console.error('获取已审核申请列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取已审核申请列表失败: ' + error.message
        });
    }
}

/**
 * 获取申请记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getApplicationRecords(req, res) {
    try {
        // 设置强制不缓存的响应头
        res.set({
            'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Last-Modified': new Date().toUTCString(),
            'ETag': `"${Date.now()}-${Math.random()}"`
        });

        const applications = applicationService.getApplicationRecords(req.user);
        res.json(applications);
    } catch (error) {
        console.error('获取申请记录失败:', error);
        res.status(500).json({
            success: false,
            message: '获取申请记录失败: ' + error.message
        });
    }
}

/**
 * 审批通过申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function approveApplication(req, res) {
    try {
        const { comment, needManagerApproval, selectedManagers, needCeoApproval } = req.body;
        const approvalData = {
            comment,
            needManagerApproval: needManagerApproval === 'true' || needManagerApproval === true,
            selectedManagers: selectedManagers || [],
            needCeoApproval: needCeoApproval !== 'false' && needCeoApproval !== false // 默认为true
        };

        const application = await applicationService.approveApplication(
            req.params.id,
            approvalData,
            req.user
        );

        res.json({
            success: true,
            message: '审批通过成功',
            application
        });
    } catch (error) {
        console.error('审批通过失败:', error);

        if (error.message === '申请不存在') {
            return res.status(404).json({
                success: false,
                message: error.message
            });
        }

        if (error.message === '没有权限审批此申请' || error.message === '该申请已完成审批') {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: '审批通过失败: ' + error.message
        });
    }
}

/**
 * 审批拒绝申请
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function rejectApplication(req, res) {
    try {
        const { comment } = req.body;
        const rejectionData = { comment };

        const application = await applicationService.rejectApplication(
            req.params.id,
            rejectionData,
            req.user
        );

        res.json({
            success: true,
            message: '审批拒绝成功',
            application
        });
    } catch (error) {
        console.error('审批拒绝失败:', error);

        if (error.message === '申请不存在') {
            return res.status(404).json({
                success: false,
                message: error.message
            });
        }

        if (error.message === '没有权限审批此申请' || error.message === '该申请已完成审批') {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: '审批拒绝失败: ' + error.message
        });
    }
}

module.exports = {
    getAllApplications,
    getApplicationById,
    createApplication,
    updateApplication,
    deleteApplication,
    downloadAttachment,
    getPendingApplications,
    getApprovedApplications,
    getApplicationRecords,
    approveApplication,
    rejectApplication
};
