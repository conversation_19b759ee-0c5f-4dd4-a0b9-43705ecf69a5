/**
 * 日志中间件
 * 记录所有HTTP请求和响应
 *
 * 遵循有效日志原则:
 * 1. 添加关键操作的日志记录，方便问题排查
 * 2. 确保错误情况下有足够详细的日志信息
 * 3. 避免过度日志导致的性能问题
 */

const logger = require('../utils/logger');
const crypto = require('crypto');

/**
 * 生成请求ID
 * @returns {string} 请求ID
 */
function generateRequestId() {
    return crypto.randomBytes(8).toString('hex');
}

/**
 * HTTP请求日志中间件
 * 记录所有请求的详细信息和响应时间
 */
function requestLogger(req, res, next) {
    // 生成唯一请求ID
    req.requestId = req.headers['x-request-id'] || generateRequestId();

    // 将请求ID添加到响应头
    res.setHeader('X-Request-ID', req.requestId);

    // 记录请求开始时间
    const start = Date.now();

    // 只在详细日志模式下记录请求开始日志
    if (process.env.VERBOSE_LOGS === 'true' && req.url.startsWith('/api') && req.url !== '/api/health') {
        logger.debug(`开始处理请求: ${req.method} ${req.url}`, {
            requestId: req.requestId,
            method: req.method,
            url: req.url
        });
    }

    // 保存原始的 end 方法
    const originalEnd = res.end;

    // 重写 end 方法以在请求完成时记录日志
    res.end = function(chunk, encoding) {
        // 计算响应时间
        const responseTime = Date.now() - start;

        // 记录请求日志
        logger.logHttpRequest(req, res, responseTime);

        // 记录性能日志（仅对慢API请求）
        if (req.url.startsWith('/api') && responseTime > 500) {
            logger.logPerformance(`HTTP ${req.method} ${req.url}`, responseTime, {
                method: req.method,
                url: req.url,
                status: res.statusCode
            }, req.requestId);
        }

        // 调用原始的 end 方法
        return originalEnd.call(this, chunk, encoding);
    };

    next();
}

/**
 * 错误日志中间件
 * 记录所有未捕获的错误
 */
function errorLogger(err, req, res, next) {
    // 使用请求ID关联错误日志
    logger.logError('Express错误', err, {
        method: req.method,
        url: req.url,
        body: req.body,
        params: req.params,
        query: req.query,
        user: req.user,
        headers: {
            'user-agent': req.headers['user-agent'],
            'content-type': req.headers['content-type'],
            'accept': req.headers['accept'],
            'referer': req.headers['referer']
        }
    }, req.requestId);

    next(err);
}

module.exports = {
    requestLogger,
    errorLogger
};
