# 电子签名系统核心实现文档

## 系统概述

本文档描述了企业审批系统中电子签名在申请书模板对应位置显示的核心实现逻辑。系统采用Node.js + Express后端，HTML/CSS/JavaScript前端的架构。

## 核心功能

### 1. 电子签名存储机制

#### 数据结构
```javascript
// 用户数据结构 (users.json)
{
  "username": "张三",
  "role": "director",
  "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." // Base64编码的签名图片
}

// 申请数据结构 (applications.json)
{
  "id": 1742345334537,
  "applicant": "余焕新",
  "status": "已通过",
  "approvals": {
    "directors": {
      "李奇伟": {
        "status": "approved",
        "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "date": "2025-03-19T01:05:57.332Z"
      }
    },
    "chief": {
      "status": "approved", 
      "signature": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAAAAAAAD...",
      "date": "2025-03-24T06:16:03.875Z"
    }
  }
}
```

#### 签名获取优先级
1. 审批记录中的签名 (`app.approvals.directors[username].signature`)
2. 用户数据中的签名 (`user.signature`)
3. 显示"无签名"占位符

### 2. 审批流程设计

#### 多级审批架构
```
申请提交 → 厂长审批 → 总监审批 → 经理审批 → CEO审批 → 完成
```

#### 状态管理
- `待厂长审核` - 等待指定厂长审批
- `待总监审批` - 等待总监审批  
- `待经理审批` - 等待指定经理审批
- `待CEO审批` - 等待CEO最终审批
- `已通过` - 审批流程完成
- `已拒绝` - 申请被拒绝

### 3. 申请书模板生成

#### 核心模板结构
```html
<div class="application-template">
  <!-- 申请基本信息 -->
  <div class="application-header">
    <h1>申请书</h1>
    <div class="application-info">
      <p>申请人：${application.applicant}</p>
      <p>申请日期：${formatDate(application.date)}</p>
      <p>申请编号：${application.applicationCode}</p>
    </div>
  </div>

  <!-- 申请内容 -->
  <div class="application-content">
    <h3>申请内容：</h3>
    <div class="content-text">${application.content}</div>
  </div>

  <!-- 审批签名区域 -->
  <div class="approval-signatures">
    <!-- 厂长审批区域 -->
    <div class="approval-section">
      <h4>厂长核准：</h4>
      <div class="signature-area">${directorApproval}</div>
    </div>

    <!-- 总监审批区域 -->
    <div class="approval-section">
      <h4>总监核准：</h4>
      <div class="signature-area">${chiefApproval}</div>
    </div>

    <!-- 经理审批区域 -->
    <div class="approval-section">
      <h4>经理核准：</h4>
      <div class="signature-area">${managerApproval}</div>
    </div>
  </div>
</div>
```

### 4. 图片处理核心流程

#### 前端图片上传处理
```javascript
// 图片上传和Base64转换
function handleImageUpload(fileInput, previewElement) {
    const file = fileInput.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
        alert('只支持JPG和PNG格式的图片');
        fileInput.value = '';
        return;
    }

    // 验证文件大小 (限制5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('图片文件不能超过5MB');
        fileInput.value = '';
        return;
    }

    // 创建FileReader进行Base64转换
    const reader = new FileReader();
    reader.onload = function(e) {
        const base64String = e.target.result;

        // 图片压缩处理
        compressImage(base64String, (compressedBase64) => {
            // 显示预览
            if (previewElement) {
                previewElement.src = compressedBase64;
                previewElement.style.display = 'block';
            }

            // 保存到用户数据
            saveUserSignature(compressedBase64);
        });
    };

    reader.onerror = function() {
        alert('图片读取失败，请重试');
        fileInput.value = '';
    };

    reader.readAsDataURL(file);
}

// 图片压缩函数
function compressImage(base64String, callback, quality = 0.8) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = function() {
        // 设置最大尺寸
        const maxWidth = 300;
        const maxHeight = 150;

        let { width, height } = img;

        // 计算缩放比例
        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
        }

        // 设置画布尺寸
        canvas.width = width;
        canvas.height = height;

        // 绘制图片
        ctx.drawImage(img, 0, 0, width, height);

        // 转换为Base64
        const compressedBase64 = canvas.toDataURL('image/jpeg', quality);
        callback(compressedBase64);
    };

    img.src = base64String;
}

// 保存用户签名
async function saveUserSignature(base64String) {
    try {
        const response = await fetch('/updateSignature', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: currentUser,
                signature: base64String
            })
        });

        const result = await response.json();
        if (result.success) {
            alert('签名保存成功');
            // 更新本地用户数据
            updateLocalUserSignature(base64String);
        } else {
            alert('签名保存失败：' + result.message);
        }
    } catch (error) {
        console.error('保存签名失败:', error);
        alert('保存签名失败，请重试');
    }
}
```

#### 后端图片处理
```javascript
// 签名更新接口
app.post('/updateSignature', (req, res) => {
    const { username, signature } = req.body;

    try {
        // 验证Base64格式
        if (!validateBase64Image(signature)) {
            return res.status(400).json({
                success: false,
                message: '无效的图片格式'
            });
        }

        // 读取用户数据
        const users = JSON.parse(fs.readFileSync(usersFile, 'utf8'));
        const userIndex = users.findIndex(u => u.username === username);

        if (userIndex === -1) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 更新用户签名
        users[userIndex].signature = signature;

        // 保存到文件
        fs.writeFileSync(usersFile, JSON.stringify(users, null, 2));

        res.json({ success: true, message: '签名更新成功' });

    } catch (error) {
        console.error('更新签名失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器错误'
        });
    }
});

// Base64图片验证函数
function validateBase64Image(base64String) {
    if (!base64String || typeof base64String !== 'string') {
        return false;
    }

    // 检查Base64格式
    const base64Regex = /^data:image\/(jpeg|jpg|png);base64,/;
    if (!base64Regex.test(base64String)) {
        return false;
    }

    // 检查Base64数据长度
    const base64Data = base64String.split(',')[1];
    if (!base64Data || base64Data.length < 100) {
        return false;
    }

    return true;
}
```

### 5. 申请书模板中的签名显示

#### JavaScript核心函数
```javascript
// 生成申请书模板的核心函数 - 专注签名处理
function generateApplicationTemplate(application) {
    // 生成厂长审批签名 - 重点处理JPG/PNG图片显示
    let directorApproval = '';
    if (application.approvals && application.approvals.directors) {
        Object.entries(application.approvals.directors).forEach(([username, approval]) => {
            if (approval.status === 'approved' || approval.status === 'rejected') {
                // 多层级获取签名数据
                const signature = getSignatureWithFallback(approval.signature, username);

                directorApproval += `
                    <div class="single-approval">
                        <div class="signature-wrapper">
                            ${signature ?
                                generateSignatureImage(signature, username, '厂长') :
                                `<div class="no-signature">无签名</div>`
                            }
                            <div class="approval-date">${formatDate(approval.date)}</div>
                            <div class="approver-name">${username}</div>
                        </div>
                    </div>
                `;
            }
        });
    }

// 签名获取的多重保障机制
function getSignatureWithFallback(approvalSignature, username) {
    // 1. 优先使用审批记录中的签名
    if (approvalSignature && validateBase64Image(approvalSignature)) {
        return approvalSignature;
    }

    // 2. 从用户数据中获取签名
    const user = allUsers.find(u => u.username === username);
    if (user && user.signature && validateBase64Image(user.signature)) {
        return user.signature;
    }

    // 3. 从缓存中获取签名
    const cachedSignature = getSignatureFromCache(username);
    if (cachedSignature && validateBase64Image(cachedSignature)) {
        return cachedSignature;
    }

    return null;
}

// 生成签名图片HTML - 处理JPG/PNG显示优化
function generateSignatureImage(signature, username, role) {
    // 检测图片格式
    const isJPEG = signature.includes('data:image/jpeg') || signature.includes('data:image/jpg');
    const isPNG = signature.includes('data:image/png');

    // 根据格式应用不同的优化策略
    let imageStyle = 'max-width: 120px; max-height: 60px; margin-bottom: 5px;';

    if (isJPEG) {
        // JPEG图片优化：提高对比度，减少压缩伪影
        imageStyle += ' image-rendering: -webkit-optimize-contrast; filter: contrast(1.1) brightness(1.05);';
    } else if (isPNG) {
        // PNG图片优化：保持清晰度，适合透明背景
        imageStyle += ' image-rendering: crisp-edges; filter: contrast(1.05);';
    }

    return `
        <img
            src="${signature}"
            alt="${username}的签名"
            class="signature-image ${isJPEG ? 'jpeg-signature' : 'png-signature'}"
            style="${imageStyle}"
            loading="lazy"
            onerror="handleSignatureError(this, '${username}')"
            onload="optimizeSignatureDisplay(this)"
        >
    `;
}

// 签名图片加载错误处理
function handleSignatureError(img, username) {
    console.error(`签名图片加载失败: ${username}`);
    img.style.display = 'none';

    // 创建错误占位符
    const errorDiv = document.createElement('div');
    errorDiv.className = 'signature-error';
    errorDiv.textContent = '签名加载失败';
    errorDiv.style.cssText = 'color: #ff6b6b; font-size: 12px; font-style: italic;';

    img.parentNode.insertBefore(errorDiv, img.nextSibling);
}

// 签名显示优化
function optimizeSignatureDisplay(img) {
    // 图片加载完成后的优化处理
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;

    // 绘制图片并应用优化
    ctx.drawImage(img, 0, 0);

    // 如果是JPEG，应用去噪处理
    if (img.src.includes('jpeg') || img.src.includes('jpg')) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // 简单的对比度增强
        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, data[i] * 1.1);     // Red
            data[i + 1] = Math.min(255, data[i + 1] * 1.1); // Green
            data[i + 2] = Math.min(255, data[i + 2] * 1.1); // Blue
        }

        ctx.putImageData(imageData, 0, 0);
        img.src = canvas.toDataURL('image/jpeg', 0.95);
    }
}

    // 生成总监审批签名
    let chiefApproval = '';
    if (application.approvals && application.approvals.chief && 
        (application.approvals.chief.status === 'approved' || application.approvals.chief.status === 'rejected')) {
        
        const signature = application.approvals.chief.signature || getChiefSignature();
        
        chiefApproval = `
            <div class="signature-wrapper">
                ${signature ? 
                    `<img src="${signature}" alt="总监签名" class="signature-image">` :
                    `<div class="no-signature">无签名</div>`
                }
                <div class="approval-date">${formatDate(application.approvals.chief.date)}</div>
            </div>
        `;
    }

    // 生成经理审批签名（类似逻辑）
    let managerApproval = generateManagerApproval(application);

    // 组装完整模板
    const template = `
        <div class="application-template">
            ${generateHeader(application)}
            ${generateContent(application)}
            <div class="approval-signatures">
                <div class="approval-section">
                    <h4>厂长核准：</h4>
                    ${directorApproval}
                </div>
                <div class="approval-section">
                    <h4>总监核准：</h4>
                    ${chiefApproval}
                </div>
                <div class="approval-section">
                    <h4>经理核准：</h4>
                    ${managerApproval}
                </div>
            </div>
        </div>
    `;

    return template;
}

// 获取用户签名的辅助函数
function getUserSignature(username) {
    const user = allUsers.find(u => u.username === username);
    return user ? user.signature : null;
}

// 日期格式化函数
function formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
}
```

### 5. CSS样式设计

#### 签名显示样式
```css
/* 审批容器样式 */
.approval-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 10px 0;
}

.single-approval {
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 5px;
    min-width: 150px;
    text-align: center;
    font-size: 14px;
}

.signature-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 签名图片样式 - 针对JPG/PNG优化 */
.signature-image {
    max-width: 120px;
    max-height: 60px;
    margin-bottom: 5px;
    border: 1px solid #eee;
    border-radius: 3px;
    display: block;
    object-fit: contain;
    background-color: #fff;
}

/* JPEG签名特殊优化 */
.signature-image.jpeg-signature {
    image-rendering: -webkit-optimize-contrast;
    filter: contrast(1.1) brightness(1.05);
    /* 减少JPEG压缩伪影 */
}

/* PNG签名特殊优化 */
.signature-image.png-signature {
    image-rendering: crisp-edges;
    filter: contrast(1.05);
    /* 保持PNG透明度和清晰度 */
}

/* 签名加载错误样式 */
.signature-error {
    color: #ff6b6b;
    font-size: 12px;
    font-style: italic;
    text-align: center;
    padding: 10px;
    border: 1px dashed #ff6b6b;
    border-radius: 3px;
    background-color: #fff5f5;
}

/* 无签名占位符样式 */
.no-signature {
    color: #666;
    font-style: italic;
    font-size: 12px;
    margin-bottom: 5px;
    font-weight: 500;
}

/* 审批日期样式 */
.approval-date {
    color: #666;
    font-size: 10px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .signature-image {
        max-width: 100px;
        max-height: 50px;
    }
    
    .no-signature {
        font-size: 11px;
    }
}

/* 打印样式 */
@media print {
    .signature-image {
        max-width: 120px;
        max-height: 60px;
        print-color-adjust: exact;
        -webkit-print-color-adjust: exact;
    }
}
```

### 6. 后端API接口

#### 签名获取接口
```javascript
// 获取所有用户签名信息
app.get('/signatures', (req, res) => {
    try {
        const users = JSON.parse(fs.readFileSync(usersFile, 'utf8'));
        const signatures = users.map(user => ({
            username: user.username,
            role: user.role,
            signature: user.signature || null
        }));
        res.json(signatures);
    } catch (error) {
        console.error('获取签名数据失败:', error);
        res.status(500).json({ success: false, message: '获取签名数据失败' });
    }
});

// 审批接口（包含签名处理）
app.post('/approve', (req, res) => {
    const { id, action, comment, signature, selectedManagers } = req.body;
    const username = req.body.username;
    
    try {
        const applications = JSON.parse(fs.readFileSync(applicationsFile, 'utf8'));
        const appIndex = applications.findIndex(app => app.id === parseInt(id));
        
        if (appIndex === -1) {
            return res.status(404).json({ success: false, message: '申请不存在' });
        }

        const app = applications[appIndex];
        
        // 根据用户角色处理审批
        if (userRole === 'director') {
            app.approvals.directors[username] = {
                status: action,
                comment: comment || '',
                date: new Date().toISOString(),
                signature: signature || '' // 保存签名
            };
        } else if (userRole === 'chief') {
            app.approvals.chief = {
                status: action,
                comment: comment || '',
                date: new Date().toISOString(),
                signature: signature || '' // 保存签名
            };
        }
        // ... 其他角色处理逻辑

        // 保存更新后的数据
        fs.writeFileSync(applicationsFile, JSON.stringify(applications, null, 2));
        
        res.json({ success: true, message: '审批成功' });
    } catch (error) {
        console.error('审批处理失败:', error);
        res.status(500).json({ success: false, message: '审批处理失败' });
    }
});
```

## 实现要点

### 1. 签名数据管理
- 使用Base64格式存储图片数据
- 支持多数据源签名获取
- 实现签名缓存机制

### 2. 模板动态生成
- 根据审批状态动态显示签名
- 支持多种设备适配
- 优化打印显示效果

### 3. 性能优化
- 图片懒加载
- 签名数据缓存
- 移动端优化

### 4. 安全考虑
- 输入数据验证
- XSS防护
- 文件上传限制

## 图片处理完整示例

### HTML文件上传界面
```html
<!-- 签名上传区域 -->
<div class="signature-upload-section">
    <label class="block text-gray-700 mb-2">
        电子签名 <span class="text-sm text-gray-500">(支持JPG/PNG格式，最大5MB)</span>
    </label>

    <input
        type="file"
        id="signatureInput"
        accept="image/jpeg,image/jpg,image/png"
        class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
        onchange="handleSignatureUpload(this)"
    >

    <!-- 预览区域 -->
    <div id="signaturePreview" class="mt-3 hidden">
        <div class="border rounded-lg p-3 bg-gray-50">
            <p class="text-sm text-gray-600 mb-2">签名预览：</p>
            <img
                id="signaturePreviewImage"
                src=""
                alt="签名预览"
                class="signature-preview-img"
                style="max-width: 200px; max-height: 100px; border: 1px solid #ddd;"
            >
            <div class="mt-2 flex space-x-2">
                <button
                    type="button"
                    onclick="saveSignature()"
                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                >
                    保存签名
                </button>
                <button
                    type="button"
                    onclick="clearSignature()"
                    class="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
                >
                    清除
                </button>
            </div>
        </div>
    </div>

    <!-- 错误提示 -->
    <div id="signatureError" class="mt-2 text-red-600 text-sm hidden"></div>
</div>
```

### 完整的图片处理JavaScript
```javascript
let currentSignatureBase64 = null;

// 处理签名上传
function handleSignatureUpload(input) {
    const file = input.files[0];
    const errorDiv = document.getElementById('signatureError');
    const previewDiv = document.getElementById('signaturePreview');

    // 清除之前的错误信息
    errorDiv.classList.add('hidden');
    previewDiv.classList.add('hidden');

    if (!file) {
        currentSignatureBase64 = null;
        return;
    }

    // 文件类型验证
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
        showError('只支持JPG和PNG格式的图片文件');
        input.value = '';
        return;
    }

    // 文件大小验证
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        showError('图片文件大小不能超过5MB');
        input.value = '';
        return;
    }

    // 显示加载状态
    showLoading('正在处理图片...');

    // 读取并处理图片
    const reader = new FileReader();
    reader.onload = function(e) {
        const originalBase64 = e.target.result;

        // 压缩和优化图片
        processSignatureImage(originalBase64, (processedBase64) => {
            currentSignatureBase64 = processedBase64;
            showPreview(processedBase64);
            hideLoading();
        });
    };

    reader.onerror = function() {
        showError('图片读取失败，请重试');
        input.value = '';
        hideLoading();
    };

    reader.readAsDataURL(file);
}

// 图片处理核心函数
function processSignatureImage(base64String, callback) {
    const img = new Image();

    img.onload = function() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 计算最佳尺寸
        const maxWidth = 300;
        const maxHeight = 150;
        let { width, height } = img;

        // 保持宽高比缩放
        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width = Math.floor(width * ratio);
            height = Math.floor(height * ratio);
        }

        // 设置画布
        canvas.width = width;
        canvas.height = height;

        // 白色背景（处理透明PNG）
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, width, height);

        // 绘制图片
        ctx.drawImage(img, 0, 0, width, height);

        // 图片优化处理
        optimizeCanvasImage(ctx, width, height);

        // 转换为JPEG格式（统一格式，减小文件大小）
        const optimizedBase64 = canvas.toDataURL('image/jpeg', 0.9);

        callback(optimizedBase64);
    };

    img.onerror = function() {
        showError('图片格式不支持或文件损坏');
    };

    img.src = base64String;
}

// 画布图片优化
function optimizeCanvasImage(ctx, width, height) {
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;

    // 增强对比度和清晰度
    for (let i = 0; i < data.length; i += 4) {
        // 获取RGB值
        let r = data[i];
        let g = data[i + 1];
        let b = data[i + 2];

        // 对比度增强
        r = Math.min(255, Math.max(0, (r - 128) * 1.2 + 128));
        g = Math.min(255, Math.max(0, (g - 128) * 1.2 + 128));
        b = Math.min(255, Math.max(0, (b - 128) * 1.2 + 128));

        data[i] = r;
        data[i + 1] = g;
        data[i + 2] = b;
    }

    ctx.putImageData(imageData, 0, 0);
}

// 显示预览
function showPreview(base64String) {
    const previewDiv = document.getElementById('signaturePreview');
    const previewImg = document.getElementById('signaturePreviewImage');

    previewImg.src = base64String;
    previewDiv.classList.remove('hidden');
}

// 保存签名
async function saveSignature() {
    if (!currentSignatureBase64) {
        showError('请先选择签名图片');
        return;
    }

    showLoading('正在保存签名...');

    try {
        const response = await fetch('/updateSignature', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: currentUser,
                signature: currentSignatureBase64
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('签名保存成功！');
            // 更新本地数据
            updateLocalUserSignature(currentSignatureBase64);
        } else {
            showError('保存失败：' + result.message);
        }
    } catch (error) {
        console.error('保存签名失败:', error);
        showError('网络错误，请重试');
    } finally {
        hideLoading();
    }
}

// 清除签名
function clearSignature() {
    document.getElementById('signatureInput').value = '';
    document.getElementById('signaturePreview').classList.add('hidden');
    document.getElementById('signatureError').classList.add('hidden');
    currentSignatureBase64 = null;
}

// 显示错误信息
function showError(message) {
    const errorDiv = document.getElementById('signatureError');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
}

// 显示加载状态
function showLoading(message) {
    // 实现加载提示
    console.log(message);
}

// 隐藏加载状态
function hideLoading() {
    // 隐藏加载提示
}
```

## 关键代码示例

### 前端签名处理
```javascript
// 从申请数据中提取签名信息
function extractSignaturesFromApplications() {
    if (!applications || applications.length === 0) return;

    allUsers = allUsers || [];

    applications.forEach(app => {
        if (app.approvals) {
            // 提取厂长签名
            if (app.approvals.directors) {
                Object.entries(app.approvals.directors).forEach(([username, approval]) => {
                    if (approval.signature) {
                        const existingUser = allUsers.find(u => u.username === username);
                        if (existingUser) {
                            if (!existingUser.signature) {
                                existingUser.signature = approval.signature;
                            }
                        } else {
                            allUsers.push({
                                username: username,
                                role: 'director',
                                signature: approval.signature
                            });
                        }
                    }
                });
            }

            // 提取总监签名
            if (app.approvals.chief && app.approvals.chief.signature) {
                const chiefUsername = app.approvals.chief.username || 'chief';
                const existingChief = allUsers.find(u => u.username === chiefUsername || u.role === 'chief');

                if (existingChief) {
                    if (!existingChief.signature) {
                        existingChief.signature = app.approvals.chief.signature;
                    }
                } else {
                    allUsers.push({
                        username: chiefUsername,
                        role: 'chief',
                        signature: app.approvals.chief.signature
                    });
                }
            }
        }
    });
}

// 签名图片优化
function optimizeImage(img) {
    img.style.imageRendering = '-webkit-optimize-contrast';
    img.style.filter = 'contrast(1.05)';
    img.setAttribute('loading', 'lazy');
}
```

### 后端文件处理
```javascript
const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

const app = express();

// 配置文件上传
const upload = multer({
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB限制
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件'));
        }
    }
});

// 签名验证中间件
function validateSignature(signature) {
    if (!signature) return true; // 允许空签名

    // 检查是否为有效的base64图片格式
    const base64Regex = /^data:image\/(png|jpeg|jpg|gif);base64,/;
    return base64Regex.test(signature);
}
```

## 数据流程图

```
用户上传签名图片 → Base64编码 → 存储到用户数据
                                    ↓
审批操作 → 获取用户签名 → 保存到审批记录 → 模板生成时显示
                                    ↓
申请书模板 ← 动态获取签名 ← 多数据源签名查找
```

