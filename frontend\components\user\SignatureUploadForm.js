/**
 * 电子签名上传表单组件
 * 处理用户电子签名的上传和预览
 */

import { uploadSignature } from '../../scripts/api/user.js';
import { getCurrentUser } from '../../scripts/api/auth.js';

export default {
    props: {
        user: Object
    },
    emits: ['updated'],
    setup(props, { emit }) {
        const { ref, watch } = Vue;

        // 签名上传相关
        const fileInput = ref(null);
        const selectedFile = ref(null);
        const dragOver = ref(false);
        const signaturePreview = ref('');
        const isUploadingSignature = ref(false);

        // 监听用户数据变化
        watch(() => props.user, (newUser) => {
            if (newUser) {
                // 如果用户有签名，显示预览
                if (newUser.hasSignature && newUser.signaturePath) {
                    // 使用正确的路径格式，添加时间戳防止缓存
                    signaturePreview.value = `/${newUser.signaturePath}?t=${new Date().getTime()}`;
                } else {
                    signaturePreview.value = '';
                }
            }
        }, { immediate: true });

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            processFile(file);
        }

        // 处理文件拖放
        function handleFileDrop(event) {
            dragOver.value = false;
            const file = event.dataTransfer.files[0];
            processFile(file);
        }

        // 处理文件
        function processFile(file) {
            if (!file) return;

            // 验证文件类型 - 只支持JPG和PNG
            const validTypes = ['image/jpeg', 'image/png'];
            if (!validTypes.includes(file.type)) {
                alert('请上传有效的图片文件（仅支持JPG/PNG格式）');
                return;
            }

            // 验证文件大小
            if (file.size > 2 * 1024 * 1024) {
                alert('文件大小不能超过2MB');
                return;
            }

            selectedFile.value = file;

            // 创建预览
            const reader = new FileReader();
            reader.onload = (e) => {
                signaturePreview.value = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // 移除文件
        function removeFile() {
            selectedFile.value = null;
            // 恢复原始签名预览，添加时间戳防止缓存
            signaturePreview.value = props.user?.hasSignature ? `/${props.user.signaturePath}?t=${new Date().getTime()}` : '';
            if (fileInput.value) {
                fileInput.value.value = '';
            }
        }

        // 上传签名
        async function submitSignatureUpload() {
            if (!selectedFile.value) {
                alert('请先选择签名图片');
                return;
            }

            try {
                isUploadingSignature.value = true;

                const result = await uploadSignature(selectedFile.value);

                if (result.success) {
                    // 签名上传成功后，重新获取完整的用户信息
                    const updatedUser = await getCurrentUser();

                    alert('签名上传成功！');
                    // 传递完整的用户信息给父组件
                    emit('updated', updatedUser);

                    selectedFile.value = null;
                    if (fileInput.value) {
                        fileInput.value.value = '';
                    }
                } else {
                    alert('签名上传失败: ' + result.message);
                }
            } catch (error) {
                console.error('签名上传失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('签名上传失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                isUploadingSignature.value = false;
            }
        }

        return {
            fileInput,
            selectedFile,
            dragOver,
            signaturePreview,
            isUploadingSignature,
            handleFileSelect,
            handleFileDrop,
            removeFile,
            submitSignatureUpload
        };
    },
    template: `
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-6">电子签名</h2>

            <div class="max-w-md mx-auto">
                <div class="space-y-6">
                    <!-- 当前签名预览 -->
                    <div>
                        <label class="block text-gray-700 mb-3 font-medium">当前签名</label>
                        <div v-if="signaturePreview" class="border border-gray-200 rounded-md p-6 bg-gray-50 flex items-center justify-center">
                            <img :src="signaturePreview" alt="签名预览" class="max-h-40 max-w-full">
                        </div>
                        <div v-else class="border border-gray-200 rounded-md p-8 bg-gray-50 text-gray-500 text-center">
                            <svg class="w-12 h-12 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p>暂无签名</p>
                        </div>
                    </div>

                    <!-- 上传新签名 -->
                    <div>
                        <label class="block text-gray-700 mb-3 font-medium">上传新签名</label>
                        <input type="file" ref="fileInput" @change="handleFileSelect"
                               class="hidden"
                               accept=".jpg,.jpeg,.png">
                        <div id="signatureInputArea"
                             class="text-center cursor-pointer py-8 border-2 border-dashed border-gray-300 rounded-md hover:border-blue-400 hover:bg-blue-50 transition-colors duration-200"
                             @click="fileInput.click()"
                             @dragover.prevent="dragOver = true"
                             @dragleave.prevent="dragOver = false"
                             @drop.prevent="handleFileDrop"
                             :class="{'border-blue-400 bg-blue-50': dragOver}">
                            <svg class="w-10 h-10 mx-auto text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <p class="text-gray-600 font-medium">点击选择文件或拖拽文件到此区域</p>
                            <p class="text-xs text-gray-500 mt-2">支持格式：仅限JPG/PNG（最大2MB）</p>
                        </div>

                        <!-- 已选择文件预览 -->
                        <div v-if="selectedFile" class="mt-4 flex items-center justify-between p-3 bg-blue-50 rounded-md border border-blue-100">
                            <div class="flex items-center space-x-3">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <div>
                                    <span class="text-sm font-medium text-gray-700">{{ selectedFile.name }}</span>
                                    <span class="text-xs text-gray-500 ml-2">{{ (selectedFile.size/1024/1024).toFixed(2) }}MB</span>
                                </div>
                            </div>
                            <button @click.prevent="removeFile"
                                    class="text-gray-500 hover:text-red-500 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 上传按钮 -->
                    <div class="pt-2">
                        <button @click="submitSignatureUpload"
                                :disabled="isUploadingSignature || !selectedFile"
                                class="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200 flex items-center justify-center">
                            <svg v-if="isUploadingSignature" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ isUploadingSignature ? '上传中...' : '上传签名' }}
                        </button>
                    </div>

                    <!-- 签名说明 -->
                    <div class="mt-4 p-4 bg-blue-50 rounded-md text-sm text-blue-800">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium">电子签名说明</p>
                                <ul class="mt-1 list-disc list-inside text-xs text-blue-700 space-y-1">
                                    <li>电子签名将用于各类申请的审批流程</li>
                                    <li>建议上传清晰的个人签名图片</li>
                                    <li>仅支持JPG和PNG格式</li>
                                    <li>文件大小不超过2MB</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};
