/**
 * 产品API调用模块
 * 封装所有产品相关的API请求
 */

import { API_URL } from '../config.js';

/**
 * 产品API类
 */
class ProductAPI {
    /**
     * 获取产品列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} API响应
     */
    static async getProducts(params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const url = `${API_URL}/products${queryString ? '?' + queryString : ''}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取产品列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取产品详情
     * @param {string} id 产品ID
     * @returns {Promise<Object>} API响应
     */
    static async getProductById(id) {
        try {
            const response = await fetch(`${API_URL}/products/${id}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取产品详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建产品
     * @param {Object} productData 产品数据
     * @returns {Promise<Object>} API响应
     */
    static async createProduct(productData) {
        try {
            const response = await fetch(`${API_URL}/products`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建产品失败:', error);
            throw error;
        }
    }

    /**
     * 更新产品
     * @param {string} id 产品ID
     * @param {Object} productData 产品数据
     * @returns {Promise<Object>} API响应
     */
    static async updateProduct(id, productData) {
        try {
            const response = await fetch(`${API_URL}/products/${id}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('更新产品失败:', error);
            throw error;
        }
    }

    /**
     * 删除产品
     * @param {string} id 产品ID
     * @returns {Promise<Object>} API响应
     */
    static async deleteProduct(id) {
        try {
            const response = await fetch(`${API_URL}/products/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('删除产品失败:', error);
            throw error;
        }
    }

    /**
     * 创建/更新产品工艺流程
     * @param {string} id 产品ID
     * @param {Array} processes 工艺流程数据
     * @returns {Promise<Object>} API响应
     */
    static async createProductionProcesses(id, processes) {
        try {
            const response = await fetch(`${API_URL}/products/${id}/processes`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ processes })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建产品工艺流程失败:', error);
            throw error;
        }
    }

    /**
     * 获取产品选项列表（用于下拉选择）
     * @returns {Promise<Object>} API响应
     */
    static async getProductOptions() {
        try {
            const response = await fetch(`${API_URL}/products/options`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取产品选项失败:', error);
            throw error;
        }
    }

    /**
     * 获取产品统计信息
     * @returns {Promise<Object>} API响应
     */
    static async getProductStatistics() {
        try {
            const response = await fetch(`${API_URL}/products/statistics`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取产品统计信息失败:', error);
            throw error;
        }
    }
}

export default ProductAPI;
