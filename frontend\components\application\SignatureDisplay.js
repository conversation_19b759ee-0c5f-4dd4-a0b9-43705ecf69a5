/**
 * 电子签名显示组件
 * 用于在申请书模板中显示审批人的电子签名
 */

export default {
    props: {
        // 审批记录
        approvalRecord: Object,
        // 签名显示类型：single（单个签名）或 multiple（多个签名）
        displayType: {
            type: String,
            default: 'single'
        },
        // 签名大小
        signatureSize: {
            type: String,
            default: 'normal' // normal, small, large
        }
    },
    setup(props) {
        const { ref, computed, onMounted } = Vue;

        // 签名图片URL
        const signatureUrl = ref('');
        const isLoading = ref(false);
        const hasError = ref(false);

        // 计算签名样式类
        const signatureClass = computed(() => {
            const baseClass = 'signature-image';
            const sizeClass = {
                'small': 'signature-small',
                'normal': 'signature-normal', 
                'large': 'signature-large'
            }[props.signatureSize] || 'signature-normal';
            
            return `${baseClass} ${sizeClass}`;
        });

        // 获取审批人签名
        async function loadApproverSignature() {
            if (!props.approvalRecord || !props.approvalRecord.approverId) {
                return;
            }

            try {
                isLoading.value = true;
                hasError.value = false;

                // 调用API获取签名信息
                const apiUrl = `/api/users/${props.approvalRecord.approverId}/signature`;
                const authToken = sessionStorage.getItem('authToken');

                const response = await fetch(apiUrl, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.hasSignature) {
                        // 优先使用Base64数据，如果没有则使用文件路径
                        if (data.signatureBase64) {
                            signatureUrl.value = data.signatureBase64;
                        } else if (data.signaturePath) {
                            // 添加时间戳防止缓存，确保签名图片能正确显示
                            signatureUrl.value = `/${data.signaturePath}?t=${new Date().getTime()}`;
                        }
                    }
                } else {
                    console.warn('获取签名失败:', response.status);
                }
            } catch (error) {
                console.error('加载审批人签名失败:', error);
                hasError.value = true;
            } finally {
                isLoading.value = false;
            }
        }

        // 处理图片加载错误
        function handleImageError() {
            hasError.value = true;
            signatureUrl.value = '';
        }

        // 组件挂载时加载签名
        onMounted(() => {
            loadApproverSignature();
        });

        return {
            signatureUrl,
            isLoading,
            hasError,
            signatureClass,
            handleImageError
        };
    },
    template: `
        <div class="signature-container">
            <!-- 审批意见 -->
            <div v-if="approvalRecord && approvalRecord.comment" class="approval-comment">
                {{ approvalRecord.comment }}
            </div>
            
            <!-- 签名显示区域 -->
            <div class="signature-display-area">
                <!-- 加载中状态 -->
                <div v-if="isLoading" class="signature-loading">
                    <span class="loading-text">加载签名中...</span>
                </div>
                
                <!-- 签名图片 -->
                <div v-else-if="signatureUrl && !hasError" class="signature-wrapper">
                    <img 
                        :src="signatureUrl" 
                        :class="signatureClass"
                        :alt="approvalRecord ? approvalRecord.approverName + '的签名' : '电子签名'"
                        @error="handleImageError"
                        crossorigin="anonymous"
                    />
                    <!-- 审批人姓名和时间 -->
                    <div class="signature-info">
                        <div class="approver-name">{{ approvalRecord ? approvalRecord.approverName : '' }}</div>
                        <div class="approval-time">{{ approvalRecord ? new Date(approvalRecord.timestamp).toLocaleDateString('zh-CN') : '' }}</div>
                    </div>
                </div>
                
                <!-- 无签名或错误状态 -->
                <div v-else class="no-signature">
                    <div class="signature-placeholder">
                        <div class="approver-name">{{ approvalRecord ? approvalRecord.approverName : '' }}</div>
                        <div class="approval-time">{{ approvalRecord ? new Date(approvalRecord.timestamp).toLocaleDateString('zh-CN') : '' }}</div>
                        <div class="no-signature-text">（无电子签名）</div>
                    </div>
                </div>
            </div>
        </div>
    `
};
