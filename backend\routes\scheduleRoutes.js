/**
 * 生产排程路由
 * 定义排程相关的API端点
 */

const express = require('express');
const router = express.Router();
const scheduleController = require('../controllers/scheduleController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 排程管理路由

/**
 * 获取排程列表
 * GET /api/schedules
 * 权限: schedule_view
 */
router.get('/',
    authenticateJWT,
    checkPermission('schedule_view'),
    scheduleController.getSchedules.bind(scheduleController)
);

/**
 * 获取排程详情
 * GET /api/schedules/:id
 * 权限: schedule_view
 */
router.get('/:id',
    authenticateJWT,
    checkPermission('schedule_view'),
    scheduleController.getScheduleById.bind(scheduleController)
);

/**
 * 创建排程
 * POST /api/schedules
 * 权限: schedule_create
 */
router.post('/',
    authenticateJWT,
    checkPermission('schedule_create'),
    scheduleController.createSchedule.bind(scheduleController)
);

/**
 * 更新排程
 * PUT /api/schedules/:id
 * 权限: schedule_edit
 */
router.put('/:id',
    authenticateJWT,
    checkPermission('schedule_edit'),
    scheduleController.updateSchedule.bind(scheduleController)
);

/**
 * 删除排程
 * DELETE /api/schedules/:id
 * 权限: schedule_delete
 */
router.delete('/:id',
    authenticateJWT,
    checkPermission('schedule_delete'),
    scheduleController.deleteSchedule.bind(scheduleController)
);

// 排程状态操作路由

/**
 * 开始执行排程
 * POST /api/schedules/:id/start
 * 权限: schedule_execute
 */
router.post('/:id/start',
    authenticateJWT,
    checkPermission('schedule_execute'),
    scheduleController.startSchedule.bind(scheduleController)
);

/**
 * 暂停排程
 * POST /api/schedules/:id/pause
 * 权限: schedule_execute
 */
router.post('/:id/pause',
    authenticateJWT,
    checkPermission('schedule_execute'),
    scheduleController.pauseSchedule.bind(scheduleController)
);

/**
 * 完成排程
 * POST /api/schedules/:id/complete
 * 权限: schedule_execute
 */
router.post('/:id/complete',
    authenticateJWT,
    checkPermission('schedule_execute'),
    scheduleController.completeSchedule.bind(scheduleController)
);

/**
 * 取消排程
 * POST /api/schedules/:id/cancel
 * 权限: schedule_execute
 */
router.post('/:id/cancel',
    authenticateJWT,
    checkPermission('schedule_execute'),
    scheduleController.cancelSchedule.bind(scheduleController)
);

module.exports = router;
