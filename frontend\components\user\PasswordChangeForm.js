/**
 * 密码修改表单组件
 * 处理用户密码修改功能
 */

import { API_URL } from '../../scripts/config.js';
import { getCurrentUser } from '../../scripts/api/auth.js';

export default {
    props: {
        user: Object
    },
    emits: ['updated'],
    data() {
        return {
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
            isChangingPassword: false,
            errorMessage: ''
        };
    },
    computed: {
        passwordError() {
            if (this.newPassword && this.newPassword.length < 6) {
                return '新密码长度至少为6个字符';
            }
            if (this.newPassword && this.confirmPassword &&
                this.newPassword !== this.confirmPassword) {
                return '两次输入的密码不一致';
            }
            return '';
        },
        isFormValid() {
            return this.currentPassword &&
                   this.newPassword &&
                   this.confirmPassword &&
                   this.newPassword === this.confirmPassword &&
                   this.newPassword.length >= 6;
        }
    },
    methods: {
        async changePassword() {
            if (!this.isFormValid) {
                if (this.passwordError) {
                    this.errorMessage = this.passwordError;
                } else {
                    this.errorMessage = '请填写所有必填字段';
                }
                return;
            }

            this.errorMessage = '';
            this.isChangingPassword = true;

            try {
                // 获取认证令牌
                const token = sessionStorage.getItem('authToken');
                if (!token) {
                    throw new Error('未登录或会话已过期');
                }

                // 直接使用fetch API发送请求
                const response = await fetch(`${API_URL}/users/password`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        currentPassword: this.currentPassword,
                        newPassword: this.newPassword
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || '修改密码失败');
                }

                if (data.success) {
                    // 密码修改成功
                    alert('密码修改成功！您需要重新登录。');

                    // 清空表单
                    this.currentPassword = '';
                    this.newPassword = '';
                    this.confirmPassword = '';

                    // 清除认证令牌，强制用户登出
                    sessionStorage.removeItem('authToken');

                    // 延迟跳转到登录页面，给用户时间看到提示
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 1500);
                } else {
                    throw new Error(data.message || '修改密码失败');
                }
            } catch (error) {
                console.error('修改密码失败:', error);
                this.errorMessage = error.message || '修改密码失败，请稍后重试';

                if (error.status === 401 || error.status === 403) {
                    // 认证失败，跳转到登录页
                    sessionStorage.removeItem('authToken');
                    window.location.href = '/login';
                }
            } finally {
                this.isChangingPassword = false;
            }
        }
    },
    template: `
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-6">修改密码</h2>

            <div class="max-w-md mx-auto">
                <div class="space-y-6">
                    <div>
                        <label class="block text-gray-700 mb-2 font-medium">当前密码</label>
                        <input type="password" v-model="currentPassword" required
                               class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700">
                    </div>

                    <div>
                        <label class="block text-gray-700 mb-2 font-medium">新密码</label>
                        <input type="password" v-model="newPassword" required
                               class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700">
                        <p class="text-xs text-gray-500 mt-1">密码长度至少为6个字符</p>
                    </div>

                    <div>
                        <label class="block text-gray-700 mb-2 font-medium">确认新密码</label>
                        <input type="password" v-model="confirmPassword" required
                               class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700">
                    </div>

                    <div v-if="errorMessage" class="text-red-500 text-sm p-2 bg-red-50 rounded-md">
                        {{ errorMessage }}
                    </div>

                    <div class="pt-2">
                        <button type="button"
                                @click="changePassword"
                                :disabled="isChangingPassword"
                                class="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200 flex items-center justify-center">
                            <svg v-if="isChangingPassword" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ isChangingPassword ? '修改中...' : '修改密码' }}
                        </button>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-blue-50 rounded-md text-sm text-blue-800">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <p class="font-medium">密码安全提示</p>
                            <ul class="mt-1 list-disc list-inside text-xs text-blue-700 space-y-1">
                                <li>密码长度至少为6个字符</li>
                                <li>建议使用字母、数字和特殊字符的组合</li>
                                <li>请勿使用与其他网站相同的密码</li>
                                <li>定期更换密码可以提高账户安全性</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};
