/**
 * 生产时间轴组件
 * 用于显示生产进度和关键节点
 */

export default {
    name: 'ProductionTimeline',
    props: {
        timeline: {
            type: Array,
            required: true
        },
        showRisk: {
            type: Boolean,
            default: true
        }
    },
    template: `
        <div class="production-timeline">
            <div class="space-y-6">
                <div v-for="(milestone, index) in timeline" :key="milestone.id" 
                     :class="['timeline-item', { 'last-item': index === timeline.length - 1 }]">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <div :class="['timeline-dot', getStatusClass(milestone.status)]"></div>
                                <h4 class="font-medium text-gray-900 ml-4">{{ milestone.name }}</h4>
                            </div>
                            <div class="ml-8 mt-2">
                                <p class="text-sm text-gray-600">{{ milestone.description }}</p>
                                <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                    <span>预计时间: {{ formatDate(milestone.estimatedDate) }}</span>
                                    <span v-if="milestone.duration">耗时: {{ milestone.duration }} 天</span>
                                    <span v-if="milestone.actualDate" class="text-green-600">
                                        实际完成: {{ formatDate(milestone.actualDate) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div v-if="showRisk" class="flex items-center ml-4">
                            <span class="risk-indicator" :class="getRiskClass(milestone.riskLevel)"></span>
                            <span class="text-sm text-gray-600 ml-2">{{ getRiskText(milestone.riskLevel) }}</span>
                        </div>
                    </div>
                    
                    <!-- 进度条 -->
                    <div v-if="milestone.progress !== undefined" class="ml-8 mt-3">
                        <div class="flex justify-between text-xs text-gray-600 mb-1">
                            <span>进度</span>
                            <span>{{ milestone.progress }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div :class="['h-2 rounded-full', getProgressClass(milestone.progress)]" 
                                 :style="{ width: milestone.progress + '%' }"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,
    methods: {
        getStatusClass(status) {
            const classMap = {
                'pending': 'bg-gray-400',
                'in_progress': 'bg-blue-500',
                'completed': 'bg-green-500',
                'delayed': 'bg-red-500',
                'cancelled': 'bg-gray-300'
            };
            return classMap[status] || 'bg-gray-400';
        },

        getRiskClass(riskLevel) {
            const classMap = {
                'low': 'bg-green-500',
                'medium': 'bg-yellow-500',
                'high': 'bg-red-500'
            };
            return classMap[riskLevel] || 'bg-gray-400';
        },

        getRiskText(riskLevel) {
            const textMap = {
                'low': '低风险',
                'medium': '中风险',
                'high': '高风险'
            };
            return textMap[riskLevel] || '未知';
        },

        getProgressClass(progress) {
            if (progress >= 100) return 'bg-green-500';
            if (progress >= 75) return 'bg-blue-500';
            if (progress >= 50) return 'bg-yellow-500';
            return 'bg-gray-400';
        },

        formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }
    },
    style: `
        <style scoped>
        .timeline-item {
            position: relative;
            padding-left: 2rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0.5rem;
            width: 2px;
            height: calc(100% + 1.5rem);
            background-color: #e5e7eb;
        }
        
        .timeline-item.last-item::before {
            display: none;
        }
        
        .timeline-dot {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            position: absolute;
            left: -0.375rem;
            top: 0.125rem;
        }
        
        .risk-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        </style>
    `
};
