/**
 * 统一错误处理工具
 * 为工厂管理系统提供统一的错误处理和用户反馈机制
 */

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
    NETWORK: 'network',           // 网络错误
    AUTH: 'auth',                // 认证错误
    PERMISSION: 'permission',     // 权限错误
    VALIDATION: 'validation',     // 验证错误
    SERVER: 'server',            // 服务器错误
    TIMEOUT: 'timeout',          // 超时错误
    UNKNOWN: 'unknown'           // 未知错误
};

/**
 * 错误严重程度
 */
export const ERROR_SEVERITY = {
    LOW: 'low',       // 低级错误，不影响主要功能
    MEDIUM: 'medium', // 中级错误，影响部分功能
    HIGH: 'high',     // 高级错误，影响主要功能
    CRITICAL: 'critical' // 严重错误，系统无法正常使用
};

/**
 * 统一错误处理器类
 */
export class ErrorHandler {
    /**
     * 处理API错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文（操作描述）
     * @param {Object} options - 处理选项
     */
    static handleAPIError(error, context = '', options = {}) {
        const {
            showToast = true,
            logError = true,
            redirectOnAuth = true,
            customMessage = null
        } = options;

        // 分析错误类型和严重程度
        const errorInfo = this.analyzeError(error);
        
        // 记录错误日志
        if (logError) {
            this.logError(error, context, errorInfo);
        }

        // 处理特殊错误类型
        if (errorInfo.type === ERROR_TYPES.AUTH && redirectOnAuth) {
            this.handleAuthError(error, context);
            return;
        }

        // 显示用户友好的错误提示
        if (showToast) {
            const message = customMessage || this.getErrorMessage(errorInfo, context);
            this.showErrorToast(message, errorInfo.severity);
        }

        return errorInfo;
    }

    /**
     * 分析错误类型和严重程度
     * @param {Error} error - 错误对象
     * @returns {Object} 错误信息
     */
    static analyzeError(error) {
        let type = ERROR_TYPES.UNKNOWN;
        let severity = ERROR_SEVERITY.MEDIUM;
        let code = null;
        let message = error.message || '未知错误';

        // 网络错误
        if (!error.response) {
            type = ERROR_TYPES.NETWORK;
            severity = ERROR_SEVERITY.HIGH;
            if (error.code === 'ECONNABORTED' || message.includes('timeout')) {
                type = ERROR_TYPES.TIMEOUT;
                severity = ERROR_SEVERITY.MEDIUM;
            }
        } else {
            // HTTP错误
            const status = error.response.status;
            code = status;
            message = error.response.data?.message || message;

            switch (status) {
                case 401:
                    type = ERROR_TYPES.AUTH;
                    severity = ERROR_SEVERITY.HIGH;
                    break;
                case 403:
                    type = ERROR_TYPES.PERMISSION;
                    severity = ERROR_SEVERITY.MEDIUM;
                    break;
                case 400:
                case 422:
                    type = ERROR_TYPES.VALIDATION;
                    severity = ERROR_SEVERITY.LOW;
                    break;
                case 404:
                    type = ERROR_TYPES.SERVER;
                    severity = ERROR_SEVERITY.LOW;
                    break;
                case 429:
                    type = ERROR_TYPES.SERVER;
                    severity = ERROR_SEVERITY.MEDIUM;
                    break;
                case 500:
                case 502:
                case 503:
                case 504:
                    type = ERROR_TYPES.SERVER;
                    severity = ERROR_SEVERITY.HIGH;
                    break;
            }
        }

        return { type, severity, code, message, originalError: error };
    }

    /**
     * 获取用户友好的错误消息
     * @param {Object} errorInfo - 错误信息
     * @param {string} context - 操作上下文
     * @returns {string} 用户友好的错误消息
     */
    static getErrorMessage(errorInfo, context) {
        const { type, code, message } = errorInfo;
        const operation = context ? `${context}时` : '';

        switch (type) {
            case ERROR_TYPES.NETWORK:
                return `${operation}网络连接失败，请检查网络设置后重试`;
            
            case ERROR_TYPES.TIMEOUT:
                return `${operation}请求超时，请稍后重试`;
            
            case ERROR_TYPES.AUTH:
                return '登录已过期，请重新登录';
            
            case ERROR_TYPES.PERMISSION:
                return '您没有执行此操作的权限';
            
            case ERROR_TYPES.VALIDATION:
                return message || `${operation}数据验证失败，请检查输入信息`;
            
            case ERROR_TYPES.SERVER:
                if (code === 404) {
                    return `${operation}请求的资源不存在`;
                } else if (code === 429) {
                    return '操作过于频繁，请稍后再试';
                } else {
                    return `${operation}服务器错误，请稍后重试`;
                }
            
            default:
                return message || `${operation}操作失败，请重试`;
        }
    }

    /**
     * 处理认证错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    static handleAuthError(error, context) {
        console.warn(`认证失败 (${context}):`, error.message);
        
        // 清除认证信息
        sessionStorage.removeItem('authToken');
        sessionStorage.removeItem('user');
        
        // 保存当前页面路径，登录后可以返回
        if (window.location.pathname !== '/login') {
            sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
        }
        
        // 显示提示并重定向到登录页
        this.showErrorToast('登录已过期，即将跳转到登录页面', ERROR_SEVERITY.HIGH);
        
        setTimeout(() => {
            window.location.href = '/login';
        }, 1500);
    }

    /**
     * 显示错误提示
     * @param {string} message - 错误消息
     * @param {string} severity - 错误严重程度
     */
    static showErrorToast(message, severity = ERROR_SEVERITY.MEDIUM) {
        // 根据严重程度确定显示类型和持续时间
        let type = 'error';
        let duration = 4000;

        switch (severity) {
            case ERROR_SEVERITY.LOW:
                type = 'warning';
                duration = 3000;
                break;
            case ERROR_SEVERITY.MEDIUM:
                type = 'error';
                duration = 4000;
                break;
            case ERROR_SEVERITY.HIGH:
            case ERROR_SEVERITY.CRITICAL:
                type = 'error';
                duration = 6000;
                break;
        }

        // 使用全局通知函数
        if (window.showNotification) {
            window.showNotification(message, type, duration);
        } else {
            // 降级到alert
            alert(message);
        }
    }

    /**
     * 记录错误日志
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     * @param {Object} errorInfo - 错误信息
     */
    static logError(error, context, errorInfo) {
        const logData = {
            timestamp: new Date().toISOString(),
            context,
            type: errorInfo.type,
            severity: errorInfo.severity,
            code: errorInfo.code,
            message: errorInfo.message,
            url: window.location.href,
            userAgent: navigator.userAgent,
            stack: error.stack
        };

        // 根据严重程度选择日志级别
        switch (errorInfo.severity) {
            case ERROR_SEVERITY.LOW:
                console.warn('错误 (低级):', logData);
                break;
            case ERROR_SEVERITY.MEDIUM:
                console.error('错误 (中级):', logData);
                break;
            case ERROR_SEVERITY.HIGH:
            case ERROR_SEVERITY.CRITICAL:
                console.error('错误 (高级):', logData);
                break;
        }
    }

    /**
     * 创建API调用包装器
     * @param {Function} apiFunction - API函数
     * @param {string} context - 操作上下文
     * @param {Object} options - 错误处理选项
     * @returns {Function} 包装后的API函数
     */
    static wrapAPICall(apiFunction, context, options = {}) {
        return async (...args) => {
            try {
                return await apiFunction(...args);
            } catch (error) {
                this.handleAPIError(error, context, options);
                throw error; // 重新抛出错误，让调用者决定如何处理
            }
        };
    }

    /**
     * 显示成功提示
     * @param {string} message - 成功消息
     * @param {number} duration - 显示时长
     */
    static showSuccess(message, duration = 3000) {
        if (window.showNotification) {
            window.showNotification(message, 'success', duration);
        } else {
            alert(message);
        }
    }

    /**
     * 显示警告提示
     * @param {string} message - 警告消息
     * @param {number} duration - 显示时长
     */
    static showWarning(message, duration = 4000) {
        if (window.showNotification) {
            window.showNotification(message, 'warning', duration);
        } else {
            alert(message);
        }
    }

    /**
     * 显示信息提示
     * @param {string} message - 信息消息
     * @param {number} duration - 显示时长
     */
    static showInfo(message, duration = 3000) {
        if (window.showNotification) {
            window.showNotification(message, 'info', duration);
        } else {
            alert(message);
        }
    }
}

// 导出默认实例
export default ErrorHandler;
