/**
 * 排程总览页面逻辑
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ScheduleAPI from '../../api/schedule.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const statistics = ref({
            total: 0,
            planned: 0,
            inProgress: 0,
            completed: 0,
            cancelled: 0
        });
        const todaySchedules = ref([]);

        // 初始化
        onMounted(async () => {
            try {
                await checkAuth();
            } finally {
                // 确保加载指示器被隐藏
                hideLoading();
            }
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
                await loadDashboardData();
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
                throw error; // 重新抛出错误以便onMounted能够捕获
            }
        }

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                console.log('开始加载仪表板数据...');
                // 获取所有排程统计
                const allSchedulesResponse = await ScheduleAPI.getSchedules({ limit: 1000 });
                console.log('API响应:', allSchedulesResponse);

                if (allSchedulesResponse.success) {
                    const schedules = allSchedulesResponse.data.schedules;
                    console.log('获取到排程数据:', schedules);
                    calculateStatistics(schedules);
                    filterTodaySchedules(schedules);
                } else {
                    console.error('API返回失败:', allSchedulesResponse.message);
                    if (window.showNotification) {
                        window.showNotification(allSchedulesResponse.message || '获取数据失败', 'error');
                    }
                }
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                if (window.showNotification) {
                    window.showNotification('加载数据失败: ' + error.message, 'error');
                }
            }
        }

        // 计算统计数据
        function calculateStatistics(schedules) {
            statistics.value = {
                total: schedules.length,
                planned: schedules.filter(s => s.status === 'planned').length,
                inProgress: schedules.filter(s => s.status === 'in_progress').length,
                completed: schedules.filter(s => s.status === 'completed').length,
                cancelled: schedules.filter(s => s.status === 'cancelled').length
            };
        }

        // 筛选今日排程
        function filterTodaySchedules(schedules) {
            const today = new Date().toISOString().split('T')[0];
            todaySchedules.value = schedules.filter(schedule => {
                const scheduleDate = new Date(schedule.startTime).toISOString().split('T')[0];
                return scheduleDate === today;
            }).slice(0, 5); // 只显示前5个
        }

        // 刷新数据
        async function refreshData() {
            await loadDashboardData();
            if (window.showNotification) {
                window.showNotification('数据已刷新', 'success');
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusClasses = {
                planned: 'bg-blue-100 text-blue-800',
                in_progress: 'bg-green-100 text-green-800',
                paused: 'bg-yellow-100 text-yellow-800',
                completed: 'bg-gray-100 text-gray-800',
                cancelled: 'bg-red-100 text-red-800'
            };
            return statusClasses[status] || 'bg-gray-100 text-gray-800';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                planned: '计划中',
                in_progress: '执行中',
                paused: '已暂停',
                completed: '已完成',
                cancelled: '已取消'
            };
            return statusMap[status] || status;
        }

        // 格式化时间
        function formatTime(dateTimeString) {
            if (!dateTimeString) return '';
            const date = new Date(dateTimeString);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        return {
            currentUser,
            isAuthenticated,
            statistics,
            todaySchedules,
            refreshData,
            getStatusClass,
            getStatusText,
            formatTime
        };
    }
}).mount('#app');
