/**
 * 维修保养记录服务层
 * 处理维修保养记录相关的业务逻辑
 */

const MaintenanceRepository = require('../database/maintenanceRepository');
const EquipmentRepository = require('../database/equipmentRepository');
const { MaintenanceModel } = require('../models/maintenanceModel');
const database = require('../database/database');
const logger = require('../utils/logger');

class MaintenanceService {
    constructor() {
        this.maintenanceRepository = new MaintenanceRepository();
        this.equipmentRepository = new EquipmentRepository(database);
    }

    /**
     * 获取维修记录列表
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 查询结果
     */
    async getMaintenanceRecords(options = {}) {
        try {
            // 如果没有分页参数，返回所有数据
            if (!options.page && !options.limit) {
                const records = this.maintenanceRepository.findAll();
                const statistics = this.maintenanceRepository.getStatistics();
                
                return {
                    success: true,
                    data: {
                        records: records.map(record => MaintenanceModel.fromDatabase(record).toResponse()),
                        statistics
                    }
                };
            }

            // 分页查询
            const result = this.maintenanceRepository.findAllWithPagination(options);
            
            return {
                success: true,
                data: {
                    records: result.data.map(record => MaintenanceModel.fromDatabase(record).toResponse()),
                    pagination: result.pagination,
                    statistics: this.maintenanceRepository.getStatistics()
                }
            };
        } catch (error) {
            logger.error('获取维修记录列表失败', { error: error.message });
            return {
                success: false,
                message: '获取维修记录列表失败',
                error: error.message
            };
        }
    }

    /**
     * 根据ID获取维修记录详情
     * @param {string} id 记录ID
     * @returns {Promise<Object>} 查询结果
     */
    async getMaintenanceRecordById(id) {
        try {
            if (!id) {
                return {
                    success: false,
                    message: '记录ID不能为空'
                };
            }

            const record = this.maintenanceRepository.findById(id);
            
            if (!record) {
                return {
                    success: false,
                    message: '维修记录不存在'
                };
            }

            return {
                success: true,
                data: MaintenanceModel.fromDatabase(record).toResponse()
            };
        } catch (error) {
            logger.error('获取维修记录详情失败', { error: error.message, recordId: id });
            return {
                success: false,
                message: '获取维修记录详情失败',
                error: error.message
            };
        }
    }

    /**
     * 生成记录编号
     * @param {string} areaId 厂区ID
     * @param {string} date 日期 (YYYY-MM-DD)
     * @returns {string} 记录编号
     */
    generateRecordNumber(areaId, date) {
        // 格式: 厂区ID + 日期(YYYYMMDD) + 序号(001)
        const areaCode = areaId || 'XX';
        const dateCode = date ? date.replace(/-/g, '') : new Date().toISOString().split('T')[0].replace(/-/g, '');

        // 查询当天已有的记录数量
        const today = date || new Date().toISOString().split('T')[0];
        const existingRecords = this.maintenanceRepository.findByDate(today);
        const sequence = String(existingRecords.length + 1).padStart(3, '0');

        return `${areaCode}${dateCode}${sequence}`;
    }

    /**
     * 创建维修记录
     * @param {Object} recordData 记录数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createMaintenanceRecord(recordData, userId) {
        try {
            // 验证设备是否存在
            const equipment = this.equipmentRepository.findById(recordData.equipmentId);
            if (!equipment) {
                return {
                    success: false,
                    message: '指定的设备不存在'
                };
            }

            // 根据设备的厂区获取厂区信息
            let factory = this.equipmentRepository.findFactoryByName(equipment.area);
            let areaId;

            if (factory) {
                areaId = factory.id;
            } else {
                // 如果factories表中没有对应的厂区，使用厂区名称前两个字符作为ID
                areaId = equipment.area ? equipment.area.substring(0, 2).toUpperCase() : 'XX';
            }

            // 生成记录编号
            const recordId = this.generateRecordNumber(areaId, recordData.maintenanceDate);

            // 创建维修记录模型
            const maintenance = new MaintenanceModel({
                ...recordData,
                id: recordId
            });

            // 验证数据
            const validation = maintenance.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 创建记录
            const createdRecord = this.maintenanceRepository.create(maintenance);

            logger.info('维修记录创建成功', { 
                recordId: createdRecord.id, 
                equipmentId: createdRecord.equipment_id,
                type: createdRecord.type,
                createdBy: userId 
            });

            return {
                success: true,
                data: MaintenanceModel.fromDatabase(createdRecord).toResponse(),
                message: '维修记录创建成功'
            };
        } catch (error) {
            logger.error('创建维修记录失败', { error: error.message, userId });
            return {
                success: false,
                message: '创建维修记录失败',
                error: error.message
            };
        }
    }

    /**
     * 更新维修记录
     * @param {string} id 记录ID
     * @param {Object} updateData 更新数据
     * @param {string} userId 更新用户ID
     * @returns {Promise<Object>} 更新结果
     */
    async updateMaintenanceRecord(id, updateData, userId) {
        try {
            if (!id) {
                return {
                    success: false,
                    message: '记录ID不能为空'
                };
            }

            // 检查记录是否存在
            const existingRecord = this.maintenanceRepository.findById(id);
            if (!existingRecord) {
                return {
                    success: false,
                    message: '维修记录不存在'
                };
            }

            // 创建更新后的模型
            const maintenance = new MaintenanceModel({
                ...existingRecord,
                ...updateData,
                id: id,
                updatedAt: new Date().toISOString()
            });

            // 验证数据
            const validation = maintenance.validate();
            if (!validation.isValid) {
                logger.error('维修记录数据验证失败', {
                    recordId: id,
                    errors: validation.errors,
                    maintenanceData: maintenance
                });
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 如果更新了设备ID，验证设备是否存在
            if (updateData.equipmentId && updateData.equipmentId !== existingRecord.equipment_id) {
                const equipment = this.equipmentRepository.findById(maintenance.equipmentId);
                if (!equipment) {
                    return {
                        success: false,
                        message: '指定的设备不存在'
                    };
                }
            }

            // 更新记录
            const updatedRecord = this.maintenanceRepository.update(id, maintenance);

            logger.info('维修记录更新成功', { 
                recordId: id, 
                updatedBy: userId 
            });

            return {
                success: true,
                data: MaintenanceModel.fromDatabase(updatedRecord).toResponse(),
                message: '维修记录更新成功'
            };
        } catch (error) {
            logger.error('更新维修记录失败', { error: error.message, recordId: id, userId });
            return {
                success: false,
                message: '更新维修记录失败',
                error: error.message
            };
        }
    }

    /**
     * 删除维修记录
     * @param {string} id 记录ID
     * @param {string} userId 删除用户ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteMaintenanceRecord(id, userId) {
        try {
            if (!id) {
                return {
                    success: false,
                    message: '记录ID不能为空'
                };
            }

            // 检查记录是否存在
            const existingRecord = this.maintenanceRepository.findById(id);
            if (!existingRecord) {
                return {
                    success: false,
                    message: '维修记录不存在'
                };
            }

            // 检查是否可以删除（例如：进行中的维修不能删除）
            if (existingRecord.status === 'in_progress') {
                return {
                    success: false,
                    message: '进行中的维修记录不能删除'
                };
            }

            // 删除记录
            const deleted = this.maintenanceRepository.delete(id);
            
            if (!deleted) {
                return {
                    success: false,
                    message: '删除维修记录失败'
                };
            }

            logger.info('维修记录删除成功', { 
                recordId: id, 
                deletedBy: userId 
            });

            return {
                success: true,
                message: '维修记录删除成功'
            };
        } catch (error) {
            logger.error('删除维修记录失败', { error: error.message, recordId: id, userId });
            return {
                success: false,
                message: '删除维修记录失败',
                error: error.message
            };
        }
    }

    /**
     * 获取统计数据
     * @returns {Promise<Object>} 统计结果
     */
    async getStatistics() {
        try {
            const statistics = this.maintenanceRepository.getStatistics();
            
            return {
                success: true,
                data: statistics
            };
        } catch (error) {
            logger.error('获取维修记录统计数据失败', { error: error.message });
            return {
                success: false,
                message: '获取统计数据失败',
                error: error.message
            };
        }
    }

    /**
     * 获取维修类型和状态选项
     * @returns {Object} 选项数据
     */
    getOptions() {
        return {
            types: MaintenanceModel.getTypeOptions(),
            statuses: MaintenanceModel.getStatusOptions()
        };
    }

    /**
     * 获取维修保养记录选项数据
     * @returns {Object} 选项数据
     */
    async getMaintenanceOptions() {
        try {
            // 获取厂区列表
            let factories = this.equipmentRepository.findAllFactories();

            // 如果factories表没有数据，从equipment表的area字段获取
            if (!factories || factories.length === 0) {
                const filterOptions = this.equipmentRepository.getFilterOptions();
                factories = filterOptions.areas.map(area => ({
                    id: area.area.substring(0, 2).toUpperCase(), // 使用厂区名称前两个字符作为ID
                    name: area.area
                }));
            }

            // 获取设备筛选选项（位置等）
            const filterOptions = this.equipmentRepository.getFilterOptions();

            const options = {
                types: [
                    { value: 'maintenance', label: '保养' },
                    { value: 'repair', label: '维修' }
                ],
                statuses: [
                    { value: 'pending', label: '待处理' },
                    { value: 'in_progress', label: '进行中' },
                    { value: 'completed', label: '已完成' },
                    { value: 'cancelled', label: '已取消' }
                ],
                severityLevels: [
                    { value: 'low', label: '轻微' },
                    { value: 'medium', label: '中等' },
                    { value: 'high', label: '严重' },
                    { value: 'critical', label: '紧急' }
                ],
                areas: factories.map(factory => ({
                    value: factory.id,
                    label: factory.name
                })),
                locations: filterOptions.locations.map(location => ({
                    value: location.location,
                    label: location.location
                }))
            };

            return {
                success: true,
                data: options,
                message: '获取选项数据成功'
            };
        } catch (error) {
            logger.error('获取维修保养记录选项数据失败', { error: error.message });
            return {
                success: false,
                message: '获取选项数据失败: ' + error.message
            };
        }
    }
}

module.exports = MaintenanceService;
