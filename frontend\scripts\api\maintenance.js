/**
 * 维修保养记录API
 * 处理维修保养记录相关的API请求
 */

import { API_URL } from '/scripts/config.js';
import ErrorHandler from '../utils/errorHandler.js';

/**
 * 获取认证头信息
 * @returns {Object} 包含Authorization头的对象
 */
function getAuthHeaders() {
    const token = sessionStorage.getItem('authToken');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

/**
 * 获取维修记录列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 维修记录列表
 */
export async function getMaintenanceRecords(params = {}) {
    try {
        // 构建查询参数
        const queryParams = new URLSearchParams();
        
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== '') {
                queryParams.append(key, params[key]);
            }
        });

        // 添加时间戳参数强制绕过缓存
        queryParams.append('_t', Date.now());
        queryParams.append('_r', Math.random());

        const url = `${API_URL}/maintenance?${queryParams.toString()}`;

        const response = await axios.get(url, {
            headers: {
                ...getAuthHeaders(),
                // 添加防缓存请求头
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取维修记录列表');
        throw error;
    }
}

/**
 * 获取单个维修记录详情
 * @param {string} id 记录ID
 * @returns {Promise<Object>} 维修记录详情
 */
export async function getMaintenanceRecordById(id) {
    try {
        const response = await axios.get(`${API_URL}/maintenance/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取维修记录详情');
        throw error;
    }
}

/**
 * 创建维修记录
 * @param {Object} recordData 记录数据
 * @returns {Promise<Object>} 创建结果
 */
export async function createMaintenanceRecord(recordData) {
    try {
        const response = await axios.post(`${API_URL}/maintenance`, recordData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '创建维修记录');
        throw error;
    }
}

/**
 * 更新维修记录
 * @param {string} id 记录ID
 * @param {Object} updateData 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export async function updateMaintenanceRecord(id, updateData) {
    try {
        const response = await axios.put(`${API_URL}/maintenance/${id}`, updateData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '更新维修记录');
        throw error;
    }
}

/**
 * 删除维修记录
 * @param {string} id 记录ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteMaintenanceRecord(id) {
    try {
        const response = await axios.delete(`${API_URL}/maintenance/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除维修记录失败:', error);

        // 返回错误响应而不是抛出异常
        if (error.response && error.response.data) {
            return error.response.data;
        }

        return {
            success: false,
            message: '删除失败，请稍后重试'
        };
    }
}

/**
 * 获取统计数据
 * @returns {Promise<Object>} 统计数据
 */
export async function getMaintenanceStatistics() {
    try {
        const response = await axios.get(`${API_URL}/maintenance/statistics`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取维修记录统计数据失败:', error);
        throw error;
    }
}

/**
 * 获取选项数据（类型、状态等）
 * @returns {Promise<Object>} 选项数据
 */
export async function getMaintenanceOptions() {
    try {
        const response = await axios.get(`${API_URL}/maintenance/options`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取维修记录选项数据失败:', error);
        throw error;
    }
}

/**
 * 导出维修记录到Excel
 * @returns {Promise<Blob>} Excel文件Blob
 */
export async function exportMaintenanceRecordsToExcel() {
    try {
        const response = await axios.get(`${API_URL}/maintenance/export`, {
            headers: getAuthHeaders(),
            responseType: 'blob' // 重要：设置响应类型为blob
        });
        return response.data;
    } catch (error) {
        console.error('导出维修记录Excel失败:', error);
        throw error;
    }
}

/**
 * 批量导入维修记录
 * @param {Array} records 记录数组
 * @returns {Promise<Object>} 导入结果
 */
export async function importMaintenanceRecords(records) {
    try {
        const response = await axios.post(`${API_URL}/maintenance/import`, {
            records
        }, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('批量导入维修记录失败:', error);
        throw error;
    }
}

/**
 * 下载Excel文件
 * @param {Blob} blob Excel文件Blob
 * @param {string} filename 文件名
 */
export function downloadExcelFile(blob, filename = '维修保养记录.xlsx') {
    try {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('下载Excel文件失败:', error);
        throw error;
    }
}

/**
 * 格式化日期时间
 * @param {string} dateString 日期字符串
 * @returns {string} 格式化后的日期
 */
export function formatDateTime(dateString) {
    if (!dateString) return '-';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

/**
 * 格式化日期
 * @param {string} dateString 日期字符串
 * @returns {string} 格式化后的日期
 */
export function formatDate(dateString) {
    if (!dateString) return '-';

    try {
        // 如果是ISO格式的日期时间字符串，只取日期部分
        if (dateString.includes('T')) {
            dateString = dateString.split('T')[0];
        }

        // 如果已经是YYYY-MM-DD格式，直接返回
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            return dateString;
        }

        const date = new Date(dateString);
        // 使用toISOString()然后取日期部分，确保格式一致
        return date.toISOString().split('T')[0];
    } catch (error) {
        return dateString;
    }
}

/**
 * 获取状态标签样式
 * @param {string} status 状态
 * @returns {string} CSS类名
 */
export function getStatusClass(status) {
    const statusClasses = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'in_progress': 'bg-blue-100 text-blue-800',
        'completed': 'bg-green-100 text-green-800',
        'cancelled': 'bg-gray-100 text-gray-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
}

/**
 * 获取类型标签样式
 * @param {string} type 类型
 * @returns {string} CSS类名
 */
export function getTypeClass(type) {
    const typeClasses = {
        'maintenance': 'bg-green-100 text-green-800',
        'repair': 'bg-red-100 text-red-800'
    };
    return typeClasses[type] || 'bg-gray-100 text-gray-800';
}
