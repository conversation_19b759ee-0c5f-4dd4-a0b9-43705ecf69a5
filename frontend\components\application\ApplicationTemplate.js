/**
 * 申请书模板组件
 * 用于生成和显示申请书模板，支持PDF下载和电子签名显示
 */

import SignatureDisplay from './SignatureDisplay.js';

export default {
    components: {
        SignatureDisplay
    },
    props: {
        application: Object,
        visible: Boolean
    },
    emits: ['close'],
    setup(props, { emit }) {
        const { ref, computed, onMounted, watch } = Vue;

        // 格式化日期 (YYYY-MM-DD 转为 YYYY年MM月DD日)
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        }

        // 输入内容安全化
        function sanitizeInput(input) {
            if (!input) return '';
            return input.toString()
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#x27;');
        }

        // 关闭申请书模板
        function closeApplicationTemplate() {
            emit('close');
        }

        // 调整表格高度以填满A4页面
        function adjustTableHeight() {
            // 获取A4页面的高度
            const a4Page = document.getElementById('applicationTemplatePage');
            if (!a4Page) return;
            
            const a4Height = a4Page.clientHeight;

            // 获取表格
            const table = document.querySelector('.application-template');
            if (!table) return;

            // 设置表格高度为A4页面高度的90%
            table.style.height = (a4Height * 0.90) + 'px';

            // 获取内容行和其他行
            const contentRow = table.querySelector('tr:nth-child(3)');
            const otherRows = Array.from(table.querySelectorAll('tr')).filter(row => row !== contentRow);

            // 计算其他行的总高度
            let otherRowsHeight = 0;
            otherRows.forEach(row => {
                otherRowsHeight += row.offsetHeight;
            });

            // 计算内容行应该的高度
            const contentHeight = (a4Height * 0.90) - otherRowsHeight;

            // 设置内容行的高度
            if (contentRow && contentHeight > 100) {
                contentRow.style.height = contentHeight + 'px';
            }
        }

        // 加载PDF相关库
        function loadPdfLibraries() {
            return new Promise((resolve, reject) => {
                // 检查是否已经加载
                if (window.html2canvas && window.jspdf) {
                    resolve();
                    return;
                }

                let loadedCount = 0;
                const totalLibs = 2;

                function checkComplete() {
                    loadedCount++;
                    if (loadedCount === totalLibs) {
                        resolve();
                    }
                }

                // 加载html2canvas
                if (!window.html2canvas) {
                    const script1 = document.createElement('script');
                    script1.src = '/js/libs/html2canvas.min.js';
                    script1.onload = checkComplete;
                    script1.onerror = () => reject(new Error('Failed to load html2canvas'));
                    document.head.appendChild(script1);
                } else {
                    checkComplete();
                }

                // 加载jsPDF
                if (!window.jspdf) {
                    const script2 = document.createElement('script');
                    script2.src = '/js/libs/jspdf.umd.min.js';
                    script2.onload = checkComplete;
                    script2.onerror = () => reject(new Error('Failed to load jsPDF'));
                    document.head.appendChild(script2);
                } else {
                    checkComplete();
                }
            });
        }

        // 将签名动态插入到申请书模板中
        async function insertSignaturesToTemplate() {
            const approvals = getApprovalsByStage.value;
            console.log('开始插入签名，审批数据:', approvals); // 调试日志

            // 插入厂长签名
            if (approvals.factoryManagers.length > 0) {
                const factoryCell = document.getElementById('factoryApprovalCell');
                console.log('厂长审批单元格:', factoryCell); // 调试日志
                if (factoryCell) {
                    // 查找所有签名占位符
                    const placeholders = factoryCell.querySelectorAll('.signature-placeholder');
                    console.log('找到厂长签名占位符:', placeholders.length); // 调试日志
                    let placeholderIndex = 0;

                    for (const approval of approvals.factoryManagers) {
                        if (placeholderIndex < placeholders.length) {
                            const placeholder = placeholders[placeholderIndex];
                            const signatureWrapper = placeholder.parentElement;

                            // 替换占位符为实际签名
                            placeholder.remove();
                            const signatureElement = await createSignatureImageElement(approval);
                            signatureWrapper.insertBefore(signatureElement, signatureWrapper.firstChild);

                            placeholderIndex++;
                        }
                    }
                }
            }

            // 插入总监签名
            if (approvals.director) {
                const directorCell = document.getElementById('directorApprovalCell');
                console.log('总监审批单元格:', directorCell); // 调试日志
                if (directorCell) {
                    const placeholder = directorCell.querySelector('.signature-placeholder');
                    console.log('找到总监签名占位符:', placeholder); // 调试日志
                    if (placeholder) {
                        const signatureWrapper = placeholder.parentElement;
                        placeholder.remove();
                        const signatureElement = await createSignatureImageElement(approvals.director);
                        signatureWrapper.insertBefore(signatureElement, signatureWrapper.firstChild);
                    }
                }
            }

            // 插入经理和CEO签名
            if (approvals.managers.length > 0 || approvals.ceo) {
                const managerCell = document.getElementById('managerApprovalCell');
                console.log('经理审批单元格:', managerCell); // 调试日志
                if (managerCell) {
                    const placeholders = managerCell.querySelectorAll('.signature-placeholder');
                    console.log('找到经理签名占位符:', placeholders.length); // 调试日志
                    let placeholderIndex = 0;

                    // 添加经理签名
                    for (const approval of approvals.managers) {
                        if (placeholderIndex < placeholders.length) {
                            const placeholder = placeholders[placeholderIndex];
                            const signatureWrapper = placeholder.parentElement;
                            placeholder.remove();
                            const signatureElement = await createSignatureImageElement(approval);
                            signatureWrapper.insertBefore(signatureElement, signatureWrapper.firstChild);
                            placeholderIndex++;
                        }
                    }

                    // 添加CEO签名
                    if (approvals.ceo && placeholderIndex < placeholders.length) {
                        const placeholder = placeholders[placeholderIndex];
                        const signatureWrapper = placeholder.parentElement;
                        placeholder.remove();
                        const signatureElement = await createSignatureImageElement(approvals.ceo);
                        signatureWrapper.insertBefore(signatureElement, signatureWrapper.firstChild);
                    }
                }
            }
        }

        // 创建签名图片元素（用于替换占位符）
        async function createSignatureImageElement(approval) {
            try {
                const apiUrl = `/api/users/${approval.approverId}/signature`;

                const response = await fetch(apiUrl, {
                    headers: {
                        'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.hasSignature) {
                        const img = document.createElement('img');

                        // 优先使用Base64数据，如果没有则使用文件路径
                        if (data.signatureBase64) {
                            img.src = data.signatureBase64;
                        } else if (data.signaturePath) {
                            img.src = `/${data.signaturePath}?t=${new Date().getTime()}`;
                        } else {
                            throw new Error('没有可用的签名数据');
                        }

                        img.className = 'signature-image signature-normal';
                        img.alt = `${approval.approverName}的签名`;
                        img.style.maxWidth = '120px';
                        img.style.maxHeight = '60px';
                        img.style.objectFit = 'contain';

                        // 等待图片加载完成
                        await new Promise((resolve, reject) => {
                            img.onload = resolve;
                            img.onerror = reject;
                            setTimeout(reject, 5000); // 5秒超时
                        });

                        return img;
                    }
                }
            } catch (error) {
                console.error('加载签名失败:', error);
            }

            // 如果加载失败，返回无签名占位符
            const placeholder = document.createElement('div');
            placeholder.className = 'no-signature-text';
            placeholder.textContent = '（无电子签名）';
            return placeholder;
        }

        // 创建签名元素
        async function createSignatureElement(approval) {
            const container = document.createElement('div');
            container.className = 'signature-container';

            // 添加审批意见
            if (approval.comment) {
                const commentDiv = document.createElement('div');
                commentDiv.className = 'approval-comment';
                commentDiv.textContent = approval.comment;
                container.appendChild(commentDiv);
            }

            // 获取签名图片
            try {
                const apiUrl = `/api/users/${approval.approverId}/signature`;
                const authToken = sessionStorage.getItem('authToken');

                const response = await fetch(apiUrl, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.hasSignature) {
                        // 创建签名图片
                        const signatureWrapper = document.createElement('div');
                        signatureWrapper.className = 'signature-wrapper';

                        const img = document.createElement('img');

                        // 优先使用Base64数据，如果没有则使用文件路径
                        if (data.signatureBase64) {
                            img.src = data.signatureBase64;
                        } else if (data.signaturePath) {
                            img.src = `/${data.signaturePath}?t=${new Date().getTime()}`;
                        } else {
                            throw new Error('没有可用的签名数据');
                        }

                        img.className = 'signature-image signature-normal';
                        img.alt = `${approval.approverName}的签名`;
                        img.crossOrigin = 'anonymous';

                        // 等待图片加载完成
                        await new Promise((resolve, reject) => {
                            img.onload = resolve;
                            img.onerror = reject;
                            setTimeout(reject, 5000); // 5秒超时
                        });

                        signatureWrapper.appendChild(img);

                        // 添加签名信息
                        const infoDiv = document.createElement('div');
                        infoDiv.className = 'signature-info';
                        infoDiv.innerHTML = `
                            <div class="approver-name">${approval.approverName}</div>
                            <div class="approval-time">${new Date(approval.timestamp).toLocaleDateString('zh-CN')}</div>
                        `;
                        signatureWrapper.appendChild(infoDiv);

                        container.appendChild(signatureWrapper);
                    } else {
                        // 无签名时显示占位符
                        container.appendChild(createNoSignaturePlaceholder(approval));
                    }
                } else {
                    container.appendChild(createNoSignaturePlaceholder(approval));
                }
            } catch (error) {
                console.error('加载签名失败:', error);
                container.appendChild(createNoSignaturePlaceholder(approval));
            }

            return container;
        }

        // 创建无签名占位符
        function createNoSignaturePlaceholder(approval) {
            const placeholder = document.createElement('div');
            placeholder.className = 'no-signature';
            placeholder.innerHTML = `
                <div class="signature-placeholder">
                    <div class="approver-name">${approval.approverName}</div>
                    <div class="approval-time">${new Date(approval.timestamp).toLocaleDateString('zh-CN')}</div>
                    <div class="no-signature-text">（无电子签名）</div>
                </div>
            `;
            return placeholder;
        }

        // 下载申请书模板为PDF
        async function downloadApplicationTemplate() {
            const element = document.getElementById('applicationTemplatePage');
            if (!element) {
                alert('申请书模板元素未找到');
                return;
            }

            const filename = `申请书_${props.application.applicationNumber || new Date().toISOString().slice(0, 10)}.pdf`;

            // 显示加载提示
            const loadingMsg = document.createElement('div');
            loadingMsg.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
            loadingMsg.textContent = '正在生成PDF，请稍候...';
            document.body.appendChild(loadingMsg);

            try {
                // 先加载PDF库
                await loadPdfLibraries();

                // 将签名插入到模板中
                await insertSignaturesToTemplate();

                // 等待DOM更新
                await new Promise(resolve => setTimeout(resolve, 500));

                // 使用html2canvas将元素转换为canvas
                const canvas = await html2canvas(element, {
                    scale: 2, // 提高清晰度
                    useCORS: true, // 允许加载跨域图片
                    logging: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    imageTimeout: 10000, // 增加图片加载超时时间
                    removeContainer: false
                });

                // 使用jsPDF将canvas转换为PDF
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                // 计算宽高比例
                const imgData = canvas.toDataURL('image/png');
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const canvasWidth = canvas.width;
                const canvasHeight = canvas.height;
                const ratio = Math.min(pageWidth / canvasWidth, pageHeight / canvasHeight);
                const imgWidth = canvasWidth * ratio;
                const imgHeight = canvasHeight * ratio;

                // 添加图像到PDF，调整y坐标为-5mm，使内容上移
                pdf.addImage(imgData, 'PNG', 0, -5, imgWidth, imgHeight);

                // 下载PDF
                pdf.save(filename);

                // 移除加载提示
                document.body.removeChild(loadingMsg);
            } catch (error) {
                console.error('生成PDF失败:', error);
                alert('生成PDF失败，请重试: ' + error.message);
                if (document.body.contains(loadingMsg)) {
                    document.body.removeChild(loadingMsg);
                }
            }
        }

        // 获取各阶段的审批记录
        const getApprovalsByStage = computed(() => {
            if (!props.application || !props.application.approvalHistory) {
                return {
                    factoryManagers: [],
                    director: null,
                    managers: [],
                    ceo: null
                };
            }

            const approvals = props.application.approvalHistory;

            return {
                factoryManagers: approvals.filter(approval =>
                    approval.stage === 'factory_manager' && approval.action === 'approve'
                ),
                director: approvals.find(approval =>
                    approval.stage === 'director' && approval.action === 'approve'
                ),
                managers: approvals.filter(approval =>
                    approval.stage === 'manager' && approval.action === 'approve'
                ),
                ceo: approvals.find(approval =>
                    approval.stage === 'ceo' && approval.action === 'approve'
                )
            };
        });

        // 生成申请书模板HTML
        const templateHTML = computed(() => {
            if (!props.application) return '';

            const application = props.application;

            // 格式化日期
            const formattedDate = formatDate(application.date);

            // 使用 getApprovalsByStage 获取审批信息
            const approvals = getApprovalsByStage.value;
            let factoryApproval = '';
            let directorApproval = '';
            let managerApproval = '';

            // 处理厂长审批信息
            if (approvals.factoryManagers.length > 0) {
                factoryApproval = `<div class="approval-container">`;
                approvals.factoryManagers.forEach(approval => {
                    factoryApproval += `
                        <div class="single-approval">
                            ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                            <div class="signature-wrapper">
                                <div class="signature-placeholder">签名加载中...</div>
                                <div class="approval-info">
                                    <div class="approver-name">${sanitizeInput(approval.approverName)}</div>
                                    <div class="approval-date">${formatDate(approval.timestamp)}</div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                factoryApproval += `</div>`;
            }

            // 处理总监审批信息
            if (approvals.director) {
                const approval = approvals.director;
                directorApproval = `
                    <div class="approval-container">
                        <div class="single-approval">
                            ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                            <div class="signature-wrapper">
                                <div class="signature-placeholder">签名加载中...</div>
                                <div class="approval-info">
                                    <div class="approver-name">${sanitizeInput(approval.approverName)}</div>
                                    <div class="approval-date">${formatDate(approval.timestamp)}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 处理经理审批信息（包含CEO签名）
            if (approvals.managers.length > 0 || approvals.ceo) {
                managerApproval = `<div class="approval-container">`;

                // 添加经理审批信息
                approvals.managers.forEach(approval => {
                    managerApproval += `
                        <div class="single-approval">
                            ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                            <div class="signature-wrapper">
                                <div class="signature-placeholder">签名加载中...</div>
                                <div class="approval-info">
                                    <div class="approver-name">${sanitizeInput(approval.approverName)}</div>
                                    <div class="approval-date">${formatDate(approval.timestamp)}</div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                // 添加CEO审批信息到经理核准区域
                if (approvals.ceo) {
                    const approval = approvals.ceo;
                    managerApproval += `
                        <div class="single-approval">
                            ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                            <div class="signature-wrapper">
                                <div class="signature-placeholder">签名加载中...</div>
                                <div class="approval-info">
                                    <div class="approver-name">${sanitizeInput(approval.approverName)}</div>
                                    <div class="approval-date">${formatDate(approval.timestamp)}</div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                managerApproval += `</div>`;
            }

            // 检测是否为移动设备
            const isMobile = window.innerWidth <= 768;
            const isSmallMobile = window.innerWidth <= 480;

            // 根据设备类型调整样式
            let mobileClass = '';
            if (isSmallMobile) {
                mobileClass = 'mobile-small';
            } else if (isMobile) {
                mobileClass = 'mobile';
            }

            return `
                <table class="application-template ${mobileClass}">
                    <tr>
                        <th colspan="4" class="title">申 请 书</th>
                    </tr>
                    <tr style="height: 5%;">
                        <td class="label">申请部门</td>
                        <td>${sanitizeInput(application.department)}</td>
                        <td class="label">申请日期</td>
                        <td>${formattedDate}</td>
                    </tr>
                    <tr>
                        <td class="label" rowspan="2">申请事由</td>
                        <td colspan="3" class="content">${sanitizeInput(application.content).replace(/\n/g, '<br>')}</td>
                    </tr>
                    <tr style="height: 5%;">
                        <td colspan="3" class="applicant">申请人: ${sanitizeInput(application.applicant)}</td>
                    </tr>
                    <tr>
                        <td class="label">厂长意见</td>
                        <td colspan="3" class="approval" id="factoryApprovalCell">
                            <div class="approval-signatures-area">
                                ${factoryApproval}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">总监意见</td>
                        <td colspan="3" class="approval" id="directorApprovalCell">
                            <div class="approval-signatures-area">
                                ${directorApproval}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">经理核准</td>
                        <td colspan="3" class="approval" id="managerApprovalCell">
                            <div class="approval-signatures-area">
                                ${managerApproval}
                            </div>
                        </td>
                    </tr>
                </table>
            `;
        });

        // 监听模板显示状态，调整表格高度并加载签名
        watch(() => props.visible, (newVal) => {
            if (newVal) {
                // 延迟执行，确保DOM已渲染
                setTimeout(async () => {
                    adjustTableHeight();
                    // 加载签名到模板中
                    await insertSignaturesToTemplate();
                }, 100);
            }
        });

        return {
            templateHTML,
            getApprovalsByStage,
            closeApplicationTemplate,
            downloadApplicationTemplate
        };
    },
    template: `
        <!-- 申请书模板预览模态框 -->
        <div v-if="visible" id="applicationTemplateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-start overflow-y-auto z-50 p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full relative">
                <div class="p-4 border-b">
                    <h2 class="text-xl font-bold">申请书</h2>
                    <button @click="closeApplicationTemplate" class="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4 overflow-auto" style="max-height: 80vh;">
                    <div class="a4-page" id="applicationTemplatePage">
                        <!-- 申请书基本信息 -->
                        <div id="applicationTemplateContent" v-html="templateHTML"></div>

                        <!-- 电子签名显示区域（隐藏，仅用于PDF生成） -->
                        <div class="signature-areas" style="display: none;">
                            <!-- 厂长签名区域 -->
                            <div id="factorySignatures" class="factory-signatures">
                                <signature-display
                                    v-for="approval in getApprovalsByStage.factoryManagers"
                                    :key="approval.approverId"
                                    :approval-record="approval"
                                    :signature-size="'normal'"
                                    :display-type="'single'"
                                />
                            </div>

                            <!-- 总监签名区域 -->
                            <div id="directorSignature" class="director-signature">
                                <signature-display
                                    v-if="getApprovalsByStage.director"
                                    :approval-record="getApprovalsByStage.director"
                                    :signature-size="'normal'"
                                    :display-type="'single'"
                                />
                            </div>

                            <!-- 经理签名区域 -->
                            <div id="managerSignatures" class="manager-signatures">
                                <signature-display
                                    v-for="approval in getApprovalsByStage.managers"
                                    :key="approval.approverId"
                                    :approval-record="approval"
                                    :signature-size="'normal'"
                                    :display-type="'single'"
                                />
                            </div>

                            <!-- CEO签名区域 -->
                            <div id="ceoSignature" class="ceo-signature">
                                <signature-display
                                    v-if="getApprovalsByStage.ceo"
                                    :approval-record="getApprovalsByStage.ceo"
                                    :signature-size="'normal'"
                                    :display-type="'single'"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-4 border-t flex justify-end space-x-2 template-actions">
                    <button @click="downloadApplicationTemplate" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        下载
                    </button>
                    <button @click="closeApplicationTemplate" class="bg-gray-300 hover:bg-gray-400 px-4 py-2 rounded">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    `
};
