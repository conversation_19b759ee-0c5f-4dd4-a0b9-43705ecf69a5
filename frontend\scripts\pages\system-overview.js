/**
 * 系统功能展示页面脚本
 */

const { createApp } = Vue;

createApp({
    data() {
        return {
            stats: {
                totalModules: 9,
                implementedModules: 2,
                developingModules: 5,
                plannedModules: 2,
                technicalFeatures: 22,
                securityFeatures: 15,
                performanceOptimizations: 12,
                deploymentSecurity: 8,
                intelligentFeatures: 6,
                monitoringFeatures: 8
            },
            implementedModules: [
                {
                    id: 1,
                    name: '用户认证与权限管理',
                    description: '完整的用户认证体系和精细化权限控制系统',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>',
                    features: ['JWT Token认证', '用户管理', '权限模板', '角色分配', 'RBAC权限控制'],
                    highlights: ['企业级安全认证', '细粒度权限控制', '动态权限分配', '审计追踪']
                },
                {
                    id: 2,
                    name: '申请管理系统',
                    description: '完整的申请流程管理，支持多级审批和状态跟踪',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
                    features: ['申请提交', '审批流程', '状态跟踪', '文档管理', '历史记录'],
                    highlights: ['多级审批流程', '实时状态跟踪', '文档自动归档', '审批效率提升60%']
                }
            ],
            developingModules: [
                {
                    id: 3,
                    name: '设备管理系统',
                    description: '全面的设备信息管理、维护管理和健康状态监控',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>',
                    features: ['设备信息管理', '设备维护管理', '设备健康评估', '产能配置', '厂区管理'],
                    highlights: ['设备全生命周期管理', '预测性维护', '健康度实时监控', '产能优化配置'],
                    progress: 85,
                    currentPhase: '功能完善阶段',
                    completedFeatures: ['设备基础信息', '维护记录', '健康监控'],
                    inProgressFeatures: ['产能优化', '智能预警']
                },
                {
                    id: 4,
                    name: '质量管理系统',
                    description: '检测报告管理、质量数据分析和质量控制流程',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
                    features: ['检测报告上传', '报告管理', '质量数据分析', '质量标准管理', '质量追溯'],
                    highlights: ['多格式文件支持', '自动报告编号', '质量趋势分析', '合规性检查'],
                    progress: 80,
                    currentPhase: '测试优化阶段',
                    completedFeatures: ['报告上传', '报告管理', '文件处理'],
                    inProgressFeatures: ['数据分析', '质量追溯']
                },
                {
                    id: 5,
                    name: '生产排程系统',
                    description: '传统生产排程管理，支持资源分配和进度跟踪',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h8a2 2 0 012 2v4m0 0V7a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h8z"></path>',
                    features: ['排程计划制定', '资源分配', '进度跟踪', '排程报告', '资源管理'],
                    highlights: ['可视化排程', '资源冲突检测', '进度实时跟踪', '效率分析报告'],
                    progress: 75,
                    currentPhase: '功能集成阶段',
                    completedFeatures: ['基础排程', '资源管理', '进度跟踪'],
                    inProgressFeatures: ['高级分析', '报告优化']
                },
                {
                    id: 6,
                    name: '智能排程系统',
                    description: 'AI驱动的智能排程优化，支持多策略算法和方案对比',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>',
                    features: ['AI算法排程', '多方案对比', '交期预测', '智能优化', '算法调优'],
                    highlights: ['5种智能算法', '方案自动对比', '交期准确预测', '持续学习优化'],
                    progress: 90,
                    currentPhase: '算法优化阶段',
                    completedFeatures: ['核心算法', '方案生成', '对比分析'],
                    inProgressFeatures: ['算法调优', '性能优化']
                },
                {
                    id: 7,
                    name: '产品管理系统',
                    description: '产品信息管理和生产工艺流程配置',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>',
                    features: ['产品信息管理', '工艺流程配置', '产品分类', '规格管理'],
                    highlights: ['产品全生命周期', '工艺标准化', '产品族管理'],
                    progress: 70,
                    currentPhase: '核心开发阶段',
                    completedFeatures: ['基础信息', '产品分类'],
                    inProgressFeatures: ['工艺流程', '规格管理']
                }
            ],
            plannedModules: [
                {
                    id: 8,
                    name: '库存管理系统',
                    description: '基于标签的智能库存管理，支持多维度分类和精准定位',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>',
                    features: ['智能标签分类', '标签批量操作', '标签搜索定位', '库存统计分析', '自动盘点'],
                    currentStatus: '库存管理缺乏有效分类和快速定位机制',
                    painPoints: ['物料查找困难', '分类标准不统一', '库存盘点耗时', '数据准确性差'],
                    plannedFeatures: ['RFID标签集成', '智能货架管理', '自动补货提醒', '库存预测分析']
                },
                {
                    id: 9,
                    name: '文档管理系统',
                    description: '企业文档管理、版本控制和协作编辑，支持数字化审批流程',
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
                    features: ['文档存储', '版本控制', '协作编辑', '权限管理', '数字签名'],
                    currentStatus: '内控文档修改需打印纸质文件，多部门主管手签后扫描存档',
                    painPoints: ['纸质签名流程繁琐', '审批周期冗长', '文档易丢失损坏', '版本追踪困难'],
                    plannedFeatures: ['电子签名集成', '在线协作编辑', '自动版本管理', '文档模板库']
                }
            ],

            // 技术架构特性
            technicalFeatures: [
                {
                    category: '前端技术栈',
                    title: 'Vue.js 3 Composition API',
                    description: '采用最新的Vue.js 3框架，使用Composition API提供更好的代码组织和类型推断',
                    highlights: [
                        '响应式数据管理 (ref, reactive)',
                        '生命周期钩子 (onMounted, onUpdated)',
                        '依赖注入 (provide/inject)',
                        '性能优化 (shallowRef, markRaw)'
                    ],
                    icon: 'vue'
                },
                {
                    category: '前端技术栈',
                    title: 'Tailwind CSS 设计系统',
                    description: '原子化CSS框架，提供一致的设计语言和快速开发体验',
                    highlights: [
                        '响应式设计 (sm:, md:, lg:)',
                        '暗色模式支持',
                        '自定义组件库',
                        '优化的构建体积'
                    ],
                    icon: 'design'
                },
                {
                    category: '后端架构',
                    title: 'Node.js + Express.js',
                    description: '高性能的JavaScript运行时环境，构建可扩展的网络应用程序',
                    highlights: [
                        'RESTful API 设计',
                        '中间件架构',
                        '异步非阻塞I/O',
                        '事件驱动编程'
                    ],
                    icon: 'server'
                },
                {
                    category: '数据存储',
                    title: 'SQLite + Better-SQLite3',
                    description: '轻量级关系型数据库，提供ACID事务和高性能查询',
                    highlights: [
                        'WAL模式并发优化',
                        '外键约束支持',
                        '事务完整性',
                        '内存映射优化'
                    ],
                    icon: 'database'
                },
                {
                    category: '性能优化',
                    title: '连接池管理',
                    description: '智能数据库连接池，提供高效的资源管理和查询优化',
                    highlights: [
                        '动态连接分配',
                        '查询缓存机制',
                        '连接健康监控',
                        '自动清理策略'
                    ],
                    icon: 'performance'
                },
                {
                    category: '智能算法',
                    title: '智能排程算法引擎',
                    description: '基于AI的多策略排程优化算法，支持复杂生产场景的智能决策',
                    highlights: [
                        '5种优化策略算法',
                        '设备-操作员智能匹配',
                        '交期预测算法',
                        '资源冲突自动检测'
                    ],
                    icon: 'ai'
                },
                {
                    category: '系统监控',
                    title: '实时系统监控',
                    description: '全方位系统健康监控，提供实时性能指标和智能告警',
                    highlights: [
                        'CPU/内存/磁盘监控',
                        '数据库性能监控',
                        '自动告警机制',
                        '性能趋势分析'
                    ],
                    icon: 'monitor'
                },
                {
                    category: '数据分析',
                    title: '业务数据分析',
                    description: '深度业务数据分析和可视化，支持决策支持和趋势预测',
                    highlights: [
                        '生产效率分析',
                        '资源利用率统计',
                        '质量趋势分析',
                        '成本效益分析'
                    ],
                    icon: 'analytics'
                }
            ],

            // 安全特性
            securityFeatures: [
                {
                    title: 'JWT身份认证',
                    description: '基于JSON Web Token的无状态身份验证机制',
                    implementation: 'jsonwebtoken + bcrypt密码加密',
                    benefits: ['无状态认证', '跨域支持', '令牌过期控制', '安全传输']
                },
                {
                    title: '基于角色的权限控制(RBAC)',
                    description: '细粒度的权限管理系统，支持动态权限分配',
                    implementation: '权限模板 + 用户角色映射',
                    benefits: ['灵活权限配置', '最小权限原则', '权限继承', '审计追踪']
                },
                {
                    title: '文件上传安全',
                    description: '多层文件上传安全验证机制',
                    implementation: 'Multer + 文件类型检查 + 大小限制',
                    benefits: ['类型白名单', '恶意文件检测', '存储隔离', '访问控制']
                },
                {
                    title: '输入验证与防护',
                    description: '全面的输入验证和安全防护措施',
                    implementation: 'Joi验证 + XSS防护 + SQL注入防护',
                    benefits: ['数据完整性', 'XSS攻击防护', 'SQL注入防护', '参数验证']
                },
                {
                    title: '本地主机部署安全',
                    description: '完全本地化部署，数据不出企业内网',
                    implementation: '内网部署 + 物理隔离 + 本地数据存储',
                    benefits: ['数据主权保护', '网络隔离安全', '合规性保障', '自主可控']
                },
                {
                    title: '零依赖外部服务',
                    description: '系统完全独立运行，无需依赖任何外部云服务',
                    implementation: '本地SQLite数据库 + 本地文件存储 + 离线运行',
                    benefits: ['数据安全可控', '服务稳定可靠', '成本可控', '隐私保护']
                },
                {
                    title: '企业级访问控制',
                    description: '基于企业内网的多层访问控制机制',
                    implementation: 'IP白名单 + 内网认证 + 会话管理',
                    benefits: ['内网安全', '访问控制', '会话保护', '审计追踪']
                },
                {
                    title: '数据加密存储',
                    description: '敏感数据本地加密存储，密钥企业自管',
                    implementation: 'AES加密 + 本地密钥管理 + 数据脱敏',
                    benefits: ['数据加密保护', '密钥自主管理', '合规性支持', '隐私安全']
                },
                {
                    title: '系统安全监控',
                    description: '实时安全事件监控和异常行为检测',
                    implementation: '安全日志记录 + 异常检测 + 实时告警',
                    benefits: ['安全事件追踪', '异常行为检测', '实时安全告警', '审计合规']
                },
                {
                    title: '备份与恢复',
                    description: '自动化数据备份和灾难恢复机制',
                    implementation: '定时备份 + 增量备份 + 快速恢复',
                    benefits: ['数据安全保障', '业务连续性', '快速恢复能力', '灾难预防']
                },
                {
                    title: '网络安全防护',
                    description: '多层网络安全防护和访问控制',
                    implementation: '防火墙配置 + DDoS防护 + 安全扫描',
                    benefits: ['网络攻击防护', '访问控制', '安全漏洞检测', '威胁防范']
                }
            ],

            // 性能优化和最佳实践
            performanceOptimizations: [
                {
                    category: '前端性能',
                    title: 'Vue.js 3 性能优化',
                    techniques: [
                        '组件懒加载 (defineAsyncComponent)',
                        '响应式优化 (shallowRef, markRaw)',
                        '计算属性缓存 (computed)',
                        '虚拟滚动 (大列表优化)',
                        'Tree-shaking 优化'
                    ],
                    impact: '页面加载速度提升40%，内存使用减少30%'
                },
                {
                    category: '后端性能',
                    title: 'Node.js 最佳实践',
                    techniques: [
                        '连接池管理 (Database Connection Pool)',
                        '查询优化器 (Query Optimizer)',
                        '缓存策略 (Redis/Memory Cache)',
                        '异步非阻塞I/O',
                        '事件循环优化'
                    ],
                    impact: 'API响应时间减少60%，并发处理能力提升3倍'
                },
                {
                    category: '数据库性能',
                    title: 'SQLite 优化策略',
                    techniques: [
                        'WAL模式 (Write-Ahead Logging)',
                        '索引优化 (Index Optimization)',
                        '查询计划分析 (EXPLAIN QUERY PLAN)',
                        '内存映射 (Memory Mapping)',
                        '事务批处理 (Batch Transactions)'
                    ],
                    impact: '查询性能提升50%，写入性能提升80%'
                },
                {
                    category: '安全最佳实践',
                    title: 'Node.js 安全防护',
                    techniques: [
                        'Helmet.js 安全头设置',
                        'Rate Limiting 限流保护',
                        'Input Validation 输入验证',
                        'HTTPS 强制加密',
                        'Secret Management 密钥管理'
                    ],
                    impact: '安全漏洞风险降低95%，攻击防护能力显著提升'
                }
            ],

            // 开发最佳实践
            developmentPractices: [
                {
                    title: '模块化架构设计',
                    description: '按业务组件组织代码结构，而非技术层级',
                    example: 'apps/orders, apps/users, libraries/logger',
                    benefits: ['高内聚低耦合', '独立部署', '团队协作', '代码复用']
                },
                {
                    title: '多阶段Docker构建',
                    description: '使用多阶段构建优化镜像大小和安全性',
                    example: 'build stage → production stage',
                    benefits: ['镜像体积减小70%', '安全性提升', '构建缓存优化', '部署效率提升']
                },
                {
                    title: '环境变量配置管理',
                    description: '使用环境变量管理敏感配置和部署参数',
                    example: 'NODE_ENV, DATABASE_URL, JWT_SECRET',
                    benefits: ['配置安全', '环境隔离', '部署灵活', '密钥保护']
                },
                {
                    title: '错误处理和日志记录',
                    description: '统一的错误处理机制和结构化日志记录',
                    example: 'AppError类, Winston日志, 错误边界',
                    benefits: ['问题快速定位', '系统稳定性', '运维效率', '用户体验']
                }
            ],

            // 智能特性
            intelligentFeatures: [
                {
                    title: '智能排程优化',
                    description: '基于AI算法的多策略排程优化',
                    algorithms: ['最早完成算法', '高效率算法', '负载均衡算法', '成本优化算法', '低风险算法'],
                    benefits: ['排程效率提升40%', '资源利用率提升35%', '交期准确率95%']
                },
                {
                    title: '设备智能匹配',
                    description: '设备与操作员的智能匹配优化',
                    features: ['技能匹配', '效率评估', '负载均衡', '经验权重'],
                    benefits: ['生产效率提升25%', '设备利用率提升30%', '操作员满意度提升']
                },
                {
                    title: '交期智能预测',
                    description: '基于历史数据和实时状态的交期预测',
                    methods: ['历史数据分析', '实时进度跟踪', '风险评估', '动态调整'],
                    benefits: ['预测准确率95%', '客户满意度提升', '库存优化']
                }
            ],

            // 系统监控特性
            monitoringFeatures: [
                {
                    category: '性能监控',
                    title: '实时系统性能监控',
                    metrics: ['CPU使用率', '内存占用', '磁盘I/O', '网络流量', '数据库性能'],
                    features: ['实时监控', '历史趋势', '性能告警', '自动优化建议']
                },
                {
                    category: '业务监控',
                    title: '业务指标监控',
                    metrics: ['申请处理量', '审批效率', '设备利用率', '生产进度', '质量指标'],
                    features: ['业务仪表板', 'KPI跟踪', '异常检测', '趋势分析']
                },
                {
                    category: '安全监控',
                    title: '安全事件监控',
                    metrics: ['登录异常', '权限变更', '数据访问', '系统操作', '安全威胁'],
                    features: ['安全日志', '异常告警', '行为分析', '合规审计']
                },
                {
                    category: '告警系统',
                    title: '智能告警系统',
                    types: ['性能告警', '业务告警', '安全告警', '系统故障'],
                    features: ['多级告警', '智能降噪', '告警聚合', '自动处理']
                }
            ],

            // 系统演进历程
            systemEvolution: [
                {
                    version: '初始阶段',
                    period: '2025年3月',
                    title: '单一申请管理系统',
                    description: '专注于企业内部申请流程的数字化管理',
                    modules: ['申请提交', '审批流程', '状态跟踪'],
                    techStack: ['基础Web技术', '简单数据存储', '基本用户界面'],
                    complexity: 'low',
                    achievements: [
                        '实现基本申请流程数字化',
                        '替代纸质申请流程',
                        '提升审批效率30%'
                    ],
                    icon: '🌱',
                    color: 'blue'
                },
                {
                    version: 'V1.0',
                    period: '2025年5月',
                    title: '双系统集成平台',
                    description: '扩展设备管理功能，形成申请+设备双核心系统',
                    modules: ['申请管理', '设备管理', '用户权限', '数据统计'],
                    techStack: ['Vue.js 2', 'Node.js', 'JSON数据存储', '模块化设计'],
                    complexity: 'medium',
                    achievements: [
                        '集成设备全生命周期管理',
                        '建立统一用户权限体系',
                        '系统使用率提升60%'
                    ],
                    icon: '🚀',
                    color: 'green'
                },
                {
                    version: 'V2.0',
                    period: '2025年7月',
                    title: '智能化企业级管理平台',
                    description: '全面升级为智能化企业级管理平台，集成AI算法和实时监控',
                    modules: ['申请管理', '设备管理', '质量管理', '生产排程', '智能排程', '产品管理', '系统监控'],
                    techStack: ['Vue.js 3 Composition API', 'Node.js + Express', 'SQLite + 连接池', 'AI算法引擎', '实时监控系统'],
                    complexity: 'high',
                    achievements: [
                        '集成AI智能排程算法',
                        '实现7大核心业务模块',
                        '性能提升200%，支持高并发',
                        '企业级安全和监控体系',
                        '模块化架构，易于扩展'
                    ],
                    icon: '⭐',
                    color: 'purple'
                }
            ],

            // 本地部署安全优势
            localDeploymentSecurity: [
                {
                    category: '数据安全',
                    title: '企业数据完全自主可控',
                    description: '所有数据存储在企业内部服务器，不依赖任何外部云服务',
                    features: [
                        '数据不出企业内网',
                        '完全物理隔离',
                        '企业自主管理',
                        '符合数据主权要求'
                    ],
                    icon: '🔒',
                    color: 'red'
                },
                {
                    category: '网络安全',
                    title: '内网部署零外部依赖',
                    description: '系统完全在企业内网运行，无需连接外部网络',
                    features: [
                        '内网独立运行',
                        '防止外部攻击',
                        '网络流量可控',
                        '访问权限严格管控'
                    ],
                    icon: '🛡️',
                    color: 'blue'
                },
                {
                    category: '合规安全',
                    title: '满足企业合规要求',
                    description: '符合各种行业安全标准和企业内控要求',
                    features: [
                        '数据本地化存储',
                        '审计日志完整',
                        '权限管理规范',
                        '安全策略可定制'
                    ],
                    icon: '📋',
                    color: 'green'
                },
                {
                    category: '运维安全',
                    title: '企业IT团队完全掌控',
                    description: '系统运维、维护、升级完全由企业IT团队控制',
                    features: [
                        '部署环境可控',
                        '维护时间自主',
                        '升级策略灵活',
                        '故障响应及时'
                    ],
                    icon: '⚙️',
                    color: 'purple'
                }
            ]
        };
    },
    mounted() {
        this.initializePage();
    },
    methods: {
        initializePage() {
            // 隐藏加载动画
            const loading = document.getElementById('loading');
            if (loading) {
                loading.style.display = 'none';
            }

            // 首先显示静态图表
            this.showStaticDiagrams();

            // 然后尝试初始化Mermaid图表
            this.initializeMermaid();

            // 初始化现代化交互效果
            this.initializeAnimations();
            this.initializeScrollEffects();
        },
        async initializeMermaid() {
            // 检查Mermaid是否已加载
            if (typeof mermaid === 'undefined') {
                console.warn('Mermaid library not loaded, using static diagrams');
                return;
            }

            try {
                // 配置Mermaid
                mermaid.initialize({
                    startOnLoad: false,
                    theme: 'default',
                    themeVariables: {
                        primaryColor: '#3b82f6',
                        primaryTextColor: '#ffffff',
                        primaryBorderColor: '#1d4ed8',
                        lineColor: '#6b7280',
                        sectionBkgColor: '#f3f4f6',
                        altSectionBkgColor: '#ffffff',
                        gridColor: '#e5e7eb',
                        secondaryColor: '#f59e0b',
                        tertiaryColor: '#10b981'
                    },
                    flowchart: {
                        useMaxWidth: true,
                        htmlLabels: true,
                        curve: 'basis'
                    },
                    securityLevel: 'loose'
                });

                // 延迟渲染图表以确保DOM完全加载
                setTimeout(async () => {
                    try {
                        // 使用预定义的图表定义
                        const diagramDefinitions = {
                            'architecture-diagram': `flowchart LR
    subgraph "前端层"
        A1[Vue.js 3<br/>用户界面]
        A2[Tailwind CSS<br/>样式框架]
        A3[响应式设计<br/>多端适配]
    end

    subgraph "中间件层"
        B1[CORS<br/>跨域处理]
        B2[身份认证<br/>JWT Token]
        B3[权限控制<br/>角色管理]
        B4[请求日志<br/>性能监控]
    end

    subgraph "业务层"
        C1[申请管理<br/>Application]
        C2[用户管理<br/>User System]
        C3[设备管理<br/>Equipment]
        C4[质量管理<br/>Quality]
        C5[排程管理<br/>Schedule]
    end

    subgraph "数据层"
        D1[SQLite<br/>主数据库]
        D2[文件存储<br/>File System]
        D3[日志存储<br/>Log Files]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
    B4 --> C5
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    C1 --> D2
    C3 --> D2
    C4 --> D2
    C1 --> D3
    C2 --> D3
    C3 --> D3
    C4 --> D3
    C5 --> D3

    style A1 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style A2 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style A3 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style B1 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style B2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style B3 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style B4 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style C1 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style C2 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style C3 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style C4 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style C5 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style D1 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style D2 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style D3 fill:#fff3e0,stroke:#f57c00,stroke-width:2px`,
                            'business-flow': `flowchart LR
    subgraph "用户入口"
        A1[用户登录<br/>Login]
        A2[身份验证<br/>Authentication]
        A3[权限检查<br/>Authorization]
    end

    subgraph "已实现模块"
        B1[申请管理<br/>✅ Application<br/>Management]
        B2[用户管理<br/>✅ User<br/>Management]
    end

    subgraph "开发中模块"
        C1[设备管理<br/>🟡 Equipment<br/>Management]
        C2[质量管理<br/>🟡 Quality<br/>Management]
        C3[排程管理<br/>🟡 Schedule<br/>Management]
    end

    subgraph "规划中模块"
        D1[库存管理<br/>🔵 Inventory<br/>Management]
        D2[文档管理<br/>🔵 Document<br/>Management]
    end

    A1 --> A2
    A2 --> A3
    A3 --> B1
    A3 --> B2
    A3 --> C1
    A3 --> C2
    A3 --> C3
    A3 --> D1
    A3 --> D2

    style A1 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style A2 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style A3 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B1 fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    style B2 fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    style C1 fill:#fff8e1,stroke:#f57c00,stroke-width:3px
    style C2 fill:#fff8e1,stroke:#f57c00,stroke-width:3px
    style C3 fill:#fff8e1,stroke:#f57c00,stroke-width:3px
    style D1 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    style D2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px`
                        };

                        // 渲染每个图表
                        const diagrams = document.querySelectorAll('.mermaid');
                        for (const diagram of diagrams) {
                            if (!diagram.getAttribute('data-processed')) {
                                try {
                                    const diagramId = diagram.id;
                                    const graphDefinition = diagramDefinitions[diagramId];

                                    if (graphDefinition) {
                                        // 使用新的render API
                                        const { svg } = await mermaid.render(`${diagramId}-${Date.now()}`, graphDefinition);
                                        diagram.innerHTML = svg;
                                        diagram.setAttribute('data-processed', 'true');
                                    }
                                } catch (renderError) {
                                    console.error('Failed to render diagram:', renderError);
                                    // 如果单个图表渲染失败，继续处理其他图表
                                }
                            }
                        }

                        // 如果Mermaid渲染成功，隐藏静态图表，显示Mermaid图表
                        this.showMermaidDiagrams();
                        console.log('Mermaid diagrams initialized successfully');
                    } catch (error) {
                        console.error('Mermaid rendering error:', error);
                        // 保持显示静态图表
                    }
                }, 2000);
            } catch (error) {
                console.error('Mermaid initialization error:', error);
                // 保持显示静态图表
            }
        },

        showMermaidDiagrams() {
            // 保持显示静态图表，隐藏Mermaid图表
            const architectureDiagram = document.getElementById('architecture-diagram');
            const businessFlowDiagram = document.getElementById('business-flow');
            const staticArchitecture = document.getElementById('static-architecture');
            const staticBusinessFlow = document.getElementById('static-business-flow');

            if (architectureDiagram && staticArchitecture) {
                architectureDiagram.style.display = 'none';
                staticArchitecture.style.display = 'block';
            }

            if (businessFlowDiagram && staticBusinessFlow) {
                businessFlowDiagram.style.display = 'none';
                staticBusinessFlow.style.display = 'block';
            }
        },

        showStaticDiagrams() {
            // 显示静态图表作为默认方案
            const staticArchitecture = document.getElementById('static-architecture');
            const staticBusinessFlow = document.getElementById('static-business-flow');

            if (staticArchitecture) {
                staticArchitecture.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-white font-bold">前端</span>
                            </div>
                            <h4 class="font-semibold">用户界面层</h4>
                            <p class="text-sm text-gray-600">Vue.js 3 + Tailwind CSS</p>
                        </div>
                        <div class="text-center">
                            <div class="w-20 h-20 bg-yellow-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-white font-bold">后端</span>
                            </div>
                            <h4 class="font-semibold">业务逻辑层</h4>
                            <p class="text-sm text-gray-600">Node.js + Express.js</p>
                        </div>
                        <div class="text-center">
                            <div class="w-20 h-20 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-white font-bold">数据</span>
                            </div>
                            <h4 class="font-semibold">数据访问层</h4>
                            <p class="text-sm text-gray-600">SQLite Database</p>
                        </div>
                    </div>
                `;
                staticArchitecture.style.display = 'block';
            }

            if (staticBusinessFlow) {
                staticBusinessFlow.innerHTML = `
                    <div class="flex flex-wrap justify-center items-center space-x-4 mb-6">
                        <div class="bg-green-500 text-white px-4 py-2 rounded-lg mb-2">用户登录</div>
                        <div class="text-gray-400">→</div>
                        <div class="bg-yellow-500 text-white px-4 py-2 rounded-lg mb-2">权限验证</div>
                        <div class="text-gray-400">→</div>
                        <div class="bg-purple-500 text-white px-4 py-2 rounded-lg mb-2">功能模块</div>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div class="bg-green-600 text-white px-3 py-2 rounded text-center text-sm">申请管理 ✅</div>
                        <div class="bg-yellow-500 text-white px-3 py-2 rounded text-center text-sm">设备管理 🟡</div>
                        <div class="bg-yellow-500 text-white px-3 py-2 rounded text-center text-sm">质量管理 🟡</div>
                        <div class="bg-yellow-500 text-white px-3 py-2 rounded text-center text-sm">生产排程 🟡</div>
                        <div class="bg-blue-500 text-white px-3 py-2 rounded text-center text-sm">库存管理 🔵</div>
                        <div class="bg-blue-500 text-white px-3 py-2 rounded text-center text-sm">文档管理 🔵</div>
                    </div>
                    <div class="mt-4 text-center text-sm text-gray-600">
                        <span class="mr-4">✅ 已实现</span>
                        <span class="mr-4">🟡 开发中</span>
                        <span>🔵 规划中</span>
                    </div>
                `;
                staticBusinessFlow.style.display = 'block';
            }

            console.log('Static diagrams displayed');
        },

        // 现代化交互功能
        initializeAnimations() {
            // 观察器用于触发动画
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            }, {
                threshold: 0.1
            });

            // 观察所有需要动画的元素
            document.querySelectorAll('.card-hover, .tech-card').forEach(el => {
                observer.observe(el);
            });
        },

        initializeScrollEffects() {
            // 平滑滚动效果
            window.addEventListener('scroll', this.handleScroll);
        },

        handleScroll() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.gradient-bg');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        },

        scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    }
}).mount('#app');
