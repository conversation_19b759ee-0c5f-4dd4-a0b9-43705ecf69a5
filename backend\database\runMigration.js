/**
 * 数据库迁移执行脚本
 * 用于执行数据库结构变更
 */

const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

// 导入迁移脚本
const addSelectedManagersField = require('./migrations/add_selected_managers_field');

/**
 * 执行所有待执行的迁移
 */
async function runMigrations() {
    try {
        const dbPath = path.join(__dirname, './application_system.db');
        
        // 检查数据库文件是否存在
        if (!fs.existsSync(dbPath)) {
            logger.error('数据库文件不存在:', dbPath);
            return;
        }
        
        logger.info('开始执行数据库迁移...');
        
        // 执行迁移：添加 selected_managers 字段
        const result = addSelectedManagersField.migrate(dbPath);
        
        if (result.success) {
            logger.info('数据库迁移完成:', result.message);
        } else {
            logger.error('数据库迁移失败:', result.message);
        }
        
    } catch (error) {
        logger.error('执行数据库迁移时发生错误:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本，则执行迁移
if (require.main === module) {
    runMigrations();
}

module.exports = {
    runMigrations
};
