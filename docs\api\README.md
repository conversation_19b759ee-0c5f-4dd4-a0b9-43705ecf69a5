# 企业管理系统 API 文档

## 📋 概述

本文档提供了企业管理系统所有API接口的详细说明，包括认证、申请管理、用户管理、设备管理、质量管理等模块的完整API参考。

## 🌐 基础信息

- **Base URL**: `http://localhost:5050/api`
- **认证方式**: Bearer <PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

## 🔐 认证

### JWT Token 认证

所有API请求都需要在请求头中包含有效的JWT令牌：

```http
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

### 获取Token

通过登录接口获取JWT令牌：

```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

**响应示例**：
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user123",
    "username": "admin",
    "role": "admin",
    "permissions": ["new_application", "view_users", ...]
  }
}
```

## 📚 API 模块

### 🔑 认证模块
- [认证API文档](./auth.md) - 用户登录、登出、令牌验证

### 📝 申请管理模块  
- [申请管理API文档](./applications.md) - 申请CRUD、审批流程、状态管理

### 👥 用户管理模块
- [用户管理API文档](./users.md) - 用户CRUD、权限管理、电子签名

### 🏭 设备管理模块
- [设备管理API文档](./equipment.md) - 设备信息、维护记录、健康评估

### 🔧 质量管理模块
- [质量管理API文档](./quality.md) - 检测报告、文件管理

### 📊 生产排程模块
- [排程管理API文档](./schedules.md) - 排程计划、资源分配

### 🤖 智能排产模块
- [智能排产API文档](./intelligent-scheduling.md) - AI算法、优化策略

### 📈 系统监控模块
- [系统监控API文档](./system.md) - 性能监控、健康检查

## 🚨 错误处理

### 标准错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "ERROR_CODE",
  "details": {
    "field": "具体错误信息"
  }
}
```

### 常见HTTP状态码

| 状态码 | 说明 | 描述 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证或令牌无效 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

## 📋 数据格式规范

### 日期时间格式
- **标准格式**: ISO 8601 (`2025-07-29T10:30:00.000Z`)
- **日期格式**: YYYY-MM-DD (`2025-07-29`)
- **时间格式**: HH:mm:ss (`10:30:00`)

### 分页参数
```json
{
  "page": 1,
  "limit": 20,
  "total": 100,
  "totalPages": 5
}
```

### 搜索参数
```json
{
  "search": "关键词",
  "filters": {
    "status": "active",
    "dateRange": {
      "start": "2025-01-01",
      "end": "2025-12-31"
    }
  },
  "sort": {
    "field": "createdAt",
    "order": "desc"
  }
}
```

## 🔧 开发工具

### API测试工具推荐
- **Postman**: 图形化API测试工具
- **curl**: 命令行HTTP客户端
- **Insomnia**: 现代化API测试工具

### 示例请求 (curl)
```bash
# 登录获取token
curl -X POST http://localhost:5050/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 使用token访问API
curl -X GET http://localhost:5050/api/applications \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

## 📞 技术支持

- **文档更新**: 2025-07-29
- **API版本**: v1.0
- **技术支持**: [<EMAIL>](mailto:<EMAIL>)
- **问题反馈**: [GitHub Issues](https://github.com/Darrowyu/management-system-v2.0.0/issues)

---

**注意**: 本文档持续更新中，如发现任何问题或需要补充内容，请及时反馈。
