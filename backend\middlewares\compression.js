/**
 * 压缩和静态资源优化中间件
 * 提供gzip压缩、缓存控制和静态资源优化
 */

const compression = require('compression');
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

/**
 * 创建智能压缩中间件
 */
function createCompressionMiddleware() {
    return compression({
        // 压缩级别 (1-9, 6是默认值，平衡压缩率和速度)
        level: 6,
        
        // 压缩阈值，小于1KB的文件不压缩
        threshold: 1024,
        
        // 过滤函数，决定哪些响应需要压缩
        filter: (req, res) => {
            // 如果响应头中已经设置了不压缩，则跳过
            if (res.getHeader('x-no-compression')) {
                return false;
            }

            // 检查内容类型
            const contentType = res.getHeader('content-type');
            if (!contentType) {
                return false;
            }

            // 压缩文本类型的内容
            const compressibleTypes = [
                'text/',
                'application/json',
                'application/javascript',
                'application/xml',
                'application/rss+xml',
                'application/atom+xml',
                'image/svg+xml'
            ];

            return compressibleTypes.some(type => contentType.includes(type));
        },

        // 压缩窗口大小
        windowBits: 15,

        // 内存级别
        memLevel: 8
    });
}

/**
 * 静态资源缓存中间件
 */
function createStaticCacheMiddleware(options = {}) {
    const {
        maxAge = 86400, // 默认1天
        immutable = false,
        etag = true
    } = options;

    return (req, res, next) => {
        const ext = path.extname(req.path).toLowerCase();
        
        // 根据文件类型设置不同的缓存策略
        const cacheSettings = getCacheSettings(ext);
        
        // 设置缓存头
        if (cacheSettings.maxAge > 0) {
            res.setHeader('Cache-Control', 
                `public, max-age=${cacheSettings.maxAge}${cacheSettings.immutable ? ', immutable' : ''}`
            );
        } else {
            res.setHeader('Cache-Control', 'no-cache');
        }

        // 设置ETag
        if (etag && cacheSettings.etag) {
            // Express会自动生成ETag，这里只是确保启用
            res.setHeader('ETag', res.getHeader('ETag') || generateETag(req.path));
        }

        // 设置Vary头
        res.setHeader('Vary', 'Accept-Encoding');

        next();
    };
}

/**
 * 根据文件扩展名获取缓存设置
 */
function getCacheSettings(ext) {
    const settings = {
        // 长期缓存的静态资源
        '.js': { maxAge: 31536000, immutable: true, etag: true },      // 1年
        '.css': { maxAge: 31536000, immutable: true, etag: true },     // 1年
        '.png': { maxAge: 2592000, immutable: false, etag: true },     // 30天
        '.jpg': { maxAge: 2592000, immutable: false, etag: true },     // 30天
        '.jpeg': { maxAge: 2592000, immutable: false, etag: true },    // 30天
        '.gif': { maxAge: 2592000, immutable: false, etag: true },     // 30天
        '.svg': { maxAge: 2592000, immutable: false, etag: true },     // 30天
        '.ico': { maxAge: 604800, immutable: false, etag: true },      // 7天
        '.woff': { maxAge: 31536000, immutable: true, etag: true },    // 1年
        '.woff2': { maxAge: 31536000, immutable: true, etag: true },   // 1年
        '.ttf': { maxAge: 31536000, immutable: true, etag: true },     // 1年
        '.eot': { maxAge: 31536000, immutable: true, etag: true },     // 1年
        
        // 短期缓存的动态内容
        '.html': { maxAge: 3600, immutable: false, etag: true },       // 1小时
        '.json': { maxAge: 300, immutable: false, etag: true },        // 5分钟
        '.xml': { maxAge: 3600, immutable: false, etag: true },        // 1小时
        
        // 不缓存的内容
        '.map': { maxAge: 0, immutable: false, etag: false }           // source maps
    };

    return settings[ext] || { maxAge: 86400, immutable: false, etag: true }; // 默认1天
}

/**
 * 生成简单的ETag
 */
function generateETag(filePath) {
    try {
        const stats = fs.statSync(filePath);
        return `"${stats.size}-${stats.mtime.getTime()}"`;
    } catch (error) {
        return `"${Date.now()}"`;
    }
}

/**
 * 预压缩中间件
 * 检查是否存在预压缩的文件（.gz, .br）
 */
function createPreCompressionMiddleware() {
    return (req, res, next) => {
        const acceptEncoding = req.headers['accept-encoding'] || '';
        const originalUrl = req.url;
        
        // 只处理静态文件请求
        if (!isStaticFile(originalUrl)) {
            return next();
        }

        // 检查Brotli支持
        if (acceptEncoding.includes('br')) {
            const brPath = originalUrl + '.br';
            if (fileExists(brPath)) {
                req.url = brPath;
                res.setHeader('Content-Encoding', 'br');
                res.setHeader('Content-Type', getContentType(originalUrl));
                return next();
            }
        }

        // 检查Gzip支持
        if (acceptEncoding.includes('gzip')) {
            const gzPath = originalUrl + '.gz';
            if (fileExists(gzPath)) {
                req.url = gzPath;
                res.setHeader('Content-Encoding', 'gzip');
                res.setHeader('Content-Type', getContentType(originalUrl));
                return next();
            }
        }

        next();
    };
}

/**
 * 检查是否为静态文件
 */
function isStaticFile(url) {
    const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot'];
    const ext = path.extname(url).toLowerCase();
    return staticExtensions.includes(ext);
}

/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
    try {
        const fullPath = path.join(__dirname, '../../frontend', filePath);
        return fs.existsSync(fullPath);
    } catch (error) {
        return false;
    }
}

/**
 * 获取内容类型
 */
function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.js': 'application/javascript',
        '.css': 'text/css',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.woff': 'font/woff',
        '.woff2': 'font/woff2',
        '.ttf': 'font/ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.html': 'text/html',
        '.json': 'application/json',
        '.xml': 'application/xml'
    };

    return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * 安全头中间件
 */
function createSecurityHeadersMiddleware() {
    return (req, res, next) => {
        // 设置安全相关的HTTP头
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // 对于静态资源，设置CORS头
        if (isStaticFile(req.path)) {
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
        }

        next();
    };
}

/**
 * 性能监控中间件
 */
function createPerformanceMiddleware() {
    return (req, res, next) => {
        const start = process.hrtime.bigint();

        // 保存原始的 end 方法
        const originalEnd = res.end;

        // 重写 end 方法以在响应发送前添加性能头
        res.end = function(chunk, encoding) {
            const end = process.hrtime.bigint();
            const duration = Number(end - start) / 1000000; // 转换为毫秒

            // 在响应发送前添加性能头
            if (!res.headersSent) {
                res.setHeader('X-Response-Time', `${duration.toFixed(2)}ms`);
            }

            // 记录慢请求
            if (duration > 1000) { // 超过1秒
                // 只在生产环境记录详细的慢请求日志
                if (process.env.NODE_ENV === 'production') {
                    logger.warn(`慢请求检测: ${req.method} ${req.originalUrl}`, {
                        duration: `${duration.toFixed(2)}ms`,
                        statusCode: res.statusCode,
                        contentLength: res.getHeader('content-length'),
                        userAgent: req.headers['user-agent']
                    });
                } else {
                    logger.debug(`慢请求检测 (开发环境): ${req.method} ${req.originalUrl}`, {
                        duration: `${duration.toFixed(2)}ms`,
                        statusCode: res.statusCode
                    });
                }
            }

            // 集成系统监控（如果可用）
            try {
                const { systemMonitor } = require('../utils/systemMonitor');
                if (systemMonitor && systemMonitor.recordRequest) {
                    systemMonitor.recordRequest(duration, res.statusCode, req.path);
                }
            } catch (error) {
                // 系统监控不可用时静默忽略
            }

            // 调用原始的 end 方法
            return originalEnd.call(this, chunk, encoding);
        };

        next();
    };
}

module.exports = {
    createCompressionMiddleware,
    createStaticCacheMiddleware,
    createPreCompressionMiddleware,
    createSecurityHeadersMiddleware,
    createPerformanceMiddleware
};
