/**
 * 申请记录列表组件
 * 显示所有申请记录
 */

import { PRIORITIES } from '../../scripts/config.js';
import { getApplicationRecords, updateApplication, deleteApplication } from '../../scripts/api/application.js';
import { getFactoryManagers } from '../../scripts/api/user.js';
import toast from '../../scripts/utils/toast.js';
import UnifiedPagination from '../common/UnifiedPagination.js';
import ErrorHandler from '../../scripts/utils/errorHandler.js';
import ContentViewModal from '../common/ContentViewModal.js';

export default {
    components: {
        UnifiedPagination,
        ContentViewModal
    },
    emits: ['view-detail'],
    setup(props, { emit }) {
        const { ref, computed, onMounted } = Vue;

        // 申请列表
        const applications = ref([]);
        const isLoading = ref(true);
        const searchTerm = ref('');

        // 内容查看弹出框
        const showContentModal = ref(false);
        const contentModalData = ref({
            title: '',
            content: ''
        });

        // 搜索筛选相关
        const searchCategory = ref('content'); // 搜索类别：默认为申请内容
        const searchType = ref('all'); // 搜索类型：all, thisWeek, thisMonth, thisYear

        // 日期选择器相关
        const showDatePicker = ref(false); // 默认收起状态

        // 初始化日期：前一周到今天
        const initializeDates = () => {
            const today = new Date();
            const oneWeekAgo = new Date(today);
            oneWeekAgo.setDate(today.getDate() - 7);

            return {
                start: oneWeekAgo.toISOString().split('T')[0],
                end: today.toISOString().split('T')[0]
            };
        };

        const { start: initialStart, end: initialEnd } = initializeDates();
        const startDate = ref(initialStart);
        const endDate = ref(initialEnd);

        // 格式化日期显示
        const formattedStartDate = computed(() => {
            return startDate.value.replace(/-/g, '/');
        });

        const formattedEndDate = computed(() => {
            return endDate.value.replace(/-/g, '/');
        });

        // 计算合计金额
        const totalAmount = computed(() => {
            return filteredApplications.value.reduce((sum, app) => {
                const amount = parseFloat(app.amount) || 0;
                return sum + amount;
            }, 0);
        });

        // 格式化金额显示
        const formattedTotalAmount = computed(() => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 2
            }).format(totalAmount.value);
        });

        // 分页相关
        const currentPage = ref(1);
        const itemsPerPage = ref(10);

        // 编辑相关状态
        const showEditModal = ref(false);
        const editingApplication = ref(null);
        const editForm = ref({
            content: '',
            amount: '',
            priority: 'normal',
            date: '',
            applicant: '',
            department: '',
            type: 'standard',
            needDirectorApproval: false,
            selectedFactoryManagers: []
        });
        const selectedFiles = ref([]);
        const factoryManagers = ref([]);
        const dragOver = ref(false);

        // 过滤后的申请列表（用于搜索和时间筛选）
        const searchFilteredApplications = computed(() => {
            let filtered = applications.value;

            // 时间筛选
            if (searchType.value !== 'all') {
                const now = new Date();
                const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

                filtered = filtered.filter(app => {
                    const appDate = new Date(app.date);

                    switch (searchType.value) {
                        case 'thisWeek':
                            const weekStart = new Date(today);
                            weekStart.setDate(today.getDate() - today.getDay());
                            return appDate >= weekStart;
                        case 'thisMonth':
                            const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                            return appDate >= monthStart;
                        case 'thisYear':
                            const yearStart = new Date(today.getFullYear(), 0, 1);
                            return appDate >= yearStart;
                        default:
                            return true;
                    }
                });
            }

            // 自定义日期范围筛选
            if (showDatePicker.value && startDate.value && endDate.value) {
                const start = new Date(startDate.value);
                const end = new Date(endDate.value);
                // 设置结束日期为当天的23:59:59，确保包含整天
                end.setHours(23, 59, 59, 999);

                filtered = filtered.filter(app => {
                    const appDate = new Date(app.date);
                    return appDate >= start && appDate <= end;
                });
            }

            // 搜索词筛选
            if (searchTerm.value) {
                const term = searchTerm.value.toLowerCase();
                filtered = filtered.filter(app => {
                    switch (searchCategory.value) {
                        case 'applicant':
                            return app.applicant.toLowerCase().includes(term);
                        case 'content':
                            return app.content && app.content.toLowerCase().includes(term);
                        default:
                            // 默认搜索所有字段
                            return app.applicant.toLowerCase().includes(term) ||
                                   app.department.toLowerCase().includes(term) ||
                                   (app.content && app.content.toLowerCase().includes(term)) ||
                                   (app.applicationNumber && app.applicationNumber.toLowerCase().includes(term)) ||
                                   app.id.toLowerCase().includes(term);
                    }
                });
            }

            return filtered;
        });

        // 分页后的申请列表（最终显示的数据）
        const filteredApplications = computed(() => {
            const filtered = searchFilteredApplications.value;
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return filtered.slice(start, end);
        });

        // 总记录数（用于分页组件）
        const totalItems = computed(() => {
            return searchFilteredApplications.value.length;
        });

        // 监听搜索变化，重置页码
        const { watch } = Vue;
        watch(searchTerm, () => {
            currentPage.value = 1;
        });

        // 监听时间筛选变化，重置页码
        watch(searchType, () => {
            currentPage.value = 1;
        });

        // 监听搜索类别变化，重置页码
        watch(searchCategory, () => {
            currentPage.value = 1;
        });

        // 初始化
        onMounted(() => {
            loadApplications();
        });

        // 加载申请列表
        async function loadApplications() {
            try {
                console.log('开始加载申请记录...');
                isLoading.value = true;
                const records = await getApplicationRecords();
                console.log(`从API获取到 ${records.length} 条申请记录`);
                applications.value = records;
                console.log('申请记录已更新到组件状态');
            } catch (error) {
                ErrorHandler.handleAPIError(error, '加载申请记录');
            } finally {
                isLoading.value = false;
            }
        }

        // 查看详情
        function viewDetail(id) {
            emit('view-detail', id);
        }

        // 查看申请内容
        function viewContent(application) {
            contentModalData.value = {
                title: `申请内容 - ${application.applicant}`,
                content: application.content || '暂无申请内容'
            };
            showContentModal.value = true;
        }

        // 关闭内容查看弹出框
        function closeContentModal() {
            showContentModal.value = false;
        }

        // 分页处理
        function handlePageChange(page) {
            currentPage.value = page;
        }

        // 搜索处理
        function handleSearch() {
            currentPage.value = 1; // 搜索时重置到第一页
        }

        // 导出Excel
        async function exportToExcel() {
            try {
                // 检查是否已经加载ExcelJS
                if (typeof window.ExcelJS === 'undefined') {
                    // 动态加载ExcelJS脚本
                    await loadExcelJS();
                }

                const ExcelJS = window.ExcelJS;

                // 创建工作簿
                const workbook = new ExcelJS.Workbook();
                const worksheet = workbook.addWorksheet('申请记录');

                // 设置列标题和宽度
                worksheet.columns = [
                    { header: '申请编号', key: 'id', width: 15 },
                    { header: '申请人', key: 'applicant', width: 12 },
                    { header: '部门', key: 'department', width: 15 },
                    { header: '申请内容', key: 'content', width: 30 },
                    { header: '申请金额', key: 'amount', width: 12 },
                    { header: '申请日期', key: 'date', width: 12 },
                    { header: '紧急程度', key: 'priority', width: 10 },
                    { header: '状态', key: 'status', width: 10 }
                ];

                // 设置标题行样式
                const headerRow = worksheet.getRow(1);
                headerRow.font = { bold: true };
                headerRow.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFE6F3FF' }
                };
                headerRow.alignment = { horizontal: 'center' };

                // 添加数据行
                filteredApplications.value.forEach(app => {
                    worksheet.addRow({
                        id: app.applicationNumber || app.id.substring(0, 8),
                        applicant: app.applicant,
                        department: app.department,
                        content: app.content,
                        amount: parseFloat(app.amount) || 0,
                        date: new Date(app.date).toLocaleDateString('zh-CN'),
                        priority: getPriorityText(app.priority),
                        status: getStatusText(app.status)
                    });
                });

                // 添加合计行
                const totalRow = worksheet.addRow({
                    id: '',
                    applicant: '',
                    department: '',
                    content: '',
                    amount: `合计: ¥${totalAmount.value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
                    date: '',
                    priority: '',
                    status: ''
                });
                totalRow.font = { bold: true };
                totalRow.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFFFEAA7' }
                };

                // 设置边框
                worksheet.eachRow((row) => {
                    row.eachCell((cell) => {
                        cell.border = {
                            top: { style: 'thin' },
                            left: { style: 'thin' },
                            bottom: { style: 'thin' },
                            right: { style: 'thin' }
                        };
                    });
                });

                // 生成文件名
                let dateRange;
                if (showDatePicker.value) {
                    // 使用选择的日期范围，格式化为YYYYMMDD-YYYYMMDD
                    const startFormatted = startDate.value.replace(/-/g, '');
                    const endFormatted = endDate.value.replace(/-/g, '');
                    dateRange = `${startFormatted}-${endFormatted}`;
                } else {
                    // 使用当前日期，格式化为YYYYMMDD
                    const today = new Date();
                    const year = today.getFullYear();
                    const month = String(today.getMonth() + 1).padStart(2, '0');
                    const day = String(today.getDate()).padStart(2, '0');
                    dateRange = `${year}${month}${day}`;
                }
                const fileName = `申请记录_${dateRange}.xlsx`;

                // 导出文件
                const buffer = await workbook.xlsx.writeBuffer();
                const blob = new Blob([buffer], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });

                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                toast.success(`Excel文件已导出：${fileName}`);
            } catch (error) {
                console.error('导出Excel失败:', error);
                let errorMessage = '导出Excel失败：';
                if (error.message.includes('ExcelJS')) {
                    errorMessage += 'ExcelJS库加载失败，请检查文件是否存在';
                } else if (error.message.includes('无法加载')) {
                    errorMessage += '无法加载ExcelJS库文件，请检查路径';
                } else {
                    errorMessage += error.message || '未知错误，请重试';
                }
                toast.error(errorMessage);
            }
        }

        // 切换日期选择器显示
        function toggleDatePicker() {
            showDatePicker.value = !showDatePicker.value;
        }

        // 处理日期变化
        function handleDateChange() {
            // 当日期变化时重置到第一页
            currentPage.value = 1;
        }

        // 动态加载ExcelJS库
        function loadExcelJS() {
            return new Promise((resolve, reject) => {
                if (typeof window.ExcelJS !== 'undefined') {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                // 使用正确的路径，与HTML页面中的其他脚本路径保持一致
                script.src = '/js/libs/exceljs.min.js';
                script.onload = () => {
                    // 等待一小段时间确保库完全加载
                    setTimeout(() => {
                        if (typeof window.ExcelJS !== 'undefined') {
                            resolve();
                        } else {
                            reject(new Error('ExcelJS库加载失败 - 库未正确暴露到window对象'));
                        }
                    }, 100);
                };
                script.onerror = () => {
                    reject(new Error(`无法加载ExcelJS库文件: ${script.src}`));
                };
                document.head.appendChild(script);
            });
        }



        // 编辑申请
        async function handleEdit(application) {
            editingApplication.value = application;
            editForm.value = {
                content: application.content || '',
                amount: application.amount || '',
                priority: application.priority || 'normal',
                date: application.date || '',
                applicant: application.applicant || '',
                department: application.department || '',
                type: application.type || 'standard',
                needDirectorApproval: application.needDirectorApproval || false,
                selectedFactoryManagers: application.selectedFactoryManagers || []
            };
            selectedFiles.value = [];

            // 如果是标准申请，加载厂长列表
            if (application.type === 'standard') {
                await loadFactoryManagers();
            }

            showEditModal.value = true;
        }

        // 加载厂长用户列表
        async function loadFactoryManagers() {
            try {
                const response = await getFactoryManagers();
                factoryManagers.value = response.factoryManagers || [];
            } catch (error) {
                console.error('加载厂长列表失败:', error);
                factoryManagers.value = [];
            }
        }

        // 保存编辑
        async function saveEdit() {
            if (!editingApplication.value) return;

            // 表单验证
            if (!editForm.value.content.trim()) {
                alert('请填写申请内容');
                return;
            }

            if (!editForm.value.date) {
                alert('请选择申请日期');
                return;
            }

            // 验证标准申请必须选择厂长
            if (editForm.value.type === 'standard' &&
                (!editForm.value.selectedFactoryManagers || editForm.value.selectedFactoryManagers.length === 0)) {
                alert('标准申请必须选择至少一个厂长');
                return;
            }

            try {
                const result = await updateApplication(
                    editingApplication.value.id,
                    editForm.value,
                    selectedFiles.value
                );

                if (result.success) {
                    alert('修改成功！');
                    showEditModal.value = false;
                    await loadApplications();
                } else {
                    alert('修改失败: ' + result.message);
                }
            } catch (error) {
                console.error('修改失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    sessionStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('修改失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        // 取消编辑
        function cancelEdit() {
            showEditModal.value = false;
            editingApplication.value = null;
            selectedFiles.value = [];
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            processFiles(files);
        }

        // 处理文件拖放
        function handleFileDrop(event) {
            dragOver.value = false;
            const files = Array.from(event.dataTransfer.files);
            processFiles(files);
        }

        // 处理文件
        function processFiles(files) {
            const validFiles = files.filter(file =>
                file.size <= 5 * 1024 * 1024 &&
                /\.(pdf|docx?|jpg|png|csv)$/i.test(file.name)
            );

            // 处理无效文件
            const invalidFiles = files.filter(file => !validFiles.includes(file));
            if(invalidFiles.length > 0) {
                alert(`以下文件不符合要求：\n${invalidFiles.map(f => f.name).join('\n')}`);
            }

            // 覆盖选中的文件（不是追加）
            selectedFiles.value = validFiles;
        }

        // 移除文件
        function removeFile(index) {
            selectedFiles.value.splice(index, 1);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 删除申请
        async function handleDelete(id) {
            if (!confirm('确定要删除这条申请记录吗？')) return;

            // 找到要删除的申请项
            const targetIndex = applications.value.findIndex(app => app.id === id);
            if (targetIndex === -1) {
                alert('申请不存在');
                return;
            }

            // 保存要删除的申请项（用于失败时恢复）
            const deletedApplication = applications.value[targetIndex];

            // 乐观更新：立即从前端列表中移除
            applications.value.splice(targetIndex, 1);

            try {
                const result = await deleteApplication(id);

                if (result.success) {
                    // 显示成功提示（非阻塞）
                    toast.success('申请删除成功');
                } else {
                    // API返回失败，恢复申请到列表中
                    applications.value.splice(targetIndex, 0, deletedApplication);
                    alert('删除失败: ' + result.message);
                }
            } catch (error) {
                // API调用失败，恢复申请到列表中
                applications.value.splice(targetIndex, 0, deletedApplication);

                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    sessionStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('删除失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        // 获取优先级样式
        function getPriorityClass(priority) {
            return PRIORITIES[priority]?.class || 'bg-gray-100';
        }

        // 获取状态样式
        function getStatusClass(status) {
            switch (status) {
                case 'pending':
                    return 'bg-yellow-100 text-yellow-800';
                case 'approved':
                    return 'bg-green-100 text-green-800';
                case 'rejected':
                    return 'bg-red-100 text-red-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            switch (priority) {
                case 'normal':
                    return '普通';
                case 'medium':
                    return '中等';
                case 'urgent':
                    return '紧急';
                default:
                    return '普通';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return '审批中';
                case 'approved':
                    return '已通过';
                case 'rejected':
                    return '已拒绝';
                default:
                    return '未知';
            }
        }

        // 获取当前阶段文本
        function getStageText(stage) {
            switch (stage) {
                case 'factory_manager':
                    return '厂长审批';
                case 'director':
                    return '总监审批';
                case 'manager':
                    return '经理审批';
                case 'ceo':
                    return 'CEO审批';
                case 'completed':
                    return '已完成';
                case 'rejected':
                    return '已拒绝';
                default:
                    return '未知';
            }
        }

        return {
            applications,
            searchTerm,
            filteredApplications,
            isLoading,
            showEditModal,
            editingApplication,
            editForm,
            selectedFiles,
            factoryManagers,
            dragOver,
            // 搜索筛选相关
            searchCategory,
            searchType,
            handleSearch,
            exportToExcel,
            // 日期选择器相关
            startDate,
            endDate,
            formattedStartDate,
            formattedEndDate,
            showDatePicker,
            toggleDatePicker,
            handleDateChange,
            loadExcelJS,
            // 合计金额相关
            totalAmount,
            formattedTotalAmount,
            // 分页相关
            currentPage,
            itemsPerPage,
            totalItems,
            handlePageChange,
            viewDetail,
            viewContent,
            closeContentModal,
            showContentModal,
            contentModalData,
            handleEdit,
            saveEdit,
            cancelEdit,
            handleDelete,
            handleFileSelect,
            handleFileDrop,
            processFiles,
            removeFile,
            formatFileSize,
            getPriorityClass,
            getStatusClass,
            getPriorityText,
            getStatusText,
            getStageText
        };
    },
    template: `
        <div>
            <!-- 搜索区域 -->
            <div class="mb-6 flex items-center justify-between gap-4">
                <!-- 左侧搜索控件 -->
                <div class="flex items-center gap-3">
                    <!-- 时间筛选下拉框 -->
                    <select v-model="searchType"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-400 bg-white">
                        <option value="all">全部</option>
                        <option value="thisWeek">本周</option>
                        <option value="thisMonth">本月</option>
                        <option value="thisYear">本年</option>
                    </select>

                    <!-- 搜索字段下拉框 -->
                    <select v-model="searchCategory"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-400 bg-white">
                        <option value="content">申请内容</option>
                        <option value="applicant">申请人</option>
                    </select>

                    <!-- 搜索输入框 -->
                    <input type="text"
                           v-model="searchTerm"
                           placeholder="请输入搜索内容..."
                           class="w-80 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-400"
                           @keyup.enter="handleSearch">

                    <!-- 搜索按钮 -->
                    <button @click="handleSearch"
                            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none">
                        搜索
                    </button>
                </div>

                <!-- 右侧操作按钮 -->
                <div class="flex items-center gap-2 relative">
                    <!-- 日期选择器区域 - 向左展开 -->
                    <div class="relative flex items-center">
                        <!-- 展开的日期显示区域 - 在蓝色按钮左侧 -->
                        <div v-if="showDatePicker"
                             class="flex items-center gap-2 border border-gray-300 rounded-md px-3 py-2 bg-white mr-2 transition-all duration-200">
                            <!-- 开始日期输入 -->
                            <input type="date"
                                   v-model="startDate"
                                   @change="handleDateChange"
                                   class="text-sm text-gray-700 border-none focus:outline-none bg-transparent w-28">

                            <!-- 至 -->
                            <span class="text-gray-500 text-sm mx-2">至</span>

                            <!-- 结束日期输入 -->
                            <input type="date"
                                   v-model="endDate"
                                   @change="handleDateChange"
                                   class="text-sm text-gray-700 border-none focus:outline-none bg-transparent w-28">
                        </div>

                        <!-- 蓝色日历图标按钮 -->
                        <button @click="toggleDatePicker"
                                class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none"
                                title="选择日期范围">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- 导出Excel按钮 -->
                    <button @click="exportToExcel"
                            class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出Excel
                    </button>


                </div>
            </div>

            <div v-if="isLoading" class="text-center py-8">
                <div class="inline-block w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                <p class="mt-2 text-gray-500">加载中...</p>
            </div>

            <div v-else-if="filteredApplications.length === 0" class="text-center py-8">
                <p class="text-gray-500">暂无申请记录</p>
            </div>

            <div v-else class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-50 text-gray-600 text-sm">
                            <th class="py-3 px-4 text-left">申请编号</th>
                            <th class="py-3 px-4 text-left">申请人</th>
                            <th class="py-3 px-4 text-left">申请部门</th>
                            <th class="py-3 px-4 text-left">申请日期</th>
                            <th class="py-3 px-4 text-left">紧急程度</th>
                            <th class="py-3 px-4 text-left">申请内容</th>
                            <th class="py-3 px-4 text-left">申请金额</th>
                            <th class="py-3 px-4 text-left">状态</th>
                            <th class="py-3 px-4 text-left">操作</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-600">
                        <tr v-for="app in filteredApplications" :key="app.id" class="border-b border-gray-100 hover:bg-gray-50">
                            <!-- 申请编号 -->
                            <td class="py-3 px-4">{{ app.applicationNumber || app.id.substring(0, 8) }}</td>
                            <!-- 申请人 -->
                            <td class="py-3 px-4">{{ app.applicant }}</td>
                            <!-- 申请部门 -->
                            <td class="py-3 px-4">{{ app.department }}</td>
                            <!-- 申请日期 -->
                            <td class="py-3 px-4">{{ app.date }}</td>
                            <!-- 紧急程度 -->
                            <td class="py-3 px-4">
                                <span :class="['px-2 py-1 rounded-full text-xs', getPriorityClass(app.priority)]">
                                    {{ app.priority === 'normal' ? '普通' : app.priority === 'medium' ? '中等' : '紧急' }}
                                </span>
                            </td>
                            <!-- 申请内容 -->
                            <td class="py-3 px-4">
                                <div class="max-w-xs">
                                    <button @click="viewContent(app)"
                                            class="text-left w-full truncate text-gray-900 hover:text-gray-700 focus:outline-none cursor-pointer transition-colors duration-200"
                                            :title="app.content ? '点击查看完整内容' : '暂无内容'">
                                        {{ app.content ? (app.content.length > 30 ? app.content.substring(0, 30) + '...' : app.content) : '-' }}
                                    </button>
                                </div>
                            </td>
                            <!-- 申请金额 -->
                            <td class="py-3 px-4">
                                <span v-if="app.amount">¥{{ app.amount }}</span>
                                <span v-else class="text-gray-400">-</span>
                            </td>
                            <!-- 状态 -->
                            <td class="py-3 px-4">
                                <span :class="['px-2 py-1 rounded-full text-xs', getStatusClass(app.status)]">
                                    {{ getStatusText(app.status) }}
                                </span>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-1">
                                    <!-- 查看按钮 -->
                                    <button @click="viewDetail(app.id)"
                                            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 transition-all duration-200 group"
                                            title="查看详情">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>

                                    <!-- 修改按钮 -->
                                    <button v-if="app.canEdit"
                                            @click="handleEdit(app)"
                                            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-emerald-50 text-emerald-600 hover:bg-emerald-100 hover:text-emerald-700 transition-all duration-200 group"
                                            title="修改申请">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>

                                    <!-- 删除按钮 -->
                                    <button v-if="app.canDelete"
                                            @click="handleDelete(app.id)"
                                            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 transition-all duration-200 group"
                                            title="删除申请">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- 合计行 -->
                        <tr class="bg-gray-50 border-t-2 border-gray-200">
                            <td class="py-3 px-4"></td>
                            <td class="py-3 px-4"></td>
                            <td class="py-3 px-4"></td>
                            <td class="py-3 px-4"></td>
                            <td class="py-3 px-4"></td>
                            <td class="py-3 px-4 text-right font-bold text-gray-700">合计：</td>
                            <td class="py-3 px-4 text-lg font-bold text-gray-700">
                                {{ formattedTotalAmount }}
                            </td>
                            <td class="py-3 px-4"></td>
                            <td class="py-3 px-4"></td>
                        </tr>
                    </tbody>
                </table>

                <!-- 分页组件 -->
                <unified-pagination
                    :current-page="currentPage"
                    :total-items="totalItems"
                    :items-per-page="itemsPerPage"
                    @page-change="handlePageChange">
                </unified-pagination>
            </div>

            <!-- 编辑申请模态框 -->
            <div v-if="showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                    <h3 class="text-lg font-semibold mb-4">修改申请</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 左侧：基本信息 -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">申请人</label>
                                <input v-model="editForm.applicant"
                                       type="text"
                                       class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                                       readonly>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                                <input v-model="editForm.department"
                                       type="text"
                                       class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                                       readonly>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">申请类型</label>
                                <select v-model="editForm.type"
                                        class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                                    <option value="standard">标准申请</option>
                                    <option value="other">其他申请</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">申请日期</label>
                                <input v-model="editForm.date"
                                       type="date"
                                       class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                                       required>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">申请金额</label>
                                <input v-model="editForm.amount"
                                       type="text"
                                       class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                                       placeholder="请输入金额">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">紧急程度</label>
                                <select v-model="editForm.priority"
                                        class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                                    <option value="normal">普通</option>
                                    <option value="medium">中等</option>
                                    <option value="urgent">紧急</option>
                                </select>
                            </div>
                        </div>

                        <!-- 右侧：申请内容和附件 -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">申请内容 *</label>
                                <textarea v-model="editForm.content"
                                          class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                                          rows="4" required placeholder="请详细描述申请内容"></textarea>
                            </div>

                            <!-- 标准申请的厂长选择 -->
                            <div v-if="editForm.type === 'standard'">
                                <label class="block text-sm font-medium text-gray-700 mb-2">选择厂长 *</label>
                                <div class="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                                    <label v-for="manager in factoryManagers" :key="manager.id"
                                           class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-1 rounded">
                                        <input type="checkbox"
                                               :value="manager"
                                               v-model="editForm.selectedFactoryManagers"
                                               class="rounded border-gray-300">
                                        <span class="text-sm">{{ manager.username }}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- 其他申请的总监审批选项 -->
                            <div v-if="editForm.type === 'other'">
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="checkbox"
                                           v-model="editForm.needDirectorApproval"
                                           class="rounded border-gray-300">
                                    <span class="text-sm">需要总监审批</span>
                                </label>
                                <p class="text-xs text-gray-500 mt-1">
                                    不勾选将直接提交给CEO审批
                                </p>
                            </div>

                            <!-- 附件上传区域 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">附件上传</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"
                                     :class="{ 'border-blue-400 bg-blue-50': dragOver }"
                                     @dragover.prevent="dragOver = true"
                                     @dragleave.prevent="dragOver = false"
                                     @drop.prevent="handleFileDrop">
                                    <input type="file"
                                           multiple
                                           accept=".pdf,.doc,.docx,.jpg,.png,.csv"
                                           @change="handleFileSelect"
                                           class="hidden"
                                           ref="fileInput">
                                    <div class="space-y-2">
                                        <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        <p class="text-sm text-gray-600">
                                            拖拽文件到此处或
                                            <button type="button"
                                                    @click="$refs.fileInput.click()"
                                                    class="text-blue-500 hover:text-blue-700 underline">
                                                点击选择文件
                                            </button>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            支持 PDF、Word、图片、CSV 格式，单个文件不超过 5MB
                                        </p>
                                        <p class="text-xs text-red-500">
                                            注意：上传新附件将覆盖原有附件
                                        </p>
                                    </div>
                                </div>

                                <!-- 已选择的文件列表 -->
                                <div v-if="selectedFiles.length > 0" class="mt-3">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">新选择的文件：</h4>
                                    <div class="space-y-2">
                                        <div v-for="(file, index) in selectedFiles" :key="index"
                                             class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                                            <div class="flex items-center space-x-2">
                                                <svg class="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700">{{ file.name }}</span>
                                                <span class="text-xs text-gray-500">({{ formatFileSize(file.size) }})</span>
                                            </div>
                                            <button @click="removeFile(index)"
                                                    class="text-red-500 hover:text-red-700">
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 显示当前附件 -->
                                <div v-if="editingApplication && editingApplication.attachments && editingApplication.attachments.length > 0" class="mt-3">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">当前附件：</h4>
                                    <div class="space-y-2">
                                        <div v-for="attachment in editingApplication.attachments" :key="attachment.id"
                                             class="flex items-center space-x-2 p-2 bg-blue-50 rounded-md">
                                            <svg class="h-4 w-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span class="text-sm text-blue-700">{{ attachment.name }}</span>
                                            <span class="text-xs text-blue-500">({{ formatFileSize(attachment.size) }})</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button @click="cancelEdit"
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button @click="saveEdit"
                                class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                            保存修改
                        </button>
                    </div>
                </div>
            </div>

            <!-- 内容查看弹出框 -->
            <content-view-modal
                :visible="showContentModal"
                :title="contentModalData.title"
                :content="contentModalData.content"
                @close="closeContentModal"
            />
        </div>
    `
};
