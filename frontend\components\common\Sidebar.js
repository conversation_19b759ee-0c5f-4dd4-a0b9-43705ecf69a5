/**
 * 侧边导航栏组件
 * 显示导航菜单和用户信息
 */

import { logout } from '../../scripts/api/auth.js';
import { getPendingApplications } from '../../scripts/api/application.js';

export default {
    props: {
        user: Object,
        sidebarOpen: {
            type: <PERSON><PERSON>an,
            default: false
        }
    },
    setup(props) {
        const { ref, computed, onMounted, onUnmounted } = Vue;

        // 响应式用户信息状态
        const sessionUser = ref(null);

        // 从sessionStorage加载用户信息
        function loadUserFromSession() {
            try {
                const userJson = sessionStorage.getItem('user');
                if (userJson) {
                    sessionUser.value = JSON.parse(userJson);
                }
            } catch (error) {
                console.warn('无法从sessionStorage获取用户信息:', error);
                sessionUser.value = null;
            }
        }

        // 智能用户信息获取：优先使用props.user，如果没有则从sessionStorage获取
        const currentUser = computed(() => {
            if (props.user) {
                return props.user;
            }
            return sessionUser.value;
        });

        // 监听用户信息更新事件
        function handleUserUpdated(event) {
            console.log('Sidebar收到用户更新事件:', event.detail);
            sessionUser.value = event.detail;
        }

        // 获取当前页面路径
        const currentPath = window.location.pathname;

        // 控制申请管理主菜单的展开状态
        const applicationManagementExpanded = ref(false);

        // 控制生产排程管理主菜单的展开状态
        const scheduleManagementExpanded = ref(false);

        // 控制设备管理主菜单的展开状态
        const equipmentManagementExpanded = ref(false);

        // 控制系统管理主菜单的展开状态
        const systemManagementExpanded = ref(false);

        // 控制质量管理主菜单的展开状态
        const qualityManagementExpanded = ref(false);

        // 控制文件管理主菜单的展开状态
        const fileManagementExpanded = ref(false);

        // 控制智能排程子菜单的展开状态
        const intelligentSchedulingExpanded = ref(false);

        // 控制产品管理子菜单的展开状态
        const productManagementExpanded = ref(false);

        // 控制新建申请子菜单的展开状态
        const newApplicationExpanded = ref(false);

        // 待审核数量
        const pendingCount = ref(0);

        // 检查当前路径是否是申请相关页面
        const isApplicationPath = currentPath.includes('new-application') ||
                                 currentPath.includes('application-record') ||
                                 currentPath.includes('pending-approval') ||
                                 currentPath.includes('approved-applications');

        // 检查当前路径是否是排程相关页面（包括智能排程和产品管理）
        const isSchedulePath = currentPath.includes('schedule') ||
                              currentPath.includes('scheduling') ||
                              currentPath.includes('product') ||
                              currentPath.includes('operator') ||
                              currentPath.includes('admin/algorithm-tuning');

        // 检查当前路径是否是设备相关页面
        const isEquipmentPath = currentPath.includes('equipment');

        // 检查当前路径是否是质量管理相关页面
        const isQualityPath = currentPath.includes('quality');

        // 检查当前路径是否是文件管理相关页面
        const isFileManagementPath = currentPath.includes('file-upload') || currentPath.includes('file-list') || currentPath.includes('file-notifications');

        // 检查当前路径是否是智能排程相关页面
        const isIntelligentSchedulingPath = currentPath.includes('scheduling') ||
                                           currentPath.includes('admin/algorithm-tuning');

        // 检查当前路径是否是产品管理相关页面
        const isProductPath = currentPath.includes('product') || currentPath.includes('operator');

        // 检查当前路径是否是系统管理相关页面
        const isSystemManagementPath = currentPath.includes('system-management') ||
                                       currentPath.includes('user-management') ||
                                       currentPath.includes('customer-management');

        // 检查当前路径是否是新建申请相关页面
        const isNewApplicationPath = currentPath.includes('new-application');

        // 如果当前在申请相关页面，默认展开申请管理菜单
        if (isApplicationPath) {
            applicationManagementExpanded.value = true;
        }

        // 如果当前在排程相关页面，默认展开排程管理菜单
        if (isSchedulePath) {
            scheduleManagementExpanded.value = true;
        }

        // 如果当前在设备相关页面，默认展开设备管理菜单
        if (isEquipmentPath) {
            equipmentManagementExpanded.value = true;
        }

        // 如果当前在质量管理相关页面，默认展开质量管理菜单
        if (isQualityPath) {
            qualityManagementExpanded.value = true;
        }

        // 如果当前在文件管理相关页面，默认展开文件管理菜单
        if (isFileManagementPath) {
            fileManagementExpanded.value = true;
        }

        // 如果当前在系统管理相关页面，默认展开系统管理菜单
        if (isSystemManagementPath) {
            systemManagementExpanded.value = true;
        }

        // 如果当前在排程相关页面，默认展开排程管理菜单
        if (isSchedulePath) {
            scheduleManagementExpanded.value = true;
        }

        // 如果当前在智能排程相关页面，默认展开智能排程子菜单
        if (isIntelligentSchedulingPath) {
            scheduleManagementExpanded.value = true;
            intelligentSchedulingExpanded.value = true;
        }

        // 如果当前在产品管理相关页面，默认展开产品管理子菜单
        if (isProductPath) {
            scheduleManagementExpanded.value = true;
            productManagementExpanded.value = true;
        }

        // 如果当前在新建申请页面，默认展开子菜单
        if (isNewApplicationPath) {
            newApplicationExpanded.value = true;
        }

        // 获取当前URL的查询参数
        const urlSearchParams = new URLSearchParams(window.location.search);
        const typeParam = urlSearchParams.get('type');

        // 检查当前是否是标准申请页面
        const isStandardApplication = isNewApplicationPath && (!typeParam || typeParam !== 'other');

        // 检查当前是否是其他申请页面
        const isOtherApplication = isNewApplicationPath && typeParam === 'other';

        // 切换申请管理主菜单的展开状态
        function toggleApplicationManagementMenu() {
            applicationManagementExpanded.value = !applicationManagementExpanded.value;
            // 展开时滚动到可见区域
            if (applicationManagementExpanded.value) {
                scrollToExpandedMenu('application-management');
            }
        }

        // 切换排程管理主菜单的展开状态
        function toggleScheduleManagementMenu() {
            scheduleManagementExpanded.value = !scheduleManagementExpanded.value;
            if (scheduleManagementExpanded.value) {
                scrollToExpandedMenu('schedule-management');
            }
        }

        // 切换设备管理主菜单的展开状态
        function toggleEquipmentManagementMenu() {
            equipmentManagementExpanded.value = !equipmentManagementExpanded.value;
            if (equipmentManagementExpanded.value) {
                scrollToExpandedMenu('equipment-management');
            }
        }

        // 切换系统管理主菜单的展开状态
        function toggleSystemManagementMenu() {
            systemManagementExpanded.value = !systemManagementExpanded.value;
            if (systemManagementExpanded.value) {
                scrollToExpandedMenu('system-management');
            }
        }

        // 切换质量管理主菜单的展开状态
        function toggleQualityManagementMenu() {
            qualityManagementExpanded.value = !qualityManagementExpanded.value;
            if (qualityManagementExpanded.value) {
                scrollToExpandedMenu('quality-management');
            }
        }

        // 切换文件管理主菜单的展开状态
        function toggleFileManagementMenu() {
            fileManagementExpanded.value = !fileManagementExpanded.value;
            if (fileManagementExpanded.value) {
                scrollToExpandedMenu('file-management');
            }
        }

        // 切换产品管理主菜单的展开状态
        function toggleProductManagementMenu() {
            productManagementExpanded.value = !productManagementExpanded.value;
            if (productManagementExpanded.value) {
                scrollToExpandedMenu('product-management');
            }
        }

        function toggleIntelligentSchedulingMenu() {
            intelligentSchedulingExpanded.value = !intelligentSchedulingExpanded.value;
            if (intelligentSchedulingExpanded.value) {
                scrollToExpandedMenu('intelligent-scheduling');
            }
        }

        // 切换新建申请子菜单的展开状态
        function toggleNewApplicationMenu() {
            newApplicationExpanded.value = !newApplicationExpanded.value;
        }

        // 获取待审核数量
        async function loadPendingCount() {
            try {
                // 检查用户是否有审批相关角色或权限
                const user = currentUser.value;
                const canApprove = user && (
                    user.role === '厂长' ||
                    user.role === 'factory_manager' ||
                    user.role === '总监' ||
                    user.role === 'director' ||
                    user.role === '经理' ||
                    user.role === 'manager' ||
                    user.role === 'CEO' ||
                    user.role === 'ceo' ||
                    user.role === 'admin' ||
                    user.role === '管理员' ||
                    hasPermission('pending_approval')
                );

                if (canApprove) {
                    const response = await getPendingApplications();

                    // 后端直接返回数组数据，不是包装格式
                    if (Array.isArray(response)) {
                        pendingCount.value = response.length;
                    } else if (response && response.data && Array.isArray(response.data)) {
                        // 兼容包装格式
                        pendingCount.value = response.data.length;
                    } else {
                        pendingCount.value = 0;
                    }
                }
            } catch (error) {
                console.error('获取待审核数量失败:', error);
                pendingCount.value = 0;
                // 静默失败，不影响侧边栏显示
            }
        }

        // 监听页面变化，更新待审核数量
        function updatePendingCount() {
            loadPendingCount();
        }

        // 暴露更新方法给全局使用
        window.updateSidebarPendingCount = updatePendingCount;

        // 移除硬编码的角色检查，完全基于权限系统

        // 检查用户是否有特定权限
        const hasPermission = (permissionId) => {
            const user = currentUser.value;
            return user &&
                   user.permissions &&
                   Array.isArray(user.permissions) &&
                   user.permissions.includes(permissionId);
        };

        // 检查用户是否可以审批（基于角色和权限双重检查）
        const canApprove = computed(() => {
            const user = currentUser.value;
            return user && (
                user.role === '厂长' ||
                user.role === 'factory_manager' ||
                user.role === '总监' ||
                user.role === 'director' ||
                user.role === '经理' ||
                user.role === 'manager' ||
                user.role === 'CEO' ||
                user.role === 'ceo' ||
                user.role === 'admin' ||
                user.role === '管理员' ||
                hasPermission('pending_approval')
            );
        });

        // 滚动到展开的菜单项
        function scrollToExpandedMenu(menuId) {
            // 使用nextTick确保DOM已更新
            Vue.nextTick(() => {
                const menuElement = document.querySelector(`[data-menu-id="${menuId}"]`);
                if (menuElement) {
                    const navElement = document.querySelector('.sidebar nav');
                    if (navElement) {
                        // 计算菜单项相对于导航容器的位置
                        const menuRect = menuElement.getBoundingClientRect();
                        const navRect = navElement.getBoundingClientRect();
                        const relativeTop = menuRect.top - navRect.top + navElement.scrollTop;

                        // 滚动到菜单项位置，留出一些边距
                        navElement.scrollTo({
                            top: Math.max(0, relativeTop - 20),
                            behavior: 'smooth'
                        });
                    }
                }
            });
        }

        // 退出登录
        function handleLogout() {
            logout();
        }

        // 检查并滚动到当前活跃的菜单
        function scrollToActiveMenu() {
            Vue.nextTick(() => {
                // 查找当前活跃的菜单项
                const activeMenuItem = document.querySelector('.sidebar nav .bg-blue-50, .sidebar nav .text-blue-600');
                if (activeMenuItem) {
                    const navElement = document.querySelector('.sidebar nav');
                    if (navElement) {
                        const menuRect = activeMenuItem.getBoundingClientRect();
                        const navRect = navElement.getBoundingClientRect();
                        const relativeTop = menuRect.top - navRect.top + navElement.scrollTop;

                        // 如果菜单项不在可视区域内，滚动到该位置
                        if (relativeTop < 0 || relativeTop > navElement.clientHeight - 100) {
                            navElement.scrollTo({
                                top: Math.max(0, relativeTop - 50),
                                behavior: 'smooth'
                            });
                        }
                    }
                }
            });
        }

        // 组件挂载时加载待审核数量和滚动到活跃菜单
        onMounted(() => {
            // 初始加载用户信息
            loadUserFromSession();

            // 监听用户信息更新事件
            window.addEventListener('userUpdated', handleUserUpdated);

            loadPendingCount();
            scrollToActiveMenu();
        });

        // 组件卸载时清理事件监听器
        onUnmounted(() => {
            window.removeEventListener('userUpdated', handleUserUpdated);
        });

        // 监听用户状态变化，重新加载待审核数量
        const { watch } = Vue;
        watch(currentUser, (newUser, oldUser) => {
            if (newUser && newUser !== oldUser) {
                loadPendingCount();
            }
        }, { immediate: true });

        // 监听窗口大小变化，重新调整滚动位置
        onMounted(() => {
            const handleResize = () => {
                scrollToActiveMenu();
            };

            window.addEventListener('resize', handleResize);

            // 组件卸载时清理事件监听器
            return () => {
                window.removeEventListener('resize', handleResize);
            };
        });

        return {
            currentPath,
            currentUser,
            hasPermission,
            handleLogout,
            scrollToExpandedMenu,
            applicationManagementExpanded,
            toggleApplicationManagementMenu,
            scheduleManagementExpanded,
            toggleScheduleManagementMenu,
            equipmentManagementExpanded,
            toggleEquipmentManagementMenu,
            systemManagementExpanded,
            toggleSystemManagementMenu,
            qualityManagementExpanded,
            toggleQualityManagementMenu,
            fileManagementExpanded,
            toggleFileManagementMenu,
            productManagementExpanded,
            toggleProductManagementMenu,
            intelligentSchedulingExpanded,
            toggleIntelligentSchedulingMenu,
            newApplicationExpanded,
            toggleNewApplicationMenu,
            isStandardApplication,
            isOtherApplication,
            pendingCount,
            updatePendingCount,
            canApprove
        };
    },
    template: `
        <div class="sidebar bg-white text-gray-700 w-64 h-screen fixed left-0 top-0 flex flex-col border-r border-gray-100 shadow-md z-10 transform -translate-x-full md:translate-x-0 transition-transform duration-300 ease-in-out" :class="{'translate-x-0': sidebarOpen}">
            <!-- 顶部Logo区域 - 固定高度 -->
            <div class="flex-shrink-0 p-4 border-b border-gray-100">
                <h1 class="text-lg font-medium text-gray-800 flex items-center">
                    <img src="/logo/Makrite-logo.png" alt="Makrite Logo" class="w-16 h-16 mr-3 object-contain">
                    管理系统
                </h1>
            </div>

            <!-- 导航菜单区域 - 可滚动 -->
            <nav class="flex-1 overflow-y-auto px-2 py-3 min-h-0">
                <ul class="space-y-1">
                    <!-- 主页 -->
                    <li>
                        <a href="/dashboard"
                           :class="['sidebar-btn w-full text-left flex items-center px-3 py-2.5 rounded-md text-sm font-medium',
                                   currentPath.includes('dashboard') || currentPath === '/' ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            主页
                        </a>
                    </li>

                    <!-- 系统功能展示 -->
                    <li>
                        <a href="/system-overview"
                           :class="['sidebar-btn w-full text-left flex items-center px-3 py-2.5 rounded-md text-sm font-medium',
                                   currentPath.includes('system-overview') ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            系统功能展示
                        </a>
                    </li>

                    <!-- 成本核算系统 -->
                    <li>
                        <a href="http://192.168.1.104:7000" target="_blank"
                           class="sidebar-btn w-full text-left flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            成本核算系统
                        </a>
                    </li>



                    <!-- 申请管理一级菜单 -->
                    <li v-if="hasPermission('new_application') || hasPermission('application_record') || hasPermission('pending_approval') || hasPermission('approved_applications')">
                        <div data-menu-id="application-management">
                            <button @click="toggleApplicationManagementMenu"
                                   :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2.5 rounded-md text-sm font-medium',
                                           (currentPath.includes('new-application') || currentPath.includes('application-record') || currentPath.includes('pending-approval') || currentPath.includes('approved-applications')) ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    申请管理
                                </div>
                                <svg :class="['w-4 h-4 transition-transform', applicationManagementExpanded ? 'transform rotate-180' : '']"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 申请管理子菜单 -->
                            <div v-show="applicationManagementExpanded" class="mt-1 ml-5 pl-3 border-l border-gray-200 space-y-0.5">
                                <!-- 新建申请子菜单 -->
                                <div v-if="hasPermission('new_application')">
                                    <button @click="toggleNewApplicationMenu"
                                           :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2 rounded-md text-sm font-normal',
                                                   currentPath.includes('new-application') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            新建申请
                                        </div>
                                        <svg :class="['w-4 h-4 transition-transform', newApplicationExpanded ? 'transform rotate-180' : '']"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>

                                    <div v-show="newApplicationExpanded" class="mt-0.5 ml-4 pl-2 border-l border-gray-200">
                                        <a href="/new-application"
                                           :class="['sidebar-btn w-full text-left flex items-center px-3 py-1.5 rounded-md text-sm font-normal',
                                                   isStandardApplication ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                            <span>标准申请</span>
                                        </a>
                                        <a href="/new-application?type=other"
                                           :class="['sidebar-btn w-full text-left flex items-center px-3 py-1.5 rounded-md text-sm font-normal',
                                                   isOtherApplication ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                            <span>其他申请</span>
                                        </a>
                                    </div>
                                </div>

                                <!-- 申请记录 -->
                                <a v-if="hasPermission('application_record')" href="/application-record"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('application-record') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    申请记录
                                </a>

                                <!-- 待审核 -->
                                <a v-if="canApprove" href="/pending-approval"
                                   :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('pending-approval') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                        待审核
                                    </div>
                                    <!-- 待审核数量徽章 -->
                                    <span v-if="pendingCount > 0"
                                          class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[20px] h-5">
                                        {{ pendingCount > 99 ? '99+' : pendingCount }}
                                    </span>
                                </a>

                                <!-- 已审核 -->
                                <a v-if="hasPermission('approved_applications')" href="/approved-applications"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('approved-applications') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    已审核
                                </a>
                            </div>
                        </div>
                    </li>

                    <!-- 设备管理一级菜单 -->
                    <li v-if="hasPermission('equipment_manage') || hasPermission('equipment_info') || hasPermission('equipment_maintenance') || hasPermission('equipment_health')">
                        <div data-menu-id="equipment-management">
                            <button @click="toggleEquipmentManagementMenu"
                                   :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2.5 rounded-md text-sm font-medium',
                                           currentPath.includes('equipment') ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    设备管理
                                </div>
                                <svg :class="['w-4 h-4 transition-transform', equipmentManagementExpanded ? 'transform rotate-180' : '']"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 设备管理子菜单 -->
                            <div v-show="equipmentManagementExpanded" class="mt-1 ml-5 pl-3 border-l border-gray-200 space-y-0.5">
                                <!-- 设备信息 -->
                                <a v-if="hasPermission('equipment_info')" href="/equipment/info"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('equipment/info') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    设备信息
                                </a>

                                <!-- 维修/保养记录 -->
                                <a v-if="hasPermission('equipment_maintenance')" href="/equipment/maintenance"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('equipment/maintenance') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    维修/保养记录
                                </a>

                                <!-- 设备健康度评估 -->
                                <a v-if="hasPermission('equipment_health')" href="/equipment/health"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('equipment/health') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    设备健康度评估
                                </a>

                                <!-- 设备产能管理 -->
                                <a v-if="hasPermission('equipment_edit')" href="/equipment/capacity"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('equipment/capacity') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    设备产能管理
                                </a>
                            </div>
                        </div>
                    </li>

                    <!-- 生产排程管理一级菜单 -->
                    <li v-if="hasPermission('schedule_view')">
                        <div data-menu-id="schedule-management">
                            <button @click="toggleScheduleManagementMenu"
                                   :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2.5 rounded-md text-sm font-medium',
                                           (currentPath.includes('schedule') || currentPath.includes('scheduling') || currentPath.includes('product') || currentPath.includes('operator') || currentPath.includes('admin/algorithm-tuning')) ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                    </svg>
                                    生产排程管理
                                </div>
                                <svg :class="['w-4 h-4 transition-transform', scheduleManagementExpanded ? 'transform rotate-180' : '']"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 生产排程管理子菜单 -->
                            <div v-show="scheduleManagementExpanded" class="mt-1 ml-5 pl-3 border-l border-gray-200 space-y-0.5">
                                <!-- 排程总览 -->
                                <a v-if="hasPermission('schedule_view')" href="/schedule/dashboard"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('schedule/dashboard') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    排程总览
                                </a>

                                <!-- 排程计划 -->
                                <a v-if="hasPermission('schedule_view')" href="/schedule/list"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('schedule/list') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                    排程计划
                                </a>

                                <!-- 智能排程管理二级子菜单 -->
                                <div v-if="hasPermission('scheduling_manage')">
                                    <button @click="toggleIntelligentSchedulingMenu"
                                           :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2 rounded-md text-sm font-normal',
                                                   (currentPath.includes('scheduling') || currentPath.includes('admin/algorithm-tuning')) ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                            </svg>
                                            智能排程管理
                                        </div>
                                        <svg :class="['w-3 h-3 transition-transform', intelligentSchedulingExpanded ? 'transform rotate-180' : '']"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>

                                    <!-- 智能排程三级子菜单 -->
                                    <div v-show="intelligentSchedulingExpanded" class="mt-0.5 ml-6 pl-2 border-l border-gray-200 space-y-0.5">
                                        <!-- 智能排程 -->
                                        <a v-if="hasPermission('scheduling_manage')" href="/scheduling/intelligent"
                                           :class="['sidebar-btn w-full text-left flex items-center px-2 py-1.5 rounded-md text-xs font-normal',
                                                   currentPath.includes('scheduling/intelligent') ? 'text-blue-600 font-medium' : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700']">
                                            <svg class="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                            </svg>
                                            智能排程
                                        </a>



                                        <!-- 算法调优 -->
                                        <a v-if="hasPermission('algorithm_tuning')" href="/admin/algorithm-tuning"
                                           :class="['sidebar-btn w-full text-left flex items-center px-2 py-1.5 rounded-md text-xs font-normal',
                                                   currentPath.includes('admin/algorithm-tuning') ? 'text-blue-600 font-medium' : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700']">
                                            <svg class="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            算法调优
                                        </a>
                                    </div>
                                </div>

                                <!-- 产品管理二级子菜单 -->
                                <div v-if="hasPermission('product_view') || hasPermission('product_create') || hasPermission('product_edit')">
                                    <button @click="toggleProductManagementMenu"
                                           :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2 rounded-md text-sm font-normal',
                                                   currentPath.includes('product') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                            产品管理
                                        </div>
                                        <svg :class="['w-3 h-3 transition-transform', productManagementExpanded ? 'transform rotate-180' : '']"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>

                                    <!-- 产品管理三级子菜单 -->
                                    <div v-show="productManagementExpanded" class="mt-0.5 ml-6 pl-2 border-l border-gray-200 space-y-0.5">
                                        <!-- 产品信息管理 -->
                                        <a v-if="hasPermission('product_view')" href="/product/management"
                                           :class="['sidebar-btn w-full text-left flex items-center px-2 py-1.5 rounded-md text-xs font-normal',
                                                   currentPath.includes('product/management') ? 'text-blue-600 font-medium' : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700']">
                                            <svg class="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                            </svg>
                                            产品信息管理
                                        </a>

                                        <!-- 工艺流程管理 -->
                                        <a v-if="hasPermission('product_edit')" href="/product/processes"
                                           :class="['sidebar-btn w-full text-left flex items-center px-2 py-1.5 rounded-md text-xs font-normal',
                                                   currentPath.includes('product/processes') ? 'text-blue-600 font-medium' : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700']">
                                            <svg class="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                            </svg>
                                            工艺流程管理
                                        </a>

                                        <!-- 操作员技能管理 -->
                                        <a v-if="hasPermission('operator_skill_manage')" href="/operator/skills"
                                           :class="['sidebar-btn w-full text-left flex items-center px-2 py-1.5 rounded-md text-xs font-normal',
                                                   currentPath.includes('operator/skills') ? 'text-blue-600 font-medium' : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700']">
                                            <svg class="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z M12 14l-9-5 9 5z"></path>
                                            </svg>
                                            操作员技能管理
                                        </a>
                                    </div>
                                </div>

                                <!-- 资源管理 -->
                                <a v-if="hasPermission('resource_manage')" href="/schedule/resources"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('schedule/resources') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                    </svg>
                                    资源管理
                                </a>

                                <!-- 数据分析 -->
                                <a v-if="hasPermission('schedule_report')" href="/schedule/reports"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('schedule/reports') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    数据分析
                                </a>
                            </div>
                        </div>
                    </li>

                    <!-- 质量管理一级菜单 -->
                    <li v-if="hasPermission('quality_upload') || hasPermission('quality_view') || hasPermission('quality_download') || hasPermission('quality_manage')">
                        <div data-menu-id="quality-management">
                            <button @click="toggleQualityManagementMenu"
                                   :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2.5 rounded-md text-sm font-medium',
                                           currentPath.includes('quality') ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    质量管理
                                </div>
                                <svg :class="['w-4 h-4 transition-transform', qualityManagementExpanded ? 'transform rotate-180' : '']"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 质量管理子菜单 -->
                            <div v-show="qualityManagementExpanded" class="mt-1 ml-5 pl-3 border-l border-gray-200 space-y-0.5">
                                <!-- 上传检测报告 -->
                                <a v-if="hasPermission('quality_upload')" href="/quality-upload"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('quality-upload') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    上传检测报告
                                </a>

                                <!-- 检测报告列表 -->
                                <a v-if="hasPermission('quality_view')" href="/quality-list"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('quality-list') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                                    </svg>
                                    检测报告列表
                                </a>
                            </div>
                        </div>
                    </li>

                    <!-- 文件管理一级菜单 -->
                    <li v-if="hasPermission('file_upload') || hasPermission('file_view') || hasPermission('file_download') || hasPermission('file_manage') || hasPermission('file_confirm')">
                        <div data-menu-id="file-management">
                            <button @click="toggleFileManagementMenu"
                                   :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2.5 rounded-md text-sm font-medium',
                                           currentPath.includes('file-management') ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    文件管理
                                </div>
                                <svg :class="['w-4 h-4 transition-transform', fileManagementExpanded ? 'transform rotate-180' : '']"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 文件管理子菜单 -->
                            <div v-show="fileManagementExpanded" class="mt-1 ml-5 pl-3 border-l border-gray-200 space-y-0.5">
                                <!-- 上传文件 -->
                                <a v-if="hasPermission('file_upload')" href="/file-upload"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('file-upload') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    上传文件
                                </a>

                                <!-- 文件列表 -->
                                <a v-if="hasPermission('file_view')" href="/file-list"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('file-list') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                                    </svg>
                                    文件列表
                                </a>

                                <!-- 文件通知 -->
                                <a v-if="hasPermission('file_confirm')" href="/file-notifications"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('file-notifications') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    文件通知
                                </a>
                            </div>
                        </div>
                    </li>

                    <li v-if="hasPermission('user_settings')">
                        <a href="/user-settings"
                           :class="['sidebar-btn w-full text-left flex items-center px-3 py-2.5 rounded-md text-sm font-medium',
                                   currentPath.includes('user-settings') ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            个人设置
                        </a>
                    </li>

                    <!-- 系统管理一级菜单 -->
                    <li v-if="currentUser && currentUser.role === 'admin'">
                        <div data-menu-id="system-management">
                            <button @click="toggleSystemManagementMenu"
                                   :class="['sidebar-btn w-full text-left flex items-center justify-between px-3 py-2.5 rounded-md text-sm font-medium',
                                           (currentPath.includes('system-management') || currentPath.includes('user-management') || currentPath.includes('customer-management')) ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900']">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    系统管理
                                </div>
                                <svg :class="['w-4 h-4 transition-transform', systemManagementExpanded ? 'transform rotate-180' : '']"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 系统管理子菜单 -->
                            <div v-show="systemManagementExpanded" class="mt-1 ml-5 pl-3 border-l border-gray-200 space-y-0.5">
                                <!-- 用户管理 -->
                                <a href="/system-management/user-management"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('user-management') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                    </svg>
                                    用户管理
                                </a>

                                <!-- 客户管理 -->
                                <a href="/system-management/customer-management"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('customer-management') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0h3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    客户管理
                                </a>

                                <!-- 系统设置 -->
                                <a href="/system-management/settings"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('system-management/settings') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                    </svg>
                                    系统设置
                                </a>

                                <!-- 系统日志 -->
                                <a href="/system-management/logs"
                                   :class="['sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm font-normal',
                                           currentPath.includes('system-management/logs') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800']">
                                    <svg class="w-4 h-4 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    系统日志
                                </a>
                            </div>
                        </div>
                    </li>

                </ul>
            </nav>

            <!-- 底部用户信息区域 - 固定位置 -->
            <div class="flex-shrink-0 p-4 border-t border-gray-100 bg-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-1 rounded-md text-gray-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <span v-if="currentUser" class="ml-2 text-gray-700 font-medium">{{ currentUser.name }}</span>
                    </div>
                    <button @click="handleLogout"
                            class="p-1 rounded-md text-gray-500 hover:text-red-500 hover:bg-gray-100 transition-colors duration-200 flex items-center"
                            title="退出登录">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `
};
