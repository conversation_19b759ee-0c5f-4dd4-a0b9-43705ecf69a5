/* 质量管理模块通用样式文件 - Quality Common CSS */

/* 此文件包含质量管理模块的通用样式 */
/* 基础样式继承自 /assets/css/common.css */

/* 加载动画样式覆盖 */
.loading-overlay {
    background-color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    width: 40px;
    height: 40px;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-content {
    max-height: 90vh;
    overflow-y: auto;
    width: 100%;
    max-width: 4xl;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .modal-content {
        max-width: 95vw;
        margin: 1rem;
    }
}

/* 表格响应式 */
@media (max-width: 640px) {
    .table-responsive table {
        font-size: 0.875rem;
    }

    .table-responsive th,
    .table-responsive td {
        padding: 0.5rem 0.25rem;
    }
}
