/**
 * 产品数据模型
 * 定义产品相关的数据结构和验证规则
 */

/**
 * 产品数据模型
 */
class ProductModel {
    constructor(data = {}) {
        this.id = data.id || null;
        this.code = data.code || '';
        this.name = data.name || '';
        this.category = data.category || '';
        this.specifications = data.specifications || {};
        this.unit = data.unit || 'pcs';
        this.standardTime = data.standardTime || 0;
        this.createdAt = data.createdAt || new Date().toISOString();
        this.updatedAt = data.updatedAt || new Date().toISOString();
    }

    /**
     * 验证产品数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.code || this.code.trim().length === 0) {
            errors.push('产品编码不能为空');
        }

        if (!this.name || this.name.trim().length === 0) {
            errors.push('产品名称不能为空');
        }

        if (this.standardTime < 0) {
            errors.push('标准工时不能为负数');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 转换为数据库存储格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            code: this.code,
            name: this.name,
            category: this.category,
            specifications: JSON.stringify(this.specifications),
            unit: this.unit,
            standard_time: this.standardTime,
            created_at: this.createdAt,
            updated_at: this.updatedAt
        };
    }

    /**
     * 从数据库格式创建模型实例
     * @param {Object} dbData 数据库数据
     * @returns {ProductModel} 模型实例
     */
    static fromDatabase(dbData) {
        return new ProductModel({
            id: dbData.id,
            code: dbData.code,
            name: dbData.name,
            category: dbData.category,
            specifications: JSON.parse(dbData.specifications || '{}'),
            unit: dbData.unit,
            standardTime: dbData.standard_time,
            createdAt: dbData.created_at,
            updatedAt: dbData.updated_at
        });
    }

    /**
     * 生成产品ID
     * @returns {string} 产品ID
     */
    static generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `PROD${timestamp}${random}`;
    }
}

/**
 * 生产工艺流程数据模型
 */
class ProductionProcessModel {
    constructor(data = {}) {
        this.id = data.id || null;
        this.productId = data.productId || '';
        this.processName = data.processName || '';
        this.sequenceOrder = data.sequenceOrder || 1;
        this.standardTime = data.standardTime || 0;
        this.requiredEquipmentType = data.requiredEquipmentType || '';
        this.skillRequirements = data.skillRequirements || [];
        this.setupTime = data.setupTime || 0;
        this.createdAt = data.createdAt || new Date().toISOString();
    }

    /**
     * 验证工艺流程数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.productId || this.productId.trim().length === 0) {
            errors.push('产品ID不能为空');
        }

        if (!this.processName || this.processName.trim().length === 0) {
            errors.push('工序名称不能为空');
        }

        if (this.sequenceOrder < 1) {
            errors.push('工序顺序必须大于0');
        }

        if (this.standardTime <= 0) {
            errors.push('标准工时必须大于0');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 转换为数据库存储格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            product_id: this.productId,
            process_name: this.processName,
            sequence_order: this.sequenceOrder,
            standard_time: this.standardTime,
            required_equipment_type: this.requiredEquipmentType,
            skill_requirements: JSON.stringify(this.skillRequirements),
            setup_time: this.setupTime,
            created_at: this.createdAt
        };
    }

    /**
     * 从数据库格式创建模型实例
     * @param {Object} dbData 数据库数据
     * @returns {ProductionProcessModel} 模型实例
     */
    static fromDatabase(dbData) {
        return new ProductionProcessModel({
            id: dbData.id,
            productId: dbData.product_id,
            processName: dbData.process_name,
            sequenceOrder: dbData.sequence_order,
            standardTime: dbData.standard_time,
            requiredEquipmentType: dbData.required_equipment_type,
            skillRequirements: JSON.parse(dbData.skill_requirements || '[]'),
            setupTime: dbData.setup_time,
            createdAt: dbData.created_at
        });
    }

    /**
     * 生成工艺流程ID
     * @returns {string} 工艺流程ID
     */
    static generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `PROC${timestamp}${random}`;
    }
}

module.exports = {
    ProductModel,
    ProductionProcessModel
};
