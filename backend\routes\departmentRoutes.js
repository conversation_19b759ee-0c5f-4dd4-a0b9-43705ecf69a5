/**
 * 部门路由
 * 处理部门相关的路由
 */

const express = require('express');
const router = express.Router();
const departmentController = require('../controllers/departmentController');
const { authenticateJWT, authorizeRoles } = require('../middlewares/auth');

// 获取所有部门 - 普通用户可查看（用于下拉选择等）
router.get('/', authenticateJWT, departmentController.getAllDepartments);

// 检查部门名称是否存在 - 需要管理员权限
router.get('/check-name',
    authenticateJWT,
    authorizeRoles('admin'),
    departmentController.checkDepartmentName
);

// 根据ID获取部门 - 普通用户可查看
router.get('/:id', authenticateJWT, departmentController.getDepartmentById);

// 检查部门是否有用户关联 - 需要管理员权限
router.get('/:id/check-users',
    authenticateJWT,
    authorizeRoles('admin'),
    departmentController.checkDepartmentUsers
);

// 创建新部门 - 需要管理员权限
router.post('/',
    authenticateJWT,
    authorizeRoles('admin'),
    departmentController.createDepartment
);

// 更新部门 - 需要管理员权限
router.put('/:id',
    authenticateJWT,
    authorizeRoles('admin'),
    departmentController.updateDepartment
);

// 删除部门 - 需要管理员权限
router.delete('/:id',
    authenticateJWT,
    authorizeRoles('admin'),
    departmentController.deleteDepartment
);

module.exports = router;
