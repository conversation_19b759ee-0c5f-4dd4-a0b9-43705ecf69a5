/**
 * 维修保养记录表单组件
 * 用于创建和编辑维修保养记录
 */

import {
    createMaintenanceRecord,
    updateMaintenanceRecord,
    getMaintenanceOptions
} from '/scripts/api/maintenance.js';
import { getEquipmentList } from '/scripts/api/equipment.js';

export default {
    props: {
        record: {
            type: Object,
            default: null
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    emits: ['success', 'cancel'],
    setup(props, { emit }) {
        const { ref, reactive, onMounted, computed, watch } = Vue;

        // 表单数据
        const formData = reactive({
            equipmentId: '',
            type: 'maintenance',
            severityLevel: '',
            description: '',
            maintenanceDate: new Date().toISOString().split('T')[0],
            startTime: '',
            endTime: '',
            cost: 0,
            technician: '',
            status: 'pending',
            notes: '',
            result: '',
            reviewer: ''
        });

        // 选项数据
        const options = reactive({
            types: [],
            statuses: [],
            severityLevels: [],
            areas: [],
            locations: [],
            equipmentList: []
        });

        // 表单状态
        const loading = ref(false);
        const submitting = ref(false);
        const errors = ref({});
        const selectedArea = ref('');
        const selectedLocation = ref('');
        const currentUserName = ref('');

        // 计算属性
        const formTitle = computed(() => {
            return props.isEdit ? '编辑维修记录' : '添加记录';
        });

        const isFormValid = computed(() => {
            return formData.equipmentId &&
                   formData.type &&
                   formData.description &&
                   formData.maintenanceDate &&
                   formData.technician;
        });

        // 根据选择的厂区过滤位置
        const filteredLocations = computed(() => {
            if (!selectedArea.value) return [];

            // 获取选中厂区的名称
            const selectedAreaName = options.areas.find(area => area.value === selectedArea.value)?.label;
            if (!selectedAreaName) return [];

            // 获取该厂区下的所有设备的位置
            const areaEquipment = options.equipmentList.filter(equipment => equipment.area === selectedAreaName);
            const uniqueLocations = [...new Set(areaEquipment.map(eq => eq.location))];

            return uniqueLocations.map(location => ({
                value: location,
                label: location
            }));
        });

        // 根据选择的厂区和位置过滤设备
        const filteredEquipment = computed(() => {
            if (!selectedArea.value) return [];

            // 获取选中厂区的名称
            const selectedAreaName = options.areas.find(area => area.value === selectedArea.value)?.label;
            if (!selectedAreaName) return [];

            return options.equipmentList.filter(equipment => {
                let matches = equipment.area === selectedAreaName;
                if (selectedLocation.value) {
                    matches = matches && equipment.location === selectedLocation.value;
                }
                return matches;
            });
        });

        /**
         * 获取当前用户信息
         */
        const getCurrentUserInfo = () => {
            try {
                const userJson = sessionStorage.getItem('user');
                if (userJson) {
                    const user = JSON.parse(userJson);
                    currentUserName.value = user.name || user.username || '';
                    // 只在新建模式下设置操作人字段
                    if (!props.isEdit) {
                        formData.technician = currentUserName.value;
                    }
                }
            } catch (error) {
                console.error('获取当前用户信息失败:', error);
                currentUserName.value = '';
                formData.technician = '';
            }
        };

        /**
         * 加载选项数据
         */
        const loadOptions = async () => {
            try {
                loading.value = true;

                // 加载维修类型和状态选项
                const optionsResponse = await getMaintenanceOptions();
                if (optionsResponse.success) {
                    options.types = optionsResponse.data.types || [];
                    options.statuses = optionsResponse.data.statuses || [];
                    options.severityLevels = optionsResponse.data.severityLevels || [];
                    options.areas = optionsResponse.data.areas || [];
                    options.locations = optionsResponse.data.locations || [];
                }

                // 加载设备列表
                const equipmentResponse = await getEquipmentList();
                if (equipmentResponse.success) {
                    options.equipmentList = equipmentResponse.data.equipment || [];
                }
            } catch (error) {
                console.error('加载选项数据失败:', error);
            } finally {
                loading.value = false;
            }
        };

        /**
         * 初始化表单数据
         */
        const initFormData = () => {
            if (props.record && props.isEdit) {
                Object.assign(formData, {
                    id: props.record.id || '', // 设置记录编号
                    equipmentId: props.record.equipmentId || '',
                    type: props.record.type || 'maintenance',
                    severityLevel: props.record.severityLevel || '',
                    description: props.record.description || '',
                    maintenanceDate: formatDate(props.record.maintenanceDate),
                    startTime: props.record.startTime || '',
                    endTime: props.record.endTime || '',
                    cost: props.record.cost || 0,
                    technician: props.record.technician || currentUserName.value, // 编辑时保持原操作人
                    status: props.record.status || 'pending',
                    notes: props.record.notes || '',
                    result: props.record.result || '',
                    reviewer: props.record.reviewer || ''
                });

                // 设置厂区和位置
                if (props.record.equipment) {
                    // 根据设备的厂区名称找到对应的厂区ID
                    const factory = options.areas.find(area => area.label === props.record.equipment.area);
                    selectedArea.value = factory ? factory.value : '';

                    // 设置位置 - 可能是设备的location字段或者从设备名称中提取
                    selectedLocation.value = props.record.equipment.location || '';


                }
            } else {
                // 重置表单
                Object.assign(formData, {
                    equipmentId: '',
                    type: 'maintenance',
                    severityLevel: '',
                    description: '',
                    maintenanceDate: new Date().toISOString().split('T')[0],
                    startTime: '',
                    endTime: '',
                    cost: 0,
                    technician: currentUserName.value, // 使用当前用户
                    status: 'pending',
                    notes: '',
                    result: '',
                    reviewer: ''
                });

                // 重置选择状态
                selectedArea.value = '';
                selectedLocation.value = '';
            }
            errors.value = {};
        };

        /**
         * 验证表单
         */
        const validateForm = () => {
            const newErrors = {};

            if (!selectedArea.value) {
                newErrors.area = '请选择厂区';
            }

            if (!selectedLocation.value) {
                newErrors.location = '请选择位置';
            }

            if (!formData.equipmentId) {
                newErrors.equipmentId = '请选择设备';
            }

            if (!formData.type) {
                newErrors.type = '请选择类型';
            }

            if (formData.type === 'repair' && !formData.severityLevel) {
                newErrors.severityLevel = '请选择故障程度';
            }

            if (!formData.description) {
                newErrors.description = '请输入问题描述或保养内容';
            } else if (formData.description.length > 500) {
                newErrors.description = '描述长度不能超过500字符';
            }

            if (!formData.maintenanceDate) {
                newErrors.maintenanceDate = '请选择维修日期';
            }

            if (formData.startTime && formData.endTime && formData.startTime >= formData.endTime) {
                newErrors.endTime = '结束时间必须晚于开始时间';
            }

            if (!formData.technician) {
                newErrors.technician = '请输入操作人';
            } else if (formData.technician.length > 50) {
                newErrors.technician = '操作人名称长度不能超过50字符';
            }

            if (formData.cost < 0) {
                newErrors.cost = '费用不能为负数';
            }

            if (formData.notes && formData.notes.length > 1000) {
                newErrors.notes = '备注长度不能超过1000字符';
            }

            errors.value = newErrors;
            return Object.keys(newErrors).length === 0;
        };

        /**
         * 格式化日期为 YYYY-MM-DD 格式
         */
        const formatDate = (dateString) => {
            if (!dateString) return '';

            // 如果已经是 YYYY-MM-DD 格式，直接返回
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                return dateString;
            }

            // 如果包含时间部分，提取日期部分
            if (dateString.includes('T')) {
                return dateString.split('T')[0];
            }

            // 尝试解析日期并格式化
            try {
                const date = new Date(dateString);
                if (!isNaN(date.getTime())) {
                    return date.toISOString().split('T')[0];
                }
            } catch (error) {
                console.error('日期格式化失败:', error);
            }

            return dateString;
        };

        /**
         * 提交表单
         */
        const submitForm = async () => {
            if (!validateForm()) {
                return;
            }

            try {
                submitting.value = true;

                // 准备提交数据，处理空值和格式
                const submitData = {
                    ...formData,
                    // 确保日期格式正确 (YYYY-MM-DD)
                    maintenanceDate: formatDate(formData.maintenanceDate),
                    // 确保空的时间字段不发送
                    startTime: formData.startTime || null,
                    endTime: formData.endTime || null,
                    // 确保数值字段正确
                    cost: Number(formData.cost) || 0
                };

                // 移除空值字段
                Object.keys(submitData).forEach(key => {
                    if (submitData[key] === null || submitData[key] === '') {
                        delete submitData[key];
                    }
                });

                let response;
                if (props.isEdit && props.record) {
                    response = await updateMaintenanceRecord(props.record.id, submitData);
                } else {
                    response = await createMaintenanceRecord(submitData);
                }

                if (response.success) {
                    emit('success', response.data);
                    
                    if (window.showToast) {
                        window.showToast(
                            props.isEdit ? '维修记录更新成功' : '维修记录创建成功', 
                            'success'
                        );
                    }
                } else {
                    console.error('提交失败:', response.message);
                    
                    if (response.errors) {
                        errors.value = response.errors.reduce((acc, error) => {
                            const field = error.toLowerCase().includes('设备') ? 'equipmentId' :
                                         error.toLowerCase().includes('类型') ? 'type' :
                                         error.toLowerCase().includes('描述') ? 'description' :
                                         error.toLowerCase().includes('时间') ? 'startDate' :
                                         error.toLowerCase().includes('技术员') ? 'technician' : 'general';
                            acc[field] = error;
                            return acc;
                        }, {});
                    }
                    
                    if (window.showToast) {
                        window.showToast(response.message || '提交失败', 'error');
                    }
                }
            } catch (error) {
                console.error('提交维修记录失败:', error);

                // 显示详细的验证错误
                if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
                    const errorMessage = '数据验证失败：\n' + error.response.data.errors.join('\n');
                    if (window.showToast) {
                        window.showToast(errorMessage, 'error');
                    } else {
                        alert(errorMessage);
                    }
                } else if (window.showToast) {
                    window.showToast('提交失败: ' + (error.response?.data?.message || error.message), 'error');
                } else {
                    alert('提交失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                submitting.value = false;
            }
        };

        /**
         * 取消操作
         */
        const cancel = () => {
            emit('cancel');
        };

        /**
         * 厂区变化处理
         */
        const onAreaChange = () => {
            selectedLocation.value = '';
            formData.equipmentId = '';
        };

        /**
         * 记录类型变化处理
         */
        const onTypeChange = () => {
            // 如果不是维修类型，清空故障程度
            if (formData.type !== 'repair') {
                formData.severityLevel = '';
            }
        };

        /**
         * 位置变化处理
         */
        const onLocationChange = () => {
            formData.equipmentId = '';
        };

        /**
         * 获取设备名称
         */
        const getEquipmentName = (equipmentId) => {
            const equipment = options.equipmentList.find(eq => eq.id === equipmentId);
            return equipment ? `${equipment.code} - ${equipment.name}` : '';
        };

        // 监听记录变化
        watch(() => props.record, () => {
            initFormData();
        }, { immediate: true });

        // 组件挂载时加载数据
        onMounted(async () => {
            getCurrentUserInfo();
            await loadOptions(); // 等待选项数据加载完成
            initFormData(); // 然后初始化表单数据
        });

        return {
            formData,
            options,
            loading,
            submitting,
            errors,
            selectedArea,
            selectedLocation,
            currentUserName,
            isFormValid,
            formTitle,
            filteredLocations,
            filteredEquipment,
            isEdit: computed(() => props.isEdit),
            submitForm,
            cancel,
            onAreaChange,
            onLocationChange,
            onTypeChange,
            getEquipmentName
        };
    },
    template: `
        <div class="maintenance-form">
            <div class="bg-white rounded-lg shadow-sm">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">{{ formTitle }}</h3>
                    <button
                        type="button"
                        @click="cancel"
                        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150"
                    >
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitForm" class="p-6 space-y-4">
                    <div v-if="loading" class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-gray-600">加载中...</p>
                    </div>

                    <div v-else class="space-y-4">
                        <!-- 记录编号 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">记录编号</label>
                            <input
                                type="text"
                                :value="formData.id || '系统自动生成'"
                                disabled
                                class="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-500"
                                placeholder="系统自动生成"
                            />
                            <p class="mt-1 text-xs text-gray-500">记录编号将根据提交的厂区和日期自动生成</p>
                        </div>

                        <!-- 厂区/区域 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                厂区/区域 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select
                                    v-model="selectedArea"
                                    @change="onAreaChange"
                                    class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
                                    :class="{ 'border-red-500': errors.area }"
                                >
                                    <option value="">请选择厂区</option>
                                    <option
                                        v-for="area in options.areas"
                                        :key="area.value"
                                        :value="area.value"
                                    >
                                        {{ area.label }}
                                    </option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            <p v-if="errors.area" class="mt-1 text-sm text-red-600">{{ errors.area }}</p>
                        </div>

                        <!-- 位置 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                位置 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select
                                    v-model="selectedLocation"
                                    @change="onLocationChange"
                                    class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
                                    :class="{ 'border-red-500': errors.location }"
                                >
                                    <option value="">请选择位置</option>
                                    <option
                                        v-for="location in filteredLocations"
                                        :key="location.value"
                                        :value="location.value"
                                    >
                                        {{ location.label }}
                                    </option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            <p v-if="errors.location" class="mt-1 text-sm text-red-600">{{ errors.location }}</p>
                        </div>

                        <!-- 关联设备 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                关联设备 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select
                                    v-model="formData.equipmentId"
                                    class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
                                    :class="{ 'border-red-500': errors.equipmentId }"
                                >
                                    <option value="">请选择设备</option>
                                    <option
                                        v-for="equipment in filteredEquipment"
                                        :key="equipment.id"
                                        :value="equipment.id"
                                    >
                                        {{ equipment.code }} - {{ equipment.name }}
                                    </option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            <p v-if="errors.equipmentId" class="mt-1 text-sm text-red-600">{{ errors.equipmentId }}</p>
                        </div>

                        <!-- 记录类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                记录类型 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select
                                    v-model="formData.type"
                                    @change="onTypeChange"
                                    class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
                                    :class="{ 'border-red-500': errors.type }"
                                >
                                    <option
                                        v-for="type in options.types"
                                        :key="type.value"
                                        :value="type.value"
                                    >
                                        {{ type.label }}
                                    </option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            <p v-if="errors.type" class="mt-1 text-sm text-red-600">{{ errors.type }}</p>
                        </div>

                        <!-- 故障程度 (只在维修时显示) -->
                        <div v-if="formData.type === 'repair'">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                故障程度 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select
                                    v-model="formData.severityLevel"
                                    class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
                                    :class="{ 'border-red-500': errors.severityLevel }"
                                >
                                    <option value="">请选择故障程度</option>
                                    <option
                                        v-for="level in options.severityLevels"
                                        :key="level.value"
                                        :value="level.value"
                                    >
                                        {{ level.label }}
                                    </option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            <p v-if="errors.severityLevel" class="mt-1 text-sm text-red-600">{{ errors.severityLevel }}</p>
                        </div>

                        <!-- 日期 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                日期 <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="date"
                                v-model="formData.maintenanceDate"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.maintenanceDate }"
                            />
                            <p v-if="errors.maintenanceDate" class="mt-1 text-sm text-red-600">{{ errors.maintenanceDate }}</p>
                        </div>

                        <!-- 开始时间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                            <input
                                type="time"
                                v-model="formData.startTime"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.startTime }"
                            />
                            <p v-if="errors.startTime" class="mt-1 text-sm text-red-600">{{ errors.startTime }}</p>
                        </div>

                        <!-- 结束时间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                            <input
                                type="time"
                                v-model="formData.endTime"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.endTime }"
                            />
                            <p v-if="errors.endTime" class="mt-1 text-sm text-red-600">{{ errors.endTime }}</p>
                        </div>

                        <!-- 操作人 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                操作人 <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                :value="formData.technician"
                                readonly
                                class="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-700 cursor-not-allowed"
                                placeholder="自动获取当前登录用户"
                            />
                            <p class="mt-1 text-xs text-gray-500">{{ isEdit ? '显示原始操作人' : '操作人自动设置为当前登录用户' }}</p>
                        </div>

                        <!-- 问题描述/保养内容 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                问题描述/保养内容 <span class="text-red-500">*</span>
                            </label>
                            <textarea
                                v-model="formData.description"
                                rows="4"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.description }"
                                placeholder="请详细描述问题或保养内容"
                            ></textarea>
                            <p v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description }}</p>
                        </div>

                        <!-- 处理结果 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">处理结果</label>
                            <textarea
                                v-model="formData.result"
                                rows="4"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.result }"
                                placeholder="请描述处理结果和解决方案"
                            ></textarea>
                            <p v-if="errors.result" class="mt-1 text-sm text-red-600">{{ errors.result }}</p>
                        </div>

                        <!-- 审查人员（选填） -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">审查人员（选填）</label>
                            <input
                                type="text"
                                v-model="formData.reviewer"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :class="{ 'border-red-500': errors.reviewer }"
                                placeholder="请输入审查人员姓名"
                            />
                            <p v-if="errors.reviewer" class="mt-1 text-sm text-red-600">{{ errors.reviewer }}</p>
                        </div>
                    </div>

                    <!-- 表单按钮 -->
                    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                        <button
                            type="button"
                            @click="cancel"
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            取消
                        </button>
                        <button
                            type="submit"
                            :disabled="submitting"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span v-if="submitting">
                                <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ isEdit ? '更新中...' : '添加中...' }}
                            </span>
                            <span v-else>
                                {{ isEdit ? '更新记录' : '添加记录' }}
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `
};
