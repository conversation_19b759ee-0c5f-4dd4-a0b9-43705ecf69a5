/**
 * 客户表单模态框组件
 * 用于添加和编辑客户信息
 */

export default {
    name: 'CustomerFormModal',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        customer: {
            type: Object,
            default: null
        },
        isEditing: {
            type: Boolean,
            default: false
        }
    },
    emits: ['close', 'save', 'saved'],
    setup(props, { emit }) {
        const { ref, reactive, watch, computed } = Vue;
        
        // 表单数据
        const formData = reactive({
            customer_name: '',
            contact_person: '',
            contact_email: '',
            contact_phone: '',
            address: '',
            description: '',
            active: true
        });
        
        // 表单验证错误
        const formErrors = reactive({});
        
        // 提交状态
        const isSubmitting = ref(false);
        
        // 监听客户数据变化
        watch(() => props.customer, (newCustomer) => {
            if (newCustomer) {
                Object.assign(formData, {
                    customer_name: newCustomer.customer_name || '',
                    contact_person: newCustomer.contact_person || '',
                    contact_email: newCustomer.contact_email || '',
                    contact_phone: newCustomer.contact_phone || '',
                    address: newCustomer.address || '',
                    description: newCustomer.description || '',
                    active: newCustomer.active !== undefined ? newCustomer.active : true
                });
            } else {
                resetForm();
            }
        }, { immediate: true });
        
        // 计算属性
        const modalTitle = computed(() => {
            return props.isEditing ? '编辑客户' : '添加客户';
        });
        
        const isFormValid = computed(() => {
            return formData.customer_name.trim() !== '';
        });
        
        // 方法
        function resetForm() {
            Object.assign(formData, {
                customer_name: '',
                contact_person: '',
                contact_email: '',
                contact_phone: '',
                address: '',
                description: '',
                active: true
            });
            Object.keys(formErrors).forEach(key => {
                delete formErrors[key];
            });
        }
        
        function validateForm() {
            const errors = {};
            
            if (!formData.customer_name.trim()) {
                errors.customer_name = '客户名称不能为空';
            }
            
            if (formData.contact_email && !isValidEmail(formData.contact_email)) {
                errors.contact_email = '请输入有效的邮箱地址';
            }
            
            Object.assign(formErrors, errors);
            return Object.keys(errors).length === 0;
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        async function handleSubmit() {
            if (!validateForm()) {
                return;
            }

            isSubmitting.value = true;

            // 清理数据
            const submitData = {
                customer_name: formData.customer_name.trim(),
                contact_person: formData.contact_person.trim() || null,
                contact_email: formData.contact_email.trim() || null,
                contact_phone: formData.contact_phone.trim() || null,
                address: formData.address.trim() || null,
                description: formData.description.trim() || null,
                active: formData.active
            };

            emit('save', submitData);
        }

        // 监听show属性变化，重置提交状态
        watch(() => props.show, (newShow) => {
            if (!newShow) {
                isSubmitting.value = false;
            }
        });
        
        function handleClose() {
            if (!isSubmitting.value) {
                emit('close');
            }
        }
        
        function handleBackdropClick(event) {
            if (event.target === event.currentTarget) {
                handleClose();
            }
        }
        
        return {
            formData,
            formErrors,
            isSubmitting,
            modalTitle,
            isFormValid,
            handleSubmit,
            handleClose,
            handleBackdropClick
        };
    },
    template: `
        <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" @click="handleBackdropClick">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- 背景遮罩 -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
                
                <!-- 模态框内容 -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <!-- 头部 -->
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">{{ modalTitle }}</h3>
                            <button @click="handleClose" 
                                    class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- 表单 -->
                        <form @submit.prevent="handleSubmit" class="space-y-4">
                            <!-- 客户名称 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    客户名称 <span class="text-red-500">*</span>
                                </label>
                                <input v-model="formData.customer_name" 
                                       type="text" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       :class="{ 'border-red-500': formErrors.customer_name }"
                                       placeholder="请输入客户名称">
                                <p v-if="formErrors.customer_name" class="text-red-500 text-sm mt-1">{{ formErrors.customer_name }}</p>
                            </div>
                            
                            <!-- 联系人 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">联系人</label>
                                <input v-model="formData.contact_person" 
                                       type="text" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入联系人姓名">
                            </div>
                            
                            <!-- 联系邮箱 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">联系邮箱</label>
                                <input v-model="formData.contact_email" 
                                       type="email" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       :class="{ 'border-red-500': formErrors.contact_email }"
                                       placeholder="请输入联系邮箱">
                                <p v-if="formErrors.contact_email" class="text-red-500 text-sm mt-1">{{ formErrors.contact_email }}</p>
                            </div>
                            
                            <!-- 联系电话 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                                <input v-model="formData.contact_phone" 
                                       type="tel" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入联系电话">
                            </div>
                            
                            <!-- 地址 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">地址</label>
                                <input v-model="formData.address" 
                                       type="text" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入客户地址">
                            </div>
                            
                            <!-- 描述 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                <textarea v-model="formData.description" 
                                          rows="3" 
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="请输入客户描述信息"></textarea>
                            </div>
                            
                            <!-- 状态 -->
                            <div>
                                <label class="flex items-center">
                                    <input v-model="formData.active" 
                                           type="checkbox" 
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">启用客户</span>
                                </label>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 底部按钮 -->
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button @click="handleSubmit" 
                                :disabled="!isFormValid || isSubmitting"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ isSubmitting ? '保存中...' : '保存' }}
                        </button>
                        <button @click="handleClose" 
                                :disabled="isSubmitting"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `
};
