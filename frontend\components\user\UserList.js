/**
 * 用户列表组件
 * 显示用户列表、搜索、分页和操作功能
 */

import { getUsers, deleteUser, checkUserDeletionStatus, updateUser, exportUsersToCSV, importUsersFromCSV, uploadSignature, uploadUserSignature, deleteSignature, getSignature } from '../../scripts/api/user.js';
import { getDepartments } from '../../scripts/api/department.js';

export default {
    props: {
        user: Object
    },
    emits: ['edit-user', 'add-user'],
    setup(props, { emit }) {
        const { ref, computed, onMounted, reactive, watch } = Vue;

        // 状态变量
        const users = ref([]);
        const isLoading = ref(false);
        const searchTerm = ref('');
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const totalPages = ref(1);
        const totalUsers = ref(0);
        const csvFileInput = ref(null);
        const signatureFileInput = ref(null);
        const departments = ref([]);

        // 高级筛选状态
        const showAdvancedFilters = ref(false);
        const filters = reactive({
            role: '',
            department: '',
            status: ''
        });

        // 排序状态
        const sortField = ref('');
        const sortDirection = ref('asc'); // 'asc' 或 'desc'

        // 模态框状态
        const showConfirmModal = ref(false);
        const confirmModalTitle = ref('');
        const confirmModalMessage = ref('');
        const confirmModalAction = ref(null);
        const confirmModalUser = ref(null);
        const showResultModal = ref(false);
        const resultModalTitle = ref('');
        const resultModalMessage = ref('');
        const resultModalSuccess = ref(true);

        // 电子签名模态框状态
        const showSignatureModal = ref(false);
        const currentUserForSignature = ref(null);
        const signatureLoading = ref(false);
        const selectedSignatureFile = ref(null);
        const previewSignatureUrl = ref('');

        // 角色样式映射
        const roleClasses = {
            '管理员': 'bg-red-100 text-red-800',
            'CEO': 'bg-purple-100 text-purple-800',
            '经理': 'bg-blue-100 text-blue-800',
            '厂长': 'bg-yellow-100 text-yellow-800',
            '总监': 'bg-blue-100 text-blue-800',
            '普通用户': 'bg-gray-100 text-gray-800'
        };

        // 角色排序顺序映射（数字越小排序越靠前）
        const roleOrder = {
            '普通用户': 1,
            '厂长': 2,
            '总监': 3,
            '经理': 4,
            'CEO': 5,
            '管理员': 6
        };

        // 用户列表，应用排序逻辑
        const paginatedUsers = computed(() => {
            if (!sortField.value) {
                return users.value;
            }

            // 创建用户数组的副本进行排序
            return [...users.value].sort((a, b) => {
                let valueA, valueB;
                let comparison;

                // 根据排序字段获取对应的值
                switch (sortField.value) {
                    case 'code':
                        valueA = a.code || '';
                        valueB = b.code || '';
                        comparison = valueA.localeCompare(valueB, 'zh-CN');
                        break;
                    case 'name':
                        valueA = a.name || '';
                        valueB = b.name || '';
                        comparison = valueA.localeCompare(valueB, 'zh-CN');
                        break;
                    case 'role':
                        // 使用自定义角色排序顺序
                        valueA = roleOrder[a.role] || 0;
                        valueB = roleOrder[b.role] || 0;
                        // 数字比较
                        comparison = valueA - valueB;
                        break;
                    default:
                        return 0;
                }

                // 根据排序方向返回结果
                return sortDirection.value === 'asc' ? comparison : -comparison;
            });
        });

        // 加载部门列表
        async function loadDepartments() {
            try {
                console.log('开始加载部门列表...');
                const response = await getDepartments();
                console.log('部门API响应:', response);
                departments.value = response.departments || [];
                console.log('设置部门数据:', departments.value);
            } catch (error) {
                console.error('加载部门列表失败:', error);
                console.log('使用默认部门列表');
                // 如果加载失败，使用默认部门列表
                departments.value = [
                    { name: '管理部' },
                    { name: '生产部' },
                    { name: '工程部' },
                    { name: '品管部' },
                    { name: '机电部' },
                    { name: '业务部' },
                    { name: '财务部' },
                    { name: '人事部' },
                    { name: '研发部' },
                    { name: '采购部' },
                    { name: '仓储部' }
                ];
            }
        }

        // 初始化
        onMounted(() => {
            loadUsers();
            loadDepartments();
        });

        // 加载用户列表
        async function loadUsers(showLoading = true) {
            try {
                if (showLoading) {
                    isLoading.value = true;
                }

                // 构建查询参数
                const params = {
                    page: currentPage.value,
                    limit: itemsPerPage.value,
                    search: searchTerm.value,
                    role: filters.role,
                    department: filters.department,
                    status: filters.status
                };

                // 调用API获取用户列表
                const response = await getUsers(params);

                // 更新状态
                users.value = response.users;
                totalPages.value = response.pagination.totalPages;
                totalUsers.value = response.pagination.total;

            } catch (error) {
                console.error('加载用户列表失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    sessionStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('加载用户列表失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                if (showLoading) {
                    isLoading.value = false;
                }
            }
        }

        // 静默加载用户列表（不显示加载状态）
        function loadUsersQuietly() {
            loadUsers(false);
        }

        // 搜索用户
        function handleSearch() {
            currentPage.value = 1; // 重置到第一页
            loadUsers();
        }

        // 应用筛选器（保留用于兼容性，但现在筛选是实时的）
        function applyFilters() {
            showAdvancedFilters.value = false; // 关闭筛选面板
        }

        // 重置筛选器
        function resetFilters() {
            filters.role = '';
            filters.department = '';
            filters.status = '';
            currentPage.value = 1;
            loadUsers();
        }

        // 计算活跃筛选器数量
        const activeFiltersCount = computed(() => {
            let count = 0;
            if (filters.role) count++;
            if (filters.department) count++;
            if (filters.status) count++;
            return count;
        });

        // 监听筛选器变化，实时应用筛选
        watch(filters, () => {
            currentPage.value = 1; // 重置到第一页
            loadUsers();
        }, { deep: true });

        // 处理页面大小变化
        function handlePageSizeChange() {
            currentPage.value = 1; // 重置到第一页
            loadUsers();
        }

        // 编辑用户
        function handleEdit(user) {
            emit('edit-user', user);
        }

        // 删除用户
        async function handleDelete(id) {
            const user = users.value.find(u => u.id === id);
            if (!user) return;

            try {
                // 先检查用户删除状态
                const statusCheck = await checkUserDeletionStatus(id);

                if (!statusCheck.success) {
                    resultModalSuccess.value = false;
                    resultModalTitle.value = '检查失败';
                    resultModalMessage.value = '无法检查用户删除状态: ' + statusCheck.message;
                    showResultModal.value = true;
                    return;
                }

                // 如果不能删除，显示详细原因
                if (!statusCheck.canDelete) {
                    let message = '无法删除该用户：\n';

                    if (statusCheck.isLastAdmin) {
                        message += '• 不能删除最后一个管理员账户\n';
                    }

                    if (statusCheck.businessCheck.hasActiveBusiness) {
                        message += '• 该用户有未完成的重要业务：\n';
                        if (statusCheck.businessCheck.pendingApplications > 0) {
                            message += `  - ${statusCheck.businessCheck.pendingApplications} 个未完成的申请\n`;
                        }
                        if (statusCheck.businessCheck.pendingApprovals > 0) {
                            message += `  - ${statusCheck.businessCheck.pendingApprovals} 个待审批的申请\n`;
                        }
                        if (statusCheck.businessCheck.recentReports > 0) {
                            message += `  - ${statusCheck.businessCheck.recentReports} 个最近上传的质量报告\n`;
                        }
                    }

                    resultModalSuccess.value = false;
                    resultModalTitle.value = '无法删除用户';
                    resultModalMessage.value = message.trim();
                    showResultModal.value = true;
                    return;
                }

                // 如果可以删除，显示确认对话框
                confirmModalTitle.value = '删除用户';
                confirmModalMessage.value = `您确定要删除用户 "${user.name}" 吗？此操作不可撤销。`;
                confirmModalUser.value = user;
                confirmModalAction.value = async () => {
                    try {
                        isLoading.value = true;

                        const result = await deleteUser(id);
                        if (result.success) {
                            // 立即从本地列表中移除用户
                            const userIndex = users.value.findIndex(u => u.id === id);
                            if (userIndex > -1) {
                                users.value.splice(userIndex, 1);
                                totalUsers.value = Math.max(0, totalUsers.value - 1);
                            }

                            // 显示成功消息
                            resultModalSuccess.value = true;
                            resultModalTitle.value = '删除成功';
                            resultModalMessage.value = `用户 "${user.name}" 已被删除`;
                            showResultModal.value = true;

                            // 静默重新加载用户列表确保数据同步（不显示加载状态）
                            loadUsersQuietly();
                        } else {
                            // 显示错误消息
                            resultModalSuccess.value = false;
                            resultModalTitle.value = '删除失败';
                            resultModalMessage.value = '删除用户失败: ' + result.message;
                            showResultModal.value = true;
                        }
                    } catch (error) {
                        console.error('删除失败:', error);

                        if (error.response?.status === 401 || error.response?.status === 403) {
                            // 认证失败，跳转到登录页
                            sessionStorage.removeItem('authToken');
                            window.location.href = '/login';
                        } else {
                            // 显示错误消息
                            resultModalSuccess.value = false;
                            resultModalTitle.value = '删除失败';
                            resultModalMessage.value = '删除用户失败: ' + (error.response?.data?.message || error.message);
                            showResultModal.value = true;
                        }
                    } finally {
                        isLoading.value = false;
                    }
                };
                showConfirmModal.value = true;

            } catch (error) {
                console.error('检查用户删除状态失败:', error);
                resultModalSuccess.value = false;
                resultModalTitle.value = '检查失败';
                resultModalMessage.value = '检查用户删除状态失败: ' + (error.response?.data?.message || error.message);
                showResultModal.value = true;
            }
        }

        // 添加新用户
        function addUser() {
            emit('add-user');
        }

        // 导出CSV
        async function exportCSV() {
            try {
                await exportUsersToCSV();
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败: ' + (error.response?.data?.message || error.message));
            }
        }

        // 触发文件选择
        function triggerFileInput() {
            csvFileInput.value.click();
        }

        // 导入CSV
        async function importCSV(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const result = await importUsersFromCSV(file);
                if (result.success) {
                    alert(`导入成功！共导入 ${result.importedCount} 条用户数据`);
                    await loadUsers();
                } else {
                    alert('导入失败: ' + result.message);
                }
            } catch (error) {
                console.error('导入失败:', error);
                alert('导入失败: ' + (error.response?.data?.message || error.message));
            } finally {
                // 清空文件输入，允许再次选择同一文件
                event.target.value = '';
            }
        }

        // 切换页面
        function goToPage(page) {
            if (page >= 1 && page <= totalPages.value) {
                currentPage.value = page;
                loadUsers(); // 加载新页面数据
            }
        }

        // 获取角色样式
        function getRoleClass(role) {
            return roleClasses[role] || 'bg-gray-100 text-gray-800';
        }

        // 处理排序
        function handleSort(field) {
            if (sortField.value === field) {
                // 如果已经按此字段排序，则切换排序方向
                sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
            } else {
                // 否则，设置新的排序字段，默认升序
                sortField.value = field;
                sortDirection.value = 'asc';
            }
        }

        // 获取排序图标类
        function getSortIconClass(field) {
            if (sortField.value !== field) {
                return 'text-gray-400 opacity-50';
            }
            return sortDirection.value === 'asc' ? 'text-blue-500' : 'text-blue-500 transform rotate-180';
        }

        // 切换用户状态
        function toggleUserStatus(user) {
            // 反转当前状态
            const newStatus = !user.active;

            // 先恢复复选框状态，等待用户确认
            setTimeout(() => {
                const checkbox = document.getElementById(`toggle-${user.id}`);
                if (checkbox) checkbox.checked = user.active;
            }, 0);

            // 显示确认模态框
            confirmModalTitle.value = newStatus ? '启用用户' : '禁用用户';
            confirmModalMessage.value = `您确定要${newStatus ? '启用' : '禁用'}用户 "${user.name}" 吗？`;
            confirmModalUser.value = user;
            confirmModalAction.value = async () => {
                try {
                    isLoading.value = true;

                    // 调用API更新用户状态
                    const result = await updateUser(user.id, {
                        ...user,
                        active: newStatus
                    });

                    if (result.success) {
                        // 更新本地用户数据
                        user.active = newStatus;

                        // 显示成功消息
                        resultModalSuccess.value = true;
                        resultModalTitle.value = '操作成功';
                        resultModalMessage.value = `用户 "${user.name}" 已${newStatus ? '启用' : '禁用'}`;
                        showResultModal.value = true;

                        // 更新复选框状态
                        setTimeout(() => {
                            const checkbox = document.getElementById(`toggle-${user.id}`);
                            if (checkbox) checkbox.checked = newStatus;
                        }, 0);
                    } else {
                        // 显示错误消息
                        resultModalSuccess.value = false;
                        resultModalTitle.value = '操作失败';
                        resultModalMessage.value = '更新用户状态失败: ' + result.message;
                        showResultModal.value = true;
                    }
                } catch (error) {
                    console.error('更新用户状态失败:', error);

                    // 显示错误消息
                    resultModalSuccess.value = false;
                    resultModalTitle.value = '操作失败';
                    resultModalMessage.value = '更新用户状态失败: ' + (error.response?.data?.message || error.message);
                    showResultModal.value = true;
                } finally {
                    isLoading.value = false;
                }
            };

            showConfirmModal.value = true;
        }

        // 确认模态框操作
        function confirmAction() {
            if (confirmModalAction.value) {
                confirmModalAction.value();
            }
            showConfirmModal.value = false;
        }

        // 取消模态框操作
        function cancelAction() {
            showConfirmModal.value = false;
        }

        // 关闭结果模态框
        function closeResultModal() {
            showResultModal.value = false;
        }

        // 打开电子签名模态框
        function openSignatureModal(user) {
            currentUserForSignature.value = user;
            signatureLoading.value = true;
            previewSignatureUrl.value = '';
            selectedSignatureFile.value = null;
            showSignatureModal.value = true;

            // 获取用户当前签名
            getSignature(user.id)
                .then(result => {
                    if (result.success && result.hasSignature && result.signaturePath) {
                        // 添加时间戳防止缓存，确保路径格式正确
                        previewSignatureUrl.value = `/${result.signaturePath}?t=${new Date().getTime()}`;
                    } else {
                        // 用户没有签名，清空预览URL
                        previewSignatureUrl.value = '';
                    }
                })
                .catch(error => {
                    console.error('获取签名失败:', error);
                    // 发生错误时，清空预览URL
                    previewSignatureUrl.value = '';
                })
                .finally(() => {
                    signatureLoading.value = false;
                });
        }

        // 关闭电子签名模态框
        function closeSignatureModal() {
            showSignatureModal.value = false;
            currentUserForSignature.value = null;
            previewSignatureUrl.value = '';
            selectedSignatureFile.value = null;
        }

        // 触发签名文件选择
        function triggerSignatureFileInput() {
            signatureFileInput.value.click();
        }

        // 处理签名文件选择
        function handleSignatureFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            selectedSignatureFile.value = file;

            // 创建预览
            const reader = new FileReader();
            reader.onload = e => {
                previewSignatureUrl.value = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // 上传签名
        async function handleSignatureUpload() {
            if (!selectedSignatureFile.value || !currentUserForSignature.value) return;

            try {
                signatureLoading.value = true;
                const result = await uploadUserSignature(currentUserForSignature.value.id, selectedSignatureFile.value);

                if (result.success) {
                    // 更新本地用户数据
                    const userIndex = users.value.findIndex(u => u.id === currentUserForSignature.value.id);
                    if (userIndex !== -1) {
                        users.value[userIndex].hasSignature = true;
                        users.value[userIndex].signaturePath = result.signaturePath;
                    }

                    // 重新加载用户列表确保数据同步
                    await loadUsersQuietly();

                    // 显示成功消息
                    resultModalSuccess.value = true;
                    resultModalTitle.value = '上传成功';
                    resultModalMessage.value = '电子签名上传成功';
                    closeSignatureModal();
                    showResultModal.value = true;
                } else {
                    // 显示错误消息
                    resultModalSuccess.value = false;
                    resultModalTitle.value = '上传失败';
                    resultModalMessage.value = '电子签名上传失败: ' + result.message;
                    showResultModal.value = true;
                }
            } catch (error) {
                console.error('上传签名失败:', error);
                resultModalSuccess.value = false;
                resultModalTitle.value = '上传失败';
                resultModalMessage.value = '电子签名上传失败: ' + (error.response?.data?.message || error.message);
                showResultModal.value = true;
            } finally {
                signatureLoading.value = false;
            }
        }

        // 删除签名
        async function removeUserSignature() {
            if (!currentUserForSignature.value) return;

            try {
                signatureLoading.value = true;
                const result = await deleteSignature(currentUserForSignature.value.id);

                if (result.success) {
                    // 更新本地用户数据
                    const userIndex = users.value.findIndex(u => u.id === currentUserForSignature.value.id);
                    if (userIndex !== -1) {
                        users.value[userIndex].hasSignature = false;
                        users.value[userIndex].signaturePath = null;
                    }

                    // 清除预览
                    previewSignatureUrl.value = '';
                    selectedSignatureFile.value = null;

                    // 重新加载用户列表确保数据同步
                    await loadUsersQuietly();

                    // 显示成功消息
                    resultModalSuccess.value = true;
                    resultModalTitle.value = '删除成功';
                    resultModalMessage.value = '电子签名删除成功';
                    closeSignatureModal();
                    showResultModal.value = true;
                } else {
                    // 显示错误消息
                    resultModalSuccess.value = false;
                    resultModalTitle.value = '删除失败';
                    resultModalMessage.value = '电子签名删除失败: ' + result.message;
                    showResultModal.value = true;
                }
            } catch (error) {
                console.error('删除签名失败:', error);
                resultModalSuccess.value = false;
                resultModalTitle.value = '删除失败';
                resultModalMessage.value = '电子签名删除失败: ' + (error.response?.data?.message || error.message);
                showResultModal.value = true;
            } finally {
                signatureLoading.value = false;
            }
        }

        return {
            users,
            paginatedUsers,
            isLoading,
            searchTerm,
            currentPage,
            totalPages,
            totalUsers,
            itemsPerPage,
            csvFileInput,
            signatureFileInput,
            departments,
            // 筛选相关
            showAdvancedFilters,
            filters,
            activeFiltersCount,
            handleSearch,
            applyFilters,
            resetFilters,
            // 用户操作
            handleEdit,
            handleDelete,
            addUser,
            exportCSV,
            triggerFileInput,
            importCSV,
            // 分页相关
            goToPage,
            handlePageSizeChange,
            // 样式和状态
            getRoleClass,
            toggleUserStatus,
            // 排序相关
            sortField,
            sortDirection,
            handleSort,
            getSortIconClass,
            // 模态框相关
            showConfirmModal,
            confirmModalTitle,
            confirmModalMessage,
            confirmAction,
            cancelAction,
            showResultModal,
            resultModalTitle,
            resultModalMessage,
            resultModalSuccess,
            closeResultModal,
            // 电子签名相关
            showSignatureModal,
            currentUserForSignature,
            signatureLoading,
            selectedSignatureFile,
            previewSignatureUrl,
            openSignatureModal,
            closeSignatureModal,
            triggerSignatureFileInput,
            handleSignatureFileSelect,
            handleSignatureUpload,
            removeUserSignature
        };
    },
    template: `
        <div>
            <!-- 搜索和操作栏 -->
            <div class="flex flex-col space-y-4 mb-4">
                <!-- 移动端和桌面端不同的布局 -->
                <div class="md:flex md:justify-between md:items-center space-y-4 md:space-y-0">
                    <!-- 搜索区域 -->
                    <div class="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2">
                        <div class="relative w-full md:w-64">
                            <input type="text" v-model="searchTerm" @keyup.enter="handleSearch" placeholder="请输入用户名或代码"
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <div class="absolute left-3 top-2.5 text-gray-400">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="handleSearch" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none flex-1 md:flex-none">
                                搜索
                            </button>
                            <button @click="showAdvancedFilters = !showAdvancedFilters"
                                    :class="[
                                        'px-4 py-2 border rounded-md focus:outline-none flex items-center flex-1 md:flex-none justify-center md:justify-start relative',
                                        activeFiltersCount > 0 ? 'border-blue-500 bg-blue-50 text-blue-700 hover:bg-blue-100' : 'border-gray-300 hover:bg-gray-50'
                                    ]">
                                <span class="hidden md:inline">高级筛选</span>
                                <span class="md:hidden">筛选</span>
                                <span v-if="activeFiltersCount > 0" class="ml-1 px-1.5 py-0.5 text-xs bg-blue-500 text-white rounded-full">
                                    {{ activeFiltersCount }}
                                </span>
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          :d="showAdvancedFilters ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="flex space-x-2">
                        <div class="hidden md:flex md:space-x-2">
                            <button @click="exportCSV" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none">
                                导出 CSV
                            </button>
                            <input type="file" ref="csvFileInput" @change="importCSV" accept=".csv" class="hidden">
                            <button @click="triggerFileInput" class="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 focus:outline-none">
                                导入 CSV
                            </button>
                        </div>
                        <button @click="addUser" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none flex-1 md:flex-none">
                            添加用户
                        </button>
                    </div>

                    <!-- 移动端专用的导入导出菜单 -->
                    <div class="flex md:hidden space-x-2 mt-2">
                        <button @click="exportCSV" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none flex-1">
                            导出 CSV
                        </button>
                        <button @click="triggerFileInput" class="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 focus:outline-none flex-1">
                            导入 CSV
                        </button>
                    </div>
                </div>

                <!-- 高级筛选面板 -->
                <div v-if="showAdvancedFilters" class="bg-gray-50 p-4 rounded-md border border-gray-200 transition-all duration-300">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                            <select v-model="filters.role" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="">全部角色</option>
                                <option value="管理员">管理员</option>
                                <option value="CEO">CEO</option>
                                <option value="总监">总监</option>
                                <option value="经理">经理</option>
                                <option value="厂长">厂长</option>
                                <option value="普通用户">普通用户</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                            <select v-model="filters.department" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="">全部部门</option>
                                <option v-for="dept in departments" :key="dept.id" :value="dept.name">{{ dept.name }}</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                            <select v-model="filters.status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="">全部状态</option>
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-between mt-4">
                        <div class="text-sm text-gray-500 flex items-center">
                            <span v-if="activeFiltersCount > 0">已应用 {{ activeFiltersCount }} 个筛选条件（实时筛选）</span>
                            <span v-else>选择筛选条件后将自动应用</span>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="resetFilters"
                                    :disabled="activeFiltersCount === 0"
                                    :class="[
                                        'px-4 py-2 rounded-md focus:outline-none transition-colors',
                                        activeFiltersCount > 0
                                            ? 'bg-red-500 text-white hover:bg-red-600'
                                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                    ]">
                                清除筛选
                            </button>
                            <button @click="showAdvancedFilters = false" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none">
                                收起
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前筛选状态 -->
            <div v-if="activeFiltersCount > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                        </svg>
                        <span class="text-sm font-medium text-blue-700">当前筛选条件：</span>
                        <div class="flex flex-wrap gap-1">
                            <span v-if="filters.role" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                角色: {{ filters.role }}
                            </span>
                            <span v-if="filters.department" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                部门: {{ filters.department }}
                            </span>
                            <span v-if="filters.status" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                状态: {{ filters.status === 'active' ? '启用' : '禁用' }}
                            </span>
                        </div>
                    </div>
                    <button @click="resetFilters" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        清除筛选
                    </button>
                </div>
            </div>

            <!-- 桌面端用户表格 -->
            <div class="hidden md:block overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-50 text-gray-600 text-left">
                            <th class="py-3 px-4 font-medium">
                                <div class="flex items-center cursor-pointer" @click="handleSort('code')">
                                    用户代码
                                    <svg :class="['w-4 h-4 ml-1', getSortIconClass('code')]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                    </svg>
                                </div>
                            </th>
                            <th class="py-3 px-4 font-medium">
                                <div class="flex items-center cursor-pointer" @click="handleSort('name')">
                                    姓名
                                    <svg :class="['w-4 h-4 ml-1', getSortIconClass('name')]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                    </svg>
                                </div>
                            </th>
                            <th class="py-3 px-4 font-medium">
                                <div class="flex items-center cursor-pointer" @click="handleSort('role')">
                                    角色
                                    <svg :class="['w-4 h-4 ml-1', getSortIconClass('role')]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                    </svg>
                                </div>
                            </th>
                            <th class="py-3 px-4 font-medium">部门</th>
                            <th class="py-3 px-4 font-medium">状态</th>
                            <th class="py-3 px-4 font-medium">电子签名</th>
                            <th class="py-3 px-4 font-medium">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-if="isLoading">
                            <td colspan="7" class="py-4 px-4 text-center text-gray-500">加载中...</td>
                        </tr>
                        <tr v-else-if="paginatedUsers.length === 0">
                            <td colspan="7" class="py-4 px-4 text-center text-gray-500">暂无用户数据</td>
                        </tr>
                        <tr v-for="user in paginatedUsers" :key="user.id" class="border-b border-gray-100">
                            <td class="py-3 px-4 text-gray-800">{{ user.code }}</td>
                            <td class="py-3 px-4">
                                <div>{{ user.name }}</div>
                                <div class="text-xs text-gray-500">{{ user.email || '-' }}</div>
                            </td>
                            <td class="py-3 px-4">
                                <span :class="['px-2 py-1 rounded-full text-xs font-medium', getRoleClass(user.role)]">
                                    {{ user.role }}
                                </span>
                            </td>
                            <td class="py-3 px-4 text-gray-800">{{ user.department || '-' }}</td>
                            <td class="py-3 px-4">
                                <div class="relative inline-block w-12 h-6 align-middle select-none">
                                    <input type="checkbox" :id="'toggle-'+user.id" :checked="user.active" @change="toggleUserStatus(user)"
                                           class="toggle-checkbox"/>
                                    <label :for="'toggle-'+user.id" class="toggle-label"></label>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center">
                                    <button @click="openSignatureModal(user)" class="text-blue-500 hover:text-blue-700 mr-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                        </svg>
                                    </button>
                                    <div v-if="user.hasSignature && user.signaturePath" class="relative w-10 h-10 border border-gray-300 rounded-md overflow-hidden bg-gray-50">
                                        <img :src="'/' + user.signaturePath + '?t=' + new Date().getTime()" class="w-full h-full object-contain" :alt="user.name + '的签名'" />
                                    </div>
                                    <div v-else class="ml-2 text-xs text-gray-500">无签名</div>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-2">
                                    <button @click="handleEdit(user)" class="text-blue-500 hover:text-blue-700">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button @click="handleDelete(user.id)" class="text-red-500 hover:text-red-700">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">创建于: {{ user.createdAt }}</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 移动端用户卡片列表 -->
            <div class="md:hidden">
                <div v-if="isLoading" class="py-4 text-center text-gray-500">
                    加载中...
                </div>
                <div v-else-if="paginatedUsers.length === 0" class="py-4 text-center text-gray-500">
                    暂无用户数据
                </div>
                <div v-else class="space-y-4">
                    <div v-for="user in paginatedUsers" :key="user.id" class="bg-white rounded-lg shadow p-4 border-l-4"
                         :class="{'border-green-500': user.active, 'border-gray-300': !user.active}">
                        <div class="flex justify-between items-start">
                            <div>
                                <div class="font-medium text-gray-900">{{ user.name }}</div>
                                <div class="text-sm text-gray-500">{{ user.code }}</div>
                            </div>
                            <span :class="['px-2 py-1 rounded-full text-xs font-medium', getRoleClass(user.role)]">
                                {{ user.role }}
                            </span>
                        </div>

                        <div class="mt-2 grid grid-cols-2 gap-2 text-sm">
                            <div>
                                <span class="text-gray-500">部门:</span>
                                <span class="text-gray-900">{{ user.department || '-' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">状态:</span>
                                <span class="text-gray-900">{{ user.active ? '启用' : '禁用' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">邮箱:</span>
                                <span class="text-gray-900">{{ user.email || '-' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">签名:</span>
                                <span class="text-gray-900">{{ user.hasSignature ? '已设置' : '未设置' }}</span>
                            </div>
                        </div>

                        <div class="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center">
                            <div class="text-xs text-gray-500">创建于: {{ user.createdAt }}</div>
                            <div class="flex space-x-3">
                                <button @click="openSignatureModal(user)" class="p-1 text-blue-500 hover:text-blue-700">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                    </svg>
                                </button>
                                <button @click="handleEdit(user)" class="p-1 text-blue-500 hover:text-blue-700">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button @click="handleDelete(user.id)" class="p-1 text-red-500 hover:text-red-700">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                                <button @click="toggleUserStatus(user)" class="p-1" :class="user.active ? 'text-green-500 hover:text-green-700' : 'text-gray-500 hover:text-gray-700'">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="flex flex-col md:flex-row md:justify-between md:items-center mt-4 space-y-3 md:space-y-0">
                <!-- 总记录数信息 -->
                <div class="text-sm text-gray-600 text-center md:text-left">
                    共 <span class="font-medium">{{ totalUsers }}</span> 条记录，当前显示 <span class="font-medium">{{ paginatedUsers.length }}</span> 条
                </div>

                <!-- 分页按钮 - 移动端简化版 -->
                <div class="md:hidden flex justify-center items-center space-x-2">
                    <button @click="goToPage(1)" :disabled="currentPage === 1"
                            class="p-2 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button @click="goToPage(currentPage - 1)" :disabled="currentPage === 1"
                            class="p-2 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <span class="px-3 py-1 text-sm">{{ currentPage }}/{{ totalPages }}</span>

                    <button @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages"
                            class="p-2 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                    <button @click="goToPage(totalPages)" :disabled="currentPage === totalPages"
                            class="p-2 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>

                <!-- 分页按钮 - 桌面端完整版 -->
                <div class="hidden md:flex md:items-center md:space-x-2">
                    <button @click="goToPage(1)" :disabled="currentPage === 1"
                            class="p-1 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button @click="goToPage(currentPage - 1)" :disabled="currentPage === 1"
                            class="p-1 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <!-- 动态分页按钮 -->
                    <template v-if="totalPages <= 7">
                        <button v-for="page in totalPages" :key="page" @click="goToPage(page)"
                                :class="['px-3 py-1 rounded-md', currentPage === page ? 'bg-blue-500 text-white' : 'border border-gray-300']">
                            {{ page }}
                        </button>
                    </template>
                    <template v-else>
                        <!-- 前部分页码 -->
                        <button v-if="currentPage > 3" @click="goToPage(1)"
                                class="px-3 py-1 rounded-md border border-gray-300">
                            1
                        </button>
                        <span v-if="currentPage > 4" class="px-1">...</span>

                        <!-- 中间页码 -->
                        <button v-for="page in 3" :key="page"
                                v-if="currentPage - 2 + page > 0 && currentPage - 2 + page <= totalPages"
                                @click="goToPage(currentPage - 2 + page)"
                                :class="['px-3 py-1 rounded-md', currentPage === (currentPage - 2 + page) ? 'bg-blue-500 text-white' : 'border border-gray-300']">
                            {{ currentPage - 2 + page }}
                        </button>

                        <!-- 后部分页码 -->
                        <span v-if="currentPage < totalPages - 3" class="px-1">...</span>
                        <button v-if="currentPage < totalPages - 2" @click="goToPage(totalPages)"
                                class="px-3 py-1 rounded-md border border-gray-300">
                            {{ totalPages }}
                        </button>
                    </template>

                    <button @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages"
                            class="p-1 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                    <button @click="goToPage(totalPages)" :disabled="currentPage === totalPages"
                            class="p-1 rounded-md border border-gray-300 disabled:opacity-50">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    <select v-model="itemsPerPage" @change="handlePageSizeChange"
                            class="ml-4 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option :value="10">10条/页</option>
                        <option :value="20">20条/页</option>
                        <option :value="50">50条/页</option>
                        <option :value="100">100条/页</option>
                    </select>
                </div>

                <!-- 移动端每页显示数量选择器 -->
                <div class="md:hidden flex justify-center">
                    <select v-model="itemsPerPage" @change="handlePageSizeChange"
                            class="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option :value="10">10条/页</option>
                        <option :value="20">20条/页</option>
                        <option :value="50">50条/页</option>
                    </select>
                </div>
            </div>

            <!-- 确认模态框 -->
            <div v-if="showConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 w-full max-w-md shadow-xl transform transition-all">
                    <div class="flex items-start mb-4">
                        <div class="flex-shrink-0 bg-blue-100 rounded-full p-2 mr-3">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">{{ confirmModalTitle }}</h3>
                            <p class="mt-2 text-sm text-gray-500">{{ confirmModalMessage }}</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button @click="cancelAction" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none">
                            取消
                        </button>
                        <button @click="confirmAction" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none">
                            确定
                        </button>
                    </div>
                </div>
            </div>

            <!-- 结果模态框 -->
            <div v-if="showResultModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 w-full max-w-md shadow-xl transform transition-all">
                    <div class="flex items-start mb-4">
                        <div v-if="resultModalSuccess" class="flex-shrink-0 bg-green-100 rounded-full p-2 mr-3">
                            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div v-else class="flex-shrink-0 bg-red-100 rounded-full p-2 mr-3">
                            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">{{ resultModalTitle }}</h3>
                            <p class="mt-2 text-sm text-gray-500">{{ resultModalMessage }}</p>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button @click="closeResultModal" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none">
                            确定
                        </button>
                    </div>
                </div>
            </div>

            <!-- 电子签名模态框 -->
            <input type="file" ref="signatureFileInput" @change="handleSignatureFileSelect" accept=".jpg,.jpeg,.png" class="hidden">
            <div v-if="showSignatureModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 w-full max-w-md shadow-xl">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ currentUserForSignature ? currentUserForSignature.name : '' }} 的电子签名</h3>
                        <button @click="closeSignatureModal" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <!-- 签名预览区域 -->
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center h-48 bg-gray-50">
                            <div v-if="signatureLoading" class="flex flex-col items-center">
                                <svg class="animate-spin h-10 w-10 text-blue-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span class="text-gray-500">加载中...</span>
                            </div>
                            <div v-else-if="previewSignatureUrl" class="flex items-center justify-center h-full w-full">
                                <img :src="previewSignatureUrl" alt="电子签名" class="max-h-full max-w-full object-contain">
                            </div>
                            <div v-else class="flex flex-col items-center justify-center h-full w-full">
                                <svg class="w-12 h-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                <span class="text-gray-500">暂无电子签名</span>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex justify-center space-x-3 mt-4">
                            <button v-if="previewSignatureUrl && !selectedSignatureFile"
                                    @click="removeUserSignature"
                                    :disabled="signatureLoading"
                                    class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none disabled:opacity-50">
                                删除签名
                            </button>
                            <button @click="triggerSignatureFileInput"
                                    :disabled="signatureLoading"
                                    class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none disabled:opacity-50">
                                {{ previewSignatureUrl && !selectedSignatureFile ? '更换签名' : '选择签名' }}
                            </button>
                            <button v-if="selectedSignatureFile"
                                    @click="handleSignatureUpload"
                                    :disabled="signatureLoading"
                                    class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none disabled:opacity-50">
                                上传签名
                            </button>
                        </div>

                        <div class="text-xs text-gray-500 mt-2 text-center">
                            支持JPG和PNG格式的图片文件
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};
