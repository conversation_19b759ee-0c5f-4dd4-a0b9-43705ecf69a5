/**
 * 编辑排程页面逻辑
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ScheduleAPI from '../../api/schedule.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const loading = ref(true);
        const submitting = ref(false);
        const scheduleId = ref(null);
        const formData = ref({
            id: '',
            title: '',
            productId: '',
            productName: '',
            quantity: 1,
            priority: 'medium',
            status: 'planned',
            progress: 0,
            startTime: '',
            endTime: '',
            notes: ''
        });

        // 初始化
        onMounted(async () => {
            try {
                await checkAuth();
            } finally {
                // 确保加载指示器被隐藏
                hideLoading();
            }
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
                extractScheduleId();
                if (scheduleId.value) {
                    await loadScheduleData();
                } else {
                    loading.value = false;
                }
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
                throw error; // 重新抛出错误以便onMounted能够捕获
            }
        }

        // 从URL中提取排程ID
        function extractScheduleId() {
            const pathParts = window.location.pathname.split('/');
            scheduleId.value = pathParts[pathParts.length - 1];
        }

        // 加载排程数据
        async function loadScheduleData() {
            try {
                const response = await ScheduleAPI.getScheduleById(scheduleId.value);

                if (response.success) {
                    const schedule = response.data;
                    formData.value = {
                        id: schedule.id,
                        title: schedule.title,
                        productId: schedule.productId,
                        productName: schedule.productName,
                        quantity: schedule.quantity,
                        priority: schedule.priority,
                        status: schedule.status,
                        progress: schedule.progress || 0,
                        startTime: schedule.startTime ? new Date(schedule.startTime).toISOString().slice(0, 16) : '',
                        endTime: schedule.endTime ? new Date(schedule.endTime).toISOString().slice(0, 16) : '',
                        notes: schedule.notes || ''
                    };
                } else {
                    window.showNotification('加载排程数据失败: ' + response.message, 'error');
                    setTimeout(() => {
                        window.location.href = '/schedule/list';
                    }, 2000);
                }
            } catch (error) {
                console.error('加载排程数据失败:', error);
                window.showNotification('加载排程数据失败', 'error');
                setTimeout(() => {
                    window.location.href = '/schedule/list';
                }, 2000);
            } finally {
                loading.value = false;
            }
        }

        // 提交表单
        async function submitForm() {
            if (submitting.value) return;

            // 基本验证
            if (!formData.value.title.trim()) {
                window.showNotification('请输入排程标题', 'error');
                return;
            }

            if (!formData.value.productId.trim()) {
                window.showNotification('请输入产品ID', 'error');
                return;
            }

            if (!formData.value.productName.trim()) {
                window.showNotification('请输入产品名称', 'error');
                return;
            }

            if (formData.value.quantity <= 0) {
                window.showNotification('生产数量必须大于0', 'error');
                return;
            }

            if (!formData.value.startTime) {
                window.showNotification('请选择开始时间', 'error');
                return;
            }

            if (!formData.value.endTime) {
                window.showNotification('请选择结束时间', 'error');
                return;
            }

            if (new Date(formData.value.startTime) >= new Date(formData.value.endTime)) {
                window.showNotification('结束时间必须晚于开始时间', 'error');
                return;
            }

            submitting.value = true;

            try {
                const updateData = {
                    title: formData.value.title,
                    productId: formData.value.productId,
                    productName: formData.value.productName,
                    quantity: parseInt(formData.value.quantity),
                    priority: formData.value.priority,
                    status: formData.value.status,
                    progress: parseInt(formData.value.progress),
                    startTime: new Date(formData.value.startTime).toISOString(),
                    endTime: new Date(formData.value.endTime).toISOString(),
                    notes: formData.value.notes
                };

                const response = await ScheduleAPI.updateSchedule(scheduleId.value, updateData);

                if (response.success) {
                    window.showNotification('排程更新成功', 'success');
                    // 跳转到排程列表页面
                    setTimeout(() => {
                        window.location.href = '/schedule/list';
                    }, 1000);
                } else {
                    window.showNotification('更新失败: ' + response.message, 'error');
                }
            } catch (error) {
                console.error('更新排程失败:', error);
                window.showNotification('更新排程失败', 'error');
            } finally {
                submitting.value = false;
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                planned: '计划中',
                in_progress: '执行中',
                paused: '已暂停',
                completed: '已完成',
                cancelled: '已取消'
            };
            return statusMap[status] || status;
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityMap = {
                low: '低',
                medium: '中',
                high: '高',
                urgent: '紧急'
            };
            return priorityMap[priority] || priority;
        }

        return {
            currentUser,
            isAuthenticated,
            loading,
            submitting,
            scheduleId,
            formData,
            submitForm,
            getStatusText,
            getPriorityText
        };
    }
}).mount('#app');
