/**
 * 交期预测图表组件
 * 用于显示交期预测的可视化图表
 */

export default {
    name: 'DeliveryPredictionChart',
    props: {
        predictionData: {
            type: Object,
            required: true
        },
        height: {
            type: Number,
            default: 300
        }
    },
    template: `
        <div class="delivery-prediction-chart">
            <div class="chart-container" :style="{ height: height + 'px' }">
                <canvas ref="chartCanvas"></canvas>
            </div>
        </div>
    `,
    data() {
        return {
            chartInstance: null
        };
    },
    mounted() {
        this.createChart();
    },
    watch: {
        predictionData: {
            handler() {
                this.updateChart();
            },
            deep: true
        }
    },
    beforeUnmount() {
        if (this.chartInstance) {
            this.chartInstance.destroy();
        }
    },
    methods: {
        createChart() {
            if (!this.$refs.chartCanvas || !this.predictionData) return;

            const ctx = this.$refs.chartCanvas.getContext('2d');
            
            // 销毁现有图表
            if (this.chartInstance) {
                this.chartInstance.destroy();
            }

            // 生成预测数据点
            const baseDate = new Date();
            const labels = [];
            const optimisticData = [];
            const realisticData = [];
            const pessimisticData = [];

            for (let i = 0; i <= 30; i++) {
                const date = this.addDays(baseDate, i);
                labels.push(this.formatDateShort(date));
                
                // 模拟不同情况下的完成概率
                const progress = Math.min(100, (i / 22) * 100); // 22天完成
                optimisticData.push(Math.min(100, progress * 1.2));
                realisticData.push(progress);
                pessimisticData.push(Math.max(0, progress * 0.8));
            }

            this.chartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: '乐观预测',
                            data: optimisticData,
                            borderColor: 'rgba(16, 185, 129, 1)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '现实预测',
                            data: realisticData,
                            borderColor: 'rgba(59, 130, 246, 1)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: false,
                            tension: 0.4,
                            borderWidth: 3
                        },
                        {
                            label: '悲观预测',
                            data: pessimisticData,
                            borderColor: 'rgba(239, 68, 68, 1)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            fill: false,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '交期预测趋势分析'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '完成进度 (%)'
                            },
                            min: 0,
                            max: 100
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        },

        updateChart() {
            if (this.chartInstance) {
                this.chartInstance.destroy();
            }
            this.createChart();
        },

        addDays(date, days) {
            const result = new Date(date);
            result.setDate(result.getDate() + days);
            return result;
        },

        formatDateShort(date) {
            return `${date.getMonth() + 1}/${date.getDate()}`;
        }
    }
};
