/**
 * 质量管理API模块
 * 处理检测报告相关的API请求
 */

import { API_URL, getAuthHeaders } from './config.js';

/**
 * 获取用户列表（用于邮件通知选择）
 * @returns {Promise<Object>} API响应
 */
export async function getQualityUsers() {
    try {
        const response = await axios.get(`${API_URL}/quality/users`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取用户列表失败:', error);
        throw error;
    }
}

/**
 * 创建检测报告
 * @param {FormData} formData - 包含报告数据和文件的FormData对象
 * @returns {Promise<Object>} API响应
 */
export async function createQualityReport(formData) {
    try {
        const response = await axios.post('/quality', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return response.data;
    } catch (error) {
        console.error('创建检测报告失败:', error);
        throw error;
    }
}

/**
 * 获取检测报告列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} API响应
 */
export async function getQualityReports(params = {}) {
    try {
        // 添加时间戳参数强制绕过缓存
        const timestamp = Date.now();
        const queryParams = {
            ...params,
            _t: timestamp,
            _r: Math.random()
        };

        const response = await axios.get('/quality', {
            params: queryParams,
            headers: {
                ...getAuthHeaders(),
                // 添加防缓存请求头
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        return response.data;
    } catch (error) {
        console.error('获取检测报告列表失败:', error);
        throw error;
    }
}

/**
 * 根据ID获取检测报告详情
 * @param {string} reportId - 报告ID
 * @returns {Promise<Object>} API响应
 */
export async function getQualityReportById(reportId) {
    try {
        const response = await axios.get(`/quality/${reportId}`);
        return response.data;
    } catch (error) {
        console.error('获取检测报告详情失败:', error);
        throw error;
    }
}

/**
 * 根据报告编号获取检测报告
 * @param {string} reportNumber - 报告编号
 * @returns {Promise<Object>} API响应
 */
export async function getQualityReportByNumber(reportNumber) {
    try {
        const response = await axios.get(`/quality/number/${reportNumber}`);
        return response.data;
    } catch (error) {
        console.error('根据编号获取检测报告失败:', error);
        throw error;
    }
}

/**
 * 更新检测报告
 * @param {string} reportId - 报告ID
 * @param {Object} reportData - 报告数据
 * @returns {Promise<Object>} API响应
 */
export async function updateQualityReport(reportId, reportData) {
    try {
        const response = await axios.put(`/quality/${reportId}`, reportData);
        return response.data;
    } catch (error) {
        console.error('更新检测报告失败:', error);
        throw error;
    }
}

/**
 * 删除检测报告
 * @param {string} reportId - 报告ID
 * @returns {Promise<Object>} API响应
 */
export async function deleteQualityReport(reportId) {
    try {
        const response = await axios.delete(`/quality/${reportId}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除检测报告失败:', error);
        throw error;
    }
}

/**
 * 下载检测报告文件
 * @param {string} fileId - 文件ID
 * @returns {Promise<Blob>} 文件数据
 */
export async function downloadQualityReportFile(fileId) {
    try {
        const response = await axios.get(`/quality/files/${fileId}/download`, {
            responseType: 'blob'
        });
        return response.data;
    } catch (error) {
        console.error('下载检测报告文件失败:', error);
        throw error;
    }
}
