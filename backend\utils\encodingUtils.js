/**
 * 编码处理工具函数
 */

/**
 * 修复文件名编码问题
 * @param {string} filename - 原始文件名
 * @returns {string} 修复后的文件名
 */
function fixFilenameEncoding(filename) {
    if (!filename || typeof filename !== 'string') {
        return filename;
    }

    try {
        let fixed = filename;

        // 方法1: 如果包含乱码字符，尝试从latin1转换为utf8
        if (hasGarbledCharacters(filename)) {
            fixed = Buffer.from(filename, 'latin1').toString('utf8');
        }

        // 方法2: 如果仍然有乱码，尝试URL解码（但要处理异常）
        if (hasGarbledCharacters(fixed)) {
            try {
                fixed = decodeURIComponent(escape(filename));
            } catch (uriError) {
                // URL解码失败，继续使用其他方法
                fixed = filename;
            }
        }

        // 方法3: 如果仍然有乱码，尝试从iso-8859-1转换为utf8
        if (hasGarbledCharacters(fixed)) {
            fixed = Buffer.from(filename, 'binary').toString('utf8');
        }

        // 如果仍然有乱码，返回原始文件名
        if (hasGarbledCharacters(fixed)) {
            return filename;
        }

        return fixed;
    } catch (error) {
        console.warn('文件名编码修复失败:', error);
        return filename;
    }
}

/**
 * 检查字符串是否包含乱码字符
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否包含乱码字符
 */
function hasGarbledCharacters(str) {
    if (!str || typeof str !== 'string') {
        return false;
    }
    
    // 常见的乱码字符模式
    const garbledPatterns = [
        /â/g,           // â 字符
        /ã/g,           // ã 字符
        /Â/g,           // Â 字符
        /Ã/g,           // Ã 字符
        /Ä/g,           // Ä 字符
        /Å/g,           // Å 字符
        /æ/g,           // æ 字符
        /ç/g,           // ç 字符
        /è/g,           // è 字符
        /é/g,           // é 字符
        /ê/g,           // ê 字符
        /ë/g,           // ë 字符
        /ì/g,           // ì 字符
        /í/g,           // í 字符
        /î/g,           // î 字符
        /ï/g,           // ï 字符
        /ð/g,           // ð 字符
        /ñ/g,           // ñ 字符
        /ò/g,           // ò 字符
        /ó/g,           // ó 字符
        /ô/g,           // ô 字符
        /õ/g,           // õ 字符
        /ö/g,           // ö 字符
        /÷/g,           // ÷ 字符
        /ø/g,           // ø 字符
        /ù/g,           // ù 字符
        /ú/g,           // ú 字符
        /û/g,           // û 字符
        /ü/g,           // ü 字符
        /ý/g,           // ý 字符
        /þ/g,           // þ 字符
        /ÿ/g,           // ÿ 字符
        /\uFFFD/g       // 替换字符 (Unicode replacement character)
    ];
    
    // 检查是否匹配任何乱码模式
    return garbledPatterns.some(pattern => pattern.test(str));
}

/**
 * 安全的字符串编码转换
 * @param {string} str - 要转换的字符串
 * @param {string} fromEncoding - 源编码
 * @param {string} toEncoding - 目标编码
 * @returns {string} 转换后的字符串
 */
function safeEncodingConvert(str, fromEncoding = 'latin1', toEncoding = 'utf8') {
    try {
        return Buffer.from(str, fromEncoding).toString(toEncoding);
    } catch (error) {
        console.warn('编码转换失败:', error);
        return str;
    }
}

module.exports = {
    fixFilenameEncoding,
    hasGarbledCharacters,
    safeEncodingConvert
};
