/**
 * 设备管理路由
 * 定义设备相关的API端点
 */

const express = require('express');
const router = express.Router();
const equipmentController = require('../controllers/equipmentController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');
const upload = require('../middlewares/upload');

// 设备信息管理路由

/**
 * 获取设备列表
 * GET /api/equipment
 * 权限: equipment_info
 */
router.get('/',
    authenticateJWT,
    checkPermission('equipment_info'),
    equipmentController.getEquipmentList.bind(equipmentController)
);

// 厂区管理路由 - 必须放在参数路由之前

/**
 * 获取厂区列表
 * GET /api/equipment/factories
 * 权限: equipment_info
 */
router.get('/factories',
    authenticateJWT,
    checkPermission('equipment_info'),
    equipmentController.getFactoryList.bind(equipmentController)
);

/**
 * 获取筛选选项
 * GET /api/equipment/filter-options
 * 权限: equipment_info
 */
router.get('/filter-options',
    authenticateJWT,
    checkPermission('equipment_info'),
    equipmentController.getFilterOptions.bind(equipmentController)
);

/**
 * 创建厂区
 * POST /api/equipment/factories
 * 权限: equipment_manage
 */
router.post('/factories',
    authenticateJWT,
    checkPermission('equipment_manage'),
    equipmentController.createFactory.bind(equipmentController)
);

/**
 * 更新厂区信息
 * PUT /api/equipment/factories/:id
 * 权限: equipment_manage
 */
router.put('/factories/:id',
    authenticateJWT,
    checkPermission('equipment_manage'),
    equipmentController.updateFactory.bind(equipmentController)
);

/**
 * 删除厂区
 * DELETE /api/equipment/factories/:id
 * 权限: equipment_manage
 */
router.delete('/factories/:id',
    authenticateJWT,
    checkPermission('equipment_manage'),
    equipmentController.deleteFactory.bind(equipmentController)
);

/**
 * 创建设备
 * POST /api/equipment
 * 权限: equipment_manage
 */
router.post('/',
    authenticateJWT,
    checkPermission('equipment_manage'),
    equipmentController.createEquipment.bind(equipmentController)
);

/**
 * 导出设备数据为Excel
 * GET /api/equipment/export
 * 权限: equipment_info
 */
router.get('/export',
    authenticateJWT,
    checkPermission('equipment_info'),
    equipmentController.exportEquipment.bind(equipmentController)
);

/**
 * 导入设备数据从Excel
 * POST /api/equipment/import
 * 权限: equipment_manage
 */
router.post('/import',
    authenticateJWT,
    checkPermission('equipment_manage'),
    upload.single('file'),
    equipmentController.importEquipment.bind(equipmentController)
);

/**
 * 获取设备详情
 * GET /api/equipment/:id
 * 权限: equipment_info
 */
router.get('/:id',
    authenticateJWT,
    checkPermission('equipment_info'),
    equipmentController.getEquipmentById.bind(equipmentController)
);

/**
 * 更新设备信息
 * PUT /api/equipment/:id
 * 权限: equipment_manage
 */
router.put('/:id',
    authenticateJWT,
    checkPermission('equipment_manage'),
    equipmentController.updateEquipment.bind(equipmentController)
);

/**
 * 批量删除设备
 * DELETE /api/equipment/batch
 * 权限: equipment_manage
 */
router.delete('/batch',
    authenticateJWT,
    checkPermission('equipment_manage'),
    equipmentController.batchDeleteEquipment.bind(equipmentController)
);



/**
 * 删除设备
 * DELETE /api/equipment/:id
 * 权限: equipment_manage
 */
router.delete('/:id',
    authenticateJWT,
    checkPermission('equipment_manage'),
    equipmentController.deleteEquipment.bind(equipmentController)
);



// 维修/保养记录管理路由

/**
 * 获取设备维修记录
 * GET /api/equipment/:id/maintenance
 * 权限: equipment_maintenance
 */
router.get('/:id/maintenance',
    authenticateJWT,
    checkPermission('equipment_maintenance'),
    equipmentController.getMaintenanceRecords.bind(equipmentController)
);

/**
 * 创建维修记录
 * POST /api/equipment/:id/maintenance
 * 权限: equipment_maintenance
 */
router.post('/:id/maintenance',
    authenticateJWT,
    checkPermission('equipment_maintenance'),
    equipmentController.createMaintenanceRecord.bind(equipmentController)
);

/**
 * 更新维修记录
 * PUT /api/equipment/maintenance/:recordId
 * 权限: equipment_maintenance
 */
router.put('/maintenance/:recordId',
    authenticateJWT,
    checkPermission('equipment_maintenance'),
    equipmentController.updateMaintenanceRecord.bind(equipmentController)
);

/**
 * 删除维修记录
 * DELETE /api/equipment/maintenance/:recordId
 * 权限: equipment_maintenance
 */
router.delete('/maintenance/:recordId',
    authenticateJWT,
    checkPermission('equipment_maintenance'),
    equipmentController.deleteMaintenanceRecord.bind(equipmentController)
);

// 设备健康度评估路由

/**
 * 获取设备健康度评估
 * GET /api/equipment/:id/health
 * 权限: equipment_health
 */
router.get('/:id/health',
    authenticateJWT,
    checkPermission('equipment_health'),
    equipmentController.getEquipmentHealth.bind(equipmentController)
);

/**
 * 计算设备健康度
 * POST /api/equipment/:id/health/calculate
 * 权限: equipment_health
 */
router.post('/:id/health/calculate',
    authenticateJWT,
    checkPermission('equipment_health'),
    equipmentController.calculateEquipmentHealth.bind(equipmentController)
);

/**
 * 获取设备健康度历史
 * GET /api/equipment/:id/health/history
 * 权限: equipment_health
 */
router.get('/:id/health/history',
    authenticateJWT,
    checkPermission('equipment_health'),
    equipmentController.getEquipmentHealthHistory.bind(equipmentController)
);

/**
 * 获取设备故障预测
 * GET /api/equipment/:id/health/prediction
 * 权限: equipment_health
 */
router.get('/:id/health/prediction',
    authenticateJWT,
    checkPermission('equipment_health'),
    equipmentController.getEquipmentFaultPrediction.bind(equipmentController)
);

/**
 * 获取设备健康度统计
 * GET /api/equipment/health/statistics
 * 权限: equipment_health
 */
router.get('/health/statistics',
    authenticateJWT,
    checkPermission('equipment_health'),
    equipmentController.getHealthStatistics.bind(equipmentController)
);

/**
 * 批量计算设备健康度
 * POST /api/equipment/health/batch-calculate
 * 权限: equipment_health
 */
router.post('/health/batch-calculate',
    authenticateJWT,
    checkPermission('equipment_health'),
    equipmentController.batchCalculateHealth.bind(equipmentController)
);

module.exports = router;
