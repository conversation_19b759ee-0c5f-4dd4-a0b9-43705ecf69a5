/**
 * 迁移现有的质量检测报告编号到编号记录表
 * 确保编号的唯一性和连续性
 */

const logger = require('../../utils/logger');

function migrateQualityReportNumbers(db) {
    try {
        logger.info('开始迁移质量检测报告编号...');

        // 检查是否已经迁移过
        const checkTable = db.prepare(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='quality_report_numbers'
        `).get();

        if (!checkTable) {
            logger.info('质量检测报告编号表不存在，跳过迁移');
            return;
        }

        // 检查是否已有数据
        const existingCount = db.prepare(`
            SELECT COUNT(*) as count FROM quality_report_numbers
        `).get();

        if (existingCount.count > 0) {
            logger.info('质量检测报告编号表已有数据，跳过迁移');
            return;
        }

        // 获取所有现有的报告编号
        const existingReports = db.prepare(`
            SELECT report_number, created_at 
            FROM quality_reports 
            ORDER BY created_at ASC
        `).all();

        if (existingReports.length === 0) {
            logger.info('没有现有的质量检测报告，迁移完成');
            return;
        }

        // 插入现有编号到编号记录表
        const insertStmt = db.prepare(`
            INSERT OR IGNORE INTO quality_report_numbers (report_number, created_at)
            VALUES (?, ?)
        `);

        const transaction = db.transaction((reports) => {
            for (const report of reports) {
                insertStmt.run(report.report_number, report.created_at);
            }
        });

        transaction(existingReports);

        logger.info(`成功迁移 ${existingReports.length} 个质量检测报告编号`);

    } catch (error) {
        logger.error('迁移质量检测报告编号失败:', error);
        throw error;
    }
}

module.exports = migrateQualityReportNumbers;
