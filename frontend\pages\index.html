<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Makrite管理系统</title>
    <!-- 不使用meta标签重定向，而是使用JavaScript根据用户角色重定向 -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #F3F4F6;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .loading {
            text-align: center;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #e5e7eb;
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading">
        <div class="spinner"></div>
        <p>正在根据您的权限跳转到相应页面...</p>
    </div>

    <!-- 引入配置和主脚本 -->
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/config.js"></script>
    <script src="/scripts/main.js"></script>
</body>
</html>
