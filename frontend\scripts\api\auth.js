/**
 * 认证API
 * 处理用户认证相关的API请求
 */

import { API_URL } from '../../scripts/config.js';
import ErrorHandler from '../utils/errorHandler.js';

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise<Object>} 登录结果
 */
export async function login(username, password) {
    try {
        const response = await axios.post(`${API_URL}/auth/login`, {
            username,
            password
        });

        if (response.data.success) {
            // 保存登录状态和用户信息到sessionStorage（浏览器关闭时会自动清除）
            sessionStorage.setItem('authToken', response.data.token);
            sessionStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '用户登录', { redirectOnAuth: false });
        throw error;
    }
}

/**
 * 获取当前用户信息
 * @returns {Promise<Object>} 用户信息
 */
export async function getCurrentUser() {
    try {
        const token = sessionStorage.getItem('authToken');
        if (!token) {
            throw new Error('未登录');
        }

        const response = await axios.get(`${API_URL}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取用户信息');
        throw error;
    }
}

/**
 * 检查用户是否已登录
 * @returns {Promise<boolean>} 是否已登录
 */
export async function checkAuth() {
    try {
        const token = sessionStorage.getItem('authToken');
        if (!token || token === 'null' || token === 'undefined') {
            return false;
        }

        // 先进行客户端token过期检查
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);

            if (payload.exp && payload.exp < currentTime) {
                // Token已过期，清除认证信息
                console.warn('Token已过期，清除认证信息');
                sessionStorage.removeItem('authToken');
                sessionStorage.removeItem('user');
                return false;
            }
        } catch (parseError) {
            // Token格式无效，清除认证信息
            console.warn('Token格式无效，清除认证信息');
            sessionStorage.removeItem('authToken');
            sessionStorage.removeItem('user');
            return false;
        }

        // 通过服务器验证token有效性
        await getCurrentUser();
        return true;
    } catch (error) {
        // 清除无效的token
        console.warn('服务器验证token失败，清除认证信息');
        sessionStorage.removeItem('authToken');
        sessionStorage.removeItem('user');
        return false;
    }
}

/**
 * 退出登录
 */
export function logout() {
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('user');
    window.location.href = '/login';
}
