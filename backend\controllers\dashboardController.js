/**
 * 主页控制器
 * 处理主页相关的API请求
 */

const dashboardService = require('../services/dashboardService');
const logger = require('../utils/logger');

class DashboardController {
    /**
     * 获取主页统计数据
     * GET /api/dashboard/stats
     */
    async getStats(req, res) {
        try {
            const user = req.user;
            const stats = await dashboardService.getDashboardStats(user);
            
            res.json(stats);
        } catch (error) {
            logger.error('获取主页统计数据失败:', error);
            res.status(500).json({
                success: false,
                message: '获取统计数据失败: ' + error.message
            });
        }
    }

    /**
     * 获取最近活动
     * GET /api/dashboard/activities
     */
    async getActivities(req, res) {
        try {
            const user = req.user;
            const limit = parseInt(req.query.limit) || 10;
            
            const activities = await dashboardService.getRecentActivities(user, limit);
            
            res.json({
                success: true,
                data: activities
            });
        } catch (error) {
            logger.error('获取最近活动失败:', error);
            res.status(500).json({
                success: false,
                message: '获取最近活动失败: ' + error.message
            });
        }
    }

    /**
     * 获取通知信息
     * GET /api/dashboard/notifications
     */
    async getNotifications(req, res) {
        try {
            const user = req.user;
            const limit = parseInt(req.query.limit) || 5;
            
            const notifications = await dashboardService.getNotifications(user, limit);
            
            res.json({
                success: true,
                data: notifications
            });
        } catch (error) {
            logger.error('获取通知信息失败:', error);
            res.status(500).json({
                success: false,
                message: '获取通知信息失败: ' + error.message
            });
        }
    }

    /**
     * 获取待处理任务
     * GET /api/dashboard/pending-tasks
     */
    async getPendingTasks(req, res) {
        try {
            const user = req.user;
            const tasks = [];

            // 获取待审批申请
            if (dashboardService.canUserApprove(user)) {
                const pendingCount = await dashboardService.getMyPendingApprovalsCount(user);
                if (pendingCount > 0) {
                    tasks.push({
                        type: 'approval',
                        title: '待审批申请',
                        count: pendingCount,
                        url: '/pending-approval',
                        priority: 'high'
                    });
                }
            }

            // 获取我的申请状态
            const databaseManager = require('../database/database');
            const db = databaseManager.db;
            const myPendingQuery = `SELECT COUNT(*) as count FROM applications WHERE applicant = ? AND status = 'pending'`;
            const myPending = db.prepare(myPendingQuery).get(user.username).count;
            
            if (myPending > 0) {
                tasks.push({
                    type: 'my_applications',
                    title: '我的待审批申请',
                    count: myPending,
                    url: '/application-record',
                    priority: 'medium'
                });
            }

            // 获取设备预警任务
            try {
                const warningQuery = `SELECT COUNT(*) as count FROM equipment_health WHERE health_score < 70`;
                const warningCount = db.prepare(warningQuery).get().count;
                if (warningCount > 0 && user.permissions && user.permissions.includes('equipment_manage')) {
                    tasks.push({
                        type: 'equipment_warning',
                        title: '设备预警',
                        count: warningCount,
                        url: '/equipment/health',
                        priority: 'medium'
                    });
                }
            } catch (e) {
                // 忽略健康度表不存在的错误
            }

            res.json({
                success: true,
                data: tasks
            });
        } catch (error) {
            logger.error('获取待处理任务失败:', error);
            res.status(500).json({
                success: false,
                message: '获取待处理任务失败: ' + error.message
            });
        }
    }

    /**
     * 获取快速操作菜单
     * GET /api/dashboard/quick-actions
     */
    async getQuickActions(req, res) {
        try {
            const user = req.user;
            const permissions = user.permissions || [];
            const actions = [];

            // 根据用户权限生成快速操作菜单
            if (permissions.includes('new_application')) {
                actions.push({
                    title: '新建申请',
                    description: '提交新的申请',
                    icon: 'plus',
                    url: '/new-application',
                    color: 'blue'
                });
            }

            if (permissions.includes('equipment_manage')) {
                actions.push({
                    title: '设备管理',
                    description: '管理设备信息',
                    icon: 'cog',
                    url: '/equipment/info',
                    color: 'green'
                });
            }

            if (permissions.includes('quality_upload')) {
                actions.push({
                    title: '质量报告',
                    description: '上传质量报告',
                    icon: 'document',
                    url: '/quality-upload',
                    color: 'purple'
                });
            }

            if (permissions.includes('schedule_create')) {
                actions.push({
                    title: '排程计划',
                    description: '创建排程计划',
                    icon: 'calendar',
                    url: '/schedule/create',
                    color: 'orange'
                });
            }

            if (permissions.includes('scheduling_manage')) {
                actions.push({
                    title: '智能排程',
                    description: 'AI智能排程',
                    icon: 'lightning',
                    url: '/scheduling/intelligent',
                    color: 'indigo'
                });
            }

            if (permissions.includes('view_users')) {
                actions.push({
                    title: '用户管理',
                    description: '管理系统用户',
                    icon: 'users',
                    url: '/user-management',
                    color: 'red'
                });
            }

            res.json({
                success: true,
                data: actions
            });
        } catch (error) {
            logger.error('获取快速操作菜单失败:', error);
            res.status(500).json({
                success: false,
                message: '获取快速操作菜单失败: ' + error.message
            });
        }
    }

    /**
     * 获取系统健康状态
     * GET /api/dashboard/health
     */
    async getSystemHealth(req, res) {
        try {
            const databaseManager = require('../database/database');
            const db = databaseManager.db;
            const memoryUsage = process.memoryUsage();
            const uptime = process.uptime();

            // 数据库健康检查
            let dbHealth = 'healthy';
            try {
                db.prepare('SELECT 1').get();
            } catch (error) {
                dbHealth = 'error';
            }

            // 系统负载检查
            const memoryPercent = Math.round((memoryUsage.used / memoryUsage.total) * 100);
            const systemLoad = memoryPercent > 80 ? 'high' : memoryPercent > 60 ? 'medium' : 'low';

            const health = {
                overall: dbHealth === 'healthy' && systemLoad !== 'high' ? 'healthy' : 'warning',
                database: dbHealth,
                systemLoad: systemLoad,
                uptime: Math.floor(uptime / 3600), // 小时
                memory: {
                    used: Math.round(memoryUsage.used / 1024 / 1024), // MB
                    total: Math.round(memoryUsage.total / 1024 / 1024), // MB
                    percent: memoryPercent
                },
                timestamp: new Date().toISOString()
            };

            res.json({
                success: true,
                data: health
            });
        } catch (error) {
            logger.error('获取系统健康状态失败:', error);
            res.status(500).json({
                success: false,
                message: '获取系统健康状态失败: ' + error.message
            });
        }
    }
}

module.exports = new DashboardController();
