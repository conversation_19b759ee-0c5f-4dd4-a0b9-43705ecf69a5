/**
 * 生产排程控制器
 * 处理排程相关的HTTP请求
 */

const ScheduleService = require('../services/scheduleService');
const logger = require('../utils/logger');

class ScheduleController {
    constructor() {
        this.scheduleService = new ScheduleService();
    }

    /**
     * 获取排程列表
     * GET /api/schedules
     */
    async getSchedules(req, res) {
        try {
            const {
                page = 1,
                limit = 10,
                status,
                startDate,
                endDate,
                priority
            } = req.query;

            const options = {
                page: parseInt(page),
                limit: parseInt(limit),
                status,
                startDate,
                endDate,
                priority
            };

            const result = await this.scheduleService.getSchedules(options);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取排程列表请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取排程详情
     * GET /api/schedules/:id
     */
    async getScheduleById(req, res) {
        try {
            const { id } = req.params;

            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: '排程ID不能为空'
                });
            }

            const result = await this.scheduleService.getScheduleById(id);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '排程不存在' ? 404 : 500;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取排程详情请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 创建排程
     * POST /api/schedules
     */
    async createSchedule(req, res) {
        try {
            const scheduleData = req.body;
            const userId = req.user.id;

            // 基础数据验证
            if (!scheduleData.title || !scheduleData.productId || !scheduleData.productName) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必要的排程信息'
                });
            }

            const result = await this.scheduleService.createSchedule(scheduleData, userId);

            if (result.success) {
                res.status(201).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('创建排程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 更新排程
     * PUT /api/schedules/:id
     */
    async updateSchedule(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const userId = req.user.id;

            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: '排程ID不能为空'
                });
            }

            const result = await this.scheduleService.updateSchedule(id, updateData, userId);

            if (result.success) {
                res.json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '排程不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('更新排程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 删除排程
     * DELETE /api/schedules/:id
     */
    async deleteSchedule(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: '排程ID不能为空'
                });
            }

            const result = await this.scheduleService.deleteSchedule(id, userId);

            if (result.success) {
                res.json({
                    success: true,
                    message: result.message
                });
            } else {
                const statusCode = result.message === '排程不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('删除排程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 开始执行排程
     * POST /api/schedules/:id/start
     */
    async startSchedule(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            const result = await this.scheduleService.updateScheduleStatus(id, 'start', userId);
            
            if (result.success) {
                res.json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '排程不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message
                });
            }
        } catch (error) {
            logger.error('开始排程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 暂停排程
     * POST /api/schedules/:id/pause
     */
    async pauseSchedule(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            const result = await this.scheduleService.updateScheduleStatus(id, 'pause', userId);
            
            if (result.success) {
                res.json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '排程不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message
                });
            }
        } catch (error) {
            logger.error('暂停排程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 完成排程
     * POST /api/schedules/:id/complete
     */
    async completeSchedule(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            const result = await this.scheduleService.updateScheduleStatus(id, 'complete', userId);
            
            if (result.success) {
                res.json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '排程不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message
                });
            }
        } catch (error) {
            logger.error('完成排程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 取消排程
     * POST /api/schedules/:id/cancel
     */
    async cancelSchedule(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            const result = await this.scheduleService.updateScheduleStatus(id, 'cancel', userId);
            
            if (result.success) {
                res.json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '排程不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message
                });
            }
        } catch (error) {
            logger.error('取消排程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }
}

module.exports = new ScheduleController();
