/**
 * 产品控制器
 * 处理产品相关的HTTP请求
 */

const ProductService = require('../services/productService');
const logger = require('../utils/logger');

class ProductController {
    constructor() {
        this.productService = new ProductService();
    }

    /**
     * 获取产品列表
     * GET /api/products
     */
    async getProducts(req, res) {
        try {
            const {
                page = 1,
                limit = 10,
                category,
                keyword
            } = req.query;

            const options = {
                page: parseInt(page),
                limit: parseInt(limit),
                category,
                keyword
            };

            const result = await this.productService.getProducts(options);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取产品列表请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 根据ID获取产品详情
     * GET /api/products/:id
     */
    async getProductById(req, res) {
        try {
            const { id } = req.params;

            const result = await this.productService.getProductById(id);

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '产品不存在' ? 404 : 500;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取产品详情请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 创建产品
     * POST /api/products
     */
    async createProduct(req, res) {
        try {
            const productData = req.body;
            const userId = req.user.id;

            // 基础数据验证
            if (!productData.code || !productData.name) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必要的产品信息'
                });
            }

            const result = await this.productService.createProduct(productData, userId);

            if (result.success) {
                res.status(201).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('创建产品请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 更新产品
     * PUT /api/products/:id
     */
    async updateProduct(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const userId = req.user.id;

            const result = await this.productService.updateProduct(id, updateData, userId);

            if (result.success) {
                res.json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '产品不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('更新产品请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 删除产品
     * DELETE /api/products/:id
     */
    async deleteProduct(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            const result = await this.productService.deleteProduct(id, userId);

            if (result.success) {
                res.json({
                    success: true,
                    message: result.message
                });
            } else {
                const statusCode = result.message === '产品不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('删除产品请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 创建/更新产品工艺流程
     * POST /api/products/:id/processes
     */
    async createProductionProcesses(req, res) {
        try {
            const { id } = req.params;
            const { processes } = req.body;
            const userId = req.user.id;

            if (!Array.isArray(processes) || processes.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '工艺流程数据不能为空'
                });
            }

            const result = await this.productService.createProductionProcesses(id, processes, userId);

            if (result.success) {
                res.status(201).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                const statusCode = result.message === '产品不存在' ? 404 : 400;
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    errors: result.errors,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('创建产品工艺流程请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }

    /**
     * 获取产品选项列表（用于下拉选择）
     * GET /api/products/options
     */
    async getProductOptions(req, res) {
        try {
            const result = await this.productService.getProductOptions();

            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            logger.error('获取产品选项请求处理失败', { error: error.message });
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }
}

module.exports = ProductController;
