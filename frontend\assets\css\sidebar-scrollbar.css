/**
 * 侧边栏滚动条样式
 * 用于美化侧边导航栏的滚动条外观，支持响应式设计
 */

/* 侧边栏基础样式 */
.sidebar {
    /* 确保侧边栏在移动端也能正确显示 */
    max-height: 100vh;
    max-height: 100dvh; /* 动态视口高度，更好的移动端支持 */
}

/* 导航区域滚动样式 */
.sidebar nav {
    /* 确保滚动区域正确计算高度 */
    max-height: calc(100vh - 160px); /* 减去头部和底部的高度 */
    max-height: calc(100dvh - 160px); /* 动态视口高度版本 */

    /* 平滑滚动 */
    scroll-behavior: smooth;

    /* 滚动条样式 - Firefox */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f8fafc;
}

/* Webkit浏览器滚动条样式 (Chrome, Safari, Edge) */
.sidebar nav::-webkit-scrollbar {
    width: 6px;
}

.sidebar nav::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
    margin: 4px 0; /* 上下留出一些空间 */
}

.sidebar nav::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.sidebar nav::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.sidebar nav::-webkit-scrollbar-thumb:active {
    background: #64748b;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .sidebar nav {
        /* 移动端调整最大高度 */
        max-height: calc(100vh - 140px);
        max-height: calc(100dvh - 140px);

        /* 移动端优化触摸滚动 */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
    }

    /* 移动端滚动条稍微宽一点，便于触摸操作 */
    .sidebar nav::-webkit-scrollbar {
        width: 8px;
    }

    /* 移动端侧边栏打开时防止背景滚动 */
    .sidebar.sidebar-open {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 50;
    }
}

/* 小屏幕设备优化 */
@media (max-height: 600px) {
    .sidebar nav {
        /* 小屏幕设备进一步调整 */
        max-height: calc(100vh - 120px);
        max-height: calc(100dvh - 120px);
    }
}

/* 确保滚动条在深色模式下也能正常显示 */
@media (prefers-color-scheme: dark) {
    .sidebar nav::-webkit-scrollbar-track {
        background: #1f2937;
    }

    .sidebar nav::-webkit-scrollbar-thumb {
        background: #4b5563;
    }

    .sidebar nav::-webkit-scrollbar-thumb:hover {
        background: #6b7280;
    }

    .sidebar nav {
        scrollbar-color: #4b5563 #1f2937;
    }
}

/* 滚动指示器 */
.sidebar-scroll-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 40px;
    background: linear-gradient(to bottom, transparent, rgba(59, 130, 246, 0.3), transparent);
    border-radius: 2px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.sidebar nav:hover .sidebar-scroll-indicator {
    opacity: 1;
}

/* 滚动阴影效果 */
.sidebar nav {
    background:
        /* 顶部阴影 */
        linear-gradient(white 30%, rgba(255,255,255,0)) 0 0/100% 20px,
        /* 底部阴影 */
        linear-gradient(rgba(255,255,255,0), white 70%) 0 100%/100% 20px,
        /* 实际背景 */
        white;
    background-repeat: no-repeat;
    background-attachment: local, local, scroll;
}
