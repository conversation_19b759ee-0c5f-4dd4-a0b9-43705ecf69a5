/**
 * 产品路由
 * 定义产品相关的API端点
 */

const express = require('express');
const router = express.Router();
const ProductController = require('../controllers/productController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

const productController = new ProductController();

/**
 * 获取产品选项列表（用于下拉选择）
 * GET /api/products/options
 * 权限: schedule_view 或 product_view
 */
router.get('/options',
    authenticateJWT,
    checkPermission(['schedule_view', 'product_view']),
    productController.getProductOptions.bind(productController)
);

/**
 * 获取产品列表
 * GET /api/products
 * 权限: product_view
 */
router.get('/',
    authenticateJWT,
    checkPermission('product_view'),
    productController.getProducts.bind(productController)
);

/**
 * 获取产品详情
 * GET /api/products/:id
 * 权限: product_view
 */
router.get('/:id',
    authenticateJWT,
    checkPermission('product_view'),
    productController.getProductById.bind(productController)
);

/**
 * 创建产品
 * POST /api/products
 * 权限: product_create
 */
router.post('/',
    authenticateJWT,
    checkPermission('product_create'),
    productController.createProduct.bind(productController)
);

/**
 * 更新产品
 * PUT /api/products/:id
 * 权限: product_edit
 */
router.put('/:id',
    authenticateJWT,
    checkPermission('product_edit'),
    productController.updateProduct.bind(productController)
);

/**
 * 删除产品
 * DELETE /api/products/:id
 * 权限: product_delete
 */
router.delete('/:id',
    authenticateJWT,
    checkPermission('product_delete'),
    productController.deleteProduct.bind(productController)
);

/**
 * 创建/更新产品工艺流程
 * POST /api/products/:id/processes
 * 权限: product_edit
 */
router.post('/:id/processes',
    authenticateJWT,
    checkPermission('product_edit'),
    productController.createProductionProcesses.bind(productController)
);

module.exports = router;
