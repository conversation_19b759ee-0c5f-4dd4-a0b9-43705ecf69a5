<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺流程管理 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/product/management.css">
    <link rel="stylesheet" href="/assets/css/product/processes.css">

</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">工艺流程管理</h1>
                            <p class="text-gray-600 mt-1">配置产品的生产工艺流程和标准工时</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <select v-model="selectedProductId"
                                    @change="loadProductProcesses"
                                    class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">选择产品</option>
                                <option v-for="product in productOptions" :key="product.id" :value="product.id">
                                    {{ product.label }}
                                </option>
                            </select>
                        </div>
                    </div>
                </header>

                <!-- 产品信息 -->
                <div v-if="selectedProduct" class="px-6 py-4 bg-blue-50 border-b border-blue-200">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <h2 class="text-lg font-medium text-blue-900">{{ selectedProduct.name }}</h2>
                            <p class="text-blue-700">产品编码: {{ selectedProduct.code }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-blue-700">标准工时: {{ selectedProduct.standardTime }} 分钟</p>
                            <p class="text-sm text-blue-700">计量单位: {{ selectedProduct.unit }}</p>
                        </div>
                    </div>
                </div>

                <!-- 工艺流程编辑区 -->
                <div class="p-6">
                    <div v-if="!selectedProductId" class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">请选择产品</h3>
                        <p class="mt-1 text-sm text-gray-500">选择一个产品来配置其生产工艺流程</p>
                    </div>

                    <div v-else>
                        <!-- 工艺流程列表 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-medium text-gray-900">生产工艺流程</h3>
                                    <button @click="addProcess"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        添加工序
                                    </button>
                                </div>
                            </div>

                            <div class="p-6">
                                <div v-if="processes.length === 0" class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">暂无工艺流程</h3>
                                    <p class="mt-1 text-sm text-gray-500">开始添加第一个工序吧</p>
                                </div>

                                <!-- 工序流程图 -->
                                <div v-else class="space-y-4">
                                    <div class="flex items-center space-x-4 overflow-x-auto pb-4">
                                        <div v-for="(process, index) in processes" 
                                             :key="process.tempId || process.id"
                                             class="process-step flex-shrink-0 bg-blue-50 border-2 border-blue-200 rounded-lg p-4 min-w-64">
                                            <div class="flex items-center justify-between mb-2">
                                                <span class="text-sm font-medium text-blue-800">工序 {{ index + 1 }}</span>
                                                <div class="flex items-center space-x-2">
                                                    <button @click="editProcess(index)"
                                                            class="text-blue-600 hover:text-blue-800">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                    </button>
                                                    <button @click="removeProcess(index)"
                                                            class="text-red-600 hover:text-red-800">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                            <h4 class="font-medium text-gray-900 mb-1">{{ process.processName }}</h4>
                                            <p class="text-sm text-gray-600">标准工时: {{ process.standardTime }} 分钟</p>
                                            <p v-if="process.setupTime > 0" class="text-sm text-gray-600">准备时间: {{ process.setupTime }} 分钟</p>
                                            <p v-if="process.requiredEquipmentType" class="text-sm text-gray-600">设备类型: {{ process.requiredEquipmentType }}</p>
                                        </div>
                                    </div>

                                    <!-- 工序详细列表 -->
                                    <div class="mt-8">
                                        <h4 class="text-lg font-medium text-gray-900 mb-4">工序详细信息</h4>
                                        <div class="overflow-x-auto">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <thead class="bg-gray-50">
                                                    <tr>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工序名称</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标准工时</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">准备时间</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备类型</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    <tr v-for="(process, index) in processes" :key="process.tempId || process.id">
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ index + 1 }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ process.processName }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ process.standardTime }} 分钟</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ process.setupTime }} 分钟</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ process.requiredEquipmentType || '-' }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                            <button @click="editProcess(index)" class="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                                                            <button @click="removeProcess(index)" class="text-red-600 hover:text-red-900">删除</button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- 保存按钮 -->
                                    <div class="mt-6 flex justify-end">
                                        <button @click="saveProcesses"
                                                :disabled="saving"
                                                class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50">
                                            {{ saving ? '保存中...' : '保存工艺流程' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工序编辑模态框 -->
        <div v-if="showProcessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="closeProcessModal">
            <div class="bg-white rounded-lg p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        {{ editingProcessIndex === -1 ? '添加工序' : '编辑工序' }}
                    </h3>
                    <button @click="closeProcessModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="saveProcess">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">工序名称 *</label>
                            <input v-model="processForm.processName"
                                   type="text"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">标准工时（分钟）*</label>
                            <input v-model.number="processForm.standardTime"
                                   type="number"
                                   min="1"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">准备时间（分钟）</label>
                            <input v-model.number="processForm.setupTime"
                                   type="number"
                                   min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所需设备类型</label>
                            <input v-model="processForm.requiredEquipmentType"
                                   type="text"
                                   placeholder="如：注塑机、包装机等"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="mt-6 flex items-center justify-end space-x-4">
                        <button type="button"
                                @click="closeProcessModal"
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/product/processes.js"></script>
</body>
</html>
