/**
 * 工艺流程管理页面
 * 产品生产工艺流程的配置和管理
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ProductAPI from '../../api/product.js';

const { createApp, ref, onMounted, computed } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const loading = ref(false);
        const saving = ref(false);
        const productOptions = ref([]);
        const selectedProductId = ref('');
        const selectedProduct = ref(null);
        const processes = ref([]);
        const showProcessModal = ref(false);
        const editingProcessIndex = ref(-1);

        // 工序表单数据
        const processForm = ref({
            processName: '',
            standardTime: 0,
            setupTime: 0,
            requiredEquipmentType: '',
            skillRequirements: []
        });

        // 计算属性
        const totalStandardTime = computed(() => {
            return processes.value.reduce((total, process) => total + (process.standardTime || 0), 0);
        });

        // 初始化
        onMounted(async () => {
            try {
                // 获取当前用户信息
                const user = await getCurrentUser();
                if (user) {
                    currentUser.value = user;
                    isAuthenticated.value = true;
                    await loadProductOptions();
                } else {
                    // 未登录，跳转到登录页
                    window.location.href = '/login';
                    return;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                window.location.href = '/login';
                return;
            } finally {
                hideLoading();
            }
        });

        // 加载产品选项
        async function loadProductOptions() {
            try {
                const response = await ProductAPI.getProductOptions();
                if (response.success) {
                    productOptions.value = response.data;
                } else {
                    console.error('获取产品选项失败:', response.message);
                    window.showNotification('获取产品选项失败', 'error');
                }
            } catch (error) {
                console.error('加载产品选项失败:', error);
                window.showNotification('加载产品选项失败', 'error');
            }
        }

        // 加载产品工艺流程
        async function loadProductProcesses() {
            if (!selectedProductId.value) {
                selectedProduct.value = null;
                processes.value = [];
                return;
            }

            loading.value = true;
            try {
                const response = await ProductAPI.getProductById(selectedProductId.value);
                if (response.success) {
                    selectedProduct.value = response.data.product;
                    processes.value = response.data.processes || [];
                    
                    // 为每个工序添加临时ID（用于Vue的key）
                    processes.value.forEach((process, index) => {
                        if (!process.tempId) {
                            process.tempId = `temp_${Date.now()}_${index}`;
                        }
                    });
                } else {
                    console.error('获取产品详情失败:', response.message);
                    window.showNotification('获取产品详情失败', 'error');
                }
            } catch (error) {
                console.error('加载产品工艺流程失败:', error);
                window.showNotification('加载产品工艺流程失败', 'error');
            } finally {
                loading.value = false;
            }
        }

        // 添加工序
        function addProcess() {
            editingProcessIndex.value = -1;
            resetProcessForm();
            showProcessModal.value = true;
        }

        // 编辑工序
        function editProcess(index) {
            editingProcessIndex.value = index;
            const process = processes.value[index];
            processForm.value = {
                processName: process.processName,
                standardTime: process.standardTime,
                setupTime: process.setupTime || 0,
                requiredEquipmentType: process.requiredEquipmentType || '',
                skillRequirements: process.skillRequirements || []
            };
            showProcessModal.value = true;
        }

        // 保存工序
        function saveProcess() {
            // 基本验证
            if (!processForm.value.processName.trim()) {
                window.showNotification('请输入工序名称', 'error');
                return;
            }

            if (!processForm.value.standardTime || processForm.value.standardTime <= 0) {
                window.showNotification('请输入有效的标准工时', 'error');
                return;
            }

            const processData = {
                ...processForm.value,
                sequenceOrder: editingProcessIndex.value === -1 ? processes.value.length + 1 : editingProcessIndex.value + 1,
                tempId: `temp_${Date.now()}_${Math.random()}`
            };

            if (editingProcessIndex.value === -1) {
                // 添加新工序
                processes.value.push(processData);
            } else {
                // 更新现有工序
                processes.value[editingProcessIndex.value] = {
                    ...processes.value[editingProcessIndex.value],
                    ...processData
                };
            }

            closeProcessModal();
            window.showNotification(
                editingProcessIndex.value === -1 ? '工序添加成功' : '工序更新成功', 
                'success'
            );
        }

        // 删除工序
        function removeProcess(index) {
            if (!confirm('确定要删除这个工序吗？')) {
                return;
            }

            processes.value.splice(index, 1);
            
            // 重新排序
            processes.value.forEach((process, idx) => {
                process.sequenceOrder = idx + 1;
            });

            window.showNotification('工序删除成功', 'success');
        }

        // 保存工艺流程
        async function saveProcesses() {
            if (!selectedProductId.value) {
                window.showNotification('请先选择产品', 'error');
                return;
            }

            if (processes.value.length === 0) {
                window.showNotification('请至少添加一个工序', 'error');
                return;
            }

            saving.value = true;
            try {
                // 准备工序数据
                const processesData = processes.value.map((process, index) => ({
                    processName: process.processName,
                    sequenceOrder: index + 1,
                    standardTime: process.standardTime,
                    setupTime: process.setupTime || 0,
                    requiredEquipmentType: process.requiredEquipmentType || '',
                    skillRequirements: process.skillRequirements || []
                }));

                const response = await ProductAPI.createProductionProcesses(selectedProductId.value, processesData);

                if (response.success) {
                    window.showNotification('工艺流程保存成功', 'success');
                    await loadProductProcesses(); // 重新加载以获取服务器生成的ID
                } else {
                    window.showNotification('保存失败: ' + response.message, 'error');
                }
            } catch (error) {
                console.error('保存工艺流程失败:', error);
                window.showNotification('保存工艺流程失败', 'error');
            } finally {
                saving.value = false;
            }
        }

        // 关闭工序模态框
        function closeProcessModal() {
            showProcessModal.value = false;
            editingProcessIndex.value = -1;
            resetProcessForm();
        }

        // 重置工序表单
        function resetProcessForm() {
            processForm.value = {
                processName: '',
                standardTime: 0,
                setupTime: 0,
                requiredEquipmentType: '',
                skillRequirements: []
            };
        }

        return {
            currentUser,
            isAuthenticated,
            loading,
            saving,
            productOptions,
            selectedProductId,
            selectedProduct,
            processes,
            showProcessModal,
            editingProcessIndex,
            processForm,
            totalStandardTime,
            loadProductProcesses,
            addProcess,
            editProcess,
            saveProcess,
            removeProcess,
            saveProcesses,
            closeProcessModal
        };
    }
}).mount('#app');
