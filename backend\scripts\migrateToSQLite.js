#!/usr/bin/env node

/**
 * 数据迁移执行脚本
 * 将JSON文件数据迁移到SQLite数据库
 */

const fs = require('fs');
const path = require('path');
const DataMigrator = require('../database/migrate');
const permissionTemplateRepository = require('../database/permissionTemplateRepository');
const logger = require('../utils/logger');

async function main() {
    console.log('='.repeat(60));
    console.log('开始数据迁移到SQLite数据库');
    console.log('='.repeat(60));

    try {
        // 创建数据迁移器实例
        const migrator = new DataMigrator();

        // 执行数据迁移
        console.log('\n1. 执行数据迁移...');
        await migrator.migrate();

        // 初始化内置权限模板
        console.log('\n2. 初始化内置权限模板...');
        permissionTemplateRepository.initBuiltInTemplates();

        // 验证迁移结果
        console.log('\n3. 验证迁移结果...');
        const stats = migrator.validateMigration();

        console.log('\n' + '='.repeat(60));
        console.log('数据迁移完成！');
        console.log('='.repeat(60));
        console.log(`迁移统计:`);
        console.log(`- 用户数量: ${stats.userCount}`);
        console.log(`- 申请数量: ${stats.applicationCount}`);
        console.log(`- 权限模板数量: ${stats.templateCount}`);

        // 询问是否删除原始JSON文件
        console.log('\n是否删除原始JSON数据文件？');
        console.log('注意：删除前请确保数据迁移正确！');
        
        // 在生产环境中，我们先不自动删除，让用户手动确认
        console.log('\n请手动运行以下命令来删除原始数据文件（在确认迁移成功后）：');
        console.log('node scripts/cleanupJsonFiles.js');

    } catch (error) {
        console.error('\n数据迁移失败:', error);
        logger.error('数据迁移失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = main;
