/**
 * 性能监控API路由
 * 提供性能数据查询和监控功能
 */

const express = require('express');
const router = express.Router();
const performanceMonitor = require('../utils/PerformanceMonitor');
const schedulingCache = require('../utils/SchedulingCache');

/**
 * 获取性能统计信息
 * GET /api/performance/stats
 */
router.get('/stats', (req, res) => {
    try {
        const { operation } = req.query;
        const stats = performanceMonitor.getStats(operation);
        
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('获取性能统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取性能统计失败',
            error: error.message
        });
    }
});

/**
 * 获取详细性能报告
 * GET /api/performance/report
 */
router.get('/report', (req, res) => {
    try {
        const {
            operation,
            timeRange,
            includeHistory = 'false'
        } = req.query;

        const options = {
            operationName: operation || null,
            timeRange: timeRange ? parseInt(timeRange) : null,
            includeHistory: includeHistory === 'true'
        };

        const report = performanceMonitor.getPerformanceReport(options);
        
        res.json({
            success: true,
            data: report
        });
    } catch (error) {
        console.error('获取性能报告失败:', error);
        res.status(500).json({
            success: false,
            message: '获取性能报告失败',
            error: error.message
        });
    }
});

/**
 * 获取系统资源使用情况
 * GET /api/performance/resources
 */
router.get('/resources', (req, res) => {
    try {
        const resources = performanceMonitor.getSystemResources();
        
        res.json({
            success: true,
            data: resources
        });
    } catch (error) {
        console.error('获取系统资源信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取系统资源信息失败',
            error: error.message
        });
    }
});

/**
 * 获取缓存统计信息
 * GET /api/performance/cache
 */
router.get('/cache', (req, res) => {
    try {
        const cacheStats = schedulingCache.getStats();
        
        res.json({
            success: true,
            data: cacheStats
        });
    } catch (error) {
        console.error('获取缓存统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取缓存统计失败',
            error: error.message
        });
    }
});

/**
 * 清空缓存
 * DELETE /api/performance/cache
 */
router.delete('/cache', (req, res) => {
    try {
        schedulingCache.clear();
        
        res.json({
            success: true,
            message: '缓存清空成功'
        });
    } catch (error) {
        console.error('清空缓存失败:', error);
        res.status(500).json({
            success: false,
            message: '清空缓存失败',
            error: error.message
        });
    }
});

/**
 * 重置性能统计
 * DELETE /api/performance/stats
 */
router.delete('/stats', (req, res) => {
    try {
        const { operation } = req.query;
        performanceMonitor.reset(operation);
        
        res.json({
            success: true,
            message: operation ? `操作 ${operation} 的性能统计已重置` : '全部性能统计已重置'
        });
    } catch (error) {
        console.error('重置性能统计失败:', error);
        res.status(500).json({
            success: false,
            message: '重置性能统计失败',
            error: error.message
        });
    }
});

/**
 * 导出性能数据
 * GET /api/performance/export
 */
router.get('/export', (req, res) => {
    try {
        const {
            includeHistory = 'true',
            timeRange,
            format = 'json'
        } = req.query;

        const options = {
            includeHistory: includeHistory === 'true',
            timeRange: timeRange ? parseInt(timeRange) : null
        };

        const exportData = performanceMonitor.export(options);

        if (format === 'json') {
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', 'attachment; filename="performance_data.json"');
            res.json(exportData);
        } else if (format === 'csv') {
            // 简化的CSV导出
            const csvData = convertPerformanceDataToCSV(exportData);
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', 'attachment; filename="performance_data.csv"');
            res.send(csvData);
        } else {
            res.status(400).json({
                success: false,
                message: '不支持的导出格式'
            });
        }
    } catch (error) {
        console.error('导出性能数据失败:', error);
        res.status(500).json({
            success: false,
            message: '导出性能数据失败',
            error: error.message
        });
    }
});

/**
 * 获取性能趋势数据
 * GET /api/performance/trends
 */
router.get('/trends', (req, res) => {
    try {
        const {
            operation,
            timeRange = '3600000', // 默认1小时
            interval = '300000'     // 默认5分钟间隔
        } = req.query;

        const trends = calculatePerformanceTrends(
            operation,
            parseInt(timeRange),
            parseInt(interval)
        );
        
        res.json({
            success: true,
            data: trends
        });
    } catch (error) {
        console.error('获取性能趋势失败:', error);
        res.status(500).json({
            success: false,
            message: '获取性能趋势失败',
            error: error.message
        });
    }
});

/**
 * 获取性能告警信息
 * GET /api/performance/alerts
 */
router.get('/alerts', (req, res) => {
    try {
        const alerts = getPerformanceAlerts();
        
        res.json({
            success: true,
            data: alerts
        });
    } catch (error) {
        console.error('获取性能告警失败:', error);
        res.status(500).json({
            success: false,
            message: '获取性能告警失败',
            error: error.message
        });
    }
});

/**
 * 健康检查接口
 * GET /api/performance/health
 */
router.get('/health', (req, res) => {
    try {
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            performance: performanceMonitor.getStats(),
            cache: schedulingCache.getStats()
        };

        // 简单的健康状态判断
        const memoryUsage = health.memory.heapUsed / health.memory.heapTotal;
        const errorRate = health.performance.global ? 
            health.performance.global.failedRequests / health.performance.global.totalRequests : 0;

        if (memoryUsage > 0.9 || errorRate > 0.1) {
            health.status = 'unhealthy';
        } else if (memoryUsage > 0.7 || errorRate > 0.05) {
            health.status = 'degraded';
        }

        const statusCode = health.status === 'healthy' ? 200 : 
                          health.status === 'degraded' ? 200 : 503;

        res.status(statusCode).json({
            success: true,
            data: health
        });
    } catch (error) {
        console.error('健康检查失败:', error);
        res.status(500).json({
            success: false,
            message: '健康检查失败',
            error: error.message
        });
    }
});

/**
 * 转换性能数据为CSV格式
 * @param {Object} data 性能数据
 * @returns {string} CSV字符串
 */
function convertPerformanceDataToCSV(data) {
    const headers = ['操作名称', '总请求数', '成功请求数', '失败请求数', '平均响应时间', '最大响应时间', '错误率'];
    const rows = [];

    for (const [operationName, operationData] of Object.entries(data.operations)) {
        const stats = operationData.stats;
        const errorRate = stats.count > 0 ? (stats.failureCount / stats.count * 100).toFixed(2) : '0.00';
        
        rows.push([
            operationName,
            stats.count,
            stats.successCount,
            stats.failureCount,
            stats.averageTime.toFixed(2),
            stats.maxTime.toFixed(2),
            `${errorRate}%`
        ]);
    }

    const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

    return csvContent;
}

/**
 * 计算性能趋势
 * @param {string} operation 操作名称
 * @param {number} timeRange 时间范围
 * @param {number} interval 间隔
 * @returns {Object} 趋势数据
 */
function calculatePerformanceTrends(operation, timeRange, interval) {
    // 简化实现，实际应该从历史数据中计算趋势
    const now = Date.now();
    const startTime = now - timeRange;
    const points = [];

    for (let time = startTime; time <= now; time += interval) {
        points.push({
            timestamp: new Date(time).toISOString(),
            responseTime: Math.random() * 1000 + 500, // 模拟数据
            throughput: Math.random() * 100 + 50,     // 模拟数据
            errorRate: Math.random() * 0.05           // 模拟数据
        });
    }

    return {
        operation: operation || 'all',
        timeRange,
        interval,
        points
    };
}

/**
 * 获取性能告警
 * @returns {Array} 告警列表
 */
function getPerformanceAlerts() {
    // 简化实现，实际应该从监控系统获取告警
    return [
        {
            id: 'alert1',
            type: 'response_time',
            level: 'warning',
            message: '响应时间超过阈值',
            timestamp: new Date().toISOString(),
            operation: 'generateSchedulePlans'
        }
    ];
}

module.exports = router;
