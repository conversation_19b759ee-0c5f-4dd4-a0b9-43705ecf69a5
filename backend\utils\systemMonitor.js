/**
 * 系统监控和告警模块
 * 监控系统性能、资源使用和异常情况
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const logger = require('./logger');

class SystemMonitor {
    constructor() {
        this.metrics = {
            cpu: [],
            memory: [],
            disk: [],
            requests: [],
            errors: []
        };
        
        this.thresholds = {
            cpu: 80,        // CPU使用率阈值 (%)
            memory: 85,     // 内存使用率阈值 (%)
            disk: 90,       // 磁盘使用率阈值 (%)
            responseTime: 2000, // 响应时间阈值 (ms)
            errorRate: 5    // 错误率阈值 (%)
        };
        
        this.alerts = [];
        this.maxMetricsHistory = 100;
        this.maxAlerts = 50;
        
        // 启动监控
        this.startMonitoring();
    }

    /**
     * 启动系统监控
     */
    startMonitoring() {
        // 每30秒收集一次系统指标
        setInterval(() => {
            this.collectSystemMetrics();
        }, 30000);

        // 每5分钟检查一次告警
        setInterval(() => {
            this.checkAlerts();
        }, 300000);

        logger.info('系统监控已启动');
    }

    /**
     * 收集系统指标
     */
    collectSystemMetrics() {
        const timestamp = new Date();
        
        // CPU指标
        const cpuUsage = this.getCPUUsage();
        this.addMetric('cpu', {
            timestamp,
            usage: cpuUsage,
            loadAverage: os.loadavg()
        });

        // 内存指标
        const memoryUsage = this.getMemoryUsage();
        this.addMetric('memory', {
            timestamp,
            ...memoryUsage
        });

        // 磁盘指标
        const diskUsage = this.getDiskUsage();
        this.addMetric('disk', {
            timestamp,
            ...diskUsage
        });

        // 进程指标
        const processMetrics = this.getProcessMetrics();
        this.addMetric('process', {
            timestamp,
            ...processMetrics
        });
    }

    /**
     * 添加指标数据
     */
    addMetric(type, data) {
        if (!this.metrics[type]) {
            this.metrics[type] = [];
        }
        
        this.metrics[type].push(data);
        
        // 限制历史数据数量
        if (this.metrics[type].length > this.maxMetricsHistory) {
            this.metrics[type] = this.metrics[type].slice(-this.maxMetricsHistory);
        }
    }

    /**
     * 获取CPU使用率
     */
    getCPUUsage() {
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;

        cpus.forEach(cpu => {
            for (const type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        });

        const idle = totalIdle / cpus.length;
        const total = totalTick / cpus.length;
        const usage = 100 - ~~(100 * idle / total);

        return {
            usage,
            cores: cpus.length,
            model: cpus[0].model,
            speed: cpus[0].speed
        };
    }

    /**
     * 获取内存使用情况
     */
    getMemoryUsage() {
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        const usagePercentage = (usedMemory / totalMemory) * 100;

        const processMemory = process.memoryUsage();

        return {
            total: totalMemory,
            free: freeMemory,
            used: usedMemory,
            usagePercentage,
            process: {
                rss: processMemory.rss,
                heapTotal: processMemory.heapTotal,
                heapUsed: processMemory.heapUsed,
                external: processMemory.external,
                arrayBuffers: processMemory.arrayBuffers
            }
        };
    }

    /**
     * 获取磁盘使用情况
     */
    getDiskUsage() {
        try {
            const stats = fs.statSync(process.cwd());
            const dbPath = path.join(__dirname, '../database/application_system.db');
            
            let dbSize = 0;
            if (fs.existsSync(dbPath)) {
                dbSize = fs.statSync(dbPath).size;
            }

            return {
                database: {
                    size: dbSize,
                    sizeMB: (dbSize / 1024 / 1024).toFixed(2)
                },
                available: true
            };
        } catch (error) {
            return {
                available: false,
                error: error.message
            };
        }
    }

    /**
     * 获取进程指标
     */
    getProcessMetrics() {
        return {
            uptime: process.uptime(),
            pid: process.pid,
            version: process.version,
            platform: process.platform,
            arch: process.arch,
            cpuUsage: process.cpuUsage(),
            resourceUsage: process.resourceUsage ? process.resourceUsage() : null
        };
    }

    /**
     * 记录请求指标
     */
    recordRequest(duration, statusCode, path) {
        const timestamp = new Date();
        
        this.addMetric('requests', {
            timestamp,
            duration,
            statusCode,
            path,
            isError: statusCode >= 400
        });
    }

    /**
     * 记录错误
     */
    recordError(error, context = {}) {
        const timestamp = new Date();
        
        this.addMetric('errors', {
            timestamp,
            message: error.message,
            stack: error.stack,
            context
        });
    }

    /**
     * 检查告警条件
     */
    checkAlerts() {
        const currentMetrics = this.getCurrentMetrics();
        
        // CPU告警
        if (currentMetrics.cpu && currentMetrics.cpu.usage > this.thresholds.cpu) {
            this.createAlert('HIGH_CPU_USAGE', `CPU使用率过高: ${currentMetrics.cpu.usage.toFixed(2)}%`);
        }

        // 内存告警
        if (currentMetrics.memory && currentMetrics.memory.usagePercentage > this.thresholds.memory) {
            this.createAlert('HIGH_MEMORY_USAGE', `内存使用率过高: ${currentMetrics.memory.usagePercentage.toFixed(2)}%`);
        }

        // 错误率告警
        const errorRate = this.getErrorRate();
        if (errorRate > this.thresholds.errorRate) {
            this.createAlert('HIGH_ERROR_RATE', `错误率过高: ${errorRate.toFixed(2)}%`);
        }

        // 响应时间告警
        const avgResponseTime = this.getAverageResponseTime();
        if (avgResponseTime > this.thresholds.responseTime) {
            this.createAlert('SLOW_RESPONSE', `平均响应时间过长: ${avgResponseTime.toFixed(2)}ms`);
        }
    }

    /**
     * 创建告警
     */
    createAlert(type, message, severity = 'warning') {
        const alert = {
            id: Date.now().toString(),
            type,
            message,
            severity,
            timestamp: new Date(),
            resolved: false
        };

        this.alerts.unshift(alert);
        
        // 限制告警数量
        if (this.alerts.length > this.maxAlerts) {
            this.alerts = this.alerts.slice(0, this.maxAlerts);
        }

        // 只在生产环境记录详细的系统告警日志
        if (process.env.NODE_ENV === 'production') {
            logger.warn(`系统告警: ${message}`, { type, severity });
        } else {
            logger.debug(`系统告警 (开发环境): ${message}`, { type, severity });
        }

        return alert;
    }

    /**
     * 获取当前指标
     */
    getCurrentMetrics() {
        const result = {};
        
        for (const [type, metrics] of Object.entries(this.metrics)) {
            if (metrics.length > 0) {
                result[type] = metrics[metrics.length - 1];
            }
        }
        
        return result;
    }

    /**
     * 获取错误率
     */
    getErrorRate() {
        const recentRequests = this.metrics.requests.slice(-50); // 最近50个请求
        if (recentRequests.length === 0) return 0;
        
        const errorCount = recentRequests.filter(req => req.isError).length;
        return (errorCount / recentRequests.length) * 100;
    }

    /**
     * 获取平均响应时间
     */
    getAverageResponseTime() {
        const recentRequests = this.metrics.requests.slice(-20); // 最近20个请求
        if (recentRequests.length === 0) return 0;
        
        const totalDuration = recentRequests.reduce((sum, req) => sum + req.duration, 0);
        return totalDuration / recentRequests.length;
    }

    /**
     * 获取系统状态摘要
     */
    getSystemStatus() {
        const currentMetrics = this.getCurrentMetrics();
        const activeAlerts = this.alerts.filter(alert => !alert.resolved);
        
        return {
            status: activeAlerts.length > 0 ? 'warning' : 'healthy',
            timestamp: new Date(),
            metrics: currentMetrics,
            alerts: activeAlerts,
            summary: {
                uptime: process.uptime(),
                errorRate: this.getErrorRate(),
                avgResponseTime: this.getAverageResponseTime(),
                activeAlerts: activeAlerts.length
            }
        };
    }

    /**
     * 获取详细统计信息
     */
    getDetailedStats() {
        return {
            metrics: this.metrics,
            alerts: this.alerts,
            thresholds: this.thresholds,
            summary: this.getSystemStatus()
        };
    }

    /**
     * 重置指标数据
     */
    resetMetrics() {
        this.metrics = {
            cpu: [],
            memory: [],
            disk: [],
            requests: [],
            errors: []
        };
        logger.info('系统指标数据已重置');
    }

    /**
     * 解决告警
     */
    resolveAlert(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.resolved = true;
            alert.resolvedAt = new Date();
            logger.info(`告警已解决: ${alert.message}`);
        }
    }
}

// 创建全局监控实例
const systemMonitor = new SystemMonitor();

module.exports = {
    SystemMonitor,
    systemMonitor
};
