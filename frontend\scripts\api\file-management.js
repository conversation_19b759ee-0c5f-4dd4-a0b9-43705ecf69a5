/**
 * 文件管理API调用模块
 * 封装文件管理相关的API请求
 */

import { API_URL, getAuthHeaders } from './config.js';

/**
 * 获取所有客户（包括停用的，用于管理页面）
 * @returns {Promise<Object>} API响应
 */
export async function getCustomers() {
    try {
        const response = await axios.get(`${API_URL}/file-management/customers`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取客户列表失败:', error);
        throw error;
    }
}

/**
 * 获取活跃客户（用于文件上传选择）
 * @returns {Promise<Object>} API响应
 */
export async function getActiveCustomers() {
    try {
        const response = await axios.get(`${API_URL}/file-management/customers/active`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取活跃客户列表失败:', error);
        throw error;
    }
}

/**
 * 创建客户
 * @param {Object} customerData - 客户数据
 * @returns {Promise<Object>} API响应
 */
export async function createCustomer(customerData) {
    try {
        const response = await axios.post(`${API_URL}/file-management/customers`, customerData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('创建客户失败:', error);
        throw error;
    }
}

/**
 * 更新客户信息
 * @param {string} customerId - 客户ID
 * @param {Object} customerData - 客户数据
 * @returns {Promise<Object>} API响应
 */
export async function updateCustomer(customerId, customerData) {
    try {
        const response = await axios.put(`${API_URL}/file-management/customers/${customerId}`, customerData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('更新客户失败:', error);
        throw error;
    }
}

/**
 * 切换客户状态（启用/停用）
 * @param {string} customerId - 客户ID
 * @returns {Promise<Object>} API响应
 */
export async function toggleCustomerStatus(customerId) {
    try {
        const response = await axios.patch(`${API_URL}/file-management/customers/${customerId}/toggle-status`, {}, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('切换客户状态失败:', error);
        throw error;
    }
}

/**
 * 删除客户
 * @param {string} customerId - 客户ID
 * @returns {Promise<Object>} API响应
 */
export async function deleteCustomer(customerId) {
    try {
        const response = await axios.delete(`${API_URL}/file-management/customers/${customerId}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除客户失败:', error);
        throw error;
    }
}

/**
 * 获取历史批次号
 * @param {string} customerId - 客户ID
 * @param {string} productModel - 产品型号
 * @returns {Promise<Object>} API响应
 */
export async function getHistoricalBatches(customerId, productModel) {
    try {
        const response = await axios.get(`${API_URL}/file-management/historical-batches`, {
            params: { customerId, productModel },
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取历史批次失败:', error);
        throw error;
    }
}

/**
 * 获取客户的产品列表
 * @param {string} customerId - 客户ID
 * @returns {Promise<Object>} API响应
 */
export async function getCustomerProducts(customerId) {
    try {
        const response = await axios.get(`${API_URL}/file-management/customers/${customerId}/products`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取客户产品失败:', error);
        throw error;
    }
}

/**
 * 创建文件记录（支持文件上传）
 * @param {FormData} formData - 包含文件数据的FormData对象
 * @returns {Promise<Object>} API响应
 */
export async function createFileRecord(formData) {
    try {
        const response = await axios.post(`${API_URL}/file-management/files`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return response.data;
    } catch (error) {
        console.error('创建文件记录失败:', error);
        throw error;
    }
}

/**
 * 获取所有文件记录
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} API响应
 */
export async function getFileRecords(params = {}) {
    try {
        const response = await axios.get(`${API_URL}/file-management/files`, {
            headers: getAuthHeaders(),
            params
        });
        return response.data;
    } catch (error) {
        console.error('获取文件记录失败:', error);
        throw error;
    }
}

/**
 * 根据ID获取文件记录详情
 * @param {string} id - 文件记录ID
 * @returns {Promise<Object>} API响应
 */
export async function getFileRecordById(id) {
    try {
        const response = await axios.get(`${API_URL}/file-management/files/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取文件记录详情失败:', error);
        throw error;
    }
}

/**
 * 根据ID获取文件记录详情（别名）
 * @param {string} id - 文件记录ID
 * @returns {Promise<Object>} API响应
 */
export const getFileById = getFileRecordById;

/**
 * 删除文件记录
 * @param {string} id - 文件记录ID
 * @returns {Promise<Object>} API响应
 */
export async function deleteFileRecord(id) {
    try {
        const response = await axios.delete(`${API_URL}/file-management/files/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除文件记录失败:', error);
        throw error;
    }
}

/**
 * 搜索文件记录
 * @param {Object} searchParams - 搜索参数
 * @returns {Promise<Object>} API响应
 */
export async function searchFileRecords(searchParams) {
    try {
        const response = await axios.get(`${API_URL}/file-management/files/search`, {
            headers: getAuthHeaders(),
            params: searchParams
        });
        return response.data;
    } catch (error) {
        console.error('搜索文件记录失败:', error);
        throw error;
    }
}

/**
 * 获取文件统计信息
 * @returns {Promise<Object>} API响应
 */
export async function getFileStatistics() {
    try {
        const response = await axios.get(`${API_URL}/file-management/files/statistics`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取文件统计信息失败:', error);
        throw error;
    }
}

/**
 * 下载文件
 * @param {string} fileId - 文件记录ID
 * @param {string} attachmentId - 附件ID
 */
export function downloadFile(fileId, attachmentId) {
    const url = `${API_URL}/file-management/files/${fileId}/attachments/${attachmentId}/download`;
    const token = localStorage.getItem('token');

    // 创建一个临时的a标签来下载文件，并携带认证头
    const link = document.createElement('a');
    link.href = url;
    link.style.display = 'none';

    // 如果有token，通过fetch下载并创建blob URL
    if (token) {
        fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('下载失败');
            }
            return response.blob();
        })
        .then(blob => {
            const blobUrl = window.URL.createObjectURL(blob);
            link.href = blobUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(blobUrl);
        })
        .catch(error => {
            console.error('下载文件失败:', error);
            alert('下载文件失败: ' + error.message);
        });
    } else {
        // 没有token时直接打开链接
        window.open(url, '_blank');
    }
}

/**
 * 获取用户的通知列表
 * @returns {Promise<Object>} API响应
 */
export async function getUserNotifications() {
    try {
        const response = await axios.get(`${API_URL}/file-management/notifications`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取通知列表失败:', error);
        throw error;
    }
}

/**
 * 确认通知
 * @param {string} notificationId - 通知ID
 * @returns {Promise<Object>} API响应
 */
export async function confirmNotification(notificationId) {
    try {
        const response = await axios.post(`${API_URL}/file-management/notifications/${notificationId}/confirm`, {}, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('确认通知失败:', error);
        throw error;
    }
}

/**
 * 获取用户列表（用于选择通知用户）
 * @returns {Promise<Object>} API响应
 */
export async function getFileManagementUsers() {
    try {
        const response = await axios.get(`${API_URL}/file-management/users`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取用户列表失败:', error);
        throw error;
    }
}

/**
 * 文件管理工具函数
 */
export const fileManagementUtils = {
    /**
     * 格式化文件大小
     */
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 格式化日期时间
     */
    formatDateTime: (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    /**
     * 格式化日期
     */
    formatDate: (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    },
    
    /**
     * 获取文件类型图标
     */
    getFileTypeIcon: (filename) => {
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': '📄',
            'doc': '📝',
            'docx': '📝',
            'xls': '📊',
            'xlsx': '📊',
            'jpg': '🖼️',
            'jpeg': '🖼️',
            'png': '🖼️',
            'gif': '🖼️',
            'zip': '📦',
            'rar': '📦',
            'txt': '📃',
            'csv': '📋'
        };
        return iconMap[ext] || '📎';
    },
    
    /**
     * 验证文件类型
     */
    validateFileType: (file) => {
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'text/plain',
            'text/csv'
        ];
        
        const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.gif', '.txt', '.csv'];
        
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        return allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);
    },
    
    /**
     * 验证文件大小
     */
    validateFileSize: (file, maxSizeMB = 10) => {
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        return file.size <= maxSizeBytes;
    },
    
    /**
     * 生成文件预览信息
     */
    generateFilePreview: (file) => {
        return {
            name: file.name,
            size: fileManagementUtils.formatFileSize(file.size),
            type: file.type,
            icon: fileManagementUtils.getFileTypeIcon(file.name),
            isValid: fileManagementUtils.validateFileType(file) && fileManagementUtils.validateFileSize(file)
        };
    }
};

/**
 * 文件管理常量
 */
export const FILE_MANAGEMENT_CONSTANTS = {
    // 文件上传限制
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_FILE_COUNT: 10,
    
    // 支持的文件类型
    SUPPORTED_FILE_TYPES: [
        'PDF文档 (.pdf)',
        'Word文档 (.doc, .docx)',
        'Excel表格 (.xls, .xlsx)',
        '图片文件 (.jpg, .jpeg, .png, .gif)',
        '文本文件 (.txt, .csv)'
    ],
    
    // 通知类型
    NOTIFICATION_TYPES: {
        NEW_FILE: 'new_file',
        FILE_UPDATE: 'file_update'
    },
    
    // 文件状态
    FILE_STATUS: {
        ACTIVE: 'active',
        DELETED: 'deleted'
    }
};
