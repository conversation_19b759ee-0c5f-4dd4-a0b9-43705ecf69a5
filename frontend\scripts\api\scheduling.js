/**
 * 智能排程API调用模块
 * 封装所有智能排程相关的API请求
 */

import { API_URL } from '../config.js';

/**
 * 智能排程API类
 */
class SchedulingAPI {
    /**
     * 创建排程方案
     * @param {Object} orderData 订单数据
     * @param {Object} options 选项参数
     * @returns {Promise<Object>} API响应
     */
    static async createPlans(orderData, options = {}) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...orderData,
                    options
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 获取排程方案
     * @param {string} orderId 订单ID
     * @returns {Promise<Object>} API响应
     */
    static async getPlans(orderId) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 更新排程方案
     * @param {string} orderId 订单ID
     * @param {string} planId 方案ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} API响应
     */
    static async updatePlan(orderId, planId, updateData) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}/${planId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('更新排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 选择排程方案
     * @param {string} orderId 订单ID
     * @param {string} planId 方案ID
     * @returns {Promise<Object>} API响应
     */
    static async selectPlan(orderId, planId) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}/${planId}/select`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('选择排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 重新生成排程方案
     * @param {string} orderId 订单ID
     * @param {Object} newConstraints 新的约束条件
     * @returns {Promise<Object>} API响应
     */
    static async regeneratePlans(orderId, newConstraints = {}) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}/regenerate`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ constraints: newConstraints })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('重新生成排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 获取排程统计信息
     * @param {Object} filters 筛选条件
     * @returns {Promise<Object>} API响应
     */
    static async getStatistics(filters = {}) {
        try {
            const queryString = new URLSearchParams(filters).toString();
            const url = `${API_URL}/scheduling/statistics${queryString ? '?' + queryString : ''}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取排程统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 批量创建排程方案
     * @param {Array} orders 订单列表
     * @param {Object} options 选项参数
     * @returns {Promise<Object>} API响应
     */
    static async createBatchPlans(orders, options = {}) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/batch`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    orders,
                    options
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('批量创建排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 获取排程方案对比
     * @param {string} orderId 订单ID
     * @param {Array} planIds 方案ID列表
     * @returns {Promise<Object>} API响应
     */
    static async comparePlans(orderId, planIds) {
        try {
            const planIdsParam = planIds.join(',');
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}/compare?planIds=${planIdsParam}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取排程方案对比失败:', error);
            throw error;
        }
    }

    /**
     * 导出排程方案
     * @param {string} orderId 订单ID
     * @param {string} format 导出格式 (json, csv)
     * @returns {Promise<Blob>} 文件数据
     */
    static async exportPlans(orderId, format = 'json') {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}/export?format=${format}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.blob();
        } catch (error) {
            console.error('导出排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 获取排程历史记录
     * @param {Object} filters 筛选条件
     * @returns {Promise<Object>} API响应
     */
    static async getHistory(filters = {}) {
        try {
            const queryString = new URLSearchParams(filters).toString();
            const url = `${API_URL}/scheduling/history${queryString ? '?' + queryString : ''}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取排程历史记录失败:', error);
            throw error;
        }
    }

    /**
     * 获取排程性能指标
     * @param {string} orderId 订单ID
     * @returns {Promise<Object>} API响应
     */
    static async getPerformanceMetrics(orderId) {
        try {
            const response = await fetch(`${API_URL}/scheduling/metrics/${orderId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取排程性能指标失败:', error);
            throw error;
        }
    }

    /**
     * 优化排程方案
     * @param {string} orderId 订单ID
     * @param {string} planId 方案ID
     * @param {Object} optimizationParams 优化参数
     * @returns {Promise<Object>} API响应
     */
    static async optimizePlan(orderId, planId, optimizationParams) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}/${planId}/optimize`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(optimizationParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('优化排程方案失败:', error);
            throw error;
        }
    }

    /**
     * 模拟排程执行
     * @param {string} orderId 订单ID
     * @param {string} planId 方案ID
     * @param {Object} simulationParams 模拟参数
     * @returns {Promise<Object>} API响应
     */
    static async simulateExecution(orderId, planId, simulationParams = {}) {
        try {
            const response = await fetch(`${API_URL}/scheduling/plans/${orderId}/${planId}/simulate`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(simulationParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('模拟排程执行失败:', error);
            throw error;
        }
    }
}

export default SchedulingAPI;
