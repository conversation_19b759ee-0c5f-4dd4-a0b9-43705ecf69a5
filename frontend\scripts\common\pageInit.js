/**
 * 页面初始化工具
 * 提供统一的页面初始化模式，减少重复代码
 */

import { initializePage, checkPermissions, checkRoles } from './utils.js';
import { initializeAPI } from '../api/config.js';
import Sidebar from '../../components/common/Sidebar.js';

const { createApp, ref, onMounted } = Vue;

/**
 * 创建标准页面应用
 * 统一的页面应用创建函数，包含通用的认证和初始化逻辑
 * @param {Object} options - 配置选项
 * @param {Object} options.setup - Vue setup函数
 * @param {Array} options.requiredPermissions - 需要的权限
 * @param {Array} options.requiredRoles - 需要的角色
 * @param {Function} options.onUserLoaded - 用户加载完成回调
 * @param {Object} options.components - 额外的组件
 * @returns {Object} Vue应用实例
 */
export function createStandardApp(options = {}) {
    const {
        setup: userSetup,
        requiredPermissions = [],
        requiredRoles = [],
        onUserLoaded = null,
        components = {}
    } = options;

    const app = createApp({
        components: {
            Sidebar,
            ...components
        },
        setup() {
            // 初始化API配置
            initializeAPI();

            // 通用状态
            const currentUser = ref(null);
            const isAuthenticated = ref(false);
            const loading = ref(true);
            // 从localStorage获取侧边栏状态，默认为false（桌面端展开，移动端折叠）
            const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

            // 页面初始化
            onMounted(async () => {
                try {
                    const user = await initializePage(async (user) => {
                        // 权限检查
                        if (requiredPermissions.length > 0) {
                            if (!checkPermissions(user, requiredPermissions)) {
                                return;
                            }
                        }

                        // 角色检查
                        if (requiredRoles.length > 0) {
                            if (!checkRoles(user, requiredRoles)) {
                                return;
                            }
                        }

                        // 设置用户信息
                        currentUser.value = user;
                        isAuthenticated.value = true;

                        // 执行用户自定义的初始化逻辑
                        if (onUserLoaded && typeof onUserLoaded === 'function') {
                            await onUserLoaded(user);
                        }
                    });
                } finally {
                    loading.value = false;
                }
            });

            // 切换侧边栏
            function toggleSidebar() {
                sidebarOpen.value = !sidebarOpen.value;
                // 保存状态到localStorage
                localStorage.setItem('sidebarOpen', sidebarOpen.value.toString());
            }

            // 关闭侧边栏
            function closeSidebar() {
                sidebarOpen.value = false;
                // 保存状态到localStorage
                localStorage.setItem('sidebarOpen', 'false');
            }

            // 执行用户自定义的setup逻辑
            const userSetupResult = userSetup ? userSetup() : {};

            // 合并返回值
            return {
                currentUser,
                isAuthenticated,
                loading,
                sidebarOpen,
                toggleSidebar,
                closeSidebar,
                ...userSetupResult
            };
        }
    });

    return app;
}

/**
 * 创建管理员页面应用
 * 专门用于管理员页面的应用创建函数
 * @param {Object} options - 配置选项
 * @returns {Object} Vue应用实例
 */
export function createAdminApp(options = {}) {
    return createStandardApp({
        ...options,
        requiredRoles: ['admin', 'CEO', '管理员', 'ceo']
    });
}

/**
 * 创建需要审批权限的页面应用
 * 专门用于审批相关页面的应用创建函数
 * @param {Object} options - 配置选项
 * @returns {Object} Vue应用实例
 */
export function createApprovalApp(options = {}) {
    return createStandardApp({
        ...options,
        requiredPermissions: ['pending_approval']
    });
}

/**
 * 创建排程管理页面应用
 * 专门用于排程管理页面的应用创建函数
 * @param {Object} options - 配置选项
 * @returns {Object} Vue应用实例
 */
export function createScheduleApp(options = {}) {
    return createStandardApp({
        ...options,
        requiredPermissions: ['schedule_view']
    });
}

/**
 * 通用表单验证工具
 * 提供常用的表单验证函数
 */
export const validators = {
    /**
     * 验证必填字段
     * @param {any} value - 要验证的值
     * @param {string} fieldName - 字段名称
     * @returns {string} 错误信息，无错误返回空字符串
     */
    required(value, fieldName = '此字段') {
        if (!value || (typeof value === 'string' && !value.trim())) {
            return `${fieldName}不能为空`;
        }
        return '';
    },

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {string} 错误信息，无错误返回空字符串
     */
    email(email) {
        if (!email) return '';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) ? '' : '邮箱格式不正确';
    },

    /**
     * 验证密码强度
     * @param {string} password - 密码
     * @returns {string} 错误信息，无错误返回空字符串
     */
    password(password) {
        if (!password) return '';
        if (password.length < 6) {
            return '密码长度不能少于6位';
        }
        return '';
    },

    /**
     * 验证确认密码
     * @param {string} password - 原密码
     * @param {string} confirmPassword - 确认密码
     * @returns {string} 错误信息，无错误返回空字符串
     */
    confirmPassword(password, confirmPassword) {
        if (!confirmPassword) return '请确认密码';
        if (password !== confirmPassword) {
            return '两次输入的密码不一致';
        }
        return '';
    },

    /**
     * 验证数字范围
     * @param {number} value - 数值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {string} 错误信息，无错误返回空字符串
     */
    numberRange(value, min = 0, max = Infinity) {
        if (isNaN(value)) return '请输入有效数字';
        if (value < min) return `数值不能小于${min}`;
        if (value > max) return `数值不能大于${max}`;
        return '';
    },

    /**
     * 验证日期范围
     * @param {string|Date} startDate - 开始日期
     * @param {string|Date} endDate - 结束日期
     * @returns {string} 错误信息，无错误返回空字符串
     */
    dateRange(startDate, endDate) {
        if (!startDate || !endDate) return '';
        const start = new Date(startDate);
        const end = new Date(endDate);
        if (start >= end) {
            return '结束时间必须晚于开始时间';
        }
        return '';
    }
};

/**
 * 通用分页工具
 * 提供分页相关的工具函数
 */
export const pagination = {
    /**
     * 创建分页状态
     * @param {number} page - 当前页码
     * @param {number} limit - 每页数量
     * @returns {Object} 分页状态对象
     */
    createState(page = 1, limit = 10) {
        return ref({
            page,
            limit,
            total: 0,
            totalPages: 0
        });
    },

    /**
     * 更新分页状态
     * @param {Object} paginationState - 分页状态
     * @param {Object} data - 分页数据
     */
    updateState(paginationState, data) {
        paginationState.value.page = data.page || 1;
        paginationState.value.limit = data.limit || 10;
        paginationState.value.total = data.total || 0;
        paginationState.value.totalPages = Math.ceil(data.total / data.limit) || 0;
    },

    /**
     * 生成页码数组
     * @param {Object} paginationState - 分页状态
     * @param {number} maxVisible - 最大显示页码数
     * @returns {Array} 页码数组
     */
    generatePageNumbers(paginationState, maxVisible = 5) {
        const { page, totalPages } = paginationState.value;
        const pages = [];
        
        if (totalPages <= maxVisible) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            const half = Math.floor(maxVisible / 2);
            let start = Math.max(1, page - half);
            let end = Math.min(totalPages, start + maxVisible - 1);
            
            if (end - start + 1 < maxVisible) {
                start = Math.max(1, end - maxVisible + 1);
            }
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
        }
        
        return pages;
    }
};

/**
 * 通用加载状态管理
 * 提供加载状态的统一管理
 */
export const loadingManager = {
    /**
     * 创建加载状态
     * @returns {Object} 加载状态对象
     */
    createState() {
        return ref(false);
    },

    /**
     * 执行异步操作并管理加载状态
     * @param {Object} loadingState - 加载状态
     * @param {Function} asyncOperation - 异步操作函数
     * @returns {Promise} 异步操作结果
     */
    async execute(loadingState, asyncOperation) {
        loadingState.value = true;
        try {
            return await asyncOperation();
        } finally {
            loadingState.value = false;
        }
    }
};
