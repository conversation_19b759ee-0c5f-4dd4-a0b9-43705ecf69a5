# 申请管理 API 文档

## 📋 概述

申请管理模块提供完整的申请生命周期管理，包括申请创建、审批流程、状态跟踪、电子签名集成等功能。

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 创建申请 | `new_application` |
| 查看申请记录 | `application_record` |
| 审批申请 | `pending_approval` |
| 查看已审批申请 | `approved_applications` |

## 📝 API 接口

### 1. 获取申请列表

**GET** `/api/applications`

获取申请列表，支持分页、搜索和筛选。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |
| search | string | 否 | 搜索关键词 |
| status | string | 否 | 状态筛选 |
| type | string | 否 | 申请类型筛选 |
| startDate | string | 否 | 开始日期 (YYYY-MM-DD) |
| endDate | string | 否 | 结束日期 (YYYY-MM-DD) |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "app123",
      "applicationNumber": "20250729-0001",
      "userId": "user123",
      "applicant": "张三",
      "department": "技术部",
      "date": "2025-07-29",
      "content": "申请购买办公设备",
      "amount": "5000",
      "priority": "normal",
      "type": "standard",
      "status": "pending",
      "currentStage": "factory_manager",
      "needManagerApproval": true,
      "needCeoApproval": true,
      "selectedFactoryManagers": [
        {
          "id": "fm001",
          "username": "厂长A",
          "usercode": "FM001"
        }
      ],
      "selectedManagers": [],
      "pdfPath": "/uploads/applications/app123.pdf",
      "attachments": [
        {
          "filename": "设备清单.xlsx",
          "path": "/uploads/attachments/file123.xlsx",
          "size": 15360
        }
      ],
      "createdAt": "2025-07-29T08:00:00.000Z",
      "updatedAt": "2025-07-29T08:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### 2. 获取申请详情

**GET** `/api/applications/:id`

获取指定申请的详细信息，包括审批历史和电子签名。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 申请ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "app123",
    "applicationNumber": "20250729-0001",
    "userId": "user123",
    "applicant": "张三",
    "department": "技术部",
    "date": "2025-07-29",
    "content": "申请购买办公设备",
    "amount": "5000",
    "priority": "normal",
    "type": "standard",
    "status": "approved",
    "currentStage": "completed",
    "needManagerApproval": true,
    "needCeoApproval": true,
    "selectedFactoryManagers": [...],
    "selectedManagers": [...],
    "pdfPath": "/uploads/applications/app123.pdf",
    "attachments": [...],
    "approvalHistory": [
      {
        "id": "ah001",
        "applicationId": "app123",
        "stage": "factory_manager",
        "approverId": "fm001",
        "approverName": "厂长A",
        "approverRole": "厂长",
        "action": "approve",
        "comment": "同意购买",
        "signaturePath": "/uploads/signatures/fm001_20250729.jpg",
        "timestamp": "2025-07-29T09:00:00.000Z"
      },
      {
        "id": "ah002",
        "applicationId": "app123",
        "stage": "director",
        "approverId": "dir001",
        "approverName": "总监B",
        "approverRole": "总监",
        "action": "approve",
        "comment": "批准",
        "signaturePath": "/uploads/signatures/dir001_20250729.jpg",
        "timestamp": "2025-07-29T10:00:00.000Z"
      }
    ],
    "createdAt": "2025-07-29T08:00:00.000Z",
    "updatedAt": "2025-07-29T10:00:00.000Z"
  }
}
```

### 3. 创建申请

**POST** `/api/applications`

创建新的申请，支持文件上传。

#### 请求格式
- **Content-Type**: `multipart/form-data`
- **文件字段**: `attachments` (支持多文件)

#### 请求参数

```json
{
  "applicant": "张三",
  "department": "技术部",
  "date": "2025-07-29",
  "content": "申请购买办公设备",
  "amount": "5000",
  "priority": "normal",
  "type": "standard",
  "selectedFactoryManagers": ["fm001", "fm002"],
  "selectedManagers": ["mgr001"],
  "needManagerApproval": true,
  "needCeoApproval": true
}
```

#### 响应示例

**成功响应 (201)**:
```json
{
  "success": true,
  "message": "申请创建成功",
  "data": {
    "id": "app124",
    "applicationNumber": "20250729-0002",
    "status": "pending",
    "currentStage": "factory_manager",
    "createdAt": "2025-07-29T11:00:00.000Z"
  }
}
```

### 4. 审批申请

**POST** `/api/applications/:id/approve`

对申请进行审批操作（通过或拒绝）。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 申请ID |

#### 请求参数

```json
{
  "action": "approve",
  "comment": "同意申请",
  "selectedManagers": ["mgr001", "mgr002"]
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| action | string | 是 | 操作类型：approve/reject |
| comment | string | 否 | 审批意见 |
| selectedManagers | array | 否 | 选择的经理（总监审批时） |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "审批成功",
  "data": {
    "id": "app123",
    "status": "pending",
    "currentStage": "director",
    "nextApprovers": [
      {
        "id": "dir001",
        "username": "总监B",
        "role": "总监"
      }
    ]
  }
}
```

### 5. 获取待审核申请

**GET** `/api/applications/pending`

获取当前用户待审核的申请列表。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "app123",
      "applicationNumber": "20250729-0001",
      "applicant": "张三",
      "department": "技术部",
      "content": "申请购买办公设备",
      "amount": "5000",
      "priority": "normal",
      "currentStage": "factory_manager",
      "canApprove": true,
      "createdAt": "2025-07-29T08:00:00.000Z"
    }
  ],
  "count": 5
}
```

### 6. 获取已审核申请

**GET** `/api/applications/approved`

获取已审核的申请列表。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "app122",
      "applicationNumber": "20250728-0001",
      "applicant": "李四",
      "department": "销售部",
      "content": "申请差旅费用",
      "amount": "3000",
      "status": "approved",
      "finalApprovalDate": "2025-07-28T15:30:00.000Z",
      "createdAt": "2025-07-28T09:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

## 📊 申请状态说明

| 状态 | 描述 | 可执行操作 |
|------|------|------------|
| pending | 待审批 | 审批、撤回 |
| approved | 已通过 | 查看、下载 |
| rejected | 已拒绝 | 查看、重新申请 |
| cancelled | 已取消 | 查看 |

## 🔄 审批流程

### 标准申请流程
1. **厂长审批** → 2. **总监审批** → 3. **经理审批**（可选） → 4. **CEO审批** → 5. **完成**

### 其他申请流程  
1. **总监审批**（可选） → 2. **CEO审批** → 3. **完成**

### 7. 删除申请

**DELETE** `/api/applications/:id`

删除指定申请（仅申请人或管理员可操作）。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 申请ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "申请删除成功"
}
```

### 8. 下载申请附件

**GET** `/api/applications/:id/attachments/:filename`

下载申请的附件文件。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 申请ID |
| filename | string | 是 | 文件名 |

#### 响应
返回文件流，浏览器会自动下载文件。

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| APPLICATION_NOT_FOUND | 404 | 申请不存在 |
| PERMISSION_DENIED | 403 | 权限不足 |
| INVALID_STAGE | 400 | 当前阶段不允许此操作 |
| APPROVAL_CONFLICT | 409 | 审批冲突 |
| FILE_UPLOAD_ERROR | 400 | 文件上传失败 |

---

**更新时间**: 2025-07-29
**版本**: v1.0
