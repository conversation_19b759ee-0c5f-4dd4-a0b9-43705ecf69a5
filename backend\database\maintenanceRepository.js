/**
 * 维修保养记录数据访问层
 * 处理维修保养记录相关的数据库操作
 */

const databaseManager = require('./database');
const { MaintenanceModel } = require('../models/maintenanceModel');
const logger = require('../utils/logger');

class MaintenanceRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        this.initStatements();
        logger.info('维修保养记录数据访问层初始化完成');
    }

    /**
     * 初始化预编译语句
     */
    initStatements() {
        this.statements = {
            // 基本CRUD操作
            findAll: this.db.prepare(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                ORDER BY m.created_at DESC
            `),
            findById: this.db.prepare(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE m.id = ?
            `),
            insert: this.db.prepare(`
                INSERT INTO equipment_maintenance (
                    id, equipment_id, type, severity_level, description, maintenance_date,
                    start_time, end_time, cost, technician, status, notes, result, reviewer, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            update: this.db.prepare(`
                UPDATE equipment_maintenance SET
                    equipment_id = ?, type = ?, severity_level = ?, description = ?, maintenance_date = ?,
                    start_time = ?, end_time = ?, cost = ?, technician = ?, status = ?, notes = ?,
                    result = ?, reviewer = ?, updated_at = ?
                WHERE id = ?
            `),
            delete: this.db.prepare('DELETE FROM equipment_maintenance WHERE id = ?'),
            
            // 统计查询
            countAll: this.db.prepare('SELECT COUNT(*) as count FROM equipment_maintenance'),
            getStatistics: this.db.prepare(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN type = 'maintenance' THEN 1 END) as maintenance_count,
                    COUNT(CASE WHEN type = 'repair' THEN 1 END) as repair_count,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    AVG(cost) as avg_cost,
                    SUM(cost) as total_cost
                FROM equipment_maintenance
            `)
        };
    }

    /**
     * 获取所有维修记录
     * @returns {Array} 维修记录列表
     */
    findAll() {
        try {
            const records = this.statements.findAll.all();
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error('获取维修记录列表失败:', error);
            throw error;
        }
    }

    /**
     * 分页查询维修记录
     * @param {Object} options 查询选项
     * @returns {Object} 分页结果
     */
    findAllWithPagination(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                search = '',
                equipmentId = '',
                type = '',
                status = '',
                technician = '',
                area = '',
                startDate = '',
                endDate = ''
            } = options;

            // 构建查询条件
            let whereClause = 'WHERE 1=1';
            const params = [];

            if (search) {
                whereClause += ` AND (m.id LIKE ? OR m.description LIKE ? OR e.code LIKE ? OR e.name LIKE ? OR m.technician LIKE ?)`;
                const searchParam = `%${search}%`;
                params.push(searchParam, searchParam, searchParam, searchParam, searchParam);
            }

            if (equipmentId) {
                whereClause += ` AND m.equipment_id = ?`;
                params.push(equipmentId);
            }

            if (type) {
                whereClause += ` AND m.type = ?`;
                params.push(type);
            }

            if (status) {
                whereClause += ` AND m.status = ?`;
                params.push(status);
            }

            if (technician) {
                whereClause += ` AND m.technician LIKE ?`;
                params.push(`%${technician}%`);
            }

            if (area) {
                whereClause += ` AND e.area = ?`;
                params.push(area);
            }

            if (startDate) {
                whereClause += ` AND m.maintenance_date >= ?`;
                params.push(startDate);
            }

            if (endDate) {
                whereClause += ` AND m.maintenance_date <= ?`;
                params.push(endDate);
            }

            // 计算总数
            const countQuery = `
                SELECT COUNT(*) as count
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                ${whereClause}
            `;
            const countStmt = this.db.prepare(countQuery);
            const { count: total } = countStmt.get(...params);

            // 分页查询
            const offset = (page - 1) * limit;
            const dataQuery = `
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                ${whereClause}
                ORDER BY m.created_at DESC
                LIMIT ? OFFSET ?
            `;
            const dataStmt = this.db.prepare(dataQuery);
            const records = dataStmt.all(...params, limit, offset);

            return {
                data: records.map(record => this.enrichRecord(record)),
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            logger.error('分页查询维修记录失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取维修记录
     * @param {string} id 记录ID
     * @returns {Object|null} 维修记录
     */
    findById(id) {
        try {
            const record = this.statements.findById.get(id);
            return record ? this.enrichRecord(record) : null;
        } catch (error) {
            logger.error('根据ID获取维修记录失败:', error);
            throw error;
        }
    }

    /**
     * 根据日期获取维修记录
     * @param {string} date 日期 (YYYY-MM-DD)
     * @returns {Array} 维修记录列表
     */
    findByDate(date) {
        try {
            const sql = `
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE DATE(m.maintenance_date) = ?
                ORDER BY m.created_at DESC
            `;
            const records = this.db.prepare(sql).all(date);
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error('根据日期获取维修记录失败:', error);
            throw error;
        }
    }

    /**
     * 创建维修记录
     * @param {MaintenanceModel} maintenance 维修记录模型
     * @returns {MaintenanceModel} 创建的维修记录
     */
    create(maintenance) {
        try {
            const dbData = maintenance.toDatabase();
            
            this.statements.insert.run(
                dbData.id, dbData.equipment_id, dbData.type, dbData.severity_level, dbData.description,
                dbData.maintenance_date, dbData.start_time, dbData.end_time, dbData.cost, dbData.technician,
                dbData.status, dbData.notes, dbData.result, dbData.reviewer, dbData.created_at, dbData.updated_at
            );

            logger.info('维修记录创建成功', { maintenanceId: maintenance.id });
            return this.findById(maintenance.id);
        } catch (error) {
            logger.error('创建维修记录失败', { error: error.message, maintenanceId: maintenance.id });
            throw error;
        }
    }

    /**
     * 更新维修记录
     * @param {string} id 记录ID
     * @param {MaintenanceModel} maintenance 维修记录模型
     * @returns {MaintenanceModel} 更新后的维修记录
     */
    update(id, maintenance) {
        try {
            const dbData = maintenance.toDatabase();
            dbData.updated_at = new Date().toISOString();

            this.statements.update.run(
                dbData.equipment_id, dbData.type, dbData.severity_level, dbData.description,
                dbData.maintenance_date, dbData.start_time, dbData.end_time, dbData.cost, dbData.technician,
                dbData.status, dbData.notes, dbData.result, dbData.reviewer, dbData.updated_at, id
            );

            logger.info('维修记录更新成功', { maintenanceId: id });
            return this.findById(id);
        } catch (error) {
            logger.error('更新维修记录失败', { error: error.message, maintenanceId: id });
            throw error;
        }
    }

    /**
     * 删除维修记录
     * @param {string} id 记录ID
     * @returns {boolean} 删除结果
     */
    delete(id) {
        try {
            const result = this.statements.delete.run(id);
            const success = result.changes > 0;
            
            if (success) {
                logger.info('维修记录删除成功', { maintenanceId: id });
            } else {
                logger.warn('维修记录删除失败，记录不存在', { maintenanceId: id });
            }
            
            return success;
        } catch (error) {
            logger.error('删除维修记录失败', { error: error.message, maintenanceId: id });
            throw error;
        }
    }

    /**
     * 获取统计数据
     * @returns {Object} 统计数据
     */
    getStatistics() {
        try {
            const stats = this.statements.getStatistics.get();
            return {
                total: stats.total || 0,
                maintenanceCount: stats.maintenance_count || 0,
                repairCount: stats.repair_count || 0,
                pendingCount: stats.pending_count || 0,
                inProgressCount: stats.in_progress_count || 0,
                completedCount: stats.completed_count || 0,
                avgCost: stats.avg_cost || 0,
                totalCost: stats.total_cost || 0
            };
        } catch (error) {
            logger.error('获取维修记录统计数据失败:', error);
            throw error;
        }
    }

    /**
     * 丰富记录数据（添加设备信息）
     * @param {Object} record 原始记录
     * @returns {Object} 丰富后的记录
     */
    enrichRecord(record) {
        return {
            ...record,
            equipment: {
                id: record.equipment_id,
                code: record.equipment_code,
                name: record.equipment_name,
                area: record.equipment_area
            }
        };
    }
}

module.exports = MaintenanceRepository;
