/**
 * 并发管理器
 * 管理智能排程算法的并发执行，防止资源竞争和过载
 */

const logger = require('./logger');

/**
 * 并发管理器类
 * 控制并发任务的执行
 */
class ConcurrencyManager {
    constructor() {
        // 并发配置
        this.config = {
            maxConcurrentTasks: 5,      // 最大并发任务数
            queueMaxSize: 100,          // 队列最大大小
            taskTimeout: 60000,         // 任务超时时间（60秒）
            retryAttempts: 3,           // 重试次数
            retryDelay: 1000           // 重试延迟（毫秒）
        };

        // 任务队列和状态
        this.runningTasks = new Map();
        this.taskQueue = [];
        this.taskCounter = 0;

        // 统计信息
        this.stats = {
            totalTasks: 0,
            completedTasks: 0,
            failedTasks: 0,
            queuedTasks: 0,
            runningTasks: 0,
            averageExecutionTime: 0,
            totalExecutionTime: 0
        };

        logger.info('并发管理器初始化完成', {
            maxConcurrentTasks: this.config.maxConcurrentTasks,
            queueMaxSize: this.config.queueMaxSize
        });
    }

    /**
     * 执行任务
     * @param {Function} taskFunction 任务函数
     * @param {Object} taskData 任务数据
     * @param {Object} options 选项
     * @returns {Promise<any>} 任务结果
     */
    async executeTask(taskFunction, taskData, options = {}) {
        const taskId = this.generateTaskId();
        const task = {
            id: taskId,
            function: taskFunction,
            data: taskData,
            options: {
                priority: options.priority || 'normal',
                timeout: options.timeout || this.config.taskTimeout,
                retryAttempts: options.retryAttempts || this.config.retryAttempts,
                ...options
            },
            createdAt: Date.now(),
            attempts: 0
        };

        return new Promise((resolve, reject) => {
            task.resolve = resolve;
            task.reject = reject;

            this.addTaskToQueue(task);
            this.processQueue();
        });
    }

    /**
     * 添加任务到队列
     * @param {Object} task 任务对象
     */
    addTaskToQueue(task) {
        // 检查队列大小限制
        if (this.taskQueue.length >= this.config.queueMaxSize) {
            task.reject(new Error('任务队列已满'));
            return;
        }

        // 根据优先级插入任务
        const insertIndex = this.findInsertPosition(task);
        this.taskQueue.splice(insertIndex, 0, task);

        this.stats.totalTasks++;
        this.stats.queuedTasks++;

        logger.debug('任务添加到队列', {
            taskId: task.id,
            priority: task.options.priority,
            queueSize: this.taskQueue.length
        });
    }

    /**
     * 查找插入位置（按优先级排序）
     * @param {Object} task 任务对象
     * @returns {number} 插入位置
     */
    findInsertPosition(task) {
        const priorityOrder = { 'high': 0, 'normal': 1, 'low': 2 };
        const taskPriority = priorityOrder[task.options.priority] || 1;

        for (let i = 0; i < this.taskQueue.length; i++) {
            const queuedTaskPriority = priorityOrder[this.taskQueue[i].options.priority] || 1;
            if (taskPriority < queuedTaskPriority) {
                return i;
            }
        }

        return this.taskQueue.length;
    }

    /**
     * 处理任务队列
     */
    async processQueue() {
        // 检查是否可以执行更多任务
        while (this.runningTasks.size < this.config.maxConcurrentTasks && this.taskQueue.length > 0) {
            const task = this.taskQueue.shift();
            this.stats.queuedTasks--;
            
            this.executeTaskNow(task);
        }
    }

    /**
     * 立即执行任务
     * @param {Object} task 任务对象
     */
    async executeTaskNow(task) {
        this.runningTasks.set(task.id, task);
        this.stats.runningTasks++;
        task.startTime = Date.now();

        logger.debug('开始执行任务', {
            taskId: task.id,
            runningTasks: this.runningTasks.size
        });

        try {
            // 设置超时
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`任务超时: ${task.options.timeout}ms`));
                }, task.options.timeout);
            });

            // 执行任务
            const taskPromise = task.function(task.data);
            const result = await Promise.race([taskPromise, timeoutPromise]);

            // 任务成功完成
            this.completeTask(task, result, true);

        } catch (error) {
            // 任务失败，检查是否需要重试
            task.attempts++;
            
            if (task.attempts < task.options.retryAttempts) {
                logger.warn('任务执行失败，准备重试', {
                    taskId: task.id,
                    attempt: task.attempts,
                    maxAttempts: task.options.retryAttempts,
                    error: error.message
                });

                // 延迟后重试
                setTimeout(() => {
                    this.retryTask(task);
                }, this.config.retryDelay * task.attempts);
            } else {
                // 重试次数用完，任务失败
                this.completeTask(task, null, false, error);
            }
        }
    }

    /**
     * 重试任务
     * @param {Object} task 任务对象
     */
    async retryTask(task) {
        // 从运行任务中移除
        this.runningTasks.delete(task.id);
        this.stats.runningTasks--;

        // 重新执行
        this.executeTaskNow(task);
    }

    /**
     * 完成任务
     * @param {Object} task 任务对象
     * @param {any} result 任务结果
     * @param {boolean} success 是否成功
     * @param {Error} error 错误对象
     */
    completeTask(task, result, success, error = null) {
        const executionTime = Date.now() - task.startTime;

        // 从运行任务中移除
        this.runningTasks.delete(task.id);
        this.stats.runningTasks--;

        // 更新统计信息
        if (success) {
            this.stats.completedTasks++;
            task.resolve(result);
        } else {
            this.stats.failedTasks++;
            task.reject(error);
        }

        this.stats.totalExecutionTime += executionTime;
        this.stats.averageExecutionTime = this.stats.totalExecutionTime / 
            (this.stats.completedTasks + this.stats.failedTasks);

        logger.debug('任务完成', {
            taskId: task.id,
            success,
            executionTime: `${executionTime}ms`,
            attempts: task.attempts,
            runningTasks: this.runningTasks.size
        });

        // 继续处理队列
        this.processQueue();
    }

    /**
     * 生成任务ID
     * @returns {string} 任务ID
     */
    generateTaskId() {
        return `task_${++this.taskCounter}_${Date.now()}`;
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            queueSize: this.taskQueue.length,
            runningTasksCount: this.runningTasks.size,
            successRate: this.stats.totalTasks > 0 ? 
                this.stats.completedTasks / this.stats.totalTasks : 0,
            failureRate: this.stats.totalTasks > 0 ? 
                this.stats.failedTasks / this.stats.totalTasks : 0
        };
    }

    /**
     * 获取队列状态
     * @returns {Object} 队列状态
     */
    getQueueStatus() {
        return {
            queueSize: this.taskQueue.length,
            runningTasks: this.runningTasks.size,
            maxConcurrentTasks: this.config.maxConcurrentTasks,
            queuedTasks: this.taskQueue.map(task => ({
                id: task.id,
                priority: task.options.priority,
                createdAt: task.createdAt,
                waitTime: Date.now() - task.createdAt
            })),
            runningTasksList: Array.from(this.runningTasks.values()).map(task => ({
                id: task.id,
                priority: task.options.priority,
                startTime: task.startTime,
                runningTime: Date.now() - task.startTime,
                attempts: task.attempts
            }))
        };
    }

    /**
     * 取消任务
     * @param {string} taskId 任务ID
     * @returns {boolean} 是否成功取消
     */
    cancelTask(taskId) {
        // 检查队列中的任务
        const queueIndex = this.taskQueue.findIndex(task => task.id === taskId);
        if (queueIndex !== -1) {
            const task = this.taskQueue.splice(queueIndex, 1)[0];
            this.stats.queuedTasks--;
            task.reject(new Error('任务已取消'));
            
            logger.info('队列中的任务已取消', { taskId });
            return true;
        }

        // 检查运行中的任务
        const runningTask = this.runningTasks.get(taskId);
        if (runningTask) {
            this.runningTasks.delete(taskId);
            this.stats.runningTasks--;
            runningTask.reject(new Error('任务已取消'));
            
            logger.info('运行中的任务已取消', { taskId });
            this.processQueue(); // 继续处理队列
            return true;
        }

        return false;
    }

    /**
     * 清空队列
     */
    clearQueue() {
        const canceledCount = this.taskQueue.length;
        
        // 取消所有队列中的任务
        this.taskQueue.forEach(task => {
            task.reject(new Error('队列已清空'));
        });
        
        this.taskQueue = [];
        this.stats.queuedTasks = 0;
        
        logger.info('任务队列已清空', { canceledCount });
    }

    /**
     * 设置并发限制
     * @param {number} maxConcurrentTasks 最大并发任务数
     */
    setMaxConcurrentTasks(maxConcurrentTasks) {
        const oldLimit = this.config.maxConcurrentTasks;
        this.config.maxConcurrentTasks = maxConcurrentTasks;
        
        logger.info('并发限制已更新', {
            oldLimit,
            newLimit: maxConcurrentTasks
        });

        // 如果增加了并发限制，尝试处理更多任务
        if (maxConcurrentTasks > oldLimit) {
            this.processQueue();
        }
    }

    /**
     * 批量执行任务
     * @param {Array} tasks 任务列表
     * @param {Object} options 选项
     * @returns {Promise<Array>} 任务结果列表
     */
    async executeBatch(tasks, options = {}) {
        const batchId = `batch_${Date.now()}`;
        
        logger.info('开始批量执行任务', {
            batchId,
            taskCount: tasks.length
        });

        const promises = tasks.map((task, index) => {
            return this.executeTask(task.function, task.data, {
                ...task.options,
                batchId,
                batchIndex: index
            });
        });

        try {
            const results = await Promise.allSettled(promises);
            
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            
            logger.info('批量任务执行完成', {
                batchId,
                total: tasks.length,
                successful,
                failed
            });

            return results;
        } catch (error) {
            logger.error('批量任务执行失败', {
                batchId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 优雅关闭
     * @param {number} timeout 超时时间
     * @returns {Promise<void>}
     */
    async shutdown(timeout = 30000) {
        logger.info('开始优雅关闭并发管理器', {
            runningTasks: this.runningTasks.size,
            queuedTasks: this.taskQueue.length
        });

        // 停止接受新任务
        this.clearQueue();

        // 等待运行中的任务完成
        const startTime = Date.now();
        while (this.runningTasks.size > 0 && (Date.now() - startTime) < timeout) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // 强制取消剩余任务
        if (this.runningTasks.size > 0) {
            logger.warn('强制取消剩余任务', {
                remainingTasks: this.runningTasks.size
            });
            
            for (const task of this.runningTasks.values()) {
                task.reject(new Error('系统关闭'));
            }
            this.runningTasks.clear();
        }

        logger.info('并发管理器已关闭');
    }
}

// 创建全局并发管理器实例
const concurrencyManager = new ConcurrencyManager();

module.exports = concurrencyManager;
