/**
 * 部门管理API
 * 处理部门相关的API请求
 */

import { API_URL, getAuthHeaders } from './config.js';

/**
 * 获取部门列表
 * @returns {Promise<Object>} 部门列表
 */
export async function getDepartments() {
    try {
        const response = await axios.get(`${API_URL}/departments`, {
            headers: getAuthHeaders(),
            params: {
                _t: Date.now() // 添加时间戳防止缓存
            }
        });
        return response.data;
    } catch (error) {
        console.error('获取部门列表失败:', error);
        throw error;
    }
}

/**
 * 根据ID获取部门
 * @param {string} id - 部门ID
 * @returns {Promise<Object>} 部门信息
 */
export async function getDepartmentById(id) {
    try {
        const response = await axios.get(`${API_URL}/departments/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取部门信息失败:', error);
        throw error;
    }
}

/**
 * 创建新部门
 * @param {Object} departmentData - 部门数据
 * @returns {Promise<Object>} 创建结果
 */
export async function createDepartment(departmentData) {
    try {
        const response = await axios.post(`${API_URL}/departments`, departmentData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('创建部门失败:', error);
        throw error;
    }
}

/**
 * 更新部门
 * @param {string} id - 部门ID
 * @param {Object} departmentData - 部门数据
 * @returns {Promise<Object>} 更新结果
 */
export async function updateDepartment(id, departmentData) {
    try {
        const response = await axios.put(`${API_URL}/departments/${id}`, departmentData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('更新部门失败:', error);
        throw error;
    }
}

/**
 * 删除部门
 * @param {string} id - 部门ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteDepartment(id) {
    try {
        const response = await axios.delete(`${API_URL}/departments/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除部门失败:', error);
        throw error;
    }
}

/**
 * 检查部门名称是否存在
 * @param {string} name - 部门名称
 * @param {string} excludeId - 排除的部门ID（用于编辑时检查）
 * @returns {Promise<Object>} 检查结果
 */
export async function checkDepartmentName(name, excludeId = '') {
    try {
        const response = await axios.get(`${API_URL}/departments/check-name`, {
            params: { name, excludeId },
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('检查部门名称失败:', error);
        throw error;
    }
}

/**
 * 检查部门是否有用户关联
 * @param {string} id - 部门ID
 * @returns {Promise<Object>} 检查结果
 */
export async function checkDepartmentUsers(id) {
    try {
        const response = await axios.get(`${API_URL}/departments/${id}/check-users`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('检查部门用户关联失败:', error);
        throw error;
    }
}


