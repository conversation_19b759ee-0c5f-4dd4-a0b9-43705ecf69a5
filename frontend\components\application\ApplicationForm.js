/**
 * 申请表单组件
 * 处理申请表单的显示和提交
 */

import { PRIORITIES } from '../../scripts/config.js';
import { createApplication, getApplicationById } from '../../scripts/api/application.js';
import { getFactoryManagers } from '../../scripts/api/user.js';
import { getDepartments } from '../../scripts/api/department.js';
import ApplicationTemplate from './ApplicationTemplate.js';
import ConfirmDialog from '../common/ConfirmDialog.js';
import ErrorHandler from '../../scripts/utils/errorHandler.js';

export default {
    components: {
        ApplicationTemplate,
        ConfirmDialog
    },
    props: {
        user: Object,
        applicationType: {
            type: String,
            default: 'standard' // 默认为标准申请
        }
    },
    emits: ['submitted'],
    setup(props, { emit }) {
        const { ref, reactive, onMounted, computed, watch } = Vue;

        // 申请书模板状态
        const showApplicationTemplate = ref(false);
        const submittedApplication = ref(null);

        // 确认对话框状态
        const showConfirmDialog = ref(false);
        const confirmDialogConfig = ref({
            title: '请注意',
            message: '',
            confirmText: '确认',
            cancelText: '返回填写'
        });

        // 文件上传相关
        const fileInput = ref(null);
        const selectedFiles = ref([]);
        const dragOver = ref(false);

        // 表单区域展开状态
        const expandedSections = reactive({
            basic: true,         // 基本信息默认展开
            content: true,       // 申请内容默认展开
            factoryManager: true, // 厂长选择默认展开（仅标准申请）
            attachments: true    // 附件上传默认展开
        });

        // 表单数据
        const formData = reactive({
            applicant: '',
            department: '',
            date: new Date().toISOString().split('T')[0],
            content: '',
            amount: '', // 申请金额
            priority: 'normal',
            type: props.applicationType,
            needDirectorApproval: true, // 默认需要总监审批（仅对其他申请有效）
            selectedFactoryManagers: [] // 选择的厂长用户（仅标准申请有效）
        });

        // 表单验证状态
        const formErrors = reactive({
            applicant: '',
            department: '',
            date: '',
            content: '',
            amount: '',
            selectedFactoryManagers: ''
        });

        // 加载状态
        const isSubmitting = ref(false);

        // 是否为其他申请
        const isOtherApplication = computed(() => {
            return props.applicationType === 'other';
        });

        // 是否为标准申请
        const isStandardApplication = computed(() => {
            return props.applicationType === 'standard';
        });

        // 常用金额选项
        const commonAmounts = [1000, 5000, 10000];

        // 厂长用户列表
        const factoryManagers = ref([]);
        const isLoadingFactoryManagers = ref(false);

        // 部门列表
        const departments = ref([]);

        // 监听申请类型变化
        watch(() => props.applicationType, (newType) => {
            formData.type = newType;
        });

        // 监听用户信息变化，自动填充申请人和部门信息
        watch(() => props.user, (newUser) => {
            if (newUser) {
                formData.applicant = newUser.name || newUser.username || '';
                formData.department = newUser.department || '';
            }
        }, { immediate: true });

        // 加载部门列表
        async function loadDepartments() {
            try {
                const response = await getDepartments();
                departments.value = response.departments || [];
            } catch (error) {
                console.error('加载部门列表失败:', error);
                // 如果加载失败，使用默认部门列表
                departments.value = [
                    { name: '生产部' },
                    { name: '工程部' },
                    { name: '品管部' },
                    { name: '机电部' },
                    { name: '业务部' },
                    { name: '财务部' },
                    { name: '人事部' },
                    { name: '管理部' },
                    { name: '研发部' },
                    { name: '采购部' },
                    { name: '仓储部' }
                ];
            }
        }

        // 获取厂长用户列表
        async function loadFactoryManagers() {
            if (!isStandardApplication.value) return;

            try {
                isLoadingFactoryManagers.value = true;
                const response = await getFactoryManagers();
                if (response.success) {
                    factoryManagers.value = response.factoryManagers;
                } else {
                    console.error('获取厂长用户列表失败:', response.message);
                    alert('获取厂长用户列表失败: ' + response.message);
                }
            } catch (error) {
                ErrorHandler.handleAPIError(error, '获取厂长用户列表');
            } finally {
                isLoadingFactoryManagers.value = false;
            }
        }

        // 初始化
        onMounted(async () => {
            // 加载部门列表
            await loadDepartments();

            // 如果用户信息已经存在，立即填充
            if (props.user) {
                formData.applicant = props.user.name || props.user.username || '';
                formData.department = props.user.department || '';
            }
            formData.type = props.applicationType;

            // 如果是标准申请，加载厂长用户列表
            if (isStandardApplication.value) {
                await loadFactoryManagers();
            }
        });

        // 处理文件选择
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            processFiles(files);
        }

        // 处理文件拖放
        function handleFileDrop(event) {
            dragOver.value = false;
            const files = Array.from(event.dataTransfer.files);
            processFiles(files);
        }

        // 处理文件
        function processFiles(files) {
            const validFiles = files.filter(file =>
                file.size <= 5 * 1024 * 1024 &&
                /\.(pdf|docx?|jpg|png)$/i.test(file.name)
            );

            // 处理无效文件
            const invalidFiles = files.filter(file => !validFiles.includes(file));
            if(invalidFiles.length > 0) {
                alert(`以下文件不符合要求：\n${invalidFiles.map(f => f.name).join('\n')}`);
            }

            // 更新选中的文件
            selectedFiles.value = [...selectedFiles.value, ...validFiles];
        }

        // 移除文件
        function removeFile(index) {
            selectedFiles.value.splice(index, 1);
        }

        // 切换厂长选择
        function toggleFactoryManager(manager) {
            const index = formData.selectedFactoryManagers.findIndex(m => m.id === manager.id);
            if (index > -1) {
                // 如果已选择，则移除
                formData.selectedFactoryManagers.splice(index, 1);
            } else {
                // 如果未选择，则添加
                formData.selectedFactoryManagers.push(manager);
            }
        }

        // 检查厂长是否已选择
        function isFactoryManagerSelected(manager) {
            return formData.selectedFactoryManagers.some(m => m.id === manager.id);
        }

        // 验证整个表单
        function validateForm() {
            let isValid = true;

            // 重置错误
            Object.keys(formErrors).forEach(key => {
                formErrors[key] = '';
            });

            // 验证基本信息
            if (!formData.applicant.trim()) {
                formErrors.applicant = '请填写申请人姓名';
                isValid = false;
            }

            if (!formData.department) {
                formErrors.department = '请选择申请部门';
                isValid = false;
            }

            if (!formData.date) {
                formErrors.date = '请选择申请日期';
                isValid = false;
            }

            // 验证申请内容
            if (!formData.content.trim()) {
                formErrors.content = '请填写申请内容';
                isValid = false;
            }

            if (formData.amount && isNaN(parseFloat(formData.amount))) {
                formErrors.amount = '申请金额必须是有效数字';
                isValid = false;
            }

            // 验证厂长选择（仅标准申请）
            if (isStandardApplication.value && formData.selectedFactoryManagers.length === 0) {
                formErrors.selectedFactoryManagers = '请至少选择一个厂长进行审批';
                isValid = false;
            }

            return isValid;
        }

        // 滚动到第一个错误字段
        function scrollToFirstError() {
            // 定义错误字段的优先级顺序和对应的选择器
            const errorFieldsOrder = [
                {
                    field: 'applicant',
                    selector: 'input[placeholder*="申请人"]',
                    section: 'basic'
                },
                {
                    field: 'department',
                    selector: 'input[placeholder*="部门"]',
                    section: 'basic'
                },
                {
                    field: 'date',
                    selector: 'input[type="date"]',
                    section: 'basic'
                },
                {
                    field: 'content',
                    selector: 'textarea[placeholder*="申请内容"]',
                    section: 'content'
                },
                {
                    field: 'amount',
                    selector: 'input[type="number"]',
                    section: 'content'
                },
                {
                    field: 'selectedFactoryManagers',
                    selector: '.factory-manager-section',
                    section: 'factoryManager'
                }
            ];

            // 查找第一个有错误的字段
            for (const errorField of errorFieldsOrder) {
                if (formErrors[errorField.field]) {
                    // 确保对应的区域是展开的
                    if (errorField.section && !expandedSections[errorField.section]) {
                        expandedSections[errorField.section] = true;
                    }

                    // 延迟滚动，确保区域展开动画完成
                    setTimeout(() => {
                        let targetElement = null;

                        if (errorField.field === 'selectedFactoryManagers') {
                            // 对于厂长选择，滚动到厂长选择区域的标题
                            targetElement = document.querySelector('.factory-manager-section') ||
                                          document.querySelector('h4:contains("厂长选择")') ||
                                          document.querySelector('.text-red-500');
                        } else {
                            // 对于其他字段，尝试找到具体的输入元素
                            targetElement = document.querySelector(errorField.selector);
                        }

                        if (targetElement) {
                            targetElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center',
                                inline: 'nearest'
                            });

                            // 如果是输入字段，尝试聚焦
                            if (targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA') {
                                targetElement.focus();
                            }
                        }
                    }, 300); // 等待区域展开动画

                    break; // 只处理第一个错误
                }
            }
        }

        // 切换区域展开状态
        function toggleSection(section) {
            expandedSections[section] = !expandedSections[section];
        }

        // 设置常用金额
        function setAmount(amount) {
            formData.amount = amount;
        }



        // 提交申请
        async function submitApplication() {
            // 表单验证
            if (!validateForm()) {
                // 精确定位到错误字段并滚动
                scrollToFirstError();
                return;
            }

            // 检查是否填写了金额
            const amountValue = parseFloat(formData.amount);
            if (!formData.amount || formData.amount.toString().trim() === '' || isNaN(amountValue) || amountValue === 0) {
                showAmountConfirmDialog();
                return;
            }

            // 直接执行提交
            await doSubmit();
        }

        // 显示金额确认对话框
        function showAmountConfirmDialog() {
            confirmDialogConfig.value = {
                title: '请注意',
                message: '您未填写申请/采购金额，点击<span style="color: #3b82f6; font-weight: 600;">确认</span>则直接提交申请，<br/>点击<span style="color: #6b7280; font-weight: 600;">返回填写</span>完成金额填写',
                confirmText: '确认',
                cancelText: '返回填写'
            };
            showConfirmDialog.value = true;
        }

        // 处理确认对话框的确认操作
        async function handleConfirmSubmit() {
            showConfirmDialog.value = false;
            console.log('用户确认提交，开始提交申请...', formData);
            await doSubmit();
        }

        // 处理确认对话框的取消操作
        function handleCancelSubmit() {
            showConfirmDialog.value = false;
            // 聚焦到金额输入框
            setTimeout(() => {
                const amountInput = document.querySelector('input[type="number"]');
                if (amountInput) {
                    amountInput.focus();
                }
            }, 100);
        }

        // 执行实际的提交操作
        async function doSubmit() {
            try {
                isSubmitting.value = true;
                console.log('开始提交申请...', formData);

                const result = await createApplication(formData, selectedFiles.value);
                console.log('申请提交结果:', result);

                if (result.success) {
                    alert('申请提交成功！');

                    // 更新侧边栏待审核数量
                    if (window.updateSidebarPendingCount) {
                        window.updateSidebarPendingCount();
                    }

                    // 获取提交的申请详情并显示申请书模板
                    try {
                        const applicationDetail = await getApplicationById(result.id);
                        submittedApplication.value = applicationDetail;
                        showApplicationTemplate.value = true;
                    } catch (error) {
                        ErrorHandler.handleAPIError(error, '获取申请详情', { showToast: false });
                        // 即使获取详情失败，也不影响提交成功的流程
                    }

                    resetForm();
                    emit('submitted', result.id);
                } else {
                    console.error('申请提交失败:', result);
                    alert('申请提交失败: ' + result.message);
                }
            } catch (error) {
                ErrorHandler.handleAPIError(error, '提交申请');
            } finally {
                console.log('提交流程结束，重置提交状态');
                isSubmitting.value = false;
            }
        }

        // 重置表单
        function resetForm() {
            Object.assign(formData, {
                applicant: props.user?.name || props.user?.username || '',
                department: props.user?.department || '',
                date: new Date().toISOString().split('T')[0],
                content: '',
                amount: '', // 重置申请金额
                priority: 'normal',
                type: props.applicationType,
                needDirectorApproval: true,
                selectedFactoryManagers: [] // 重置厂长选择
            });
            selectedFiles.value = [];
        }



        // 预览申请
        function previewApplication() {
            // 验证必填字段
            if (!validateForm()) {
                // 精确定位到错误字段并滚动
                scrollToFirstError();
                return;
            }

            // 创建预览用的申请对象
            const previewApplication = {
                id: 'preview-' + Date.now(),
                applicationNumber: 'PREVIEW-' + new Date().toISOString().slice(0, 10),
                applicant: formData.applicant,
                department: formData.department,
                date: formData.date,
                content: formData.content,
                amount: formData.amount,
                priority: formData.priority,
                type: formData.type,
                status: 'pending',
                approvals: {
                    directors: {},
                    chief: null,
                    managers: {},
                    ceo: null
                }
            };

            // 显示申请书模板预览
            submittedApplication.value = previewApplication;
            showApplicationTemplate.value = true;
        }

        // 关闭申请书模板
        function closeApplicationTemplate() {
            showApplicationTemplate.value = false;
            submittedApplication.value = null;
        }

        return {
            formData,
            formErrors,
            fileInput,
            selectedFiles,
            dragOver,
            isSubmitting,
            isOtherApplication,
            isStandardApplication,
            expandedSections,
            commonAmounts,
            departments,
            priorities: PRIORITIES,
            factoryManagers,
            isLoadingFactoryManagers,
            showApplicationTemplate,
            submittedApplication,
            showConfirmDialog,
            confirmDialogConfig,
            handleFileSelect,
            handleFileDrop,
            removeFile,
            toggleSection,
            setAmount,
            toggleFactoryManager,
            isFactoryManagerSelected,
            submitApplication,
            handleConfirmSubmit,
            handleCancelSubmit,
            closeApplicationTemplate,
            previewApplication
        };
    },
    template: `
        <section class="space-y-6">
            <!-- 表单标题 -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800">申请信息</h3>
            </div>

            <form @submit.prevent="submitApplication" class="space-y-6">
                <!-- 基本信息区域 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('basic')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            基本信息
                        </h4>
                        <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                     expandedSections.basic ? 'rotate-180' : '']"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    <div v-show="expandedSections.basic" class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">
                                    申请人姓名 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" v-model="formData.applicant" readonly
                                       class="w-full p-3 border border-gray-200 rounded-md bg-gray-50 text-gray-700 cursor-not-allowed">
                                <p v-if="formErrors.applicant" class="mt-1 text-sm text-red-500">{{ formErrors.applicant }}</p>
                            </div>

                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">
                                    申请部门 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" v-model="formData.department" readonly
                                       class="w-full p-3 border border-gray-200 rounded-md bg-gray-50 text-gray-700 cursor-not-allowed">
                                <p v-if="formErrors.department" class="mt-1 text-sm text-red-500">{{ formErrors.department }}</p>
                            </div>

                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">
                                    申请日期 <span class="text-red-500">*</span>
                                </label>
                                <input type="date" v-model="formData.date"
                                       class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700">
                                <p v-if="formErrors.date" class="mt-1 text-sm text-red-500">{{ formErrors.date }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 申请内容区域 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('content')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            申请内容
                        </h4>
                        <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                     expandedSections.content ? 'rotate-180' : '']"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    <div v-show="expandedSections.content" class="p-6 space-y-6">
                        <div>
                            <label class="block text-gray-700 mb-2 font-medium">
                                申请内容 <span class="text-red-500">*</span>
                            </label>
                            <textarea v-model="formData.content" rows="6" placeholder="请详细描述您的申请内容..."
                                   class="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 resize-vertical"></textarea>
                            <p v-if="formErrors.content" class="mt-1 text-sm text-red-500">{{ formErrors.content }}</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">申请金额 (元)</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 text-sm">¥</span>
                                    </div>
                                    <input type="number" v-model="formData.amount" min="0" step="0.01" placeholder="0.00"
                                           class="pl-8 w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700">
                                </div>
                                <div class="mt-3 flex flex-wrap gap-2">
                                    <button type="button" v-for="amount in commonAmounts" :key="amount"
                                            @click="setAmount(amount)"
                                            class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                        ¥{{ amount.toLocaleString() }}
                                    </button>
                                </div>
                                <p v-if="formErrors.amount" class="mt-1 text-sm text-red-500">{{ formErrors.amount }}</p>
                                <p class="text-xs text-gray-500 mt-1">请输入申请金额，精确到小数点后两位</p>
                            </div>

                            <div>
                                <label class="block text-gray-700 mb-2 font-medium">紧急程度</label>
                                <div class="grid grid-cols-1 gap-2">
                                    <label v-for="(priority, key) in priorities" :key="key"
                                           :class="['flex items-center p-3 rounded-md border cursor-pointer transition-colors',
                                                   formData.priority === key ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:bg-gray-50']">
                                        <input type="radio" v-model="formData.priority" :value="key" class="mr-3 text-blue-600">
                                        <div>
                                            <div class="font-medium">{{ priority.label }}</div>
                                            <div class="text-sm text-gray-500">{{ priority.description || '' }}</div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 其他申请特有选项 -->
                        <div v-if="isOtherApplication" class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <label class="block text-gray-700 mb-3 font-medium flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                审批流程选项
                            </label>
                            <div class="flex items-center">
                                <input type="checkbox" v-model="formData.needDirectorApproval" id="needDirectorApproval"
                                       class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="needDirectorApproval" class="ml-3 text-gray-700">需要总监审批</label>
                            </div>
                            <p class="text-sm text-gray-600 mt-2">如不勾选，申请将直接提交给CEO审批</p>
                        </div>
                    </div>
                </div>

                <!-- 厂长选择区域（仅标准申请） -->
                <div v-if="isStandardApplication" class="factory-manager-section bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('factoryManager')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            厂长选择
                            <span class="text-red-500 ml-1">*</span>
                        </h4>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">已选择 {{ formData.selectedFactoryManagers.length }} 人</span>
                            <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                         expandedSections.factoryManager ? 'rotate-180' : '']"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div v-show="expandedSections.factoryManager" class="p-6">
                        <div class="mb-4">
                            <p class="text-sm text-gray-600 mb-4">
                                请选择一个或多个厂长进行审批。标准申请需要厂长审批后才能进入下一审批环节。
                            </p>
                        </div>

                        <!-- 加载状态 -->
                        <div v-if="isLoadingFactoryManagers" class="flex items-center justify-center py-8">
                            <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="ml-2 text-gray-600">正在加载厂长列表...</span>
                        </div>

                        <!-- 厂长列表 -->
                        <div v-else-if="factoryManagers.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div v-for="manager in factoryManagers" :key="manager.id"
                                 @click="toggleFactoryManager(manager)"
                                 :class="['p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                                         isFactoryManagerSelected(manager)
                                         ? 'border-blue-500 bg-blue-50 shadow-md'
                                         : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50']">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div :class="['w-10 h-10 rounded-full flex items-center justify-center text-white font-medium',
                                                     isFactoryManagerSelected(manager) ? 'bg-blue-600' : 'bg-gray-400']">
                                            {{ manager.name.charAt(0) }}
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">{{ manager.name }}</p>
                                        <p class="text-xs text-gray-500 truncate">{{ manager.department || '厂长' }}</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <svg v-if="isFactoryManagerSelected(manager)"
                                             class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg v-else class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 无厂长用户提示 -->
                        <div v-else class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <p class="text-gray-500">暂无可选择的厂长用户</p>
                            <p class="text-sm text-gray-400 mt-1">请联系管理员添加厂长角色用户</p>
                        </div>

                        <!-- 已选择的厂长列表 -->
                        <div v-if="formData.selectedFactoryManagers.length > 0" class="mt-6 pt-4 border-t border-gray-200">
                            <h5 class="text-sm font-medium text-gray-700 mb-3">已选择的厂长 ({{ formData.selectedFactoryManagers.length }})</h5>
                            <div class="flex flex-wrap gap-2">
                                <div v-for="manager in formData.selectedFactoryManagers" :key="manager.id"
                                     class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                                    <span>{{ manager.name }}</span>
                                    <button @click="toggleFactoryManager(manager)" type="button"
                                            class="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-600 hover:bg-blue-200 hover:text-blue-800">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 验证错误提示 -->
                        <p v-if="formErrors.selectedFactoryManagers" class="mt-3 text-sm text-red-500">
                            {{ formErrors.selectedFactoryManagers }}
                        </p>
                    </div>
                </div>

                <!-- 附件上传区域 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
                         @click="toggleSection('attachments')">
                        <h4 class="text-lg font-medium text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            附件上传
                            <span class="ml-2 text-sm text-gray-500">(可选)</span>
                        </h4>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">{{ selectedFiles.length }}个文件</span>
                            <svg :class="['w-5 h-5 text-gray-400 transition-transform duration-200',
                                         expandedSections.attachments ? 'rotate-180' : '']"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div v-show="expandedSections.attachments" class="p-6">
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 transition-all hover:border-blue-400 hover:bg-blue-50">
                            <input type="file" ref="fileInput" @change="handleFileSelect" multiple class="hidden" accept=".pdf,.doc,.docx,.jpg,.png">
                            <div class="text-center cursor-pointer" @click="fileInput.click()"
                                 @dragover.prevent="dragOver = true"
                                 @dragleave.prevent="dragOver = false"
                                 @drop.prevent="handleFileDrop"
                                 :class="{'bg-blue-50 border-blue-400': dragOver}">
                                <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="text-lg text-gray-600 mb-2">拖放文件到此处或点击上传</p>
                                <p class="text-sm text-gray-500">支持 PDF、Word、JPG、PNG 格式，单个文件最大 5MB</p>
                            </div>
                        </div>

                        <div v-if="selectedFiles.length > 0" class="mt-6">
                            <h5 class="text-sm font-medium text-gray-700 mb-3">已选择的文件 ({{ selectedFiles.length }})</h5>
                            <div class="space-y-3">
                                <div v-for="(file, index) in selectedFiles" :key="index"
                                     class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ file.name }}</p>
                                            <p class="text-xs text-gray-500">{{ (file.size/1024/1024).toFixed(2) }}MB</p>
                                        </div>
                                    </div>
                                    <button @click="removeFile(index)" type="button"
                                            class="p-2 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮区域 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">

                        <div class="flex flex-col sm:flex-row gap-3 justify-start">
                            <!-- 预览申请按钮 -->
                            <button type="button" @click="previewApplication" :disabled="isSubmitting"
                                    class="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center font-medium">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                预览申请
                            </button>

                            <!-- 提交申请按钮 -->
                            <button type="submit" :disabled="isSubmitting"
                                    class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center font-medium">
                                <span v-if="isSubmitting" class="mr-2">
                                    <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"></path>
                                </svg>
                                {{ isSubmitting ? '提交中...' : '提交申请' }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </section>

        <!-- 申请书模板组件 -->
        <application-template
            :application="submittedApplication"
            :visible="showApplicationTemplate"
            @close="closeApplicationTemplate">
        </application-template>

        <!-- 确认对话框组件 -->
        <confirm-dialog
            :visible="showConfirmDialog"
            :title="confirmDialogConfig.title"
            :message="confirmDialogConfig.message"
            :confirm-text="confirmDialogConfig.confirmText"
            :cancel-text="confirmDialogConfig.cancelText"
            @confirm="handleConfirmSubmit"
            @cancel="handleCancelSubmit"
            @close="handleCancelSubmit">
        </confirm-dialog>
    `
};
