/**
 * 导出服务
 * 处理各种格式的数据导出功能
 */

const ExcelJS = require('exceljs');
const logger = require('../utils/logger');

class ExportService {
    constructor() {
        // 基于实际测量数据的精确转换系数
        this.MEASURED_POINTS_PER_CM = 43.33; // 行高转换系数
        this.MEASURED_CHARS_PER_CM = 5.17;   // 列宽转换系数
    }

    /**
     * 计算文本在指定列宽下需要的行数
     * @param {string} text 文本内容
     * @param {number} columnWidthCm 列宽（厘米）
     * @returns {number} 需要的行数
     */
    calculateTextLines(text, columnWidthCm) {
        if (!text || text.toString().trim() === '') return 1;

        const textStr = text.toString();
        const charsPerLine = Math.floor(columnWidthCm * this.MEASURED_CHARS_PER_CM);

        // 按换行符分割文本
        const paragraphs = textStr.split(/\r?\n/);
        let totalLines = 0;

        paragraphs.forEach(paragraph => {
            if (paragraph.trim() === '') {
                totalLines += 1; // 空行也占一行
            } else {
                // 计算每个段落需要的行数
                let charCount = 0;
                for (let i = 0; i < paragraph.length; i++) {
                    const char = paragraph[i];
                    // 中文字符占2个字符宽度，英文字符占1个字符宽度
                    const charWidth = /[\u4e00-\u9fff]/.test(char) ? 2 : 1;
                    charCount += charWidth;
                }
                const linesNeeded = Math.ceil(charCount / charsPerLine);
                totalLines += Math.max(1, linesNeeded); // 至少1行
            }
        });

        return Math.max(1, totalLines); // 至少返回1行
    }

    /**
     * 格式化日期时间（不显示秒）
     * @param {string} timeString 时间字符串（如"10:34"）
     * @param {string} dateString 日期字符串（如"2025-01-07"）
     * @returns {string} 格式化后的日期时间
     */
    formatDateTimeNoSeconds(timeString, dateString) {
        if (!timeString) return '';
        try {
            console.log('formatDateTimeNoSeconds 输入:', { timeString, dateString });

            // 如果只有时间没有日期，使用当前日期
            let fullDateTimeString;
            if (dateString) {
                fullDateTimeString = `${dateString} ${timeString}`;
            } else {
                // 使用当前日期
                const today = new Date().toISOString().split('T')[0];
                fullDateTimeString = `${today} ${timeString}`;
            }

            console.log('组合的日期时间字符串:', fullDateTimeString);

            const date = new Date(fullDateTimeString);
            console.log('创建的Date对象:', date);

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                console.log('无效日期，返回原始值:', timeString);
                return timeString;
            }

            // 手动格式化为 YYYY/M/D H:MM 格式（不使用前导零）
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // 月份从0开始，需要+1
            const day = date.getDate();
            const hour = date.getHours();
            const minute = date.getMinutes().toString().padStart(2, '0'); // 分钟保持两位数

            const result = `${year}/${month}/${day} ${hour}:${minute}`;
            console.log('格式化结果:', result);
            return result;
        } catch (error) {
            console.log('格式化错误:', error);
            return timeString;
        }
    }

    /**
     * 导出维修保养记录到Excel
     * @param {Array} records 维修记录数组
     * @returns {Buffer} Excel文件Buffer
     */
    async exportMaintenanceRecordsToExcel(records = []) {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('维修及保养记录', {
                pageSetup: {
                    paperSize: 9, // A4
                    orientation: 'landscape', // 横向
                    margins: {
                        left: 0.7, right: 0.7,
                        top: 0.75, bottom: 0.75,
                        header: 0.3, footer: 0.3
                    }
                }
            });

            // 设置列定义和宽度（按照详解文档的精确要求）
            const headers = [
                { header: '开始时间', key: 'startTime', width: Math.round(3.5 * this.MEASURED_CHARS_PER_CM * 10) / 10 },        // 3.5cm
                { header: '结束时间', key: 'endTime', width: Math.round(3.5 * this.MEASURED_CHARS_PER_CM * 10) / 10 },          // 3.5cm
                { header: '机台名称', key: 'deviceName', width: Math.round(6.5 * this.MEASURED_CHARS_PER_CM * 10) / 10 },       // 6.5cm
                { header: '保养/维修', key: 'type', width: Math.round(1.73 * this.MEASURED_CHARS_PER_CM * 10) / 10 },           // 1.73cm
                { header: '维修/保养记录', key: 'description', width: Math.round(6.5 * this.MEASURED_CHARS_PER_CM * 10) / 10 },   // 6.5cm
                { header: '机修人员', key: 'operator', width: Math.round(1.59 * this.MEASURED_CHARS_PER_CM * 10) / 10 },        // 1.59cm
                { header: '审查', key: 'reviewer', width: Math.round(0.92 * this.MEASURED_CHARS_PER_CM * 10) / 10 }             // 0.92cm
            ];

            // 手动设置列宽，不使用columns属性（避免自动生成第一行标题）
            headers.forEach((header, index) => {
                worksheet.getColumn(index + 1).width = header.width;
            });

            // 第1行：在右上方添加版本号"SO4-09406 R:1.0"
            const versionCell = worksheet.getCell('G1');
            versionCell.value = 'SO4-09406 R:1.0';
            versionCell.font = {
                bold: false,
                size: 10,
                name: '宋体'
            };
            versionCell.alignment = {
                horizontal: 'right',
                vertical: 'top'
            };
            // 设置第1行高度
            worksheet.getRow(1).height = Math.round(0.46 * this.MEASURED_POINTS_PER_CM * 10) / 10;

            // 第2行：添加主标题"维修及保养记录"
            worksheet.mergeCells('A2:G2');
            const titleCell = worksheet.getCell('A2');
            titleCell.value = '维修及保养记录';
            titleCell.font = {
                bold: true,
                size: 18,
                name: '宋体'
            };
            titleCell.alignment = {
                horizontal: 'center',
                vertical: 'middle'
            };
            worksheet.getRow(2).height = Math.round(0.79 * this.MEASURED_POINTS_PER_CM * 10) / 10;

            // 第3行：添加列标题
            const headerRow = worksheet.getRow(3);
            headers.forEach((header, index) => {
                const cell = headerRow.getCell(index + 1);
                cell.value = header.header;
                cell.font = { bold: true, name: '宋体', size: 10 }; // 设置为10号字体
                cell.alignment = {
                    horizontal: 'center',
                    vertical: 'middle',
                    wrapText: false // 列标题不自动换行
                };
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFE6F3FF' }
                };
            });
            // 设置列标题行高为0.46cm
            headerRow.height = Math.round(0.46 * this.MEASURED_POINTS_PER_CM * 10) / 10;

            // 第4行开始：添加数据行
            let currentRow = 4;
            records.forEach((record, index) => {
                const row = worksheet.getRow(currentRow);

                // 设置单元格值（时间只显示时和分，不显示秒）
                console.log('完整记录数据:', {
                    startTime: record.startTime,
                    endTime: record.endTime,
                    maintenanceDate: record.maintenanceDate,
                    type: typeof record.startTime
                });

                const formattedStartTime = record.startTime ? this.formatDateTimeNoSeconds(record.startTime, record.maintenanceDate) : '-';
                const formattedEndTime = record.endTime ? this.formatDateTimeNoSeconds(record.endTime, record.maintenanceDate) : '-';

                console.log('格式化后时间:', {
                    formattedStartTime,
                    formattedEndTime
                });

                row.getCell(1).value = formattedStartTime;
                row.getCell(2).value = formattedEndTime;

                // 处理设备名称显示（从equipment对象中获取）
                let deviceDisplayName = '';
                if (record.equipment) {
                    const equipment = record.equipment;
                    if (equipment.code && equipment.name) {
                        deviceDisplayName = `${equipment.code}(${equipment.name})`;
                    } else if (equipment.code) {
                        deviceDisplayName = equipment.code;
                    } else if (equipment.name) {
                        deviceDisplayName = equipment.name;
                    }
                }
                row.getCell(3).value = deviceDisplayName;

                // 转换type为中文
                const typeText = record.type === 'maintenance' ? '保养' : record.type === 'repair' ? '维修' : record.type || '';
                row.getCell(4).value = typeText;

                row.getCell(5).value = record.description || '';
                row.getCell(6).value = record.technician || '';
                row.getCell(7).value = record.reviewer || '';

                // 设置每个单元格的样式
                for (let col = 1; col <= 7; col++) {
                    const cell = row.getCell(col);
                    cell.font = { name: '宋体', size: 10 }; // 设置为10号字体
                    cell.alignment = {
                        horizontal: col === 3 || col === 5 ? 'left' : 'center', // 机台名称和维修记录左对齐，其他居中
                        vertical: 'middle',
                        wrapText: col === 3 || col === 5 // 只有机台名称(第3列)和维修/保养记录(第5列)自动换行
                    };
                }

                // 动态计算行高以适应换行内容
                const deviceNameLines = this.calculateTextLines(deviceDisplayName, 6.5); // 机台名称列宽6.5cm
                const descriptionLines = this.calculateTextLines(record.description || '', 6.5); // 维修/保养记录列宽6.5cm
                const maxLines = Math.max(1, deviceNameLines, descriptionLines); // 至少1行

                // 基础行高0.46cm，每增加一行增加0.46cm
                const calculatedHeight = Math.round(0.46 * maxLines * this.MEASURED_POINTS_PER_CM * 10) / 10;
                row.height = calculatedHeight;

                currentRow++;
            });

            // 设置边框（从第3行开始，跳过版本号行和标题行）
            for (let rowNum = 3; rowNum <= currentRow - 1; rowNum++) {
                const row = worksheet.getRow(rowNum);
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            }

            // 生成Excel文件Buffer
            const buffer = await workbook.xlsx.writeBuffer();
            logger.info('维修保养记录Excel导出成功', { recordCount: records.length });
            return buffer;

        } catch (error) {
            logger.error('导出维修保养记录Excel失败:', error);
            throw error;
        }
    }
}

module.exports = ExportService;
