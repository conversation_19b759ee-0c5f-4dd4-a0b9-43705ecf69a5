/**
 * 用户数据访问层
 * 使用SQLite数据库
 */

const databaseManager = require('./database');
const logger = require('../utils/logger');

class UserRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        this.initStatements();
    }

    /**
     * 初始化预编译SQL语句
     */
    initStatements() {
        this.statements = {
            findAll: this.db.prepare('SELECT * FROM users WHERE active = 1 ORDER BY created_at DESC'),
            findById: this.db.prepare('SELECT * FROM users WHERE id = ?'),
            findByUsercode: this.db.prepare('SELECT * FROM users WHERE usercode = ?'),
            findByUsername: this.db.prepare('SELECT * FROM users WHERE username = ?'),
            insert: this.db.prepare(`
                INSERT INTO users (
                    id, usercode, username, password, role, department, email,
                    active, permissions, has_signature, signature_path, signature_base64, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            update: this.db.prepare(`
                UPDATE users SET
                    usercode = ?, username = ?, password = ?, role = ?, department = ?,
                    email = ?, active = ?, permissions = ?, has_signature = ?, signature_path = ?, signature_base64 = ?, updated_at = ?
                WHERE id = ?
            `),
            updateLastLogin: this.db.prepare(`
                UPDATE users SET last_login_at = ?, last_active_at = ?, updated_at = ?
                WHERE id = ?
            `),
            delete: this.db.prepare('DELETE FROM users WHERE id = ?'),
            deactivate: this.db.prepare('UPDATE users SET active = 0, updated_at = ? WHERE id = ?'),
            checkUsercodeExists: this.db.prepare('SELECT COUNT(*) as count FROM users WHERE usercode = ? AND id != ?')
        };
    }

    /**
     * 获取所有用户
     */
    findAll() {
        try {
            const users = this.statements.findAll.all();
            return users.map(user => this.transformUser(user));
        } catch (error) {
            logger.error('获取所有用户失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找用户
     */
    findById(id) {
        try {
            const user = this.statements.findById.get(id);
            return user ? this.transformUser(user) : null;
        } catch (error) {
            logger.error(`根据ID查找用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据用户代码查找用户
     */
    findByUsercode(usercode) {
        try {
            const user = this.statements.findByUsercode.get(usercode);
            return user ? this.transformUser(user) : null;
        } catch (error) {
            logger.error(`根据用户代码查找用户失败 (${usercode}):`, error);
            throw error;
        }
    }

    /**
     * 根据用户名查找用户
     */
    findByUsername(username) {
        try {
            const user = this.statements.findByUsername.get(username);
            return user ? this.transformUser(user) : null;
        } catch (error) {
            logger.error(`根据用户名查找用户失败 (${username}):`, error);
            throw error;
        }
    }

    /**
     * 创建新用户
     */
    create(userData) {
        try {
            const now = new Date().toISOString();
            
            this.statements.insert.run(
                userData.id,
                userData.usercode,
                userData.username,
                userData.password,
                userData.role,
                userData.department || '',
                userData.email || '',
                userData.active !== false ? 1 : 0,
                JSON.stringify(userData.permissions || []),
                userData.hasSignature ? 1 : 0,
                userData.signaturePath || null,
                userData.signatureBase64 || null,
                now,
                now
            );

            return this.findById(userData.id);
        } catch (error) {
            logger.error('创建用户失败:', error);
            throw error;
        }
    }

    /**
     * 更新用户
     */
    update(id, userData) {
        try {
            const now = new Date().toISOString();
            
            this.statements.update.run(
                userData.usercode,
                userData.username,
                userData.password,
                userData.role,
                userData.department || '',
                userData.email || '',
                userData.active !== false ? 1 : 0,
                JSON.stringify(userData.permissions || []),
                userData.hasSignature ? 1 : 0,
                userData.signaturePath || null,
                userData.signatureBase64 || null,
                now,
                id
            );

            return this.findById(id);
        } catch (error) {
            logger.error(`更新用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 更新用户最后登录时间
     */
    updateLastLogin(id) {
        try {
            const now = new Date().toISOString();
            this.statements.updateLastLogin.run(now, now, now, id);
        } catch (error) {
            logger.error(`更新用户最后登录时间失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除用户（处理外键约束）
     */
    delete(id) {
        try {
            // 开始事务
            const transaction = this.db.transaction(() => {
                // 1. 删除审批历史记录中的关联
                this.db.prepare('DELETE FROM approval_history WHERE approver_id = ?').run(id);

                // 2. 更新申请表中的用户关联为NULL（保留申请记录但移除用户关联）
                this.db.prepare('UPDATE applications SET user_id = NULL WHERE user_id = ?').run(id);

                // 3. 删除操作员技能记录（如果表存在）
                try {
                    this.db.prepare('DELETE FROM operator_skills WHERE operator_id = ?').run(id);
                } catch (e) {
                    // 表不存在时忽略错误
                    if (!e.message.includes('no such table')) throw e;
                }

                // 4. 删除设备操作员关联（如果表存在）
                try {
                    this.db.prepare('DELETE FROM equipment_operators WHERE operator_id = ?').run(id);
                } catch (e) {
                    // 表不存在时忽略错误
                    if (!e.message.includes('no such table')) throw e;
                }

                // 5. 更新质量报告的上传者为NULL（保留报告但移除用户关联）
                try {
                    this.db.prepare('UPDATE quality_reports SET uploaded_by = NULL WHERE uploaded_by = ?').run(id);
                } catch (e) {
                    // 表不存在时忽略错误
                    if (!e.message.includes('no such table')) throw e;
                }

                // 6. 最后删除用户
                const result = this.statements.delete.run(id);
                return result.changes > 0;
            });

            return transaction();
        } catch (error) {
            logger.error(`删除用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 停用用户
     */
    deactivate(id) {
        try {
            const now = new Date().toISOString();
            const result = this.statements.deactivate.run(now, id);
            return result.changes > 0;
        } catch (error) {
            logger.error(`停用用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 检查用户代码是否存在
     */
    isUsercodeExists(usercode, excludeId = '') {
        try {
            const result = this.statements.checkUsercodeExists.get(usercode, excludeId);
            return result.count > 0;
        } catch (error) {
            logger.error(`检查用户代码是否存在失败 (${usercode}):`, error);
            throw error;
        }
    }

    /**
     * 转换数据库用户对象为应用层对象
     */
    transformUser(dbUser) {
        if (!dbUser) return null;

        return {
            id: dbUser.id,
            usercode: dbUser.usercode,
            username: dbUser.username,
            password: dbUser.password,
            role: dbUser.role,
            department: dbUser.department,
            email: dbUser.email,
            active: dbUser.active === 1,
            permissions: JSON.parse(dbUser.permissions || '[]'),
            hasSignature: dbUser.has_signature === 1,
            signaturePath: dbUser.signature_path,
            signatureBase64: dbUser.signature_base64,
            lastLoginAt: dbUser.last_login_at,
            lastActiveAt: dbUser.last_active_at,
            createdAt: dbUser.created_at,
            updatedAt: dbUser.updated_at
        };
    }

    /**
     * 生成10位纯数字唯一ID
     */
    generateId() {
        const min = 1000000000;
        const max = 9999999999;

        let newId;
        do {
            newId = Math.floor(Math.random() * (max - min + 1)) + min;
            newId = newId.toString();
        } while (this.findById(newId));

        return newId;
    }
}

module.exports = new UserRepository();
