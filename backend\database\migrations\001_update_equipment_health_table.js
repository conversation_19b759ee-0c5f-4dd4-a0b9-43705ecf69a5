/**
 * 数据库迁移脚本：更新设备健康度表结构
 * 将旧的equipment_health表结构更新为新的四维度评估体系
 */

const logger = require('../../utils/logger');

class EquipmentHealthTableMigration {
    constructor(database) {
        this.db = database;
    }

    /**
     * 执行迁移
     */
    async migrate() {
        try {
            logger.info('开始执行设备健康度表结构迁移...');

            // 检查当前表结构
            const tableInfo = this.db.prepare("PRAGMA table_info(equipment_health)").all();
            const columnNames = tableInfo.map(col => col.name);

            logger.info('当前equipment_health表字段:', columnNames);

            // 检查是否需要迁移
            if (columnNames.includes('total_score')) {
                logger.info('表结构已是最新版本，无需迁移');
                return;
            }

            // 开始迁移事务
            const migration = this.db.transaction(() => {
                // 1. 重命名旧表
                this.db.exec('ALTER TABLE equipment_health RENAME TO equipment_health_old');
                logger.info('已重命名旧表为 equipment_health_old');

                // 2. 创建新表结构
                this.db.exec(`
                    CREATE TABLE equipment_health (
                        id TEXT PRIMARY KEY,
                        equipment_id TEXT NOT NULL,
                        total_score INTEGER NOT NULL,
                        health_level TEXT NOT NULL,
                        age_score INTEGER NOT NULL,
                        repair_frequency_score INTEGER NOT NULL,
                        fault_severity_score INTEGER NOT NULL,
                        maintenance_score INTEGER NOT NULL,
                        assessment_date TEXT NOT NULL,
                        assessor TEXT NOT NULL,
                        calculation_details TEXT DEFAULT '{}',
                        recommendations TEXT DEFAULT '[]',
                        next_maintenance_date TEXT,
                        failure_probability REAL DEFAULT 0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (equipment_id) REFERENCES equipment (id)
                    )
                `);
                logger.info('已创建新的equipment_health表结构');

                // 3. 迁移数据（如果旧表有数据）
                const oldData = this.db.prepare('SELECT * FROM equipment_health_old').all();
                if (oldData.length > 0) {
                    logger.info(`开始迁移 ${oldData.length} 条历史数据...`);
                    
                    const insertStmt = this.db.prepare(`
                        INSERT INTO equipment_health (
                            id, equipment_id, total_score, health_level,
                            age_score, repair_frequency_score, fault_severity_score, maintenance_score,
                            assessment_date, assessor, calculation_details, recommendations,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `);

                    oldData.forEach(row => {
                        // 将旧数据映射到新结构
                        const newId = `MIGRATED_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                        const totalScore = row.health_score || 80; // 使用旧的health_score作为总分
                        const healthLevel = this.getHealthLevel(totalScore);
                        
                        insertStmt.run(
                            newId,
                            row.equipment_id,
                            totalScore,
                            healthLevel,
                            row.performance_score || 80,    // 映射到年龄评分
                            row.reliability_score || 80,    // 映射到维修频率评分
                            row.efficiency_score || 80,     // 映射到故障严重程度评分
                            80,                              // 保养情况评分（默认值）
                            row.assessment_date,
                            row.assessor,
                            '{}',                            // 计算详情（空对象）
                            '[]',                            // 建议（空数组）
                            row.created_at,
                            row.updated_at
                        );
                    });
                    
                    logger.info('历史数据迁移完成');
                }

                // 4. 创建健康度历史记录表
                this.db.exec(`
                    CREATE TABLE IF NOT EXISTS equipment_health_history (
                        id TEXT PRIMARY KEY,
                        equipment_id TEXT NOT NULL,
                        total_score INTEGER NOT NULL,
                        health_level TEXT NOT NULL,
                        age_score INTEGER NOT NULL,
                        repair_frequency_score INTEGER NOT NULL,
                        fault_severity_score INTEGER NOT NULL,
                        maintenance_score INTEGER NOT NULL,
                        assessment_date TEXT NOT NULL,
                        assessor TEXT NOT NULL,
                        calculation_details TEXT DEFAULT '{}',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (equipment_id) REFERENCES equipment (id)
                    )
                `);
                logger.info('已创建equipment_health_history表');

                // 5. 创建索引
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_equipment_health_equipment_id ON equipment_health (equipment_id)');
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_equipment_health_assessment_date ON equipment_health (assessment_date)');
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_equipment_health_total_score ON equipment_health (total_score)');
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_equipment_health_level ON equipment_health (health_level)');
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_equipment_health_history_equipment_id ON equipment_health_history (equipment_id)');
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_equipment_health_history_assessment_date ON equipment_health_history (assessment_date)');
                logger.info('已创建相关索引');

                // 6. 删除旧表
                this.db.exec('DROP TABLE equipment_health_old');
                logger.info('已删除旧表 equipment_health_old');
            });

            // 执行迁移
            migration();
            
            logger.info('设备健康度表结构迁移完成！');
            
        } catch (error) {
            logger.error('设备健康度表结构迁移失败:', error);
            throw error;
        }
    }

    /**
     * 根据分数获取健康度等级
     */
    getHealthLevel(score) {
        if (score >= 90) return '优秀';
        if (score >= 80) return '良好';
        if (score >= 70) return '一般';
        if (score >= 60) return '较差';
        return '危险';
    }
}

module.exports = EquipmentHealthTableMigration;
