/**
 * 质量检测报告上传页面
 * 处理检测报告的上传功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';
import QualityReportForm from '../../../components/quality/QualityReportForm.js';
import { createQualityReport } from '../../api/quality.js';

createStandardApp({
    components: {
        Sidebar,
        QualityReportForm
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        // 从localStorage获取侧边栏状态，默认为false（桌面端展开，移动端折叠）
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);
        const isSubmitting = ref(false);

        // 处理表单提交
        async function handleSubmit(formData) {
            try {
                isSubmitting.value = true;

                const response = await createQualityReport(formData);

                if (response.success) {
                    if (window.showNotification) {
                        window.showNotification('检测报告上传成功！', 'success');
                    } else {
                        alert('检测报告上传成功！');
                    }

                    // 立即跳转到列表页面，添加时间戳强制刷新
                    const timestamp = Date.now();
                    window.location.href = `/quality-list?refresh=${timestamp}`;
                } else {
                    throw new Error(response.message || '上传失败');
                }
            } catch (error) {
                console.error('上传检测报告失败:', error);
                
                let errorMessage = '上传检测报告失败';
                if (error.response?.data?.message) {
                    errorMessage += ': ' + error.response.data.message;
                } else if (error.message) {
                    errorMessage += ': ' + error.message;
                }

                if (window.showNotification) {
                    window.showNotification(errorMessage, 'error');
                } else {
                    alert(errorMessage);
                }
            } finally {
                isSubmitting.value = false;
            }
        }

        // 处理取消操作
        function handleCancel() {
            if (confirm('确定要取消上传吗？未保存的数据将丢失。')) {
                window.location.href = '/quality-list';
            }
        }

        return {
            sidebarOpen,
            isSubmitting,
            handleSubmit,
            handleCancel
        };
    },
    requiredPermissions: ['quality_upload'],
    onUserLoaded: async (user) => {
        console.log('质量检测报告上传页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
