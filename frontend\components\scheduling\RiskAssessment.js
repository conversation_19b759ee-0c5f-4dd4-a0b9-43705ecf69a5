/**
 * 风险评估组件
 * 用于显示排程风险分析和评估结果
 */

export default {
    name: 'RiskAssessment',
    props: {
        riskData: {
            type: Object,
            required: true
        },
        showDetails: {
            type: Boolean,
            default: true
        }
    },
    template: `
        <div class="risk-assessment">
            <!-- 风险概览 -->
            <div class="risk-overview mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">风险评估</h3>
                    <div class="flex items-center">
                        <span class="risk-indicator" :class="getRiskClass(riskData.level)"></span>
                        <span class="ml-2 font-medium" :class="getRiskTextClass(riskData.level)">
                            {{ getRiskText(riskData.level) }}
                        </span>
                    </div>
                </div>
                
                <!-- 风险评分 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-gray-600">总体风险评分</span>
                        <span class="font-bold text-lg">{{ (riskData.totalScore * 100).toFixed(1) }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div :class="['h-3 rounded-full', getRiskBarClass(riskData.totalScore)]" 
                             :style="{ width: (riskData.totalScore * 100) + '%' }"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>低风险</span>
                        <span>高风险</span>
                    </div>
                </div>
            </div>

            <!-- 详细风险因素 -->
            <div v-if="showDetails && riskData.factors" class="risk-factors">
                <h4 class="font-medium text-gray-900 mb-4">风险因素分析</h4>
                <div class="space-y-4">
                    <div v-for="factor in riskData.factors" :key="factor.type" 
                         class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <span class="risk-indicator" :class="getRiskClass(factor.level)"></span>
                                <span class="ml-2 font-medium text-gray-900">{{ factor.name }}</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">
                                {{ (factor.score * 100).toFixed(1) }}%
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">{{ factor.description }}</p>
                        
                        <!-- 风险因素进度条 -->
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div :class="['h-2 rounded-full', getRiskBarClass(factor.score)]" 
                                 :style="{ width: (factor.score * 100) + '%' }"></div>
                        </div>
                        
                        <!-- 建议措施 -->
                        <div v-if="factor.suggestions && factor.suggestions.length > 0" class="mt-3">
                            <p class="text-xs font-medium text-gray-700 mb-1">建议措施:</p>
                            <ul class="text-xs text-gray-600 space-y-1">
                                <li v-for="suggestion in factor.suggestions" :key="suggestion" 
                                    class="flex items-start">
                                    <svg class="w-3 h-3 text-blue-500 mt-0.5 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ suggestion }}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 风险趋势 -->
            <div v-if="riskData.trend" class="risk-trend mt-6">
                <h4 class="font-medium text-gray-900 mb-4">风险趋势</h4>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">相比上次评估</span>
                        <div class="flex items-center">
                            <svg v-if="riskData.trend === 'up'" class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <svg v-else-if="riskData.trend === 'down'" class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <svg v-else class="w-4 h-4 text-gray-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span :class="getTrendTextClass(riskData.trend)">
                                {{ getTrendText(riskData.trend) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 总体建议 -->
            <div v-if="riskData.recommendations" class="recommendations mt-6">
                <h4 class="font-medium text-gray-900 mb-4">优化建议</h4>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <ul class="space-y-2">
                        <li v-for="recommendation in riskData.recommendations" :key="recommendation" 
                            class="flex items-start text-sm text-blue-800">
                            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            {{ recommendation }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    `,
    methods: {
        getRiskClass(riskLevel) {
            const classMap = {
                'low': 'bg-green-500',
                'medium': 'bg-yellow-500',
                'high': 'bg-red-500'
            };
            return classMap[riskLevel] || 'bg-gray-400';
        },

        getRiskText(riskLevel) {
            const textMap = {
                'low': '低风险',
                'medium': '中风险',
                'high': '高风险'
            };
            return textMap[riskLevel] || '未知';
        },

        getRiskTextClass(riskLevel) {
            const classMap = {
                'low': 'text-green-700',
                'medium': 'text-yellow-700',
                'high': 'text-red-700'
            };
            return classMap[riskLevel] || 'text-gray-700';
        },

        getRiskBarClass(score) {
            if (score <= 0.3) return 'bg-green-500';
            if (score <= 0.6) return 'bg-yellow-500';
            return 'bg-red-500';
        },

        getTrendText(trend) {
            const textMap = {
                'up': '风险上升',
                'down': '风险下降',
                'stable': '风险稳定'
            };
            return textMap[trend] || '无变化';
        },

        getTrendTextClass(trend) {
            const classMap = {
                'up': 'text-red-600',
                'down': 'text-green-600',
                'stable': 'text-gray-600'
            };
            return classMap[trend] || 'text-gray-600';
        }
    },
    style: `
        <style scoped>
        .risk-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        </style>
    `
};
