/**
 * 文件管理数据访问层
 * 使用SQLite数据库
 */

const databaseManager = require('./database');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class FileManagementRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        this.initStatements();
    }

    initStatements() {
        try {
            // 客户相关语句
            this.customerStatements = {
                findAll: this.db.prepare(`
                    SELECT * FROM file_management_customers
                    ORDER BY customer_name
                `),
                findActive: this.db.prepare(`
                    SELECT * FROM file_management_customers
                    WHERE active = 1
                    ORDER BY customer_name
                `),
                findById: this.db.prepare(`
                    SELECT * FROM file_management_customers 
                    WHERE id = ? AND active = 1
                `),
                findByName: this.db.prepare(`
                    SELECT * FROM file_management_customers
                    WHERE customer_name = ? AND active = 1
                `),
                findByNameAll: this.db.prepare(`
                    SELECT * FROM file_management_customers
                    WHERE customer_name = ?
                `),
                delete: this.db.prepare(`
                    DELETE FROM file_management_customers
                    WHERE id = ?
                `),
                create: this.db.prepare(`
                    INSERT INTO file_management_customers
                    (id, customer_name, customer_code, contact_person, contact_email,
                     contact_phone, address, description, active, created_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `),
                update: this.db.prepare(`
                    UPDATE file_management_customers 
                    SET customer_name = ?, customer_code = ?, contact_person = ?, 
                        contact_email = ?, contact_phone = ?, address = ?, 
                        description = ?, updated_at = ?
                    WHERE id = ?
                `)
            };

            // 产品相关语句
            this.productStatements = {
                findByCustomer: this.db.prepare(`
                    SELECT * FROM file_management_products 
                    WHERE customer_id = ? AND active = 1 
                    ORDER BY product_model
                `),
                findById: this.db.prepare(`
                    SELECT * FROM file_management_products 
                    WHERE id = ? AND active = 1
                `),
                findByCustomerAndModel: this.db.prepare(`
                    SELECT * FROM file_management_products 
                    WHERE customer_id = ? AND product_model = ? AND batch_number = ? AND active = 1
                `),
                create: this.db.prepare(`
                    INSERT INTO file_management_products 
                    (id, customer_id, product_model, product_name, batch_number, 
                     specification, description, created_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `)
            };

            // 文件记录相关语句
            this.fileStatements = {
                findAll: this.db.prepare(`
                    SELECT f.*, c.customer_name, p.product_model, p.product_name, u.username as uploaded_by_name
                    FROM file_management_files f
                    JOIN file_management_customers c ON f.customer_id = c.id
                    JOIN file_management_products p ON f.product_id = p.id
                    JOIN users u ON f.uploaded_by = u.id
                    WHERE f.status = 'active'
                    ORDER BY f.uploaded_at DESC
                `),
                findById: this.db.prepare(`
                    SELECT f.*, c.customer_name, p.product_model, p.product_name, u.username as uploaded_by_name
                    FROM file_management_files f
                    JOIN file_management_customers c ON f.customer_id = c.id
                    JOIN file_management_products p ON f.product_id = p.id
                    JOIN users u ON f.uploaded_by = u.id
                    WHERE f.id = ? AND f.status = 'active'
                `),
                findByProduct: this.db.prepare(`
                    SELECT * FROM file_management_files
                    WHERE product_id = ? AND status = 'active'
                    ORDER BY version DESC
                `),
                findByCustomerId: this.db.prepare(`
                    SELECT * FROM file_management_files
                    WHERE customer_id = ? AND status = 'active'
                `),
                getMaxVersion: this.db.prepare(`
                    SELECT MAX(version) as max_version 
                    FROM file_management_files 
                    WHERE product_id = ? AND status = 'active'
                `),
                create: this.db.prepare(`
                    INSERT INTO file_management_files 
                    (id, file_number, customer_id, product_id, title, description, 
                     version, is_first_version, change_description, uploaded_by, uploaded_at, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `),
                getTodayFileCount: this.db.prepare(`
                    SELECT COUNT(*) as count 
                    FROM file_management_files 
                    WHERE file_number LIKE ?
                `),
                getMaxUsedSequenceForDate: this.db.prepare(`
                    SELECT MAX(CAST(SUBSTR(file_number, -4) AS INTEGER)) as max_sequence
                    FROM file_management_files 
                    WHERE file_number LIKE ?
                    UNION ALL
                    SELECT MAX(CAST(SUBSTR(file_number, -4) AS INTEGER)) as max_sequence
                    FROM file_management_number_reservations 
                    WHERE file_number LIKE ?
                    ORDER BY max_sequence DESC
                    LIMIT 1
                `)
            };

            // 附件相关语句
            this.attachmentStatements = {
                findByFileRecord: this.db.prepare(`
                    SELECT * FROM file_management_attachments 
                    WHERE file_record_id = ? 
                    ORDER BY uploaded_at
                `),
                create: this.db.prepare(`
                    INSERT INTO file_management_attachments 
                    (id, file_record_id, original_filename, stored_filename, 
                     file_path, file_size, file_type, mime_type, uploaded_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `)
            };

            // 通知相关语句
            this.notificationStatements = {
                findByUser: this.db.prepare(`
                    SELECT n.*, f.title, f.file_number, c.customer_name, p.product_model
                    FROM file_management_notifications n
                    JOIN file_management_files f ON n.file_record_id = f.id
                    JOIN file_management_customers c ON f.customer_id = c.id
                    JOIN file_management_products p ON f.product_id = p.id
                    WHERE n.notified_user_id = ?
                    ORDER BY n.sent_at DESC
                `),
                create: this.db.prepare(`
                    INSERT INTO file_management_notifications
                    (id, file_record_id, notified_user_id, notification_type, sent_at)
                    VALUES (?, ?, ?, ?, ?)
                `),
                confirm: this.db.prepare(`
                    UPDATE file_management_notifications
                    SET confirmed = 1, confirmed_at = ?
                    WHERE id = ?
                `),
                checkAllConfirmed: this.db.prepare(`
                    SELECT
                        COUNT(*) as total_notifications,
                        SUM(CASE WHEN confirmed = 1 THEN 1 ELSE 0 END) as confirmed_notifications
                    FROM file_management_notifications
                    WHERE file_record_id = ?
                `),
                getFileUploader: this.db.prepare(`
                    SELECT f.uploaded_by, u.username, u.email, f.title, f.file_number
                    FROM file_management_files f
                    JOIN users u ON f.uploaded_by = u.id
                    WHERE f.id = ?
                `)
            };

        } catch (error) {
            logger.error('初始化文件管理数据库语句失败:', error);
            throw error;
        }
    }

    // 数据转换辅助方法
    transformCustomerData(customer) {
        if (!customer) return null;
        return {
            ...customer,
            active: Boolean(customer.active)
        };
    }

    transformCustomersData(customers) {
        return customers.map(customer => this.transformCustomerData(customer));
    }

    // 客户管理方法
    getAllCustomers() {
        try {
            const customers = this.customerStatements.findAll.all();
            return this.transformCustomersData(customers);
        } catch (error) {
            logger.error('获取所有客户失败:', error);
            throw error;
        }
    }

    getActiveCustomers() {
        try {
            const customers = this.customerStatements.findActive.all();
            return this.transformCustomersData(customers);
        } catch (error) {
            logger.error('获取活跃客户失败:', error);
            throw error;
        }
    }

    getCustomerById(id) {
        try {
            const customer = this.customerStatements.findById.get(id);
            return this.transformCustomerData(customer);
        } catch (error) {
            logger.error(`根据ID获取客户失败 (${id}):`, error);
            throw error;
        }
    }

    getCustomerByName(name) {
        try {
            const customer = this.customerStatements.findByName.get(name);
            return this.transformCustomerData(customer);
        } catch (error) {
            logger.error(`根据名称获取客户失败 (${name}):`, error);
            throw error;
        }
    }

    getCustomerByNameAll(name) {
        try {
            const customer = this.customerStatements.findByNameAll.get(name);
            return this.transformCustomerData(customer);
        } catch (error) {
            logger.error(`根据名称获取客户失败（包含停用） (${name}):`, error);
            throw error;
        }
    }

    createCustomer(customerData) {
        try {
            const now = new Date().toISOString();
            const id = uuidv4();
            
            this.customerStatements.create.run(
                id,
                customerData.customer_name,
                customerData.customer_code || '',
                customerData.contact_person || '',
                customerData.contact_email || '',
                customerData.contact_phone || '',
                customerData.address || '',
                customerData.description || '',
                customerData.active !== undefined ? (customerData.active ? 1 : 0) : 1,
                customerData.created_by,
                now,
                now
            );

            return this.getCustomerById(id);
        } catch (error) {
            logger.error('创建客户失败:', error);
            throw error;
        }
    }

    updateCustomer(id, customerData) {
        try {
            const now = new Date().toISOString();

            // 构建更新语句
            const updateFields = [];
            const updateValues = [];

            if (customerData.customer_name !== undefined) {
                updateFields.push('customer_name = ?');
                updateValues.push(customerData.customer_name);
            }
            if (customerData.customer_code !== undefined) {
                updateFields.push('customer_code = ?');
                updateValues.push(customerData.customer_code);
            }
            if (customerData.contact_person !== undefined) {
                updateFields.push('contact_person = ?');
                updateValues.push(customerData.contact_person);
            }
            if (customerData.contact_email !== undefined) {
                updateFields.push('contact_email = ?');
                updateValues.push(customerData.contact_email);
            }
            if (customerData.contact_phone !== undefined) {
                updateFields.push('contact_phone = ?');
                updateValues.push(customerData.contact_phone);
            }
            if (customerData.address !== undefined) {
                updateFields.push('address = ?');
                updateValues.push(customerData.address);
            }
            if (customerData.description !== undefined) {
                updateFields.push('description = ?');
                updateValues.push(customerData.description);
            }
            if (customerData.active !== undefined) {
                updateFields.push('active = ?');
                updateValues.push(customerData.active ? 1 : 0);
            }
            if (customerData.updated_by !== undefined) {
                updateFields.push('updated_by = ?');
                updateValues.push(customerData.updated_by);
            }

            updateFields.push('updated_at = ?');
            updateValues.push(now);
            updateValues.push(id);

            const updateSQL = `UPDATE file_management_customers SET ${updateFields.join(', ')} WHERE id = ?`;
            const updateStatement = this.db.prepare(updateSQL);
            updateStatement.run(...updateValues);

            return this.getCustomerById(id);
        } catch (error) {
            logger.error('更新客户失败:', error);
            throw error;
        }
    }

    deleteCustomer(id) {
        try {
            const result = this.customerStatements.delete.run(id);
            if (result.changes === 0) {
                throw new Error('客户不存在或已被删除');
            }
            return { success: true, changes: result.changes };
        } catch (error) {
            logger.error('删除客户失败:', error);
            throw error;
        }
    }

    getFilesByCustomerId(customerId) {
        try {
            return this.fileStatements.findByCustomerId.all(customerId);
        } catch (error) {
            logger.error(`获取客户文件记录失败 (${customerId}):`, error);
            throw error;
        }
    }

    getHistoricalBatchesByCustomerAndProduct(customerId, productModel) {
        try {
            // 查询该客户和产品型号的所有产品记录，获取批次号
            const query = `
                SELECT DISTINCT batch_number
                FROM file_management_products
                WHERE customer_id = ? AND product_model = ? AND active = 1
                ORDER BY created_at ASC
            `;
            const statement = this.db.prepare(query);
            return statement.all(customerId, productModel);
        } catch (error) {
            logger.error(`获取历史批次失败 (${customerId}, ${productModel}):`, error);
            throw error;
        }
    }

    // 产品管理方法
    getProductsByCustomer(customerId) {
        try {
            return this.productStatements.findByCustomer.all(customerId);
        } catch (error) {
            logger.error(`获取客户产品失败 (${customerId}):`, error);
            throw error;
        }
    }

    getProductById(id) {
        try {
            return this.productStatements.findById.get(id);
        } catch (error) {
            logger.error(`根据ID获取产品失败 (${id}):`, error);
            throw error;
        }
    }

    findProductByCustomerAndModel(customerId, productModel, batchNumber) {
        try {
            return this.productStatements.findByCustomerAndModel.get(customerId, productModel, batchNumber);
        } catch (error) {
            logger.error(`根据客户和型号获取产品失败:`, error);
            throw error;
        }
    }

    createProduct(productData) {
        try {
            const now = new Date().toISOString();
            const id = uuidv4();
            
            this.productStatements.create.run(
                id,
                productData.customer_id,
                productData.product_model,
                productData.product_name || '',
                productData.batch_number || '',
                productData.specification || '',
                productData.description || '',
                productData.created_by,
                now,
                now
            );

            return this.getProductById(id);
        } catch (error) {
            logger.error('创建产品失败:', error);
            throw error;
        }
    }

    // 文件记录管理方法
    getAllFiles() {
        try {
            return this.fileStatements.findAll.all();
        } catch (error) {
            logger.error('获取所有文件记录失败:', error);
            throw error;
        }
    }

    getFileById(id) {
        try {
            return this.fileStatements.findById.get(id);
        } catch (error) {
            logger.error(`根据ID获取文件记录失败 (${id}):`, error);
            throw error;
        }
    }

    getFilesByProduct(productId) {
        try {
            return this.fileStatements.findByProduct.all(productId);
        } catch (error) {
            logger.error(`获取产品文件记录失败 (${productId}):`, error);
            throw error;
        }
    }

    getMaxVersionForProduct(productId) {
        try {
            const result = this.fileStatements.getMaxVersion.get(productId);
            return result ? result.max_version || 0 : 0;
        } catch (error) {
            logger.error(`获取产品最大版本号失败 (${productId}):`, error);
            throw error;
        }
    }

    createFileRecord(fileData) {
        try {
            const now = new Date().toISOString();
            const id = uuidv4();
            
            this.fileStatements.create.run(
                id,
                fileData.file_number,
                fileData.customer_id,
                fileData.product_id,
                fileData.title,
                fileData.description || '',
                fileData.version,
                fileData.is_first_version ? 1 : 0,
                fileData.change_description || '',
                fileData.uploaded_by,
                fileData.uploaded_at,
                now,
                now
            );

            return this.getFileById(id);
        } catch (error) {
            logger.error('创建文件记录失败:', error);
            throw error;
        }
    }

    // 文件编号生成相关方法
    getTodayFileCount() {
        try {
            const today = new Date();
            const dateStr = today.getFullYear().toString() +
                           (today.getMonth() + 1).toString().padStart(2, '0') +
                           today.getDate().toString().padStart(2, '0');
            const pattern = `FM${dateStr}%`;
            
            const result = this.fileStatements.getTodayFileCount.get(pattern);
            return result ? result.count : 0;
        } catch (error) {
            logger.error('获取今日文件数量失败:', error);
            throw error;
        }
    }

    getMaxUsedSequenceForDate(datePrefix) {
        try {
            const pattern = `${datePrefix}%`;
            const result = this.fileStatements.getMaxUsedSequenceForDate.get(pattern, pattern);
            return result ? result.max_sequence || 0 : 0;
        } catch (error) {
            logger.error('获取日期最大序号失败:', error);
            throw error;
        }
    }

    // 附件管理方法
    getAttachmentsByFileRecord(fileRecordId) {
        try {
            return this.attachmentStatements.findByFileRecord.all(fileRecordId);
        } catch (error) {
            logger.error(`获取文件附件失败 (${fileRecordId}):`, error);
            throw error;
        }
    }

    createAttachment(attachmentData) {
        try {
            const now = new Date().toISOString();
            const id = uuidv4();
            
            this.attachmentStatements.create.run(
                id,
                attachmentData.file_record_id,
                attachmentData.original_filename,
                attachmentData.stored_filename,
                attachmentData.file_path,
                attachmentData.file_size,
                attachmentData.file_type,
                attachmentData.mime_type,
                now
            );

            return { id, ...attachmentData, uploaded_at: now };
        } catch (error) {
            logger.error('创建附件记录失败:', error);
            throw error;
        }
    }

    // 通知管理方法
    getNotificationsByUser(userId) {
        try {
            return this.notificationStatements.findByUser.all(userId);
        } catch (error) {
            logger.error(`获取用户通知失败 (${userId}):`, error);
            throw error;
        }
    }

    createNotification(notificationData) {
        try {
            const now = new Date().toISOString();
            const id = uuidv4();
            
            this.notificationStatements.create.run(
                id,
                notificationData.file_record_id,
                notificationData.notified_user_id,
                notificationData.notification_type || 'new_file',
                now
            );

            return { id, ...notificationData, sent_at: now };
        } catch (error) {
            logger.error('创建通知记录失败:', error);
            throw error;
        }
    }

    confirmNotification(notificationId) {
        try {
            const now = new Date().toISOString();
            const result = this.notificationStatements.confirm.run(now, notificationId);
            return result.changes > 0;
        } catch (error) {
            logger.error(`确认通知失败 (${notificationId}):`, error);
            throw error;
        }
    }

    /**
     * 检查文件的所有通知是否都已确认
     * @param {string} fileRecordId - 文件记录ID
     * @returns {Object} 确认状态信息
     */
    checkAllNotificationsConfirmed(fileRecordId) {
        try {
            const result = this.notificationStatements.checkAllConfirmed.get(fileRecordId);
            return {
                totalNotifications: result.total_notifications,
                confirmedNotifications: result.confirmed_notifications,
                allConfirmed: result.total_notifications > 0 && result.total_notifications === result.confirmed_notifications
            };
        } catch (error) {
            logger.error(`检查通知确认状态失败 (${fileRecordId}):`, error);
            throw error;
        }
    }

    /**
     * 获取文件上传者信息
     * @param {string} fileRecordId - 文件记录ID
     * @returns {Object} 上传者信息
     */
    getFileUploader(fileRecordId) {
        try {
            return this.notificationStatements.getFileUploader.get(fileRecordId);
        } catch (error) {
            logger.error(`获取文件上传者信息失败 (${fileRecordId}):`, error);
            throw error;
        }
    }
}

module.exports = FileManagementRepository;
