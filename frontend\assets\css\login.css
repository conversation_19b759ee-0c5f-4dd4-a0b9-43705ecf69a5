/* 登录页面样式文件 - Login CSS */

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
    display: none;
}

/* 主背景 */
.main-bg {
    background: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* 登录卡片样式 */
.login-card {
    background: white;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    position: relative;
    z-index: 10;
}

/* 输入框样式 */
.input-field {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.input-field:focus {
    background: white;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮样式 */
.login-btn {
    background: #3b82f6;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.login-btn:active {
    transform: translateY(0);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f8fafc;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 底部SVG装饰 */
.bottom-svg {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 200px;
    height: auto;
    z-index: 1;
    pointer-events: none;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .bottom-svg {
        width: 150px;
        bottom: 15px;
        right: 15px;
    }
}
