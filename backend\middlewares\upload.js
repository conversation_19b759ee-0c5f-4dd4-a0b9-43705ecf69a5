/**
 * 文件上传中间件
 * 处理文件上传配置
 */

const multer = require('multer');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');

// 配置文件存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, config.paths.uploads);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名
        const uniqueFilename = `${uuidv4()}-${file.originalname}`;
        cb(null, uniqueFilename);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 允许的文件扩展名
    const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.csv'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    // 允许的MIME类型
    const allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'text/csv',
        'application/csv'
    ];

    const isExtensionAllowed = allowedExtensions.includes(fileExtension);
    const isMimeTypeAllowed = allowedMimeTypes.includes(file.mimetype);

    if (isExtensionAllowed && isMimeTypeAllowed) {
        return cb(null, true);
    } else {
        cb(new Error(`不支持的文件类型！支持的格式：${allowedExtensions.join(', ')}`));
    }
};

// 配置上传
const upload = multer({
    storage: storage,
    limits: { fileSize: config.upload.maxFileSize },
    fileFilter: fileFilter,
    // 确保正确处理文件名编码
    preservePath: false
});

module.exports = upload;
