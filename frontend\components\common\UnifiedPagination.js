/**
 * 统一分页组件
 * 按照图片要求设计：共117条记录，每页10条 + 上一页 1 2 3 4 5 ... 12 下一页
 */

export default {
    props: {
        currentPage: {
            type: Number,
            default: 1
        },
        totalItems: {
            type: Number,
            default: 0
        },
        itemsPerPage: {
            type: Number,
            default: 10
        }
    },
    emits: ['page-change'],
    setup(props, { emit }) {
        const { computed } = Vue;

        // 计算总页数
        const totalPages = computed(() => {
            return Math.ceil(props.totalItems / props.itemsPerPage);
        });

        // 计算要显示的页码数组
        const pageNumbers = computed(() => {
            const current = props.currentPage;
            const total = totalPages.value;
            const pages = [];

            if (total <= 7) {
                // 总页数少于等于7页，显示所有页码
                for (let i = 1; i <= total; i++) {
                    pages.push(i);
                }
            } else {
                // 总页数大于7页，使用省略号逻辑
                if (current <= 4) {
                    // 当前页在前面，显示 1,2,3,4,5,...,total
                    for (let i = 1; i <= 5; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                    pages.push(total);
                } else if (current >= total - 3) {
                    // 当前页在后面，显示 1,...,total-4,total-3,total-2,total-1,total
                    pages.push(1);
                    pages.push('...');
                    for (let i = total - 4; i <= total; i++) {
                        pages.push(i);
                    }
                } else {
                    // 当前页在中间，显示 1,...,current-1,current,current+1,...,total
                    pages.push(1);
                    pages.push('...');
                    for (let i = current - 1; i <= current + 1; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                    pages.push(total);
                }
            }

            return pages;
        });

        // 是否可以上一页
        const canGoPrev = computed(() => {
            return props.currentPage > 1;
        });

        // 是否可以下一页
        const canGoNext = computed(() => {
            return props.currentPage < totalPages.value;
        });

        // 页码点击处理
        function handlePageClick(page) {
            if (typeof page === 'number' && page !== props.currentPage) {
                emit('page-change', page);
            }
        }

        // 上一页
        function handlePrevPage() {
            if (canGoPrev.value) {
                emit('page-change', props.currentPage - 1);
            }
        }

        // 下一页
        function handleNextPage() {
            if (canGoNext.value) {
                emit('page-change', props.currentPage + 1);
            }
        }

        return {
            totalPages,
            pageNumbers,
            canGoPrev,
            canGoNext,
            handlePageClick,
            handlePrevPage,
            handleNextPage
        };
    },
    template: `
        <div v-if="totalItems > 0" class="flex items-center justify-between mt-6">
            <!-- 左侧：记录统计信息 -->
            <div class="text-sm text-gray-600">
                共 {{ totalItems }} 条记录，每页 {{ itemsPerPage }} 条
            </div>
            
            <!-- 右侧：分页控件 -->
            <div class="flex items-center space-x-1">
                <!-- 上一页按钮 -->
                <button 
                    @click="handlePrevPage"
                    :disabled="!canGoPrev"
                    :class="[
                        'px-3 py-1 text-sm border border-gray-300 rounded',
                        canGoPrev 
                            ? 'text-gray-700 hover:bg-gray-50 cursor-pointer' 
                            : 'text-gray-400 cursor-not-allowed opacity-50'
                    ]">
                    上一页
                </button>

                <!-- 页码按钮 -->
                <template v-for="(page, index) in pageNumbers" :key="index">
                    <button 
                        v-if="typeof page === 'number'"
                        @click="handlePageClick(page)"
                        :class="[
                            'px-3 py-1 text-sm rounded min-w-[32px]',
                            page === currentPage
                                ? 'bg-blue-500 text-white'
                                : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                        ]">
                        {{ page }}
                    </button>
                    <span 
                        v-else
                        class="px-2 py-1 text-sm text-gray-400">
                        {{ page }}
                    </span>
                </template>

                <!-- 下一页按钮 -->
                <button 
                    @click="handleNextPage"
                    :disabled="!canGoNext"
                    :class="[
                        'px-3 py-1 text-sm border border-gray-300 rounded',
                        canGoNext 
                            ? 'text-gray-700 hover:bg-gray-50 cursor-pointer' 
                            : 'text-gray-400 cursor-not-allowed opacity-50'
                    ]">
                    下一页
                </button>
            </div>
        </div>
    `
};
