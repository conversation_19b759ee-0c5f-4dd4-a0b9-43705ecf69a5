/**
 * 算法调优控制器
 * 处理算法调优相关的HTTP请求
 */

const AlgorithmTuner = require('../services/scheduling/AlgorithmTuner');

class TuningController {
    constructor() {
        this.algorithmTuner = new AlgorithmTuner();
    }

    /**
     * 执行算法调优
     */
    async tunePlatform(req, res) {
        try {
            console.log('开始执行算法调优...');
            
            const tuningResults = await this.algorithmTuner.tuneAlgorithms();
            
            res.json({
                success: true,
                message: '算法调优完成',
                data: {
                    tuningResults,
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            console.error('算法调优失败:', error);
            res.status(500).json({
                success: false,
                message: '算法调优失败',
                error: error.message
            });
        }
    }

    /**
     * 获取调优统计信息
     */
    async getTuningStats(req, res) {
        try {
            const stats = this.algorithmTuner.getTuningStats();
            
            res.json({
                success: true,
                message: '获取调优统计成功',
                data: {
                    stats,
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            console.error('获取调优统计失败:', error);
            res.status(500).json({
                success: false,
                message: '获取调优统计失败',
                error: error.message
            });
        }
    }

    /**
     * 获取当前调优参数
     */
    async getTuningParams(req, res) {
        try {
            const params = this.algorithmTuner.tuningParams;
            
            res.json({
                success: true,
                message: '获取调优参数成功',
                data: {
                    params,
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            console.error('获取调优参数失败:', error);
            res.status(500).json({
                success: false,
                message: '获取调优参数失败',
                error: error.message
            });
        }
    }

    /**
     * 更新调优参数
     */
    async updateTuningParams(req, res) {
        try {
            const { params } = req.body;
            
            if (!params) {
                return res.status(400).json({
                    success: false,
                    message: '缺少调优参数'
                });
            }
            
            // 验证参数格式
            const validationResult = this.validateTuningParams(params);
            if (!validationResult.valid) {
                return res.status(400).json({
                    success: false,
                    message: '调优参数格式无效',
                    errors: validationResult.errors
                });
            }
            
            // 更新参数
            Object.assign(this.algorithmTuner.tuningParams, params);
            
            res.json({
                success: true,
                message: '调优参数更新成功',
                data: {
                    updatedParams: this.algorithmTuner.tuningParams,
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            console.error('更新调优参数失败:', error);
            res.status(500).json({
                success: false,
                message: '更新调优参数失败',
                error: error.message
            });
        }
    }

    /**
     * 重置调优参数为默认值
     */
    async resetTuningParams(req, res) {
        try {
            // 重新创建调优器以恢复默认参数
            this.algorithmTuner = new AlgorithmTuner();
            
            res.json({
                success: true,
                message: '调优参数重置成功',
                data: {
                    params: this.algorithmTuner.tuningParams,
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            console.error('重置调优参数失败:', error);
            res.status(500).json({
                success: false,
                message: '重置调优参数失败',
                error: error.message
            });
        }
    }

    /**
     * 获取性能历史数据
     */
    async getPerformanceHistory(req, res) {
        try {
            const history = this.algorithmTuner.performanceHistory;
            
            res.json({
                success: true,
                message: '获取性能历史成功',
                data: {
                    history,
                    count: history.length,
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            console.error('获取性能历史失败:', error);
            res.status(500).json({
                success: false,
                message: '获取性能历史失败',
                error: error.message
            });
        }
    }

    /**
     * 执行性能基准测试
     */
    async runBenchmark(req, res) {
        try {
            const { testCount = 10, testType = 'full' } = req.body;
            
            console.log(`开始执行性能基准测试，测试次数: ${testCount}, 测试类型: ${testType}`);
            
            const benchmarkResults = await this.runPerformanceBenchmark(testCount, testType);
            
            res.json({
                success: true,
                message: '性能基准测试完成',
                data: {
                    results: benchmarkResults,
                    timestamp: new Date().toISOString()
                }
            });
            
        } catch (error) {
            console.error('性能基准测试失败:', error);
            res.status(500).json({
                success: false,
                message: '性能基准测试失败',
                error: error.message
            });
        }
    }

    /**
     * 验证调优参数
     */
    validateTuningParams(params) {
        const errors = [];
        
        // 验证交期预测参数
        if (params.deliveryPrediction) {
            const dp = params.deliveryPrediction;
            
            if (dp.baseAccuracy && (dp.baseAccuracy < 0 || dp.baseAccuracy > 1)) {
                errors.push('baseAccuracy 必须在 0-1 之间');
            }
            
            if (dp.riskFactorWeight && (dp.riskFactorWeight < 0 || dp.riskFactorWeight > 1)) {
                errors.push('riskFactorWeight 必须在 0-1 之间');
            }
            
            if (dp.historicalDataWeight && (dp.historicalDataWeight < 0 || dp.historicalDataWeight > 1)) {
                errors.push('historicalDataWeight 必须在 0-1 之间');
            }
            
            if (dp.currentStatusWeight && (dp.currentStatusWeight < 0 || dp.currentStatusWeight > 1)) {
                errors.push('currentStatusWeight 必须在 0-1 之间');
            }
            
            // 验证权重总和
            const totalWeight = (dp.riskFactorWeight || 0) + 
                               (dp.historicalDataWeight || 0) + 
                               (dp.currentStatusWeight || 0);
            if (Math.abs(totalWeight - 1) > 0.01) {
                errors.push('权重总和必须等于 1');
            }
        }
        
        // 验证资源优化参数
        if (params.resourceOptimization) {
            const ro = params.resourceOptimization;
            
            const totalWeight = (ro.efficiencyWeight || 0) + 
                               (ro.costWeight || 0) + 
                               (ro.riskWeight || 0);
            if (Math.abs(totalWeight - 1) > 0.01) {
                errors.push('资源优化权重总和必须等于 1');
            }
        }
        
        // 验证方案生成参数
        if (params.planGeneration) {
            const pg = params.planGeneration;
            
            if (pg.maxPlansCount && (pg.maxPlansCount < 1 || pg.maxPlansCount > 10)) {
                errors.push('maxPlansCount 必须在 1-10 之间');
            }
            
            if (pg.timeoutMs && (pg.timeoutMs < 1000 || pg.timeoutMs > 30000)) {
                errors.push('timeoutMs 必须在 1000-30000 之间');
            }
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * 执行性能基准测试
     */
    async runPerformanceBenchmark(testCount, testType) {
        const results = {
            testCount,
            testType,
            startTime: new Date().toISOString(),
            tests: [],
            summary: {}
        };
        
        const testOrder = {
            id: 'BENCHMARK_' + Date.now(),
            productId: 'PROD001',
            quantity: 1000,
            requiredDate: '2024-12-31',
            priority: 'normal'
        };
        
        // 执行多次测试
        for (let i = 0; i < testCount; i++) {
            const testStart = Date.now();
            
            try {
                // 这里应该调用实际的排程算法
                // 模拟测试执行
                await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));
                
                const testEnd = Date.now();
                const duration = testEnd - testStart;
                
                results.tests.push({
                    testIndex: i + 1,
                    duration,
                    success: true,
                    timestamp: new Date(testStart).toISOString()
                });
                
            } catch (error) {
                const testEnd = Date.now();
                const duration = testEnd - testStart;
                
                results.tests.push({
                    testIndex: i + 1,
                    duration,
                    success: false,
                    error: error.message,
                    timestamp: new Date(testStart).toISOString()
                });
            }
        }
        
        // 计算汇总统计
        const successfulTests = results.tests.filter(t => t.success);
        const durations = successfulTests.map(t => t.duration);
        
        results.summary = {
            totalTests: testCount,
            successfulTests: successfulTests.length,
            failedTests: testCount - successfulTests.length,
            successRate: successfulTests.length / testCount,
            averageDuration: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
            minDuration: durations.length > 0 ? Math.min(...durations) : 0,
            maxDuration: durations.length > 0 ? Math.max(...durations) : 0,
            endTime: new Date().toISOString()
        };
        
        return results;
    }
}

module.exports = TuningController;
