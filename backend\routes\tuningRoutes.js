/**
 * 算法调优路由
 * 处理算法调优相关的路由
 */

const express = require('express');
const router = express.Router();
const TuningController = require('../controllers/TuningController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 创建控制器实例
const tuningController = new TuningController();

/**
 * @route POST /api/tuning/tune
 * @desc 执行算法调优
 * @access Admin or schedule_manage
 */
router.post('/tune',
    authenticateJWT,
    checkPermission('schedule_manage'),
    async (req, res) => {
        await tuningController.tunePlatform(req, res);
    }
);

/**
 * @route GET /api/tuning/stats
 * @desc 获取调优统计信息
 * @access Admin or schedule_view
 */
router.get('/stats',
    authenticateJWT,
    checkPermission('schedule_view'),
    async (req, res) => {
        await tuningController.getTuningStats(req, res);
    }
);

/**
 * @route GET /api/tuning/params
 * @desc 获取当前调优参数
 * @access Admin or schedule_view
 */
router.get('/params',
    authenticateJWT,
    checkPermission('schedule_view'),
    async (req, res) => {
        await tuningController.getTuningParams(req, res);
    }
);

/**
 * @route PUT /api/tuning/params
 * @desc 更新调优参数
 * @access Admin or schedule_manage
 */
router.put('/params',
    authenticateJWT,
    checkPermission('schedule_manage'),
    async (req, res) => {
        await tuningController.updateTuningParams(req, res);
    }
);

/**
 * @route POST /api/tuning/params/reset
 * @desc 重置调优参数为默认值
 * @access Admin or schedule_manage
 */
router.post('/params/reset',
    authenticateJWT,
    checkPermission('schedule_manage'),
    async (req, res) => {
        await tuningController.resetTuningParams(req, res);
    }
);

/**
 * @route GET /api/tuning/performance/history
 * @desc 获取性能历史数据
 * @access Admin or schedule_view
 */
router.get('/performance/history',
    authenticateJWT,
    checkPermission('schedule_view'),
    async (req, res) => {
        await tuningController.getPerformanceHistory(req, res);
    }
);

/**
 * @route POST /api/tuning/benchmark
 * @desc 执行性能基准测试
 * @access Admin or schedule_manage
 */
router.post('/benchmark',
    authenticateJWT,
    checkPermission('schedule_manage'),
    async (req, res) => {
        await tuningController.runBenchmark(req, res);
    }
);

module.exports = router;
