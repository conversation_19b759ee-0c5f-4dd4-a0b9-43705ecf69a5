/**
 * 申请API
 * 处理申请相关的API请求
 */

import { API_URL } from '../../scripts/config.js';
import ErrorHandler from '../utils/errorHandler.js';

/**
 * 获取认证头信息
 * @returns {Object} 包含Authorization头的对象
 */
function getAuthHeaders() {
    const token = sessionStorage.getItem('authToken');
    return {
        'Authorization': `Bearer ${token}`
    };
}

/**
 * 获取所有申请
 * @returns {Promise<Array>} 申请列表
 */
export async function getApplications() {
    try {
        // 添加时间戳参数强制绕过缓存
        const timestamp = Date.now();
        const url = `${API_URL}/applications?_t=${timestamp}&_r=${Math.random()}`;

        const response = await axios.get(url, {
            headers: {
                ...getAuthHeaders(),
                // 添加防缓存请求头
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取申请列表');
        throw error;
    }
}

/**
 * 获取单个申请详情
 * @param {string} id - 申请ID
 * @returns {Promise<Object>} 申请详情
 */
export async function getApplicationById(id) {
    try {
        const response = await axios.get(`${API_URL}/applications/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取申请详情');
        throw error;
    }
}

/**
 * 创建新申请
 * @param {Object} applicationData - 申请数据
 * @param {Array} files - 附件文件
 * @returns {Promise<Object>} 创建结果
 */
export async function createApplication(applicationData, files) {
    try {
        console.log('准备创建申请，数据:', applicationData);
        console.log('附件文件数量:', files.length);

        const formData = new FormData();

        // 添加表单数据
        Object.keys(applicationData).forEach(key => {
            if (key === 'selectedFactoryManagers') {
                // 特殊处理厂长选择数据，序列化为JSON字符串
                const jsonValue = JSON.stringify(applicationData[key]);
                console.log(`添加字段 ${key}:`, jsonValue);
                formData.append(key, jsonValue);
            } else {
                console.log(`添加字段 ${key}:`, applicationData[key]);
                formData.append(key, applicationData[key]);
            }
        });

        // 添加文件
        files.forEach((file, index) => {
            console.log(`添加附件 ${index}:`, file.name, file.size);
            formData.append('attachments', file);
        });

        console.log('发送POST请求到:', `${API_URL}/applications`);
        const response = await axios.post(`${API_URL}/applications`, formData, {
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'multipart/form-data'
            },
            timeout: 30000 // 增加超时时间到30秒
        });

        console.log('申请创建成功，响应:', response.data);
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '创建申请');
        throw error;
    }
}

/**
 * 修改申请
 * @param {string} id - 申请ID
 * @param {Object} updateData - 更新数据
 * @param {Array} files - 新上传的文件（可选）
 * @returns {Promise<Object>} 修改结果
 */
export async function updateApplication(id, updateData, files = []) {
    try {
        // 如果有文件上传，使用FormData格式
        if (files && files.length > 0) {
            const formData = new FormData();

            // 添加表单数据
            Object.keys(updateData).forEach(key => {
                if (key === 'selectedFactoryManagers') {
                    // 特殊处理厂长选择数据，序列化为JSON字符串
                    formData.append(key, JSON.stringify(updateData[key]));
                } else {
                    formData.append(key, updateData[key]);
                }
            });

            // 添加文件
            files.forEach(file => {
                formData.append('attachments', file);
            });

            const response = await axios.put(`${API_URL}/applications/${id}`, formData, {
                headers: {
                    ...getAuthHeaders(),
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } else {
            // 没有文件上传，使用JSON格式
            const response = await axios.put(`${API_URL}/applications/${id}`, updateData, {
                headers: {
                    ...getAuthHeaders(),
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        }
    } catch (error) {
        ErrorHandler.handleAPIError(error, '修改申请');
        throw error;
    }
}

/**
 * 删除申请
 * @param {string} id - 申请ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteApplication(id) {
    try {
        const response = await axios.delete(`${API_URL}/applications/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '删除申请');
        throw error;
    }
}

/**
 * 下载附件
 * @param {Object} file - 附件信息
 * @returns {Promise<void>}
 */
export async function downloadAttachment(file) {
    try {
        const response = await axios.get(`${API_URL}/applications/attachments/${file.id}`, {
            responseType: 'blob',
            headers: getAuthHeaders()
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', file.name);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理blob URL
        window.URL.revokeObjectURL(url);
    } catch (error) {
        ErrorHandler.handleAPIError(error, '下载附件');
        throw error;
    }
}

/**
 * 获取待审核申请
 * @returns {Promise<Array>} 待审核申请列表
 */
export async function getPendingApplications() {
    try {
        // 添加时间戳参数强制绕过缓存
        const timestamp = Date.now();
        const url = `${API_URL}/applications/pending?_t=${timestamp}&_r=${Math.random()}`;

        const response = await axios.get(url, {
            headers: {
                ...getAuthHeaders(),
                // 添加防缓存请求头
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取待审核申请列表');
        throw error;
    }
}

/**
 * 获取已审核申请
 * @returns {Promise<Array>} 已审核申请列表
 */
export async function getApprovedApplications() {
    try {
        // 添加时间戳参数强制绕过缓存
        const timestamp = Date.now();
        const url = `${API_URL}/applications/approved?_t=${timestamp}&_r=${Math.random()}`;

        const response = await axios.get(url, {
            headers: {
                ...getAuthHeaders(),
                // 添加防缓存请求头
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取已审核申请列表');
        throw error;
    }
}

/**
 * 获取申请记录
 * @returns {Promise<Array>} 申请记录列表
 */
export async function getApplicationRecords() {
    try {
        console.log('发送获取申请记录请求...');

        // 添加时间戳参数强制绕过缓存
        const timestamp = Date.now();
        const url = `${API_URL}/applications/records?_t=${timestamp}&_r=${Math.random()}`;

        const response = await axios.get(url, {
            headers: {
                ...getAuthHeaders(),
                // 添加防缓存请求头
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        console.log(`API返回 ${response.data.length} 条申请记录`);
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '获取申请记录');
        throw error;
    }
}

/**
 * 审批通过申请
 * @param {string} id - 申请ID
 * @param {Object} approvalData - 审批数据
 * @returns {Promise<Object>} 审批结果
 */
export async function approveApplication(id, approvalData = {}) {
    try {
        const response = await axios.post(`${API_URL}/applications/${id}/approve`, approvalData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '审批通过');
        throw error;
    }
}

/**
 * 审批拒绝申请
 * @param {string} id - 申请ID
 * @param {Object} rejectionData - 拒绝数据
 * @returns {Promise<Object>} 拒绝结果
 */
export async function rejectApplication(id, rejectionData = {}) {
    try {
        const response = await axios.post(`${API_URL}/applications/${id}/reject`, rejectionData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        ErrorHandler.handleAPIError(error, '审批拒绝');
        throw error;
    }
}
