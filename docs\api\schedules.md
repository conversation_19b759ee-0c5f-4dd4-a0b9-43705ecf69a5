# 排程管理 API 文档

## 📋 概述

排程管理模块提供生产排程计划的创建、管理、执行和监控功能，支持资源分配、时间安排和进度跟踪。

## 🌐 基础信息

- **Base URL**: `http://localhost:5050/api`
- **认证方式**: Bearer <PERSON>ken (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

## 🔐 认证

所有API请求都需要在请求头中包含有效的JWT令牌：

```http
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 查看排程 | `schedule_view` |
| 创建排程 | `schedule_create` |
| 编辑排程 | `schedule_edit` |
| 删除排程 | `schedule_delete` |
| 执行排程 | `schedule_execute` |

## 📅 API 接口

### 1. 获取排程列表

**GET** `/api/schedules`

获取排程计划列表，支持分页、搜索和筛选。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |
| status | string | 否 | 状态筛选 |
| priority | string | 否 | 优先级筛选 |
| startDate | string | 否 | 开始日期 (YYYY-MM-DD) |
| endDate | string | 否 | 结束日期 (YYYY-MM-DD) |
| factory | string | 否 | 厂区筛选 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "sch001",
      "scheduleNumber": "SCH-20250729-001",
      "title": "7月生产排程计划",
      "description": "7月份主要产品生产排程",
      "status": "active",
      "priority": "high",
      "factory": "第一厂区",
      "planStartDate": "2025-08-01",
      "planEndDate": "2025-08-31",
      "actualStartDate": "2025-08-01",
      "actualEndDate": null,
      "progress": 0.25,
      "totalTasks": 50,
      "completedTasks": 12,
      "resources": {
        "equipment": 15,
        "operators": 25,
        "materials": 8
      },
      "createdBy": "张主管",
      "createdAt": "2025-07-29T10:00:00.000Z",
      "updatedAt": "2025-07-29T14:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

### 2. 获取排程详情

**GET** `/api/schedules/:id`

获取指定排程的详细信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 排程ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "sch001",
    "scheduleNumber": "SCH-20250729-001",
    "title": "7月生产排程计划",
    "description": "7月份主要产品生产排程",
    "status": "active",
    "priority": "high",
    "factory": "第一厂区",
    "planStartDate": "2025-08-01",
    "planEndDate": "2025-08-31",
    "actualStartDate": "2025-08-01",
    "actualEndDate": null,
    "progress": 0.25,
    "tasks": [
      {
        "id": "task001",
        "taskNumber": "T-001",
        "title": "产品A生产",
        "description": "生产产品A 1000件",
        "status": "in_progress",
        "priority": "high",
        "planStartDate": "2025-08-01",
        "planEndDate": "2025-08-05",
        "actualStartDate": "2025-08-01",
        "actualEndDate": null,
        "progress": 0.6,
        "assignedEquipment": [
          {
            "id": "EQ001",
            "name": "数控机床A",
            "allocatedHours": 32
          }
        ],
        "assignedOperators": [
          {
            "id": "OP001",
            "name": "张师傅",
            "allocatedHours": 32
          }
        ],
        "materials": [
          {
            "id": "MAT001",
            "name": "原料A",
            "quantity": 500,
            "unit": "kg"
          }
        ]
      }
    ],
    "resources": {
      "equipment": [
        {
          "id": "EQ001",
          "name": "数控机床A",
          "type": "数控机床",
          "status": "allocated",
          "utilizationRate": 0.8
        }
      ],
      "operators": [
        {
          "id": "OP001",
          "name": "张师傅",
          "skillLevel": 5,
          "status": "allocated",
          "workload": 0.75
        }
      ],
      "materials": [
        {
          "id": "MAT001",
          "name": "原料A",
          "totalRequired": 2500,
          "allocated": 1500,
          "remaining": 1000,
          "unit": "kg"
        }
      ]
    },
    "milestones": [
      {
        "id": "ms001",
        "title": "第一阶段完成",
        "targetDate": "2025-08-10",
        "status": "pending",
        "progress": 0.4
      }
    ],
    "metrics": {
      "efficiency": 0.85,
      "onTimeDelivery": 0.92,
      "resourceUtilization": 0.78,
      "qualityScore": 0.95
    },
    "createdBy": "张主管",
    "createdAt": "2025-07-29T10:00:00.000Z",
    "updatedAt": "2025-07-29T14:30:00.000Z"
  }
}
```

### 3. 创建排程

**POST** `/api/schedules`

创建新的排程计划。

#### 请求参数

```json
{
  "title": "8月生产排程计划",
  "description": "8月份主要产品生产排程",
  "priority": "high",
  "factory": "第一厂区",
  "planStartDate": "2025-08-01",
  "planEndDate": "2025-08-31",
  "tasks": [
    {
      "title": "产品B生产",
      "description": "生产产品B 800件",
      "priority": "medium",
      "planStartDate": "2025-08-01",
      "planEndDate": "2025-08-10",
      "requiredEquipment": ["EQ002"],
      "requiredOperators": ["OP002"],
      "materials": [
        {
          "materialId": "MAT002",
          "quantity": 400,
          "unit": "kg"
        }
      ]
    }
  ],
  "milestones": [
    {
      "title": "第一批次完成",
      "targetDate": "2025-08-15",
      "description": "完成第一批次产品生产"
    }
  ]
}
```

#### 响应示例

**成功响应 (201)**:
```json
{
  "success": true,
  "message": "排程创建成功",
  "data": {
    "id": "sch002",
    "scheduleNumber": "SCH-20250729-002",
    "title": "8月生产排程计划",
    "status": "draft",
    "createdAt": "2025-07-29T15:00:00.000Z"
  }
}
```

### 4. 更新排程

**PUT** `/api/schedules/:id`

更新排程信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 排程ID |

#### 请求参数

```json
{
  "title": "8月生产排程计划（修订版）",
  "description": "8月份主要产品生产排程，已调整优先级",
  "priority": "medium",
  "planEndDate": "2025-09-05"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "排程更新成功",
  "data": {
    "id": "sch002",
    "title": "8月生产排程计划（修订版）",
    "priority": "medium",
    "updatedAt": "2025-07-29T15:30:00.000Z"
  }
}
```

### 5. 删除排程

**DELETE** `/api/schedules/:id`

删除排程计划。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 排程ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "排程删除成功"
}
```

### 6. 执行排程

**POST** `/api/schedules/:id/execute`

启动排程执行。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 排程ID |

#### 请求参数

```json
{
  "executeDate": "2025-08-01",
  "notes": "按计划执行排程"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "排程执行成功",
  "data": {
    "id": "sch001",
    "status": "executing",
    "actualStartDate": "2025-08-01T08:00:00.000Z",
    "executedBy": "张主管"
  }
}
```

### 7. 获取排程统计

**GET** `/api/schedules/statistics`

获取排程管理的统计数据。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| period | string | 否 | 统计周期（week、month、quarter） |
| factory | string | 否 | 厂区筛选 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalSchedules": 25,
      "activeSchedules": 8,
      "completedSchedules": 15,
      "cancelledSchedules": 2,
      "averageProgress": 0.68
    },
    "performance": {
      "onTimeCompletion": 0.85,
      "averageEfficiency": 0.82,
      "resourceUtilization": 0.75,
      "qualityScore": 0.93
    },
    "trends": {
      "monthlyCompletion": [
        {
          "month": "2025-06",
          "completed": 12,
          "onTime": 10
        },
        {
          "month": "2025-07",
          "completed": 15,
          "onTime": 13
        }
      ]
    }
  }
}
```

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| SCHEDULE_NOT_FOUND | 404 | 排程不存在 |
| RESOURCE_CONFLICT | 409 | 资源冲突 |
| INVALID_STATUS | 400 | 无效的排程状态 |
| PERMISSION_DENIED | 403 | 权限不足 |
| EXECUTION_FAILED | 500 | 排程执行失败 |

### 排程状态说明

| 状态 | 描述 | 可执行操作 |
|------|------|------------|
| draft | 草稿 | 编辑、删除、执行 |
| active | 活跃 | 监控、暂停、完成 |
| executing | 执行中 | 监控、暂停 |
| paused | 暂停 | 恢复、取消 |
| completed | 已完成 | 查看、归档 |
| cancelled | 已取消 | 查看 |

---

**更新时间**: 2025-07-29
**版本**: v1.0
