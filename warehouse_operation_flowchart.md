# 仓库管理系统操作流程图

## 一、原物料管理流程

```mermaid
flowchart TD
    A[物料到厂] --> B{物料类型}
    B -->|常规物料| C{是否已贴码?}
    B -->|客供物料| D[客户提供物料到厂]

    C -->|是| F[供应商贴码]
    C -->|否| G[现场贴码]
    F --> H[仓库扫码]
    G --> I[填写订单信息] --> J[生成条码] --> K[粘贴条码] --> H
    H --> L[系统解析条码结构]
    L --> M{条码是否有效?}
    M -->|是| N[入库成功反馈]
    M -->|否| O[提示条码异常] --> P[重新处理条码] --> H
    N --> Q[记录入库信息]

    D --> R[标识客供来源]
    R --> S[生成客供条码<br/>供应商代码:CUST]
    S --> T[扫码入库]
    T --> U[系统记录客供属性]
    U --> V[客供入库完成]

    Q --> EE[库存更新]
    V --> EE
    EE --> FF[发料需求]
    FF --> GG[仓库人员按正常流程执行发料]
    GG --> HH[系统检查库存]
    HH --> II{库存是否充足?}
    II -->|是| JJ[执行发料]
    II -->|否| KK[提示库存不足<br/>转异常待处理]
    JJ --> LL[生成发料记录]
    LL --> MM[记录发料信息:<br/>发料单编号、产品编号、<br/>产品品名、数量、单位、<br/>时间、责任人]
    MM --> NN[更新库存]
    NN --> OO[发料完成]
    KK --> PP[填写纸质记录单<br/>后补系统补录]

    OO --> QQ[退料需求]
    QQ --> RR{退料类型}
    RR -->|车间退料| SS[车间退回多余物料]
    RR -->|退货给供应商| TT[发现问题]

    SS --> UU[仓库确认数量]
    UU --> VV[扫码原条码]
    VV --> WW[系统识别物料信息]
    WW --> XX[更新库存数量]
    XX --> YY[更新异动日期]
    YY --> ZZ[退料完成]

    TT --> AAA[选择出库操作]
    AAA --> BBB[选择出库原因:<br/>退货给供应商]
    BBB --> CCC[选择具体原因:<br/>质量问题/规格不符/<br/>数量错误/其他]
    CCC --> DDD[确定退货数量]
    DDD --> EEE[扫码出库]
    EEE --> FFF[系统记录完整出库信息:<br/>出库单号、订单代号、<br/>产品编码、产品品名、<br/>数量、单位、出库原因、<br/>时间、操作人员、供应商信息]
```

## 二、成品管理流程

```mermaid
flowchart TD
    A[成品管理开始] --> B{成品类型}
    B -->|生产成品| C[车间成品包装完成]
    B -->|返仓成品| D[已出库成品返回]

    C --> E[贴标准条码]
    E --> F[首箱扫码]
    F --> G[填写结构字段:<br/>每箱盒数、每盒PCS]
    G --> H[系统计算整箱数量<br/>如:12盒×10PCS=120PCS/箱]
    H --> I[扫码其余外箱]
    I --> J[系统根据首箱结构<br/>自动提取数量]
    J --> K{是否扫描完所有外箱?}
    K -->|否| I
    K -->|是| L[点击"是"确认]
    L --> M[二次确认弹窗]
    M --> N[确认后批量入库]
    N --> O[系统统计总数量]
    O --> P[生产入库完成]

    D --> Q[扫码返仓成品]
    Q --> R[选择返仓类型:<br/>客户退货/物流退回/<br/>订单取消]
    R --> S[质量检查确认状态]
    S --> T[入库处理]
    T --> U[更新库存]
    U --> V[标记返仓属性<br/>避免重复计算产能]
    V --> W[保留返仓历史记录]
    W --> X[返仓入库完成]

    P --> Y[库存更新]
    X --> Y
    Y --> Z[出库需求]
    Z --> AA{出库类型}
    AA -->|正常出库| BB[接收出库指令<br/>订单/出货通知]
    AA -->|部分出库| CC[订单数量不足整箱]

    BB --> DD[核对出库数量与订单要求]
    DD --> EE[扫码需出货的外箱]
    EE --> FF[系统自动累计出库数量]
    FF --> GG{是否达到订单要求?}
    GG -->|否| EE
    GG -->|是| HH[点击"是"确认出库]
    HH --> II[系统记录完整信息:<br/>出库单号、订单代号、<br/>产品编码、产品品名、<br/>数量、单位、出库日期、<br/>时间、操作人员、<br/>出库原因、目的地]
    II --> JJ[更新剩余库存]
    JJ --> KK[正常出库完成]

    CC --> LL[按实际需要外箱数量扫码]
    LL --> MM[系统计算部分出库后剩余数量]
    MM --> NN{需要拆箱?}
    NN -->|是| OO[拆箱出库重新贴码标识]
    NN -->|否| PP[保持原批次信息]
    OO --> QQ[记录拆箱出库历史]
    PP --> QQ
    QQ --> RR[记录完整出库信息]
    RR --> SS[更新库存]
    SS --> TT[部分出库完成]
```

## 三、系统功能流程

```mermaid
flowchart TD
    A[系统功能开始] --> B[扫码开始]
    B --> C[条码输入]
    C --> D[实时验证反馈]
    D --> E{条码是否重复?}
    E -->|是| F[提示:该条码已入库]
    E -->|否| G{条码结构是否正确?}
    G -->|否| H[提示:条码结构异常]
    G -->|是| I{是否属于当前订单?}
    I -->|否| J[提示:条码不匹配当前订单]
    I -->|是| K[验证通过]
    F --> L[重新扫码]
    H --> L
    J --> L
    L --> C
    K --> M[更新扫描数量统计]
    M --> N{是否扫描完所有外箱?}
    N -->|否| O[继续扫码]
    N -->|是| P[二次确认弹窗]
    O --> C
    P --> Q{确认执行?}
    Q -->|否| O
    Q -->|是| R[批量操作执行]

    R --> S[系统记录当前进度]
    S --> T[扫码进行中]
    T --> U{设备是否断电?}
    U -->|否| V[继续正常扫码]
    U -->|是| W[系统保存当前状态]
    V --> X{操作是否完成?}
    X -->|否| T
    X -->|是| Y[操作结束]
    W --> Z[设备重启]
    Z --> AA[读取保存的状态]
    AA --> BB[恢复扫描进度]
    BB --> CC[显示已扫描箱数]
    CC --> DD[继续扫码操作]
    DD --> T

    Y --> EE[检测到异常]
    EE --> FF{异常类型}
    FF -->|库存不足| GG[提示库存不足<br/>阻止出库操作]
    FF -->|条码异常| HH[记录异常信息<br/>提示重新扫码]
    FF -->|订单变更| II[暂停当前操作<br/>等待确认]
    FF -->|系统故障| JJ[启用备用方案<br/>纸质记录]
    GG --> KK[转异常待处理状态]
    HH --> LL[异常记录中心记录]
    II --> MM[人工确认处理]
    JJ --> NN[后补系统录入]
    KK --> OO[管理员处理]
    LL --> PP[提供操作建议]
    MM --> QQ[继续或取消操作]
    NN --> RR[数据同步]
```

## 四、数据流转流程

```mermaid
flowchart TD
    A[数据流转开始] --> B[入库操作]
    B --> C[库存增加]
    A --> D[出库操作]
    D --> E[库存减少]
    A --> F[退料操作]
    F --> G[库存调整]
    C --> H[实时库存更新]
    E --> H
    G --> H
    H --> I[库存预警检查]
    I --> J{低于安全库存?}
    J -->|是| K[触发预警提醒]
    J -->|否| L[正常状态]
    K --> M[预警日志记录]
    L --> N[继续监控]
    M --> O[管理员处理]

    A --> P[条码生成]
    P --> Q[入库记录]
    Q --> R[库存记录]
    R --> S[出库记录]
    S --> T[追溯链条完整]
    A --> U[退货记录]
    U --> V[补货记录]
    V --> W[质量追溯]
    Q --> X[批次信息]
    X --> Y[质量信息]
    Y --> Z[供应商信息]
    Z --> AA[完整追溯体系]
```
