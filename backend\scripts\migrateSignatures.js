/**
 * 签名迁移脚本
 * 将现有的签名文件转换为Base64并更新数据库
 */

const path = require('path');
const fs = require('fs');
const userService = require('../services/userService');
const signatureConverter = require('../utils/signatureConverter');
const logger = require('../utils/logger');

/**
 * 迁移所有用户的签名文件到Base64
 */
async function migrateAllSignatures() {
    try {
        logger.info('开始迁移签名文件到Base64...');
        
        // 获取所有用户
        const users = userService.getAllUsers();
        const signaturesDir = path.join(__dirname, '../uploads/signatures');
        
        if (!fs.existsSync(signaturesDir)) {
            logger.warn('签名目录不存在，跳过迁移');
            return;
        }

        let migratedCount = 0;
        let skippedCount = 0;
        let errorCount = 0;

        for (const user of users) {
            try {
                // 如果用户已经有Base64签名，跳过
                if (user.signatureBase64) {
                    logger.info(`用户 ${user.usercode} 已有Base64签名，跳过`);
                    skippedCount++;
                    continue;
                }

                // 使用多重保障机制获取签名
                const signatureBase64 = await signatureConverter.getSignatureWithFallback(user, signaturesDir);
                
                if (signatureBase64) {
                    // 更新用户数据
                    user.signatureBase64 = signatureBase64;
                    user.hasSignature = true;
                    
                    // 保存到数据库
                    userService.writeUsers([user]);
                    
                    logger.info(`成功迁移用户 ${user.usercode} 的签名`);
                    migratedCount++;
                } else {
                    logger.info(`用户 ${user.usercode} 没有找到签名文件`);
                    skippedCount++;
                }
            } catch (error) {
                logger.error(`迁移用户 ${user.usercode} 签名失败:`, error);
                errorCount++;
            }
        }

        logger.info(`签名迁移完成: 成功 ${migratedCount}, 跳过 ${skippedCount}, 错误 ${errorCount}`);
        
        return {
            migrated: migratedCount,
            skipped: skippedCount,
            errors: errorCount
        };
    } catch (error) {
        logger.error('签名迁移失败:', error);
        throw error;
    }
}

/**
 * 验证迁移结果
 */
async function validateMigration() {
    try {
        logger.info('开始验证迁移结果...');
        
        const users = userService.getAllUsers();
        let validCount = 0;
        let invalidCount = 0;

        for (const user of users) {
            if (user.hasSignature) {
                if (user.signatureBase64 && signatureConverter.validateBase64Image(user.signatureBase64)) {
                    validCount++;
                    logger.info(`用户 ${user.usercode} 签名验证通过`);
                } else {
                    invalidCount++;
                    logger.warn(`用户 ${user.usercode} 签名验证失败`);
                }
            }
        }

        logger.info(`验证完成: 有效 ${validCount}, 无效 ${invalidCount}`);
        
        return {
            valid: validCount,
            invalid: invalidCount
        };
    } catch (error) {
        logger.error('验证迁移结果失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    (async () => {
        try {
            console.log('=== 开始签名迁移 ===');
            
            // 执行迁移
            const migrationResult = await migrateAllSignatures();
            console.log('迁移结果:', migrationResult);
            
            // 验证迁移
            const validationResult = await validateMigration();
            console.log('验证结果:', validationResult);
            
            console.log('=== 签名迁移完成 ===');
            process.exit(0);
        } catch (error) {
            console.error('签名迁移失败:', error);
            process.exit(1);
        }
    })();
}

module.exports = {
    migrateAllSignatures,
    validateMigration
};
