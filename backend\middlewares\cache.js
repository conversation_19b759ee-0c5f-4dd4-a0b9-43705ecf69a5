/**
 * 缓存中间件
 * 提供内存缓存和Redis缓存支持
 */

const logger = require('../utils/logger');

// 内存缓存配置
const CACHE_CONFIG = {
    // 缓存时间配置（毫秒）
    TTL: {
        SHORT: 30 * 1000,      // 30秒
        MEDIUM: 5 * 60 * 1000, // 5分钟
        LONG: 30 * 60 * 1000,  // 30分钟
        VERY_LONG: 2 * 60 * 60 * 1000 // 2小时
    },
    // 最大缓存条目数
    MAX_ENTRIES: 1000,
    // 清理间隔
    CLEANUP_INTERVAL: 5 * 60 * 1000 // 5分钟
};

/**
 * 内存缓存实现
 */
class MemoryCache {
    constructor() {
        this.cache = new Map();
        this.timers = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0
        };
        
        // 定期清理过期缓存
        this.startCleanupTimer();
    }

    /**
     * 生成缓存键
     */
    generateKey(req) {
        const { method, originalUrl, user } = req;
        const userId = user ? user.id : 'anonymous';
        return `${method}:${originalUrl}:${userId}`;
    }

    /**
     * 设置缓存
     */
    set(key, data, ttl = CACHE_CONFIG.TTL.MEDIUM) {
        // 如果缓存已满，删除最旧的条目
        if (this.cache.size >= CACHE_CONFIG.MAX_ENTRIES) {
            const firstKey = this.cache.keys().next().value;
            this.delete(firstKey);
        }

        // 设置缓存数据
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl,
            accessCount: 0
        });

        // 设置过期定时器
        const timer = setTimeout(() => {
            this.delete(key);
        }, ttl);
        
        this.timers.set(key, timer);
        this.stats.sets++;

        logger.debug(`缓存设置: ${key}`, { ttl, size: this.cache.size });
    }

    /**
     * 获取缓存
     */
    get(key) {
        const cached = this.cache.get(key);
        if (!cached) {
            this.stats.misses++;
            return null;
        }

        // 检查是否过期
        if (Date.now() - cached.timestamp > cached.ttl) {
            this.delete(key);
            this.stats.misses++;
            return null;
        }

        // 更新访问计数
        cached.accessCount++;
        this.stats.hits++;

        logger.debug(`缓存命中: ${key}`, { 
            age: Date.now() - cached.timestamp,
            accessCount: cached.accessCount 
        });

        return cached.data;
    }

    /**
     * 删除缓存
     */
    delete(key) {
        const deleted = this.cache.delete(key);
        const timer = this.timers.get(key);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(key);
        }
        if (deleted) {
            this.stats.deletes++;
        }
        return deleted;
    }

    /**
     * 清空缓存
     */
    clear() {
        this.cache.clear();
        this.timers.forEach(timer => clearTimeout(timer));
        this.timers.clear();
        logger.info('缓存已清空');
    }

    /**
     * 获取缓存统计信息
     */
    getStats() {
        const hitRate = this.stats.hits + this.stats.misses > 0 
            ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
            : 0;

        return {
            size: this.cache.size,
            maxSize: CACHE_CONFIG.MAX_ENTRIES,
            hitRate: `${hitRate}%`,
            ...this.stats
        };
    }

    /**
     * 启动清理定时器
     */
    startCleanupTimer() {
        setInterval(() => {
            this.cleanup();
        }, CACHE_CONFIG.CLEANUP_INTERVAL);
    }

    /**
     * 清理过期缓存
     */
    cleanup() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, cached] of this.cache.entries()) {
            if (now - cached.timestamp > cached.ttl) {
                this.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            logger.debug(`清理过期缓存: ${cleanedCount} 个条目`);
        }
    }

    /**
     * 获取热点数据
     */
    getHotKeys(limit = 10) {
        return Array.from(this.cache.entries())
            .sort((a, b) => b[1].accessCount - a[1].accessCount)
            .slice(0, limit)
            .map(([key, data]) => ({
                key,
                accessCount: data.accessCount,
                age: Date.now() - data.timestamp
            }));
    }
}

// 创建全局缓存实例
const memoryCache = new MemoryCache();

/**
 * 缓存中间件工厂函数
 */
function createCacheMiddleware(options = {}) {
    const {
        ttl = CACHE_CONFIG.TTL.MEDIUM,
        keyGenerator = null,
        condition = null,
        skipMethods = ['POST', 'PUT', 'DELETE', 'PATCH']
    } = options;

    return (req, res, next) => {
        // 跳过不需要缓存的方法
        if (skipMethods.includes(req.method)) {
            return next();
        }

        // 检查缓存条件
        if (condition && !condition(req)) {
            return next();
        }

        // 生成缓存键
        const cacheKey = keyGenerator ? keyGenerator(req) : memoryCache.generateKey(req);

        // 尝试从缓存获取
        const cached = memoryCache.get(cacheKey);
        if (cached) {
            res.setHeader('X-Cache', 'HIT');
            res.setHeader('X-Cache-Key', cacheKey);
            return res.json(cached);
        }

        // 缓存未命中，继续处理请求
        res.setHeader('X-Cache', 'MISS');
        res.setHeader('X-Cache-Key', cacheKey);

        // 保存原始的 json 方法
        const originalJson = res.json;

        // 重写 json 方法以缓存响应
        res.json = function(data) {
            // 只缓存成功的响应
            if (res.statusCode >= 200 && res.statusCode < 300) {
                memoryCache.set(cacheKey, data, ttl);
            }
            
            // 调用原始的 json 方法
            return originalJson.call(this, data);
        };

        next();
    };
}

/**
 * 缓存失效中间件
 */
function invalidateCache(patterns = []) {
    return (req, res, next) => {
        // 保存原始的 json 方法
        const originalJson = res.json;

        // 重写 json 方法以在成功响应后清理缓存
        res.json = function(data) {
            // 只在成功响应后清理缓存
            if (res.statusCode >= 200 && res.statusCode < 300) {
                patterns.forEach(pattern => {
                    if (typeof pattern === 'string') {
                        // 简单字符串匹配
                        for (const key of memoryCache.cache.keys()) {
                            if (key.includes(pattern)) {
                                memoryCache.delete(key);
                            }
                        }
                    } else if (pattern instanceof RegExp) {
                        // 正则表达式匹配
                        for (const key of memoryCache.cache.keys()) {
                            if (pattern.test(key)) {
                                memoryCache.delete(key);
                            }
                        }
                    }
                });
            }
            
            // 调用原始的 json 方法
            return originalJson.call(this, data);
        };

        next();
    };
}

module.exports = {
    memoryCache,
    createCacheMiddleware,
    invalidateCache,
    CACHE_CONFIG
};
