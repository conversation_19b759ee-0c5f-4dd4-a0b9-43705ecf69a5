<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能排程 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/scheduling/common.css">

</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">智能排程</h1>
                            <p class="text-gray-600 mt-1">基于AI算法的智能生产排程系统</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button @click="showOrderModal = true"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                创建排程方案
                            </button>
                        </div>
                    </div>
                </header>

                <!-- 统计卡片 -->
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">总订单数</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ statistics.totalOrders || 0 }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">已排程</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ statistics.scheduledOrders || 0 }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-yellow-100 rounded-lg">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">平均交期</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ statistics.averageLeadTime || 0 }}天</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">设备利用率</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ Math.round((statistics.resourceUtilization?.equipment || 0) * 100) }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 排程方案列表 -->
                <div class="px-6 pb-6">
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">排程方案</h2>
                        </div>

                        <div v-if="loading" class="p-8 text-center">
                            <div class="loading-spinner mx-auto"></div>
                            <p class="mt-4 text-gray-600">加载中...</p>
                        </div>

                        <div v-else-if="schedulePlans.length === 0" class="p-8 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无排程方案</h3>
                            <p class="mt-1 text-sm text-gray-500">开始创建您的第一个智能排程方案</p>
                        </div>

                        <div v-else class="p-6">
                            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                                <div v-for="plan in schedulePlans" :key="plan.id"
                                     :class="['plan-card bg-white border rounded-lg p-6 cursor-pointer', 
                                             plan.selected ? 'selected' : '']"
                                     @click="selectPlan(plan)">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-medium text-gray-900">{{ plan.name }}</h3>
                                        <span :class="['px-2 py-1 text-xs font-medium rounded-full', 
                                                      getRiskClass(plan.riskAssessment?.level)]">
                                            {{ getRiskText(plan.riskAssessment?.level) }}
                                        </span>
                                    </div>

                                    <div class="space-y-3">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">预计交期:</span>
                                            <span class="text-sm font-medium">{{ formatDate(plan.finalPrediction?.deliveryDate) }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">按时概率:</span>
                                            <span class="text-sm font-medium">{{ Math.round((plan.finalPrediction?.onTimeProb || 0) * 100) }}%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">效率评分:</span>
                                            <span class="text-sm font-medium">{{ (plan.metrics?.efficiency || 0).toFixed(2) }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">成本估算:</span>
                                            <span class="text-sm font-medium">¥{{ (plan.metrics?.cost || 0).toLocaleString() }}</span>
                                        </div>
                                    </div>

                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                        <p class="text-xs text-gray-500">{{ plan.description }}</p>
                                    </div>

                                    <div v-if="plan.selected" class="mt-4 flex space-x-2">
                                        <button @click.stop="viewPlanDetails(plan)"
                                                class="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                            查看详情
                                        </button>
                                        <button @click.stop="confirmPlan(plan)"
                                                class="flex-1 px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700">
                                            确认方案
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- 方案对比按钮 -->
                            <div v-if="schedulePlans.length > 1" class="mt-6 text-center">
                                <button @click="compareAllPlans"
                                        class="px-6 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 font-medium">
                                    对比所有方案
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建订单模态框 -->
        <div v-if="showOrderModal" class="modal-overlay" @click.self="closeOrderModal">
            <div class="modal-content w-full max-w-2xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">创建排程方案</h2>
                    <button @click="closeOrderModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="createSchedulePlans">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">订单ID *</label>
                            <input v-model="orderForm.id"
                                   type="text"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">产品ID *</label>
                            <input v-model="orderForm.productId"
                                   type="text"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">订单数量 *</label>
                            <input v-model.number="orderForm.quantity"
                                   type="number"
                                   min="1"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">要求交期 *</label>
                            <input v-model="orderForm.requiredDate"
                                   type="date"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                            <select v-model="orderForm.priority"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="low">低</option>
                                <option value="normal">普通</option>
                                <option value="high">高</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">客户名称</label>
                            <input v-model="orderForm.customerName"
                                   type="text"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="mt-6 flex items-center justify-end space-x-4">
                        <button type="button"
                                @click="closeOrderModal"
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                :disabled="submitting"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                            {{ submitting ? '生成中...' : '生成方案' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/scheduling/intelligent.js"></script>
</body>
</html>
