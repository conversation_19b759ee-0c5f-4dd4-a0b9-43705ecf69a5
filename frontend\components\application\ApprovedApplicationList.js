/**
 * 已审核申请列表组件
 * 显示已审核的申请
 */

import { PRIORITIES } from '../../scripts/config.js';
import { getApprovedApplications } from '../../scripts/api/application.js';
import UnifiedPagination from '../common/UnifiedPagination.js';
import ContentViewModal from '../common/ContentViewModal.js';

export default {
    components: {
        UnifiedPagination,
        ContentViewModal
    },
    emits: ['view-detail'],
    setup(props, { emit }) {
        const { ref, computed, onMounted } = Vue;

        // 申请列表
        const applications = ref([]);
        const isLoading = ref(true);
        const currentUser = ref(null);

        // 内容查看弹出框
        const showContentModal = ref(false);
        const contentModalData = ref({
            title: '',
            content: ''
        });
        const searchTerm = ref('');
        const showHistoryModal = ref(false);
        const currentHistory = ref([]);

        // 分页相关
        const currentPage = ref(1);
        const itemsPerPage = ref(10);

        // 过滤后的申请列表（用于搜索）
        const searchFilteredApplications = computed(() => {
            if (!searchTerm.value) return applications.value;

            const term = searchTerm.value.toLowerCase();
            return applications.value.filter(app =>
                app.applicant.toLowerCase().includes(term) ||
                app.department.toLowerCase().includes(term) ||
                app.content.toLowerCase().includes(term) ||
                (app.applicationNumber ? app.applicationNumber.toLowerCase().includes(term) : false) ||
                app.id.toLowerCase().includes(term)
            );
        });

        // 分页后的申请列表（最终显示的数据）
        const filteredApplications = computed(() => {
            const filtered = searchFilteredApplications.value;
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return filtered.slice(start, end);
        });

        // 总记录数（用于分页组件）
        const totalItems = computed(() => {
            return searchFilteredApplications.value.length;
        });

        // 监听搜索变化，重置页码
        const { watch } = Vue;
        watch(searchTerm, () => {
            currentPage.value = 1;
        });

        // 初始化
        onMounted(async () => {
            try {
                // 获取当前用户信息
                const { getCurrentUser } = await import('../../scripts/api/auth.js');
                currentUser.value = await getCurrentUser();
            } catch (error) {
                console.error('获取用户信息失败:', error);
            }
            loadApplications();
        });

        // 加载已审核申请列表
        async function loadApplications() {
            try {
                isLoading.value = true;
                applications.value = await getApprovedApplications();
            } catch (error) {
                console.error('加载已审核申请列表失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    sessionStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('加载已审核申请列表失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                isLoading.value = false;
            }
        }

        // 查看详情
        function viewDetail(id) {
            emit('view-detail', id);
        }

        // 查看申请内容
        function viewContent(application) {
            contentModalData.value = {
                title: `申请内容 - ${application.applicant}`,
                content: application.content || '暂无申请内容'
            };
            showContentModal.value = true;
        }

        // 关闭内容查看弹出框
        function closeContentModal() {
            showContentModal.value = false;
        }

        // 分页处理
        function handlePageChange(page) {
            currentPage.value = page;
        }

        // 查看审批历史
        function viewHistory(app) {
            if (app.approvalHistory && app.approvalHistory.length > 0) {
                currentHistory.value = app.approvalHistory;
                showHistoryModal.value = true;
            } else {
                alert('该申请暂无审批历史记录');
            }
        }

        // 关闭历史模态框
        function closeHistoryModal() {
            showHistoryModal.value = false;
        }

        // 获取优先级样式
        function getPriorityClass(priority) {
            const priorityStyles = {
                'high': 'bg-red-100 text-red-800',
                'medium': 'bg-yellow-100 text-yellow-800',
                'normal': 'bg-green-100 text-green-800'
            };
            return priorityStyles[priority] || 'bg-gray-100 text-gray-800';
        }

        // 获取优先级圆点样式
        function getPriorityDotClass(priority) {
            const dotStyles = {
                'high': 'bg-red-500',
                'medium': 'bg-yellow-500',
                'normal': 'bg-green-500'
            };
            return dotStyles[priority] || 'bg-gray-500';
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityTexts = {
                'high': '紧急',
                'medium': '中等',
                'normal': '普通'
            };
            return priorityTexts[priority] || '未知';
        }

        // 获取状态样式
        function getStatusClass(status) {
            return status === 'approved'
                ? 'bg-green-100 text-green-800'
                : status === 'rejected'
                ? 'bg-red-100 text-red-800'
                : status === 'pending'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-800';
        }

        // 获取状态文本
        function getStatusText(status) {
            return status === 'approved'
                ? '已通过'
                : status === 'rejected'
                ? '已拒绝'
                : status === 'pending'
                ? '审批中'
                : '未知';
        }

        // 获取用户对申请的审批状态
        function getUserApprovalStatus(application) {
            if (!application.approvalHistory || application.approvalHistory.length === 0) {
                return { action: 'none', text: '未审批', class: 'bg-gray-100 text-gray-800' };
            }

            // 查找当前用户的审批记录
            const userApproval = application.approvalHistory.find(history =>
                history.approverId === currentUser.value?.id
            );

            if (userApproval) {
                return userApproval.action === 'approve'
                    ? { action: 'approve', text: '我已通过', class: 'bg-green-100 text-green-800' }
                    : { action: 'reject', text: '我已拒绝', class: 'bg-red-100 text-red-800' };
            }

            return { action: 'none', text: '未审批', class: 'bg-gray-100 text-gray-800' };
        }

        // 获取申请类型文本
        function getTypeText(type) {
            return type === 'standard' ? '标准申请' : '其他申请';
        }

        // 获取审批阶段文本
        function getStageText(stage) {
            switch (stage) {
                case 'factory_manager':
                    return '厂长审批';
                case 'director':
                    return '总监审批';
                case 'manager':
                    return '经理审批';
                case 'ceo':
                    return 'CEO审批';
                case 'completed':
                    return '已完成';
                case 'rejected':
                    return '已拒绝';
                default:
                    return '未知';
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        return {
            applications,
            searchTerm,
            filteredApplications,
            isLoading,
            showHistoryModal,
            currentHistory,
            // 分页相关
            currentPage,
            itemsPerPage,
            totalItems,
            handlePageChange,
            viewDetail,
            viewContent,
            closeContentModal,
            showContentModal,
            contentModalData,
            viewHistory,
            closeHistoryModal,
            getPriorityClass,
            getPriorityDotClass,
            getPriorityText,
            getStatusClass,
            getStatusText,
            getUserApprovalStatus,
            getTypeText,
            getStageText,
            formatDateTime
        };
    },
    template: `
        <div>
            <!-- 搜索和筛选区域 -->
            <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                <div class="flex flex-col sm:flex-row gap-4">
                    <!-- 搜索框 -->
                    <div class="flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" v-model="searchTerm"
                                   placeholder="搜索申请编号、申请人、部门或内容..."
                                   class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-150 ease-in-out">
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            <span>共 {{ totalItems }} 条记录</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="text-center py-12">
                <div class="inline-block w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                <p class="mt-4 text-gray-500">正在加载已审核申请...</p>
            </div>

            <!-- 空状态 -->
            <div v-else-if="filteredApplications.length === 0" class="text-center py-12">
                <div class="mx-auto h-24 w-24 text-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="mt-4 text-lg font-medium text-gray-900">暂无已审核申请</h3>
                <p class="mt-2 text-gray-500">当前没有已审核的申请记录</p>
            </div>

            <!-- 申请列表 -->
            <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    申请信息
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    申请人
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    申请内容
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    优先级
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    审核结果
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    完成时间
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="app in filteredApplications" :key="app.id"
                                class="hover:bg-gray-50 transition-colors duration-150 ease-in-out">
                                <!-- 申请信息 -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-col">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ app.applicationNumber || app.id.substring(0, 8) }}
                                        </div>
                                        <div class="text-sm text-gray-500 mt-1">
                                            {{ getTypeText(app.type) }}
                                        </div>
                                        <div class="text-xs text-gray-400 mt-1">
                                            {{ app.date }}
                                        </div>
                                    </div>
                                </td>

                                <!-- 申请人信息 -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                <span class="text-sm font-medium text-blue-600">
                                                    {{ app.applicant.charAt(0) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ app.applicant }}</div>
                                            <div class="text-sm text-gray-500">{{ app.department }}</div>
                                        </div>
                                    </div>
                                </td>

                                <!-- 申请内容 -->
                                <td class="px-6 py-4">
                                    <div class="max-w-xs">
                                        <button @click="viewContent(app)"
                                                class="text-left w-full truncate text-gray-900 hover:text-gray-700 focus:outline-none cursor-pointer transition-colors duration-200"
                                                :title="app.content ? '点击查看完整内容' : '暂无内容'">
                                            {{ app.content ? (app.content.length > 30 ? app.content.substring(0, 30) + '...' : app.content) : '-' }}
                                        </button>
                                    </div>
                                </td>

                                <!-- 优先级 -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getPriorityClass(app.priority)]">
                                        <span class="w-1.5 h-1.5 mr-1.5 rounded-full" :class="getPriorityDotClass(app.priority)"></span>
                                        {{ getPriorityText(app.priority) }}
                                    </span>
                                </td>

                                <!-- 审核结果 -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-col space-y-1">
                                        <!-- 用户的审批状态 -->
                                        <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getUserApprovalStatus(app).class]">
                                            <svg v-if="getUserApprovalStatus(app).action === 'approve'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg v-else-if="getUserApprovalStatus(app).action === 'reject'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ getUserApprovalStatus(app).text }}
                                        </span>
                                        <!-- 申请整体状态 -->
                                        <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getStatusClass(app.status)]">
                                            <svg v-if="app.status === 'approved'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg v-else-if="app.status === 'rejected'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg v-else-if="app.status === 'pending'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ getStatusText(app.status) }}
                                        </span>
                                    </div>
                                </td>

                                <!-- 完成时间 -->
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex flex-col">
                                        <span>{{ formatDateTime(app.completedAt || app.updatedAt) }}</span>
                                        <span class="text-xs text-gray-400 mt-1">
                                            {{ getStageText(app.currentStage) }}
                                        </span>
                                    </div>
                                </td>

                                <!-- 操作按钮 -->
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <!-- 查看详情按钮 -->
                                        <button @click="viewDetail(app.id)"
                                                class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:text-blue-700 hover:border-blue-300 transition-all duration-200"
                                                title="查看详情">
                                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            查看详情
                                        </button>

                                        <!-- 审批历史按钮 -->
                                        <button @click="viewHistory(app)"
                                                class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-indigo-50 text-indigo-600 hover:bg-indigo-100 hover:text-indigo-700 transition-all duration-200 group"
                                                title="查看审批历史">
                                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页组件 -->
                <div class="bg-white px-4 py-3 border-t border-gray-200">
                    <unified-pagination
                        :current-page="currentPage"
                        :total-items="totalItems"
                        :items-per-page="itemsPerPage"
                        @page-change="handlePageChange">
                    </unified-pagination>
                </div>
            </div>

            <!-- 审批历史模态框 -->
            <div v-if="showHistoryModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                    <!-- 模态框头部 -->
                    <div class="flex justify-between items-center p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900">审批历史</h3>
                                <p class="text-sm text-gray-600 mt-1">查看申请的完整审批流程</p>
                            </div>
                        </div>
                        <button @click="closeHistoryModal"
                                class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-2 hover:bg-gray-100 rounded-full">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- 模态框内容 -->
                    <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                        <div v-if="currentHistory.length === 0" class="text-center py-8">
                            <div class="mx-auto h-16 w-16 text-gray-400">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="mt-4 text-lg font-medium text-gray-900">暂无审批历史</h3>
                            <p class="mt-2 text-gray-500">该申请暂无审批历史记录</p>
                        </div>

                        <div v-else class="space-y-4">
                            <!-- 时间线样式的审批历史 -->
                            <div v-for="(history, index) in currentHistory" :key="index" class="relative">
                                <!-- 时间线连接线 -->
                                <div v-if="index < currentHistory.length - 1"
                                     class="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"></div>

                                <!-- 审批记录卡片 -->
                                <div class="flex items-start space-x-4">
                                    <!-- 时间线圆点 -->
                                    <div class="flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center"
                                         :class="history.action === 'approve' ? 'bg-green-100' : 'bg-red-100'">
                                        <svg v-if="history.action === 'approve'" class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg v-else class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>

                                    <!-- 审批信息卡片 -->
                                    <div class="flex-1 bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center space-x-3">
                                                <h4 class="text-lg font-medium text-gray-900">{{ getStageText(history.stage) }}</h4>
                                                <span :class="[
                                                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                    history.action === 'approve' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                ]">
                                                    <svg v-if="history.action === 'approve'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <svg v-else class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ history.action === 'approve' ? '通过' : '拒绝' }}
                                                </span>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ formatDateTime(history.timestamp) }}
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                            <div>
                                                <span class="text-sm font-medium text-gray-500">审批人：</span>
                                                <span class="text-sm text-gray-900">{{ history.approverName }}</span>
                                            </div>
                                            <div>
                                                <span class="text-sm font-medium text-gray-500">角色：</span>
                                                <span class="text-sm text-gray-900">{{ history.approverRole }}</span>
                                            </div>
                                        </div>

                                        <div v-if="history.comment" class="mt-3 p-3 bg-gray-50 rounded-md">
                                            <span class="text-sm font-medium text-gray-500">审批意见：</span>
                                            <p class="text-sm text-gray-900 mt-1">{{ history.comment }}</p>
                                        </div>
                                        <div v-else class="mt-3 p-3 bg-gray-50 rounded-md">
                                            <span class="text-sm text-gray-500 italic">无审批意见</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模态框底部 -->
                    <div class="flex justify-end items-center p-6 border-t border-gray-200 bg-gray-50">
                        <button
                            @click="closeHistoryModal"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            关闭
                        </button>
                    </div>
                </div>
            </div>

            <!-- 内容查看弹出框 -->
            <content-view-modal
                :visible="showContentModal"
                :title="contentModalData.title"
                :content="contentModalData.content"
                @close="closeContentModal"
            />
        </div>
    `
};
