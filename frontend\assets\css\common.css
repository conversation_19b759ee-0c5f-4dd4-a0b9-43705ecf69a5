/* 全局通用样式文件 - Common CSS */

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
    display: none;
}

/* 侧边栏按钮过渡效果 */
.sidebar-btn {
    transition: background-color 0.2s;
}

/* 加载中覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #F3F4F6;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* 加载中旋转动画 */
.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e5e7eb;
    border-top: 5px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
