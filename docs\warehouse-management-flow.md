# 仓库管理系统业务流程图

## 系统概述

本仓库管理系统基于条码技术，通过手机扫码实现物品的精确进出库管控。系统核心功能包括订单管理、包装计算、条码生成、库存管理和移动端扫码操作。

## 主要业务流程

### 1. 完整业务流程图

```mermaid
flowchart TD
    A[开始] --> B[订单录入]
    B --> C{产品型号存在?}
    C -->|否| D[创建产品信息]
    C -->|是| E[获取产品包装信息]
    D --> E
    E --> F[计算包装数量]
    F --> G[生成订单详情]
    G --> H[生成条码]
    H --> I[打印条码标签]
    I --> J[等待入库]
    
    J --> K[扫码入库]
    K --> L{条码验证}
    L -->|失败| M[显示错误信息]
    L -->|成功| N[更新库存]
    M --> K
    N --> O[分配库位]
    O --> P[入库完成]
    
    P --> Q[库存管理]
    Q --> R{出库申请?}
    R -->|是| S[扫码出库]
    R -->|否| Q
    
    S --> T{出库验证}
    T -->|失败| U[显示错误信息]
    T -->|成功| V[扣减库存]
    U --> S
    V --> W[更新订单状态]
    W --> X[出库完成]
    X --> Y[结束]
    
    style A fill:#e1f5fe
    style Y fill:#e8f5e8
    style M fill:#ffebee
    style U fill:#ffebee
```

### 2. 订单处理详细流程

```mermaid
flowchart TD
    A1[订单录入] --> B1[输入客户信息]
    B1 --> C1[输入订单号]
    C1 --> D1[输入生产型号]
    D1 --> E1[输入订单数量]
    E1 --> F1[查询产品信息]
    
    F1 --> G1{产品存在?}
    G1 -->|否| H1[创建新产品]
    G1 -->|是| I1[获取包装规格]
    
    H1 --> I2[设置最小包装数量]
    I2 --> J2[设置每箱包装数量]
    J2 --> K2[设置每栈板箱数]
    K2 --> I1
    
    I1 --> L1[计算所需盒数]
    L1 --> M1[计算所需箱数]
    M1 --> N1[计算所需栈板数]
    N1 --> O1[生成订单明细]
    O1 --> P1[订单处理完成]
    
    style H1 fill:#fff3e0
    style P1 fill:#e8f5e8
```

### 3. 条码生成流程

```mermaid
flowchart TD
    A2[开始生成条码] --> B2[生成产品条码]
    B2 --> C2[生成箱条码]
    C2 --> D2[生成栈板条码]
    D2 --> E2[建立条码关联关系]
    
    E2 --> F2[产品条码 → 箱条码]
    F2 --> G2[箱条码 → 栈板条码]
    G2 --> H2[栈板条码 → 订单号]
    H2 --> I2[保存条码数据]
    I2 --> J2[生成打印任务]
    J2 --> K2[条码生成完成]
    
    style A2 fill:#e1f5fe
    style K2 fill:#e8f5e8
```

### 4. 移动端扫码流程

```mermaid
flowchart TD
    A3[打开扫码应用] --> B3[选择操作类型]
    B3 --> C3{操作类型}
    C3 -->|入库| D3[扫描条码]
    C3 -->|出库| E3[扫描条码]
    C3 -->|盘点| F3[扫描条码]
    
    D3 --> G3[验证入库权限]
    E3 --> H3[验证出库权限]
    F3 --> I3[验证盘点权限]
    
    G3 --> J3{权限验证}
    H3 --> J3
    I3 --> J3
    
    J3 -->|失败| K3[显示权限错误]
    J3 -->|成功| L3[解析条码信息]
    
    K3 --> B3
    L3 --> M3{条码有效?}
    M3 -->|否| N3[显示条码错误]
    M3 -->|是| O3[执行相应操作]
    
    N3 --> D3
    O3 --> P3[更新数据库]
    P3 --> Q3[显示操作结果]
    Q3 --> R3[操作完成]
    
    style K3 fill:#ffebee
    style N3 fill:#ffebee
    style R3 fill:#e8f5e8
```

## 核心业务逻辑说明

### 包装计算逻辑

1. **输入参数**：
   - 订单数量（pieces）
   - 最小包装数量（min_pack）
   - 每箱包装数量（box_pack）
   - 每栈板箱数（pallet_boxes）

2. **计算公式**：
   ```
   所需盒数 = ceil(订单数量 / 最小包装数量)
   所需箱数 = ceil(所需盒数 / 每箱包装数量)
   所需栈板数 = ceil(所需箱数 / 每栈板箱数)
   ```

### 条码编码规则

1. **产品条码**：`P{产品型号}{序号8位}`
2. **箱条码**：`B{订单号}{箱序号4位}`
3. **栈板条码**：`T{订单号}{栈板序号4位}`

### 库存状态管理

- **待入库**：条码已生成，等待扫码入库
- **在库**：已完成入库，可用于出库
- **预留**：已分配给出库订单，暂不可用
- **已出库**：已完成出库操作

## 系统优势

1. **精确管控**：通过条码实现单品级别的精确追踪
2. **移动便捷**：手机扫码操作，提高作业效率
3. **实时更新**：库存状态实时同步，避免数据滞后
4. **层级管理**：支持产品-箱-栈板的多层级管理
5. **异常处理**：完善的验证机制，确保操作准确性
