/**
 * 文件管理通知页面
 * 显示和管理文件变更通知
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';
import NotificationList from '../../../components/file-management/NotificationList.js';

createStandardApp({
    components: {
        Sidebar,
        NotificationList
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        return {
            sidebarOpen
        };
    },
    requiredPermissions: ['file_confirm'],
    onUserLoaded: async (user) => {
        console.log('文件管理通知页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
