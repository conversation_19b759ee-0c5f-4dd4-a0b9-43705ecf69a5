/**
 * 生产排程数据模型
 * 定义排程相关的数据结构和验证规则
 */

/**
 * 排程状态枚举
 */
const SCHEDULE_STATUS = {
    PLANNED: 'planned',
    IN_PROGRESS: 'in_progress',
    PAUSED: 'paused',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

/**
 * 优先级枚举
 */
const PRIORITY_LEVELS = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent'
};

/**
 * 排程数据模型
 */
class ScheduleModel {
    constructor(data = {}) {
        this.id = data.id || null;
        this.title = data.title || '';
        this.productId = data.productId || '';
        this.productName = data.productName || '';
        this.quantity = data.quantity || 0;
        this.startTime = data.startTime || null;
        this.endTime = data.endTime || null;
        this.status = data.status || SCHEDULE_STATUS.PLANNED;
        this.priority = data.priority || PRIORITY_LEVELS.MEDIUM;
        this.assignedEquipment = data.assignedEquipment || [];
        this.assignedPersonnel = data.assignedPersonnel || [];
        this.requiredMaterials = data.requiredMaterials || [];
        this.progress = data.progress || 0;
        this.notes = data.notes || '';
        this.createdBy = data.createdBy || '';
        this.createdAt = data.createdAt || new Date().toISOString();
        this.updatedAt = data.updatedAt || new Date().toISOString();
    }

    /**
     * 验证排程数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.title || this.title.trim().length === 0) {
            errors.push('排程标题不能为空');
        }

        if (!this.productId || this.productId.trim().length === 0) {
            errors.push('产品ID不能为空');
        }

        if (!this.productName || this.productName.trim().length === 0) {
            errors.push('产品名称不能为空');
        }

        if (!this.quantity || this.quantity <= 0) {
            errors.push('生产数量必须大于0');
        }

        if (!this.startTime) {
            errors.push('开始时间不能为空');
        }

        if (!this.endTime) {
            errors.push('结束时间不能为空');
        }

        if (this.startTime && this.endTime && new Date(this.startTime) >= new Date(this.endTime)) {
            errors.push('结束时间必须晚于开始时间');
        }

        if (!Object.values(SCHEDULE_STATUS).includes(this.status)) {
            errors.push('无效的排程状态');
        }

        if (!Object.values(PRIORITY_LEVELS).includes(this.priority)) {
            errors.push('无效的优先级');
        }

        if (this.progress < 0 || this.progress > 100) {
            errors.push('进度值必须在0-100之间');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 转换为数据库存储格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            title: this.title,
            product_id: this.productId,
            product_name: this.productName,
            quantity: this.quantity,
            start_time: this.startTime,
            end_time: this.endTime,
            status: this.status,
            priority: this.priority,
            assigned_equipment: JSON.stringify(this.assignedEquipment),
            assigned_personnel: JSON.stringify(this.assignedPersonnel),
            required_materials: JSON.stringify(this.requiredMaterials),
            progress: this.progress,
            notes: this.notes,
            created_by: this.createdBy,
            created_at: this.createdAt,
            updated_at: this.updatedAt
        };
    }

    /**
     * 从数据库格式创建模型实例
     * @param {Object} dbData 数据库数据
     * @returns {ScheduleModel} 模型实例
     */
    static fromDatabase(dbData) {
        return new ScheduleModel({
            id: dbData.id,
            title: dbData.title,
            productId: dbData.product_id,
            productName: dbData.product_name,
            quantity: dbData.quantity,
            startTime: dbData.start_time,
            endTime: dbData.end_time,
            status: dbData.status,
            priority: dbData.priority,
            assignedEquipment: JSON.parse(dbData.assigned_equipment || '[]'),
            assignedPersonnel: JSON.parse(dbData.assigned_personnel || '[]'),
            requiredMaterials: JSON.parse(dbData.required_materials || '[]'),
            progress: dbData.progress,
            notes: dbData.notes,
            createdBy: dbData.created_by,
            createdAt: dbData.created_at,
            updatedAt: dbData.updated_at
        });
    }

    /**
     * 生成排程ID
     * @returns {string} 排程ID
     */
    static generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `SCH${timestamp}${random}`;
    }
}

/**
 * 资源数据模型
 */
class ResourceModel {
    constructor(data = {}) {
        this.id = data.id || '';
        this.name = data.name || '';
        this.type = data.type || '';
        this.status = data.status || 'available';
        this.capacity = data.capacity || 0;
        this.currentLoad = data.currentLoad || 0;
        this.department = data.department || '';
        this.specifications = data.specifications || {};
    }
}

module.exports = {
    ScheduleModel,
    ResourceModel,
    SCHEDULE_STATUS,
    PRIORITY_LEVELS
};
