/* 排程管理模块通用样式文件 - Schedule Common CSS */

/* 此文件包含排程管理模块的通用样式 */
/* 基础样式继承自 /assets/css/common.css */

/* 加载动画背景色覆盖 */
.loading-overlay {
    background-color: #F9FAFB;
}

/* 状态样式 */
.status-planned { @apply bg-blue-100 text-blue-800; }
.status-in_progress { @apply bg-green-100 text-green-800; }
.status-paused { @apply bg-yellow-100 text-yellow-800; }
.status-completed { @apply bg-gray-100 text-gray-800; }
.status-cancelled { @apply bg-red-100 text-red-800; }

/* 优先级样式 */
.priority-low { @apply bg-gray-100 text-gray-600; }
.priority-medium { @apply bg-blue-100 text-blue-600; }
.priority-high { @apply bg-orange-100 text-orange-600; }
.priority-urgent { @apply bg-red-100 text-red-600; }
