<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法调优 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <style>
        [v-cloak] {
            display: none;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #F9FAFB;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #e5e7eb;
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">算法调优</h1>
                            <p class="text-gray-600 mt-1">智能排程算法性能优化和参数调整</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button @click="runBenchmark" :disabled="benchmarking"
                                    class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50 disabled:opacity-50">
                                {{ benchmarking ? '测试中...' : '性能测试' }}
                            </button>
                            <button @click="executeTuning" :disabled="tuning"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                                {{ tuning ? '调优中...' : '执行调优' }}
                            </button>
                        </div>
                    </div>
                </header>

                <!-- 调优统计概览 -->
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="metric-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 bg-blue-100 rounded-lg">
                                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">总调优次数</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ tuningStats.totalTunings || 0 }}</p>
                                    <p class="text-sm text-gray-500">历史记录</p>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 bg-green-100 rounded-lg">
                                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">成功率</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ getSuccessRate() }}%</p>
                                    <p class="text-sm text-gray-500">调优成功率</p>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 bg-yellow-100 rounded-lg">
                                    <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">平均改进</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ (tuningStats.averageImprovement * 100).toFixed(1) }}%</p>
                                    <p class="text-sm text-gray-500">性能提升</p>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 bg-purple-100 rounded-lg">
                                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">最后调优</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ formatLastTuning() }}</p>
                                    <p class="text-sm text-gray-500">时间</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="px-6 pb-6">
                    <div class="space-y-6">
                        <!-- 调优参数配置 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h2 class="text-lg font-medium text-gray-900">调优参数配置</h2>
                                    <div class="flex space-x-2">
                                        <button @click="resetParams" :disabled="resetting"
                                                class="px-3 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50">
                                            {{ resetting ? '重置中...' : '重置默认' }}
                                        </button>
                                        <button @click="saveParams" :disabled="saving"
                                                class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                                            {{ saving ? '保存中...' : '保存参数' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    <!-- 交期预测参数 -->
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-4">交期预测参数</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">基础准确性</label>
                                                <input type="number" v-model.number="tuningParams.deliveryPrediction.baseAccuracy" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">风险因素权重</label>
                                                <input type="number" v-model.number="tuningParams.deliveryPrediction.riskFactorWeight" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">历史数据权重</label>
                                                <input type="number" v-model.number="tuningParams.deliveryPrediction.historicalDataWeight" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">当前状态权重</label>
                                                <input type="number" v-model.number="tuningParams.deliveryPrediction.currentStatusWeight" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 资源优化参数 -->
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-4">资源优化参数</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">效率权重</label>
                                                <input type="number" v-model.number="tuningParams.resourceOptimization.efficiencyWeight" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">成本权重</label>
                                                <input type="number" v-model.number="tuningParams.resourceOptimization.costWeight" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">风险权重</label>
                                                <input type="number" v-model.number="tuningParams.resourceOptimization.riskWeight" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">负载均衡阈值</label>
                                                <input type="number" v-model.number="tuningParams.resourceOptimization.loadBalanceThreshold" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 方案生成参数 -->
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-4">方案生成参数</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">最大方案数</label>
                                                <input type="number" v-model.number="tuningParams.planGeneration.maxPlansCount" 
                                                       min="1" max="10" step="1"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">多样性阈值</label>
                                                <input type="number" v-model.number="tuningParams.planGeneration.diversityThreshold" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">质量阈值</label>
                                                <input type="number" v-model.number="tuningParams.planGeneration.qualityThreshold" 
                                                       min="0" max="1" step="0.01"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">超时时间(ms)</label>
                                                <input type="number" v-model.number="tuningParams.planGeneration.timeoutMs" 
                                                       min="1000" max="30000" step="1000"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 性能历史图表 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-lg font-medium text-gray-900">性能历史趋势</h2>
                                <p class="text-sm text-gray-600 mt-1">算法性能指标的历史变化趋势</p>
                            </div>
                            <div class="p-6">
                                <div class="chart-container">
                                    <canvas ref="performanceChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 最近调优结果 -->
                        <div v-if="lastTuningResult" class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-lg font-medium text-gray-900">最近调优结果</h2>
                                <p class="text-sm text-gray-600 mt-1">{{ formatDate(lastTuningResult.timestamp) }}</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-3">调优改进</h3>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">总体改进:</span>
                                                <span :class="['text-sm font-medium', lastTuningResult.improvement.overall > 0 ? 'text-green-600' : 'text-red-600']">
                                                    {{ (lastTuningResult.improvement.overall * 100).toFixed(2) }}%
                                                </span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">响应时间:</span>
                                                <span :class="['text-sm font-medium', lastTuningResult.improvement.details.responseTime > 0 ? 'text-green-600' : 'text-red-600']">
                                                    {{ (lastTuningResult.improvement.details.responseTime * 100).toFixed(2) }}%
                                                </span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">准确性:</span>
                                                <span :class="['text-sm font-medium', lastTuningResult.improvement.details.accuracy > 0 ? 'text-green-600' : 'text-red-600']">
                                                    {{ (lastTuningResult.improvement.details.accuracy * 100).toFixed(2) }}%
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-3">当前性能</h3>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">响应时间:</span>
                                                <span class="text-sm font-medium">{{ lastTuningResult.newPerformance.responseTime }}ms</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">准确性:</span>
                                                <span class="text-sm font-medium">{{ (lastTuningResult.newPerformance.accuracy * 100).toFixed(1) }}%</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">缓存命中率:</span>
                                                <span class="text-sm font-medium">{{ (lastTuningResult.newPerformance.cacheHitRate * 100).toFixed(1) }}%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 基准测试结果 -->
                        <div v-if="benchmarkResult" class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-lg font-medium text-gray-900">基准测试结果</h2>
                                <p class="text-sm text-gray-600 mt-1">{{ formatDate(benchmarkResult.startTime) }}</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-gray-900">{{ benchmarkResult.summary.totalTests }}</p>
                                        <p class="text-sm text-gray-600">总测试数</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-green-600">{{ benchmarkResult.summary.successfulTests }}</p>
                                        <p class="text-sm text-gray-600">成功测试</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-blue-600">{{ Math.round(benchmarkResult.summary.averageDuration) }}ms</p>
                                        <p class="text-sm text-gray-600">平均响应时间</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-purple-600">{{ (benchmarkResult.summary.successRate * 100).toFixed(1) }}%</p>
                                        <p class="text-sm text-gray-600">成功率</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script src="/js/libs/chart.umd.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/admin/algorithm-tuning.js"></script>
</body>
</html>
