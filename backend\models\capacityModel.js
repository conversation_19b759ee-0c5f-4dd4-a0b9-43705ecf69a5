/**
 * 设备产能数据模型
 * 定义设备产能相关的数据结构和验证规则
 */

/**
 * 设备产能数据模型
 */
class EquipmentCapabilityModel {
    constructor(data = {}) {
        this.id = data.id || null;
        this.equipmentId = data.equipmentId || '';
        this.productId = data.productId || '';
        this.processId = data.processId || null;
        this.capacityPerHour = data.capacityPerHour || 0;
        this.efficiencyFactor = data.efficiencyFactor || 1.0;
        this.setupTime = data.setupTime || 0;
        this.createdAt = data.createdAt || new Date().toISOString();
    }

    /**
     * 验证设备产能数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.equipmentId || this.equipmentId.trim().length === 0) {
            errors.push('设备ID不能为空');
        }

        if (!this.productId || this.productId.trim().length === 0) {
            errors.push('产品ID不能为空');
        }

        if (this.capacityPerHour <= 0) {
            errors.push('每小时产能必须大于0');
        }

        if (this.efficiencyFactor <= 0 || this.efficiencyFactor > 2.0) {
            errors.push('效率系数必须在0-2.0之间');
        }

        if (this.setupTime < 0) {
            errors.push('准备时间不能为负数');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 转换为数据库存储格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            equipment_id: this.equipmentId,
            product_id: this.productId,
            process_id: this.processId,
            capacity_per_hour: this.capacityPerHour,
            efficiency_factor: this.efficiencyFactor,
            setup_time: this.setupTime,
            created_at: this.createdAt
        };
    }

    /**
     * 从数据库格式创建模型实例
     * @param {Object} dbData 数据库数据
     * @returns {EquipmentCapabilityModel} 模型实例
     */
    static fromDatabase(dbData) {
        return new EquipmentCapabilityModel({
            id: dbData.id,
            equipmentId: dbData.equipment_id,
            productId: dbData.product_id,
            processId: dbData.process_id,
            capacityPerHour: dbData.capacity_per_hour,
            efficiencyFactor: dbData.efficiency_factor,
            setupTime: dbData.setup_time,
            createdAt: dbData.created_at
        });
    }

    /**
     * 生成设备产能ID
     * @returns {string} 设备产能ID
     */
    static generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `CAP${timestamp}${random}`;
    }
}

/**
 * 操作员技能数据模型
 */
class OperatorSkillModel {
    constructor(data = {}) {
        this.id = data.id || null;
        this.operatorId = data.operatorId || '';
        this.equipmentId = data.equipmentId || '';
        this.skillLevel = data.skillLevel || 1;
        this.efficiencyFactor = data.efficiencyFactor || 1.0;
        this.certificationDate = data.certificationDate || null;
        this.createdAt = data.createdAt || new Date().toISOString();
    }

    /**
     * 验证操作员技能数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.operatorId || this.operatorId.trim().length === 0) {
            errors.push('操作员ID不能为空');
        }

        if (!this.equipmentId || this.equipmentId.trim().length === 0) {
            errors.push('设备ID不能为空');
        }

        if (this.skillLevel < 1 || this.skillLevel > 5) {
            errors.push('技能等级必须在1-5之间');
        }

        if (this.efficiencyFactor <= 0 || this.efficiencyFactor > 2.0) {
            errors.push('效率系数必须在0-2.0之间');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 转换为数据库存储格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            operator_id: this.operatorId,
            equipment_id: this.equipmentId,
            skill_level: this.skillLevel,
            efficiency_factor: this.efficiencyFactor,
            certification_date: this.certificationDate,
            created_at: this.createdAt
        };
    }

    /**
     * 从数据库格式创建模型实例
     * @param {Object} dbData 数据库数据
     * @returns {OperatorSkillModel} 模型实例
     */
    static fromDatabase(dbData) {
        return new OperatorSkillModel({
            id: dbData.id,
            operatorId: dbData.operator_id,
            equipmentId: dbData.equipment_id,
            skillLevel: dbData.skill_level,
            efficiencyFactor: dbData.efficiency_factor,
            certificationDate: dbData.certification_date,
            createdAt: dbData.created_at
        });
    }

    /**
     * 生成操作员技能ID
     * @returns {string} 操作员技能ID
     */
    static generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `SKILL${timestamp}${random}`;
    }
}

/**
 * 设备操作员关联数据模型
 */
class EquipmentOperatorModel {
    constructor(data = {}) {
        this.id = data.id || null;
        this.equipmentId = data.equipmentId || '';
        this.operatorId = data.operatorId || '';
        this.isPrimary = data.isPrimary || false;
        this.createdAt = data.createdAt || new Date().toISOString();
    }

    /**
     * 验证设备操作员关联数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.equipmentId || this.equipmentId.trim().length === 0) {
            errors.push('设备ID不能为空');
        }

        if (!this.operatorId || this.operatorId.trim().length === 0) {
            errors.push('操作员ID不能为空');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 转换为数据库存储格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            equipment_id: this.equipmentId,
            operator_id: this.operatorId,
            is_primary: this.isPrimary ? 1 : 0,
            created_at: this.createdAt
        };
    }

    /**
     * 从数据库格式创建模型实例
     * @param {Object} dbData 数据库数据
     * @returns {EquipmentOperatorModel} 模型实例
     */
    static fromDatabase(dbData) {
        return new EquipmentOperatorModel({
            id: dbData.id,
            equipmentId: dbData.equipment_id,
            operatorId: dbData.operator_id,
            isPrimary: dbData.is_primary === 1,
            createdAt: dbData.created_at
        });
    }

    /**
     * 生成设备操作员关联ID
     * @returns {string} 设备操作员关联ID
     */
    static generateId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `EO${timestamp}${random}`;
    }
}

module.exports = {
    EquipmentCapabilityModel,
    OperatorSkillModel,
    EquipmentOperatorModel
};
