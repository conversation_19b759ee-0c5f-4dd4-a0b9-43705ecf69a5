/**
 * 维护建议生成引擎
 * 基于健康度评估结果生成维护建议
 */

const logger = require('./logger');

class RecommendationEngine {
    constructor() {
        // 建议优先级权重
        this.priorityWeights = {
            'urgent': 4,
            'high': 3,
            'medium': 2,
            'low': 1
        };

        // 成本估算映射
        this.costEstimates = {
            'very_high': '> 50,000元',
            'high': '10,000-50,000元',
            'medium': '5,000-10,000元',
            'low': '< 5,000元'
        };
    }

    /**
     * 生成维护建议
     */
    generateRecommendations(dimensions, equipmentData, maintenanceRecords) {
        try {
            const recommendations = [];
            const totalScore = this.calculateTotalScore(dimensions);
            
            // 基于总分的建议
            this.addOverallRecommendations(recommendations, totalScore);
            
            // 基于各维度的建议
            this.addDimensionRecommendations(recommendations, dimensions);
            
            // 基于设备年龄的建议
            this.addAgeBasedRecommendations(recommendations, dimensions.age, equipmentData);
            
            // 基于维修历史的建议
            this.addHistoryBasedRecommendations(recommendations, maintenanceRecords);
            
            // 排序并去重
            return this.prioritizeAndDeduplicateRecommendations(recommendations);
        } catch (error) {
            logger.error('生成维护建议失败', { error: error.message });
            return [];
        }
    }

    /**
     * 计算总分
     */
    calculateTotalScore(dimensions) {
        let totalScore = 0;
        for (const [dimensionName, dimensionData] of Object.entries(dimensions)) {
            totalScore += dimensionData.score * dimensionData.weight;
        }
        return Math.round(totalScore);
    }

    /**
     * 基于总分生成建议
     */
    addOverallRecommendations(recommendations, totalScore) {
        if (totalScore < 60) {
            recommendations.push({
                priority: 'urgent',
                type: 'comprehensive_repair',
                title: '立即全面检修',
                description: '设备健康度危险，需要立即停机进行全面检修',
                estimatedCost: 'high',
                timeframe: '立即执行'
            });
        } else if (totalScore < 70) {
            recommendations.push({
                priority: 'high',
                type: 'major_maintenance',
                title: '安排重大维护',
                description: '设备状态较差，需要安排重大维护作业',
                estimatedCost: 'medium',
                timeframe: '本周内'
            });
        }
    }

    /**
     * 基于各维度生成建议
     */
    addDimensionRecommendations(recommendations, dimensions) {
        // 维修频率建议
        if (dimensions.repairFrequency.score < 70) {
            recommendations.push({
                priority: 'high',
                type: 'operation_review',
                title: '检查运行环境和操作规范',
                description: '维修频率过高，建议检查设备运行环境、操作规范和人员培训',
                estimatedCost: 'medium',
                timeframe: '1-2周内'
            });
        }

        // 故障严重程度建议
        if (dimensions.faultSeverity.score < 70) {
            recommendations.push({
                priority: 'high',
                type: 'thorough_inspection',
                title: '进行全面检修',
                description: '近期故障较为严重，建议进行全面的设备检修和部件更换',
                estimatedCost: 'high',
                timeframe: '本月内'
            });
        }

        // 保养情况建议
        if (dimensions.maintenance.score < 70) {
            recommendations.push({
                priority: 'medium',
                type: 'maintenance_plan',
                title: '制定定期保养计划',
                description: '保养不够及时，建议制定并严格执行定期保养计划',
                estimatedCost: 'low',
                timeframe: '下月开始'
            });
        }
    }

    /**
     * 基于设备年龄生成建议
     */
    addAgeBasedRecommendations(recommendations, ageDimension, equipmentData) {
        const ageInYears = ageDimension.details.ageInYears;
        
        if (ageInYears > 10) {
            recommendations.push({
                priority: 'medium',
                type: 'replacement_evaluation',
                title: '考虑设备更换评估',
                description: '设备使用年限较长，建议评估更换的经济性和必要性',
                estimatedCost: 'very_high',
                timeframe: '年度规划'
            });
        } else if (ageInYears > 5) {
            recommendations.push({
                priority: 'low',
                type: 'upgrade_evaluation',
                title: '考虑设备升级评估',
                description: '设备使用年限较长，建议评估升级或更换的必要性',
                estimatedCost: 'high',
                timeframe: '年度规划'
            });
        }
    }

    /**
     * 基于维修历史生成建议
     */
    addHistoryBasedRecommendations(recommendations, maintenanceRecords) {
        const recentRecords = maintenanceRecords
            .filter(record => {
                const recordDate = new Date(record.maintenance_date);
                const monthsAgo = (new Date() - recordDate) / (30.44 * 24 * 60 * 60 * 1000);
                return monthsAgo <= 6; // 最近6个月
            });

        // 检查是否有重复故障
        const faultTypes = recentRecords
            .filter(record => record.type === 'repair')
            .map(record => record.description.toLowerCase());
        
        const duplicateFaults = this.findDuplicateFaults(faultTypes);
        
        if (duplicateFaults.length > 0) {
            recommendations.push({
                priority: 'high',
                type: 'root_cause_analysis',
                title: '进行根本原因分析',
                description: `检测到重复故障：${duplicateFaults.join(', ')}，建议进行根本原因分析`,
                estimatedCost: 'medium',
                timeframe: '2周内'
            });
        }

        // 检查维护成本趋势
        const costTrend = this.analyzeCostTrend(recentRecords);
        if (costTrend.isIncreasing && costTrend.increaseRate > 0.2) {
            recommendations.push({
                priority: 'medium',
                type: 'cost_optimization',
                title: '优化维护成本',
                description: '维护成本呈上升趋势，建议优化维护策略以控制成本',
                estimatedCost: 'low',
                timeframe: '1个月内'
            });
        }
    }

    /**
     * 查找重复故障
     */
    findDuplicateFaults(faultDescriptions) {
        const faultCounts = {};
        faultDescriptions.forEach(fault => {
            faultCounts[fault] = (faultCounts[fault] || 0) + 1;
        });
        
        return Object.keys(faultCounts).filter(fault => faultCounts[fault] > 1);
    }

    /**
     * 分析成本趋势
     */
    analyzeCostTrend(records) {
        if (records.length < 3) {
            return { isIncreasing: false, increaseRate: 0 };
        }

        const costs = records
            .filter(record => record.cost && record.cost > 0)
            .sort((a, b) => new Date(a.maintenance_date) - new Date(b.maintenance_date))
            .map(record => record.cost);

        if (costs.length < 3) {
            return { isIncreasing: false, increaseRate: 0 };
        }

        const firstHalf = costs.slice(0, Math.floor(costs.length / 2));
        const secondHalf = costs.slice(Math.floor(costs.length / 2));

        const firstAvg = firstHalf.reduce((sum, cost) => sum + cost, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, cost) => sum + cost, 0) / secondHalf.length;

        const increaseRate = (secondAvg - firstAvg) / firstAvg;

        return {
            isIncreasing: increaseRate > 0,
            increaseRate: Math.abs(increaseRate)
        };
    }

    /**
     * 优先级排序和去重
     */
    prioritizeAndDeduplicateRecommendations(recommendations) {
        // 去重（基于type）
        const uniqueRecommendations = recommendations.reduce((acc, current) => {
            const existing = acc.find(item => item.type === current.type);
            if (!existing) {
                acc.push(current);
            } else if (this.priorityWeights[current.priority] > this.priorityWeights[existing.priority]) {
                // 如果新建议优先级更高，替换现有建议
                const index = acc.indexOf(existing);
                acc[index] = current;
            }
            return acc;
        }, []);

        // 按优先级排序
        return uniqueRecommendations.sort((a, b) => 
            this.priorityWeights[b.priority] - this.priorityWeights[a.priority]
        );
    }

    /**
     * 格式化建议输出
     */
    formatRecommendations(recommendations) {
        return recommendations.map(rec => ({
            ...rec,
            estimatedCostDescription: this.costEstimates[rec.estimatedCost] || '待评估',
            priorityLevel: this.priorityWeights[rec.priority] || 1
        }));
    }
}

module.exports = RecommendationEngine;
