/**
 * 系统管理 - 系统设置页面
 * 配置系统参数、邮件设置和其他系统选项
 */

import { createAdminApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';

createAdminApp({
    components: {
        Sidebar
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        return {
            sidebarOpen
        };
    },
    onUserLoaded: async (user) => {
        console.log('系统设置页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
