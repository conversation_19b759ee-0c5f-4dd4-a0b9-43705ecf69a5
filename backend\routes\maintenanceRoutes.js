/**
 * 维修保养记录路由
 * 处理维修保养记录相关的路由配置
 */

const express = require('express');
const router = express.Router();
const MaintenanceController = require('../controllers/maintenanceController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 创建控制器实例
const maintenanceController = new MaintenanceController();

/**
 * 获取选项数据（类型、状态等）
 * GET /api/maintenance/options
 * 权限: equipment_maintenance
 */
router.get('/options',
    authenticateJWT,
    checkPermission('equipment_maintenance'),
    (req, res) => {
        maintenanceController.getOptions(req, res);
    }
);

/**
 * 获取统计数据
 * GET /api/maintenance/statistics
 * 权限: equipment_maintenance
 */
router.get('/statistics',
    authenticateJWT,
    checkPermission('equipment_maintenance'),
    (req, res) => {
        maintenanceController.getStatistics(req, res);
    }
);

/**
 * 导出维修记录到Excel
 * GET /api/maintenance/export
 */
router.get('/export', (req, res) => {
    maintenanceController.exportMaintenanceRecordsToExcel(req, res);
});

/**
 * 批量导入维修记录
 * POST /api/maintenance/import
 */
router.post('/import', (req, res) => {
    maintenanceController.importMaintenanceRecords(req, res);
});

/**
 * 获取维修记录列表
 * GET /api/maintenance
 * 
 * 查询参数：
 * - page: 页码
 * - limit: 每页数量
 * - search: 搜索关键词
 * - equipmentId: 设备ID筛选
 * - type: 类型筛选
 * - status: 状态筛选
 * - technician: 技术员筛选
 * - area: 厂区筛选
 * - startDate: 开始时间筛选
 * - endDate: 结束时间筛选
 */
router.get('/', (req, res) => {
    maintenanceController.getMaintenanceRecords(req, res);
});

/**
 * 创建维修记录
 * POST /api/maintenance
 * 
 * 请求体：
 * {
 *   "equipmentId": "设备ID",
 *   "type": "maintenance|repair",
 *   "description": "描述",
 *   "startDate": "开始时间",
 *   "endDate": "结束时间（可选）",
 *   "cost": "费用（可选）",
 *   "technician": "技术员",
 *   "status": "状态（可选）",
 *   "notes": "备注（可选）"
 * }
 */
router.post('/', (req, res) => {
    maintenanceController.createMaintenanceRecord(req, res);
});

/**
 * 获取单个维修记录详情
 * GET /api/maintenance/:id
 */
router.get('/:id', (req, res) => {
    maintenanceController.getMaintenanceRecordById(req, res);
});

/**
 * 更新维修记录
 * PUT /api/maintenance/:id
 * 
 * 请求体：与创建时相同，所有字段都是可选的
 */
router.put('/:id', (req, res) => {
    maintenanceController.updateMaintenanceRecord(req, res);
});

/**
 * 删除维修记录
 * DELETE /api/maintenance/:id
 */
router.delete('/:id', (req, res) => {
    maintenanceController.deleteMaintenanceRecord(req, res);
});

module.exports = router;
