/**
 * 数据库连接池管理器
 * 优化数据库连接和查询性能
 */

const Database = require('better-sqlite3');
const path = require('path');
const logger = require('./logger');

class ConnectionPool {
    constructor(options = {}) {
        this.dbPath = options.dbPath || path.join(__dirname, '../database/application_system.db');
        this.maxConnections = options.maxConnections || 10;
        this.minConnections = options.minConnections || 2;
        this.acquireTimeout = options.acquireTimeout || 30000; // 30秒
        this.idleTimeout = options.idleTimeout || 300000; // 5分钟
        
        this.connections = [];
        this.availableConnections = [];
        this.busyConnections = new Set();
        this.waitingQueue = [];
        
        this.stats = {
            created: 0,
            destroyed: 0,
            acquired: 0,
            released: 0,
            timeouts: 0,
            errors: 0
        };

        // 初始化最小连接数
        this.initialize();
        
        // 启动清理定时器
        this.startCleanupTimer();
    }

    /**
     * 初始化连接池
     */
    async initialize() {
        try {
            for (let i = 0; i < this.minConnections; i++) {
                const connection = this.createConnection();
                this.availableConnections.push(connection);
            }
            logger.info(`数据库连接池初始化完成，创建 ${this.minConnections} 个连接`);
        } catch (error) {
            logger.error('连接池初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建新的数据库连接
     */
    createConnection() {
        try {
            const connection = new Database(this.dbPath);
            
            // 配置连接
            connection.pragma('journal_mode = WAL');
            connection.pragma('foreign_keys = ON');
            connection.pragma('synchronous = NORMAL');
            connection.pragma('cache_size = -64000'); // 64MB缓存
            connection.pragma('temp_store = MEMORY');
            connection.pragma('mmap_size = 268435456'); // 256MB内存映射
            
            // 添加连接元数据
            connection._poolMetadata = {
                id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                createdAt: Date.now(),
                lastUsed: Date.now(),
                queryCount: 0
            };

            this.connections.push(connection);
            this.stats.created++;
            
            logger.debug(`创建新数据库连接: ${connection._poolMetadata.id}`);
            return connection;
        } catch (error) {
            this.stats.errors++;
            logger.error('创建数据库连接失败:', error);
            throw error;
        }
    }

    /**
     * 获取连接
     */
    async acquire() {
        return new Promise((resolve, reject) => {
            // 设置超时
            const timeout = setTimeout(() => {
                this.stats.timeouts++;
                reject(new Error(`获取数据库连接超时 (${this.acquireTimeout}ms)`));
            }, this.acquireTimeout);

            const tryAcquire = () => {
                // 检查是否有可用连接
                if (this.availableConnections.length > 0) {
                    const connection = this.availableConnections.pop();
                    this.busyConnections.add(connection);
                    connection._poolMetadata.lastUsed = Date.now();
                    
                    clearTimeout(timeout);
                    this.stats.acquired++;
                    resolve(connection);
                    return;
                }

                // 如果没有达到最大连接数，创建新连接
                if (this.connections.length < this.maxConnections) {
                    try {
                        const connection = this.createConnection();
                        this.busyConnections.add(connection);
                        connection._poolMetadata.lastUsed = Date.now();
                        
                        clearTimeout(timeout);
                        this.stats.acquired++;
                        resolve(connection);
                        return;
                    } catch (error) {
                        clearTimeout(timeout);
                        reject(error);
                        return;
                    }
                }

                // 加入等待队列
                this.waitingQueue.push({ resolve, reject, timeout });
            };

            tryAcquire();
        });
    }

    /**
     * 释放连接
     */
    release(connection) {
        if (!connection || !this.busyConnections.has(connection)) {
            logger.warn('尝试释放无效的数据库连接');
            return;
        }

        this.busyConnections.delete(connection);
        connection._poolMetadata.lastUsed = Date.now();
        this.stats.released++;

        // 检查是否有等待的请求
        if (this.waitingQueue.length > 0) {
            const { resolve, timeout } = this.waitingQueue.shift();
            this.busyConnections.add(connection);
            clearTimeout(timeout);
            resolve(connection);
        } else {
            this.availableConnections.push(connection);
        }

        logger.debug(`释放数据库连接: ${connection._poolMetadata.id}`);
    }

    /**
     * 执行查询（自动管理连接）
     */
    async execute(queryFn) {
        const connection = await this.acquire();
        try {
            connection._poolMetadata.queryCount++;
            const result = await queryFn(connection);
            return result;
        } finally {
            this.release(connection);
        }
    }

    /**
     * 执行事务（自动管理连接）
     */
    async transaction(transactionFn) {
        const connection = await this.acquire();
        const transaction = connection.transaction(transactionFn);
        
        try {
            connection._poolMetadata.queryCount++;
            const result = transaction();
            return result;
        } finally {
            this.release(connection);
        }
    }

    /**
     * 销毁连接
     */
    destroyConnection(connection) {
        if (!connection) return;

        try {
            // 从所有集合中移除
            this.busyConnections.delete(connection);
            const availableIndex = this.availableConnections.indexOf(connection);
            if (availableIndex !== -1) {
                this.availableConnections.splice(availableIndex, 1);
            }
            const connectionIndex = this.connections.indexOf(connection);
            if (connectionIndex !== -1) {
                this.connections.splice(connectionIndex, 1);
            }

            // 关闭连接
            connection.close();
            this.stats.destroyed++;
            
            logger.debug(`销毁数据库连接: ${connection._poolMetadata.id}`);
        } catch (error) {
            logger.error('销毁数据库连接失败:', error);
        }
    }

    /**
     * 清理空闲连接
     */
    cleanup() {
        const now = Date.now();
        const connectionsToDestroy = [];

        // 查找空闲超时的连接
        for (const connection of this.availableConnections) {
            if (now - connection._poolMetadata.lastUsed > this.idleTimeout) {
                connectionsToDestroy.push(connection);
            }
        }

        // 确保保持最小连接数
        const minToKeep = Math.max(0, this.minConnections - (this.availableConnections.length - connectionsToDestroy.length));
        const toDestroy = connectionsToDestroy.slice(minToKeep);

        // 销毁超时连接
        toDestroy.forEach(connection => {
            this.destroyConnection(connection);
        });

        if (toDestroy.length > 0) {
            logger.debug(`清理 ${toDestroy.length} 个空闲连接`);
        }
    }

    /**
     * 启动清理定时器
     */
    startCleanupTimer() {
        setInterval(() => {
            this.cleanup();
        }, 60000); // 每分钟清理一次
    }

    /**
     * 获取连接池统计信息
     */
    getStats() {
        return {
            total: this.connections.length,
            available: this.availableConnections.length,
            busy: this.busyConnections.size,
            waiting: this.waitingQueue.length,
            maxConnections: this.maxConnections,
            minConnections: this.minConnections,
            ...this.stats
        };
    }

    /**
     * 获取连接详细信息
     */
    getConnectionDetails() {
        return this.connections.map(conn => ({
            id: conn._poolMetadata.id,
            createdAt: new Date(conn._poolMetadata.createdAt).toISOString(),
            lastUsed: new Date(conn._poolMetadata.lastUsed).toISOString(),
            queryCount: conn._poolMetadata.queryCount,
            status: this.busyConnections.has(conn) ? 'busy' : 'available'
        }));
    }

    /**
     * 关闭连接池
     */
    async close() {
        // 清空等待队列
        this.waitingQueue.forEach(({ reject, timeout }) => {
            clearTimeout(timeout);
            reject(new Error('连接池已关闭'));
        });
        this.waitingQueue = [];

        // 关闭所有连接
        for (const connection of this.connections) {
            this.destroyConnection(connection);
        }

        logger.info('数据库连接池已关闭');
    }
}

// 创建全局连接池实例
const connectionPool = new ConnectionPool();

module.exports = {
    ConnectionPool,
    connectionPool
};
