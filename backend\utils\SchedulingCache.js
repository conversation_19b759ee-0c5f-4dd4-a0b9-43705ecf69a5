/**
 * 智能排程缓存管理器
 * 提供排程算法的缓存机制，提高计算性能
 */

const logger = require('./logger');

/**
 * 排程缓存类
 * 管理排程计算结果的缓存
 */
class SchedulingCache {
    constructor() {
        // 内存缓存存储
        this.cache = new Map();
        
        // 缓存配置
        this.config = {
            maxSize: 1000,              // 最大缓存条目数
            ttl: 30 * 60 * 1000,       // 缓存生存时间（30分钟）
            cleanupInterval: 5 * 60 * 1000, // 清理间隔（5分钟）
            enableCompression: true     // 启用压缩
        };

        // 缓存统计
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            cleanups: 0
        };

        // 启动定期清理
        this.startCleanupTimer();

        logger.info('排程缓存管理器初始化完成', {
            maxSize: this.config.maxSize,
            ttl: this.config.ttl
        });
    }

    /**
     * 生成缓存键
     * @param {Object} orderData 订单数据
     * @param {Object} constraints 约束条件
     * @returns {string} 缓存键
     */
    generateCacheKey(orderData, constraints = {}) {
        const keyData = {
            productId: orderData.productId,
            quantity: orderData.quantity,
            requiredDate: orderData.requiredDate,
            priority: orderData.priority,
            constraints: this.normalizeConstraints(constraints)
        };

        // 生成哈希键
        const keyString = JSON.stringify(keyData);
        return this.hashString(keyString);
    }

    /**
     * 获取缓存数据
     * @param {string} key 缓存键
     * @returns {Object|null} 缓存数据
     */
    get(key) {
        const entry = this.cache.get(key);
        
        if (!entry) {
            this.stats.misses++;
            return null;
        }

        // 检查是否过期
        if (Date.now() > entry.expireAt) {
            this.cache.delete(key);
            this.stats.misses++;
            return null;
        }

        this.stats.hits++;
        
        // 解压缩数据（如果启用了压缩）
        const data = this.config.enableCompression ? 
            this.decompress(entry.data) : entry.data;

        logger.debug('缓存命中', { key, hitRate: this.getHitRate() });
        
        return data;
    }

    /**
     * 设置缓存数据
     * @param {string} key 缓存键
     * @param {Object} data 数据
     * @param {number} customTtl 自定义TTL
     */
    set(key, data, customTtl = null) {
        // 检查缓存大小限制
        if (this.cache.size >= this.config.maxSize) {
            this.evictOldest();
        }

        const ttl = customTtl || this.config.ttl;
        const expireAt = Date.now() + ttl;

        // 压缩数据（如果启用了压缩）
        const compressedData = this.config.enableCompression ? 
            this.compress(data) : data;

        const entry = {
            data: compressedData,
            createdAt: Date.now(),
            expireAt,
            accessCount: 0,
            lastAccessed: Date.now()
        };

        this.cache.set(key, entry);
        this.stats.sets++;

        logger.debug('缓存设置', { 
            key, 
            size: this.cache.size,
            ttl 
        });
    }

    /**
     * 删除缓存数据
     * @param {string} key 缓存键
     * @returns {boolean} 是否删除成功
     */
    delete(key) {
        const deleted = this.cache.delete(key);
        if (deleted) {
            this.stats.deletes++;
            logger.debug('缓存删除', { key });
        }
        return deleted;
    }

    /**
     * 清空所有缓存
     */
    clear() {
        const size = this.cache.size;
        this.cache.clear();
        logger.info('缓存清空', { clearedCount: size });
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            size: this.cache.size,
            hitRate: this.getHitRate(),
            memoryUsage: this.getMemoryUsage()
        };
    }

    /**
     * 计算命中率
     * @returns {number} 命中率
     */
    getHitRate() {
        const total = this.stats.hits + this.stats.misses;
        return total > 0 ? (this.stats.hits / total) : 0;
    }

    /**
     * 估算内存使用量
     * @returns {number} 内存使用量（字节）
     */
    getMemoryUsage() {
        let totalSize = 0;
        
        for (const [key, entry] of this.cache) {
            totalSize += this.estimateSize(key) + this.estimateSize(entry);
        }
        
        return totalSize;
    }

    /**
     * 标准化约束条件
     * @param {Object} constraints 约束条件
     * @returns {Object} 标准化后的约束条件
     */
    normalizeConstraints(constraints) {
        const normalized = {};
        
        // 只保留影响排程结果的关键约束
        const keyConstraints = [
            'maxDeliveryDate',
            'priorityLevel',
            'specialRequirements',
            'resourceConstraints'
        ];

        keyConstraints.forEach(key => {
            if (constraints[key] !== undefined) {
                normalized[key] = constraints[key];
            }
        });

        return normalized;
    }

    /**
     * 生成字符串哈希
     * @param {string} str 字符串
     * @returns {string} 哈希值
     */
    hashString(str) {
        let hash = 0;
        if (str.length === 0) return hash.toString();
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return Math.abs(hash).toString(36);
    }

    /**
     * 压缩数据
     * @param {Object} data 数据
     * @returns {string} 压缩后的数据
     */
    compress(data) {
        // 简化实现：使用JSON字符串
        // 实际应用中可以使用更高效的压缩算法
        return JSON.stringify(data);
    }

    /**
     * 解压缩数据
     * @param {string} compressedData 压缩数据
     * @returns {Object} 解压缩后的数据
     */
    decompress(compressedData) {
        try {
            return JSON.parse(compressedData);
        } catch (error) {
            logger.error('解压缩数据失败', { error: error.message });
            return null;
        }
    }

    /**
     * 淘汰最旧的缓存条目
     */
    evictOldest() {
        let oldestKey = null;
        let oldestTime = Date.now();

        for (const [key, entry] of this.cache) {
            if (entry.createdAt < oldestTime) {
                oldestTime = entry.createdAt;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.cache.delete(oldestKey);
            logger.debug('淘汰最旧缓存', { key: oldestKey });
        }
    }

    /**
     * 启动定期清理定时器
     */
    startCleanupTimer() {
        setInterval(() => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }

    /**
     * 清理过期缓存
     */
    cleanup() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, entry] of this.cache) {
            if (now > entry.expireAt) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            this.stats.cleanups++;
            logger.debug('清理过期缓存', { 
                cleanedCount, 
                remainingSize: this.cache.size 
            });
        }
    }

    /**
     * 估算对象大小
     * @param {any} obj 对象
     * @returns {number} 估算大小（字节）
     */
    estimateSize(obj) {
        const jsonString = JSON.stringify(obj);
        return new Blob([jsonString]).size;
    }

    /**
     * 预热缓存
     * @param {Array} commonOrders 常见订单列表
     */
    async warmup(commonOrders = []) {
        logger.info('开始缓存预热', { orderCount: commonOrders.length });

        for (const order of commonOrders) {
            try {
                const key = this.generateCacheKey(order);
                
                // 如果缓存中没有，则进行计算并缓存
                if (!this.get(key)) {
                    // 这里应该调用实际的排程计算
                    // const result = await scheduler.generateSchedulePlans(order);
                    // this.set(key, result);
                }
            } catch (error) {
                logger.error('缓存预热失败', { 
                    error: error.message, 
                    orderId: order.id 
                });
            }
        }

        logger.info('缓存预热完成', { 
            cacheSize: this.cache.size,
            hitRate: this.getHitRate()
        });
    }

    /**
     * 导出缓存数据
     * @returns {Object} 缓存数据
     */
    export() {
        const exportData = {
            timestamp: Date.now(),
            config: this.config,
            stats: this.stats,
            entries: []
        };

        for (const [key, entry] of this.cache) {
            exportData.entries.push({
                key,
                ...entry
            });
        }

        return exportData;
    }

    /**
     * 导入缓存数据
     * @param {Object} importData 导入数据
     */
    import(importData) {
        if (!importData || !importData.entries) {
            throw new Error('无效的导入数据');
        }

        this.clear();

        for (const entry of importData.entries) {
            const { key, ...entryData } = entry;
            
            // 检查是否过期
            if (Date.now() <= entryData.expireAt) {
                this.cache.set(key, entryData);
            }
        }

        logger.info('缓存数据导入完成', {
            importedCount: this.cache.size
        });
    }

    /**
     * 更新缓存TTL配置
     * @param {number} newTTL 新的TTL值（毫秒）
     */
    async updateTTL(newTTL) {
        if (typeof newTTL !== 'number' || newTTL <= 0) {
            throw new Error('TTL必须是正数');
        }

        this.config.ttl = newTTL;

        logger.info('缓存TTL已更新', {
            newTTL: newTTL,
            newTTLMinutes: Math.round(newTTL / (60 * 1000))
        });

        // 重新启动清理定时器以应用新的TTL
        this.stopCleanupTimer();
        this.startCleanupTimer();
    }
}

// 创建全局缓存实例
const schedulingCache = new SchedulingCache();

module.exports = schedulingCache;
