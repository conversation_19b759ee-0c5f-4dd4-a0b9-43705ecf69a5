/* 设备管理模块 - 设备信息页面样式文件 */

/* 侧边栏按钮增强样式 */
.sidebar-btn {
    transition: all 0.2s ease-in-out;
}

.sidebar-btn:hover {
    transform: translateX(2px);
}

/* 统计卡片样式 */
.stat-card {
    border-radius: 12px;
    padding: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    opacity: 0.1;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 图表容器样式 */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 过滤按钮样式 */
.filter-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 16px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s;
}

.filter-btn:hover {
    background: #e9ecef;
}

/* 操作按钮样式 */
.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

/* 按钮颜色主题 */
.btn-view { 
    background: #e3f2fd; 
    color: #1976d2; 
}

.btn-edit { 
    background: #f3e5f5; 
    color: #7b1fa2; 
}

.btn-delete { 
    background: #ffebee; 
    color: #d32f2f; 
}

.btn-view:hover { 
    background: #bbdefb; 
}

.btn-edit:hover { 
    background: #e1bee7; 
}

.btn-delete:hover { 
    background: #ffcdd2; 
}

/* 状态标签样式 */
.status-active { 
    background: #4caf50; 
    color: white; 
    padding: 4px 8px; 
    border-radius: 12px; 
    font-size: 12px; 
}

.status-inactive { 
    background: #f44336; 
    color: white; 
    padding: 4px 8px; 
    border-radius: 12px; 
    font-size: 12px; 
}

/* 加载动画样式覆盖 */
.loading-overlay {
    background-color: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
}

/* 下拉框样式 */
select.appearance-none {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}
