/**
 * 用户路由
 * 处理用户相关的路由
 */

const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateJWT, authorizeRoles, checkPermission } = require('../middlewares/auth');
const upload = require('../middlewares/upload');

// 获取所有用户 - 需要查看用户权限
router.get('/',
    authenticateJWT,
    checkPermission('view_users'),
    userController.getAllUsers
);

// 获取厂长用户列表 - 需要查看用户权限或新建申请权限
router.get('/factory-managers',
    authenticateJWT,
    checkPermission(['view_users', 'new_application']), // 允许用户管理或申请创建权限
    userController.getFactoryManagers
);

// 获取经理用户列表 - 需要查看用户权限或新建申请权限
router.get('/managers',
    authenticateJWT,
    checkPermission(['view_users', 'new_application']), // 允许用户管理或申请创建权限
    userController.getManagers
);

// 创建新用户 - 需要创建用户权限
router.post('/',
    authenticateJWT,
    checkPermission('create_user'),
    userController.createUser
);

// 导出用户数据 - 需要管理员权限
router.get('/export',
    authenticateJWT,
    authorizeRoles('admin'),
    userController.exportUsers
);

// 导入用户数据 - 需要管理员权限
router.post('/import',
    authenticateJWT,
    authorizeRoles('admin'),
    upload.single('file'),
    userController.importUsers
);

// 修改密码 - 用户可以修改自己的密码，管理员可以修改任何用户密码
router.put('/password',
    authenticateJWT,
    userController.changePassword
);

// === 具体路由必须在通用路由之前 ===

// 检查用户删除状态 - 需要删除用户权限
router.get('/:id/deletion-status',
    authenticateJWT,
    checkPermission('delete_user'),
    userController.checkUserDeletionStatus
);

// 获取用户权限 - 需要管理权限权限
router.get('/:id/permissions',
    authenticateJWT,
    checkPermission('manage_permissions'),
    userController.getUserPermissions
);

// 更新用户权限 - 需要管理权限权限
router.put('/:id/permissions',
    authenticateJWT,
    checkPermission('manage_permissions'),
    userController.updateUserPermissions
);

// === 通用路由必须在具体路由之后 ===

// 更新用户 - 需要编辑用户权限
router.put('/:id',
    authenticateJWT,
    checkPermission('edit_user'),
    userController.updateUser
);

// 删除用户 - 需要删除用户权限
router.delete('/:id',
    authenticateJWT,
    checkPermission('delete_user'),
    userController.deleteUser
);

// 上传用户电子签名
router.post('/:id/signature',
    authenticateJWT,
    upload.single('signature'),
    userController.uploadSignature
);

// 上传当前用户电子签名
router.post('/signature',
    authenticateJWT,
    upload.single('signature'),
    userController.uploadCurrentUserSignature
);

// 删除用户电子签名
router.delete('/:id/signature',
    authenticateJWT,
    userController.deleteSignature
);

// 获取用户电子签名 - 所有认证用户都可以查看签名
router.get('/:id/signature',
    authenticateJWT,
    userController.getSignature
);



module.exports = router;
