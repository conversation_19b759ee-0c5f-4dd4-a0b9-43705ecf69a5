# 设备管理 API 文档

## 📋 概述

设备管理模块提供完整的设备生命周期管理，包括设备信息管理、维护记录、健康评估、厂区管理等功能。

## 🔐 权限要求

| 操作 | 所需权限 |
|------|----------|
| 查看设备信息 | `equipment_info` |
| 管理设备 | `equipment_manage` |
| 设备维护 | `equipment_maintenance` |
| 健康评估 | `equipment_health` |

## 🏭 API 接口

### 1. 获取设备列表

**GET** `/api/equipment`

获取设备列表，支持分页、搜索、筛选和统计。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |
| search | string | 否 | 搜索关键词（设备编号、名称） |
| status | string | 否 | 状态筛选（运行中、维护中、停机、报废） |
| factory | string | 否 | 厂区筛选 |
| type | string | 否 | 设备类型筛选 |
| responsible | string | 否 | 责任人筛选 |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "eq001",
      "equipmentCode": "EQ-001",
      "equipmentName": "数控机床A",
      "type": "数控机床",
      "model": "CNC-2000",
      "manufacturer": "某某机械",
      "status": "运行中",
      "factory": "第一厂区",
      "location": "车间A-01",
      "responsiblePerson": "张三",
      "purchaseDate": "2023-01-15",
      "warrantyExpiry": "2026-01-15",
      "lastMaintenanceDate": "2025-07-01",
      "nextMaintenanceDate": "2025-10-01",
      "healthScore": 85,
      "healthLevel": "良好",
      "specifications": {
        "power": "15KW",
        "weight": "2500kg",
        "dimensions": "3000x2000x2500mm"
      },
      "createdAt": "2023-01-15T00:00:00.000Z",
      "updatedAt": "2025-07-29T10:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  },
  "statistics": {
    "total": 150,
    "byStatus": {
      "运行中": 120,
      "维护中": 15,
      "停机": 10,
      "报废": 5
    },
    "byFactory": {
      "第一厂区": 80,
      "第二厂区": 70
    }
  }
}
```

### 2. 获取设备详情

**GET** `/api/equipment/:id`

获取指定设备的详细信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 设备ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": "eq001",
    "equipmentCode": "EQ-001",
    "equipmentName": "数控机床A",
    "type": "数控机床",
    "model": "CNC-2000",
    "manufacturer": "某某机械",
    "status": "运行中",
    "factory": "第一厂区",
    "location": "车间A-01",
    "responsiblePerson": "张三",
    "purchaseDate": "2023-01-15",
    "purchasePrice": "500000",
    "warrantyExpiry": "2026-01-15",
    "lastMaintenanceDate": "2025-07-01",
    "nextMaintenanceDate": "2025-10-01",
    "healthScore": 85,
    "healthLevel": "良好",
    "specifications": {
      "power": "15KW",
      "voltage": "380V",
      "weight": "2500kg",
      "dimensions": "3000x2000x2500mm",
      "workingTemperature": "-10°C ~ 50°C"
    },
    "maintenanceHistory": [
      {
        "id": "mh001",
        "type": "保养",
        "date": "2025-07-01",
        "operator": "李四",
        "description": "定期保养检查",
        "result": "正常",
        "cost": "500"
      }
    ],
    "createdAt": "2023-01-15T00:00:00.000Z",
    "updatedAt": "2025-07-29T10:00:00.000Z"
  }
}
```

### 3. 创建设备

**POST** `/api/equipment`

创建新设备。

#### 请求参数

```json
{
  "equipmentCode": "EQ-002",
  "equipmentName": "数控机床B",
  "type": "数控机床",
  "model": "CNC-3000",
  "manufacturer": "某某机械",
  "status": "运行中",
  "factory": "第一厂区",
  "location": "车间A-02",
  "responsiblePerson": "王五",
  "purchaseDate": "2025-07-29",
  "purchasePrice": "600000",
  "warrantyExpiry": "2028-07-29",
  "specifications": {
    "power": "20KW",
    "voltage": "380V",
    "weight": "3000kg",
    "dimensions": "3500x2200x2800mm"
  }
}
```

#### 响应示例

**成功响应 (201)**:
```json
{
  "success": true,
  "message": "设备创建成功",
  "data": {
    "id": "eq002",
    "equipmentCode": "EQ-002",
    "equipmentName": "数控机床B",
    "status": "运行中",
    "createdAt": "2025-07-29T11:00:00.000Z"
  }
}
```

### 4. 更新设备

**PUT** `/api/equipment/:id`

更新设备信息。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 设备ID |

#### 请求参数

```json
{
  "equipmentName": "数控机床B（升级版）",
  "status": "维护中",
  "location": "车间A-03",
  "responsiblePerson": "赵六",
  "specifications": {
    "power": "22KW",
    "voltage": "380V",
    "weight": "3200kg"
  }
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "设备更新成功",
  "data": {
    "id": "eq002",
    "equipmentName": "数控机床B（升级版）",
    "status": "维护中",
    "updatedAt": "2025-07-29T11:30:00.000Z"
  }
}
```

### 5. 删除设备

**DELETE** `/api/equipment/:id`

删除设备（软删除，设置状态为报废）。

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | string | 是 | 设备ID |

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "设备删除成功"
}
```

## 🏭 厂区管理

### 6. 获取厂区列表

**GET** `/api/equipment/factories`

获取所有厂区列表。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "factory001",
      "name": "第一厂区",
      "description": "主要生产车间",
      "location": "工业园区A区",
      "manager": "张厂长",
      "equipmentCount": 80,
      "createdAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "factory002",
      "name": "第二厂区", 
      "description": "辅助生产车间",
      "location": "工业园区B区",
      "manager": "李厂长",
      "equipmentCount": 70,
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### 7. 创建厂区

**POST** `/api/equipment/factories`

创建新厂区。

#### 请求参数

```json
{
  "name": "第三厂区",
  "description": "新建生产车间",
  "location": "工业园区C区",
  "manager": "王厂长"
}
```

#### 响应示例

**成功响应 (201)**:
```json
{
  "success": true,
  "message": "厂区创建成功",
  "data": {
    "id": "factory003",
    "name": "第三厂区",
    "createdAt": "2025-07-29T11:00:00.000Z"
  }
}
```

### 8. 更新厂区

**PUT** `/api/equipment/factories/:id`

更新厂区信息。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "厂区更新成功"
}
```

### 9. 删除厂区

**DELETE** `/api/equipment/factories/:id`

删除厂区。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "厂区删除成功"
}
```

## 🔧 维护记录管理

### 10. 获取维护记录

**GET** `/api/equipment/:id/maintenance`

获取设备的维护记录列表。

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "mr001",
      "equipmentId": "eq001",
      "type": "保养",
      "startTime": "2025-07-01T08:00:00.000Z",
      "endTime": "2025-07-01T12:00:00.000Z",
      "operator": "李四",
      "description": "定期保养检查",
      "result": "正常",
      "severity": "轻微",
      "status": "已完成",
      "cost": "500",
      "createdAt": "2025-07-01T08:00:00.000Z"
    }
  ]
}
```

### 11. 批量导入设备

**POST** `/api/equipment/import`

批量导入设备数据（Excel文件）。

#### 请求格式
- **Content-Type**: `multipart/form-data`
- **文件字段**: `file`
- **支持格式**: Excel (.xlsx, .xls)

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "设备导入成功",
  "data": {
    "imported": 50,
    "failed": 2,
    "errors": [
      {
        "row": 3,
        "error": "设备编号已存在"
      }
    ]
  }
}
```

### 12. 导出设备数据

**GET** `/api/equipment/export`

导出设备数据为Excel文件。

#### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| format | string | 否 | 导出格式，默认excel |
| filters | object | 否 | 筛选条件 |

#### 响应
返回Excel文件流，浏览器会自动下载文件。

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| EQUIPMENT_NOT_FOUND | 404 | 设备不存在 |
| EQUIPMENT_CODE_EXISTS | 409 | 设备编号已存在 |
| FACTORY_NOT_FOUND | 404 | 厂区不存在 |
| INVALID_STATUS | 400 | 无效的设备状态 |
| IMPORT_ERROR | 400 | 导入文件格式错误 |

### 设备状态说明

| 状态 | 描述 | 可执行操作 |
|------|------|------------|
| 运行中 | 正常运行 | 维护、停机 |
| 维护中 | 维护保养 | 恢复运行 |
| 停机 | 暂停使用 | 恢复运行、报废 |
| 报废 | 已报废 | 查看记录 |

---

**更新时间**: 2025-07-29
**版本**: v1.0
