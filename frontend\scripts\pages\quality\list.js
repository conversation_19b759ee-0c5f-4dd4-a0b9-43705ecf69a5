/**
 * 质量检测报告列表页面
 * 显示和管理检测报告列表
 */

import { createStandardApp } from '../../common/pageInit.js';
import { logUserInfo, debugLog } from '../../common/debugUtils.js';
import Sidebar from '../../../components/common/Sidebar.js';
import QualityReportList from '../../../components/quality/QualityReportList.js';
import QualityReportDetail from '../../../components/quality/QualityReportDetail.js';
import { getQualityReportById, deleteQualityReport } from '../../api/quality.js';

createStandardApp({
    components: {
        Sidebar,
        QualityReportList,
        QualityReportDetail
    },
    setup() {
        const { ref, computed } = Vue;

        // 页面状态
        // 从localStorage获取侧边栏状态，默认为false（桌面端展开，移动端折叠）
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);
        const showDetailModal = ref(false);
        const selectedReport = ref(null);
        const listComponent = ref(null);

        // 权限检查
        const canUpload = computed(() => {
            return window.currentUser && (
                window.currentUser.role === 'admin' ||
                window.currentUser.role === 'CEO' ||
                window.currentUser.role === '管理员' ||
                window.currentUser.role === 'ceo' ||
                (window.currentUser.permissions && window.currentUser.permissions.includes('quality_upload'))
            );
        });

        // 查看报告详情
        async function handleViewDetail(report) {
            try {
                // 获取完整的报告详情（包括文件列表）
                const response = await getQualityReportById(report.id);
                
                if (response.success) {
                    selectedReport.value = response.report;
                    showDetailModal.value = true;
                } else {
                    throw new Error(response.message || '获取报告详情失败');
                }
            } catch (error) {
                console.error('获取报告详情失败:', error);
                
                let errorMessage = '获取报告详情失败';
                if (error.response?.data?.message) {
                    errorMessage += ': ' + error.response.data.message;
                } else if (error.message) {
                    errorMessage += ': ' + error.message;
                }

                if (window.showNotification) {
                    window.showNotification(errorMessage, 'error');
                } else {
                    alert(errorMessage);
                }
            }
        }

        // 编辑报告
        function handleEditReport(report) {
            // 跳转到编辑页面（如果有的话）或者显示编辑表单
            window.location.href = `/quality-upload?edit=${report.id}`;
        }

        // 从详情页面编辑
        function handleEditFromDetail(report) {
            closeDetailModal();
            handleEditReport(report);
        }

        // 删除报告
        async function handleDeleteReport(deleteData) {
            const { report, deletedReport, reportIndex } = deleteData;

            try {
                const response = await deleteQualityReport(report.id);

                if (response.success) {
                    // 刷新数据以确保一致性
                    if (listComponent.value && listComponent.value.fetchReports) {
                        await listComponent.value.fetchReports();
                    }

                    if (window.showNotification) {
                        window.showNotification('检测报告删除成功', 'success');
                    } else {
                        alert('检测报告删除成功');
                    }
                } else {
                    // 删除失败，通过子组件恢复数据
                    if (listComponent.value && listComponent.value.restoreDeletedReport) {
                        listComponent.value.restoreDeletedReport(deletedReport, reportIndex);
                    }
                    throw new Error(response.message || '删除失败');
                }
            } catch (error) {
                console.error('删除检测报告失败:', error);

                // 删除失败，通过子组件恢复数据
                if (listComponent.value && listComponent.value.restoreDeletedReport) {
                    listComponent.value.restoreDeletedReport(deletedReport, reportIndex);
                }

                let errorMessage = '删除检测报告失败';
                if (error.response?.data?.message) {
                    errorMessage += ': ' + error.response.data.message;
                } else if (error.message) {
                    errorMessage += ': ' + error.message;
                }

                if (window.showNotification) {
                    window.showNotification(errorMessage, 'error');
                } else {
                    alert(errorMessage);
                }

                // 重新抛出错误，让子组件也能处理
                throw error;
            }
        }

        // 从详情页面删除
        async function handleDeleteFromDetail(report) {
            closeDetailModal();

            // 直接调用列表组件的删除方法
            if (listComponent.value && listComponent.value.deleteReport) {
                await listComponent.value.deleteReport(report);
            }
        }

        // 关闭详情模态框
        function closeDetailModal() {
            showDetailModal.value = false;
            selectedReport.value = null;
        }

        // 处理键盘事件
        function handleKeydown(event) {
            // ESC键关闭模态框
            if (event.key === 'Escape' && showDetailModal.value) {
                closeDetailModal();
            }
        }

        // 监听键盘事件
        document.addEventListener('keydown', handleKeydown);

        return {
            sidebarOpen,
            showDetailModal,
            selectedReport,
            listComponent,
            canUpload,
            handleViewDetail,
            handleEditReport,
            handleEditFromDetail,
            handleDeleteReport,
            handleDeleteFromDetail,
            closeDetailModal
        };
    },
    requiredPermissions: ['quality_view'],
    onUserLoaded: async (user) => {
        logUserInfo('质量检测报告列表页面加载完成', user);

        // 将用户信息设置为全局变量，供组件使用
        window.currentUser = user;

        // 检查URL参数，如果有refresh参数，说明是从上传页面跳转过来的
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('refresh')) {
            debugLog('检测到刷新参数，强制刷新列表数据');
            // 清理URL参数
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }
}).mount('#app');
